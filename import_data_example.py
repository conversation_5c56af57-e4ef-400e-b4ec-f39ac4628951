"""
按表类型分别导入数据的示例实现
功能：展示如何修改import_data.py中的关键函数
实现逻辑：
1. 按表类型分组待导入的工作表
2. 为每个表类型创建专用的临时映射文件
3. 按表类型顺序依次导入数据
"""

import os
import json
import logging
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QMessageBox
from PySide6.QtGui import QColor

# 需要修改的函数

def confirm_import(self):
    """按表类型分别导入数据"""
    temp_file = 'temp_mapping.json'
    if not os.path.exists(temp_file):
        QMessageBox.warning(self, "警告", "未找到匹配规则文件，请先完成字段匹配")
        return

    # 读取保存的匹配规则
    with open(temp_file, 'r', encoding='utf-8') as f:
        all_mappings = json.load(f)

    # 按表类型分组的文件列表
    files_by_table_type = {}
    unmatched_files = []
    
    logging.info(f"🔍 开始检查 {len(all_mappings)} 个匹配规则")
    
    # 遍历文件树，按表类型分组待导入的工作表
    for i in range(self.file_tree.topLevelItemCount()):
        file_item = self.file_tree.topLevelItem(i)
        for j in range(file_item.childCount()):
            worksheet_item = file_item.child(j)
            
            # 获取工作表数据
            worksheet_data = worksheet_item.data(0, Qt.UserRole)
            if not worksheet_data:
                logging.warning(f"⚠️ 工作表数据缺失，跳过: {worksheet_item.text(0)}")
                continue
            
            file_path = worksheet_data['file_path']
            worksheet_name = worksheet_data['worksheet_name']
            
            # 获取表类型
            table_type_widget = self.file_tree.itemWidget(worksheet_item, 1)
            if table_type_widget and hasattr(table_type_widget, 'currentText'):
                table_type = table_type_widget.currentText()
            else:
                table_type = worksheet_item.text(1)
            
            # 构建工作表键
            worksheet_key = f"{file_path}::{worksheet_name}"
            
            logging.info(f"📋 检查工作表: {worksheet_item.text(0)} (键: {worksheet_key})")
            logging.info(f"   匹配状态: {worksheet_item.text(2)}")
            logging.info(f"   表类型: {table_type}")

            if worksheet_item.text(2) == "已完成字段匹配" and worksheet_key in all_mappings:
                matched_info = all_mappings[worksheet_key]
                logging.info(f"✅ 找到匹配规则: {matched_info.get('rule_name', '未知规则')}")
                
                if matched_info['table_type'] == table_type:
                    # 读取完整的工作表数据用于导入
                    df = self.read_full_worksheet_data(file_path, worksheet_name)
                    if df is not None and not df.empty:
                        # 按表类型分组
                        if table_type not in files_by_table_type:
                            files_by_table_type[table_type] = []
                        
                        files_by_table_type[table_type].append((file_path, df, table_type, matched_info['matched_fields'], worksheet_name))
                        logging.info(f"✅ 添加到导入列表: {worksheet_item.text(0)} ({len(df)} 行数据)")
                    else:
                        logging.warning(f"⚠️ 读取数据失败或数据为空: {worksheet_item.text(0)}")
                else:
                    logging.warning(f"⚠️ 表类型不匹配: 期望 {table_type}, 实际 {matched_info['table_type']}")
            else:
                unmatched_files.append(f"{worksheet_item.text(0)} (工作表)")
                if worksheet_item.text(2) != "已完成字段匹配":
                    logging.info(f"⚠️ 字段匹配未完成: {worksheet_item.text(0)}")
                else:
                    logging.warning(f"⚠️ 未找到匹配规则: {worksheet_key}")

    # 汇总所有表类型的文件数量
    total_files_to_import = sum(len(files) for files in files_by_table_type.values())
    logging.info(f"📊 导入检查完成: 可导入 {total_files_to_import} 个工作表, 未匹配 {len(unmatched_files)} 个")
    logging.info(f"📊 按表类型分组: {', '.join([f'{table}: {len(files)}个' for table, files in files_by_table_type.items()])}")

    if total_files_to_import == 0:
        QMessageBox.warning(self, "警告", 
                          f"没有可以导入的文件！\n\n"
                          f"📋 检查结果:\n"
                          f"• 总匹配规则数: {len(all_mappings)}\n"
                          f"• 未匹配工作表: {len(unmatched_files)}\n\n"
                          f"💡 可能的原因:\n"
                          f"1. 工作表字段匹配未完成\n"
                          f"2. 表类型选择不正确\n"
                          f"3. 数据文件读取失败\n\n"
                          f"🔧 建议操作:\n"
                          f"1. 检查工作表匹配状态是否为'已完成字段匹配'\n"
                          f"2. 确认表类型选择正确\n"
                          f"3. 重新进行字段匹配")
        return

    # 创建主进度对话框
    self.progress_dialog = self.create_progress_dialog("数据导入进度")
    self.progress_dialog.show()

    # 为每个表类型创建单独的临时映射文件
    for table_type, files in files_by_table_type.items():
        # 为此表类型创建专用的临时映射文件
        table_mapping_file = f'temp_mapping_{table_type.replace(" ", "_")}.json'
        table_mappings = {}
        
        # 提取此表类型的映射规则
        for file_path, _, _, matched_fields, worksheet_name in files:
            worksheet_key = f"{file_path}::{worksheet_name}"
            if worksheet_key in all_mappings:
                table_mappings[worksheet_key] = all_mappings[worksheet_key]
        
        # 保存此表类型的映射规则
        with open(table_mapping_file, 'w', encoding='utf-8') as f:
            json.dump(table_mappings, f, ensure_ascii=False, indent=4)
        
        logging.info(f"✅ 已为表类型 '{table_type}' 创建专用映射文件: {table_mapping_file}")

    # 按表类型顺序处理导入
    self.table_types_to_import = list(files_by_table_type.keys())
    self.files_by_table_type = files_by_table_type
    self.current_table_type_index = 0
    self.total_imported = 0
    
    # 开始导入第一个表类型
    self.import_next_table_type()

def import_next_table_type(self):
    """导入下一个表类型的数据"""
    if self.current_table_type_index >= len(self.table_types_to_import):
        # 所有表类型都已导入完成
        self.all_imports_finished()
        return
    
    # 获取当前要导入的表类型
    current_table_type = self.table_types_to_import[self.current_table_type_index]
    files_to_import = self.files_by_table_type[current_table_type]
    
    logging.info(f"🔄 开始导入表类型: {current_table_type} ({len(files_to_import)} 个工作表)")
    
    # 更新进度对话框标题和状态
    self.progress_dialog.setWindowTitle(f"导入 {current_table_type} 数据 ({self.current_table_type_index + 1}/{len(self.table_types_to_import)})")
    self.progress_dialog.update_status(f"正在导入 {current_table_type} 数据...")
    
    # 创建并配置导入线程
    self.import_thread = DataImportThread(
        case_id=self.case_id,
        db_path=self.db_path,
        files=files_to_import
    )
    
    # 连接信号槽
    self.import_thread.update_progress_signal.connect(self.progress_dialog.update_progress)
    self.import_thread.task_finished_signal.connect(self.show_import_result)
    self.import_thread.error_signal.connect(self.show_import_error)
    self.import_thread.finished.connect(self.table_type_import_finished)
    
    self.threads.append(self.import_thread)
    self.import_thread.start()

def table_type_import_finished(self):
    """一个表类型的数据导入完成后的处理"""
    current_table_type = self.table_types_to_import[self.current_table_type_index]
    logging.info(f"✅ 表类型 '{current_table_type}' 导入完成")
    
    # 移动到下一个表类型
    self.current_table_type_index += 1
    
    # 更新进度条显示总体进度
    overall_progress = int((self.current_table_type_index / len(self.table_types_to_import)) * 100)
    self.progress_dialog.update_progress(overall_progress)
    
    # 继续导入下一个表类型
    self.import_next_table_type()

def all_imports_finished(self):
    """所有表类型都导入完成后的处理"""
    try:
        # 创建辅助索引
        conn = get_db_connection()
        cursor = conn.cursor()
        
        self.progress_dialog.update_status("正在创建数据库索引...")
        
        # 创建交易账号相关索引（基于导入阶段已补全的字段）
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_jiaoyi_zhangkahao 
            ON 账户交易明细表 (交易账卡号) 
            WHERE 交易账卡号 IS NOT NULL AND 交易账卡号 != '';
        """)
        # ... 其他索引创建代码 ...
        
        conn.commit()
        logging.info("已成功创建所有辅助索引")
        
    except Exception as e:
        logging.error(f"创建索引时发生错误: {e}")
    finally:
        if conn:
            conn.close()
    
    # 清理临时映射文件
    self.clear_temp_mapping_files()
    
    # 完成导入流程
    self.progress_dialog.update_status("导入完成！")
    self.progress_dialog.set_finished(True)
    
    # 显示总结信息
    QMessageBox.information(self, "导入完成", 
                          f"所有数据导入完成！\n\n"
                          f"📊 导入统计:\n"
                          f"• 导入表类型: {len(self.table_types_to_import)} 种\n"
                          f"• 导入工作表: {sum(len(files) for files in self.files_by_table_type.values())} 个\n"
                          f"• 导入记录总数: {self.total_imported} 条\n\n"
                          f"✅ 数据已成功导入到数据库，并创建了必要的索引")

def clear_temp_mapping_files(self):
    """清空所有临时匹配文件"""
    # 清空主临时文件
    temp_file = 'temp_mapping.json'
    if os.path.exists(temp_file):
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump({}, f, ensure_ascii=False, indent=4)
        logging.info(f"{temp_file} 已清空")
    
    # 清空表类型专用临时文件
    for table_type in self.table_types_to_import:
        table_mapping_file = f'temp_mapping_{table_type.replace(" ", "_")}.json'
        if os.path.exists(table_mapping_file):
            try:
                os.remove(table_mapping_file)
                logging.info(f"{table_mapping_file} 已删除")
            except Exception as e:
                logging.error(f"删除 {table_mapping_file} 时出错: {e}")

def show_import_result(self, total_imported, file_path, count):
    """显示导入结果"""
    self.total_imported += count
    current_table_type = self.table_types_to_import[self.current_table_type_index]
    logging.info(f"导入完成: {file_path}, 导入 {count} 条记录, 表类型: {current_table_type}, 总计 {self.total_imported} 条")
    
    # 更新树视图中的导入状态
    for i in range(self.file_tree.topLevelItemCount()):
        file_item = self.file_tree.topLevelItem(i)
        for j in range(file_item.childCount()):
            worksheet_item = file_item.child(j)
            worksheet_data = worksheet_item.data(0, Qt.UserRole)
            if worksheet_data and worksheet_data['file_path'] == file_path:
                worksheet_item.setText(4, "已导入")
                worksheet_item.setText(5, str(count))
                worksheet_item.setForeground(4, QColor("#00D016"))
                break

# 需要添加到ProgressBarDialog类的方法
def update_status(self, text):
    """更新状态文本"""
    self.status_label.setText(text)

# 创建进度对话框的辅助函数
def create_progress_dialog(self, title):
    """创建并配置进度对话框"""
    dialog = ProgressBarDialog(title=title)
    dialog.setWindowTitle(title)
    return dialog 