import pandas as pd
import numpy as np

def analyze_excel_config():
    """分析数据库表格配置Excel文件"""
    try:
        # 读取Excel文件
        df = pd.read_excel('数据库表格配置.xlsx', sheet_name='Sheet1')
        
        print('Excel文件结构分析:')
        print(f'总行数: {len(df)}')
        print(f'总列数: {len(df.columns)}')
        print()
        
        # 查看前几行的详细结构
        print('前10行数据:')
        for i in range(min(10, len(df))):
            row = df.iloc[i]
            # 只显示前6列以便查看
            row_data = []
            for j in range(min(6, len(row))):
                val = row.iloc[j]
                if pd.notna(val):
                    row_data.append(str(val))
                else:
                    row_data.append("NaN")
            print(f'第{i}行: {row_data}')
        
        print()
        
        # 查看第2列（表名列）的内容
        print('表名列内容:')
        table_names = df.iloc[:, 1].dropna().unique()
        for i, name in enumerate(table_names):
            print(f'{i+1}. {name}')
        
        print(f'\n总表数量: {len(table_names)}')
        
        # 分析每个表的字段结构
        print('\n表格字段分析:')
        tables_info = []
        
        for i, table_name in enumerate(table_names):
            # 找到该表对应的行
            table_row = df[df.iloc[:, 1] == table_name].iloc[0]
            
            # 提取字段信息（从第3列开始，忽略NaN和Unnamed列）
            fields = []
            for j in range(2, len(table_row)):  # 从第3列开始
                field_name = table_row.iloc[j]
                if pd.notna(field_name) and str(field_name).strip() != '' and not str(field_name).startswith('Unnamed'):
                    fields.append(str(field_name).strip())
            
            tables_info.append({
                'table_name': table_name,
                'fields': fields
            })
            
            print(f'{i+1}. {table_name} - 字段数: {len(fields)}')
            if len(fields) > 0:
                print(f'   字段: {fields[:10]}{"..." if len(fields) > 10 else ""}')
        
        return tables_info
        
    except Exception as e:
        print(f'分析Excel文件失败: {e}')
        return []

if __name__ == "__main__":
    tables_info = analyze_excel_config()
    
    print(f'\n总结: 发现 {len(tables_info)} 个表的配置信息') 