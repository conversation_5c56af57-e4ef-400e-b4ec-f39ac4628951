#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现金匹配诊断脚本

本文件的功能和实现逻辑：
1. 诊断ATM取款、ATM存款等关键词没有被识别为现金的原因
2. 分析现金匹配逻辑的执行情况
3. 提供详细的诊断报告和修复建议
4. 测试现金关键词的匹配效果

主要功能：
- 查找包含ATM关键词但未被标记为现金的记录
- 分析这些记录不匹配的具体原因
- 验证现金匹配的前提条件
- 生成详细的诊断报告
"""

import logging
import sys
from datetime import datetime
from database_setup import get_db_connection

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'cash_matching_diagnosis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class CashMatchingDiagnostic:
    """现金匹配诊断器"""
    
    def __init__(self, case_id):
        self.case_id = case_id
        self.conn = None
        self.cursor = None
        self.diagnosis_results = {}
    
    def setup_database(self):
        """设置数据库连接"""
        try:
            self.conn = get_db_connection()
            if not self.conn:
                raise Exception("无法获取数据库连接")
            
            self.cursor = self.conn.cursor()
            logging.info("✅ 数据库连接建立成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def cleanup_database(self):
        """清理数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
            logging.info("✅ 数据库连接已关闭")
        except Exception as e:
            logging.error(f"⚠️ 关闭数据库连接时出错: {e}")
    
    def analyze_atm_records(self):
        """分析ATM相关记录"""
        logging.info("🔍 开始分析ATM相关记录...")
        
        try:
            # 查找包含ATM关键词的所有记录
            self.cursor.execute("""
                SELECT 
                    id,
                    "摘要说明",
                    "交易类型", 
                    "对手户名",
                    "对手账号",
                    "对手卡号",
                    "交易金额",
                    "收付标志"
                FROM "账户交易明细表"
                WHERE "案件编号" = %s
                AND (
                    LOWER("摘要说明") LIKE '%atm取款%' 
                    OR LOWER("摘要说明") LIKE '%atm存款%'
                    OR LOWER("摘要说明") LIKE '%atm%'
                    OR LOWER("交易类型") LIKE '%atm取款%'
                    OR LOWER("交易类型") LIKE '%atm存款%'
                    OR LOWER("交易类型") LIKE '%atm%'
                )
                ORDER BY id
                LIMIT 100
            """, (self.case_id,))
            
            atm_records = self.cursor.fetchall()
            
            if not atm_records:
                logging.warning("⚠️ 未找到包含ATM关键词的记录")
                return
            
            logging.info(f"📊 找到 {len(atm_records)} 条包含ATM关键词的记录")
            
            # 分析这些记录
            cash_marked = 0
            not_cash_marked = 0
            analysis_details = []
            
            for record in atm_records:
                record_id, summary, transaction_type, counterpart_name, counterpart_account, counterpart_card, amount, direction = record
                
                is_cash = counterpart_name == '现金'
                
                if is_cash:
                    cash_marked += 1
                else:
                    not_cash_marked += 1
                    
                    # 分析为什么没有被标记为现金
                    reasons = []
                    
                    if counterpart_name and counterpart_name.strip():
                        reasons.append(f"对手户名不为空: '{counterpart_name}'")
                    
                    if counterpart_account and counterpart_account.strip():
                        reasons.append(f"对手账号不为空: '{counterpart_account}'")
                    
                    if counterpart_card and counterpart_card.strip():
                        reasons.append(f"对手卡号不为空: '{counterpart_card}'")
                    
                    # 检查是否包含例外关键词
                    if summary and '转账' in summary.lower():
                        reasons.append("摘要说明包含例外关键词'转账'")
                    
                    if transaction_type and '转账' in transaction_type.lower():
                        reasons.append("交易类型包含例外关键词'转账'")
                    
                    if not reasons:
                        reasons.append("未知原因 - 可能是匹配逻辑问题")
                    
                    analysis_details.append({
                        'id': record_id,
                        'summary': summary,
                        'transaction_type': transaction_type,
                        'counterpart_name': counterpart_name,
                        'counterpart_account': counterpart_account,
                        'counterpart_card': counterpart_card,
                        'amount': amount,
                        'direction': direction,
                        'reasons': reasons
                    })
            
            self.diagnosis_results['atm_analysis'] = {
                'total_atm_records': len(atm_records),
                'cash_marked': cash_marked,
                'not_cash_marked': not_cash_marked,
                'analysis_details': analysis_details[:10]  # 只保存前10条详细信息
            }
            
            logging.info(f"📊 ATM记录分析结果:")
            logging.info(f"   - 总ATM记录数: {len(atm_records)}")
            logging.info(f"   - 已标记为现金: {cash_marked}")
            logging.info(f"   - 未标记为现金: {not_cash_marked}")
            
            if not_cash_marked > 0:
                logging.warning(f"⚠️ 发现 {not_cash_marked} 条ATM记录未被标记为现金")
                
                # 显示前几条未标记的记录详情
                for i, detail in enumerate(analysis_details[:5], 1):
                    logging.info(f"   未标记记录 {i}:")
                    logging.info(f"     ID: {detail['id']}")
                    logging.info(f"     摘要说明: {detail['summary']}")
                    logging.info(f"     交易类型: {detail['transaction_type']}")
                    logging.info(f"     对手户名: {detail['counterpart_name']}")
                    logging.info(f"     原因: {'; '.join(detail['reasons'])}")
            
        except Exception as e:
            logging.error(f"❌ 分析ATM记录时出错: {e}")
    
    def test_cash_keywords(self):
        """测试现金关键词的匹配效果"""
        logging.info("🧪 开始测试现金关键词匹配效果...")
        
        # 定义要测试的关键词
        test_keywords = [
            'ATM取款', 'ATM存款', 'atm取款', 'atm存款',
            '现金', '取款', '存款', 'ATM', 'atm',
            '现金存款', '现金取款', 'ATM机'
        ]
        
        keyword_results = {}
        
        try:
            for keyword in test_keywords:
                # 测试摘要说明字段
                self.cursor.execute("""
                    SELECT COUNT(*) 
                    FROM "账户交易明细表"
                    WHERE "案件编号" = %s
                    AND LOWER("摘要说明") LIKE %s
                """, (self.case_id, f'%{keyword.lower()}%'))
                
                summary_count = self.cursor.fetchone()[0] if self.cursor.fetchone() else 0
                
                # 重新执行查询获取结果
                self.cursor.execute("""
                    SELECT COUNT(*) 
                    FROM "账户交易明细表"
                    WHERE "案件编号" = %s
                    AND LOWER("摘要说明") LIKE %s
                """, (self.case_id, f'%{keyword.lower()}%'))
                
                result = self.cursor.fetchone()
                summary_count = result[0] if result else 0
                
                # 测试交易类型字段
                self.cursor.execute("""
                    SELECT COUNT(*) 
                    FROM "账户交易明细表"
                    WHERE "案件编号" = %s
                    AND LOWER("交易类型") LIKE %s
                """, (self.case_id, f'%{keyword.lower()}%'))
                
                result = self.cursor.fetchone()
                type_count = result[0] if result else 0
                
                # 测试已标记为现金的记录
                self.cursor.execute("""
                    SELECT COUNT(*) 
                    FROM "账户交易明细表"
                    WHERE "案件编号" = %s
                    AND (
                        LOWER("摘要说明") LIKE %s
                        OR LOWER("交易类型") LIKE %s
                    )
                    AND "对手户名" = '现金'
                """, (self.case_id, f'%{keyword.lower()}%', f'%{keyword.lower()}%'))
                
                result = self.cursor.fetchone()
                cash_marked_count = result[0] if result else 0
                
                keyword_results[keyword] = {
                    'summary_matches': summary_count,
                    'type_matches': type_count,
                    'cash_marked': cash_marked_count,
                    'total_matches': summary_count + type_count
                }
                
                logging.info(f"   关键词 '{keyword}': 摘要={summary_count}, 类型={type_count}, 已标记现金={cash_marked_count}")
            
            self.diagnosis_results['keyword_test'] = keyword_results
            
        except Exception as e:
            logging.error(f"❌ 测试现金关键词时出错: {e}")
    
    def check_cash_matching_conditions(self):
        """检查现金匹配的前提条件"""
        logging.info("🔍 检查现金匹配的前提条件...")
        
        try:
            # 检查符合现金匹配前提条件的记录数
            self.cursor.execute("""
                SELECT COUNT(*) 
                FROM "账户交易明细表"
                WHERE "案件编号" = %s
                AND ("对手户名" IS NULL OR "对手户名" = '')
                AND ("对手账号" IS NULL OR "对手账号" = '')
                AND ("对手卡号" IS NULL OR "对手卡号" = '')
            """, (self.case_id,))
            
            result = self.cursor.fetchone()
            eligible_count = result[0] if result else 0
            
            # 检查其中包含ATM关键词的记录
            self.cursor.execute("""
                SELECT COUNT(*) 
                FROM "账户交易明细表"
                WHERE "案件编号" = %s
                AND ("对手户名" IS NULL OR "对手户名" = '')
                AND ("对手账号" IS NULL OR "对手账号" = '')
                AND ("对手卡号" IS NULL OR "对手卡号" = '')
                AND (
                    LOWER("摘要说明") LIKE '%atm%'
                    OR LOWER("交易类型") LIKE '%atm%'
                )
            """, (self.case_id,))
            
            result = self.cursor.fetchone()
            atm_eligible_count = result[0] if result else 0
            
            # 检查其中已被标记为现金的记录
            self.cursor.execute("""
                SELECT COUNT(*) 
                FROM "账户交易明细表"
                WHERE "案件编号" = %s
                AND ("对手户名" = '现金')
                AND (
                    LOWER("摘要说明") LIKE '%atm%'
                    OR LOWER("交易类型") LIKE '%atm%'
                )
            """, (self.case_id,))
            
            result = self.cursor.fetchone()
            atm_cash_marked_count = result[0] if result else 0
            
            self.diagnosis_results['conditions_check'] = {
                'eligible_for_cash_matching': eligible_count,
                'atm_eligible': atm_eligible_count,
                'atm_cash_marked': atm_cash_marked_count,
                'atm_not_marked': atm_eligible_count - atm_cash_marked_count
            }
            
            logging.info(f"📊 现金匹配条件检查结果:")
            logging.info(f"   - 符合现金匹配条件的记录: {eligible_count}")
            logging.info(f"   - 其中包含ATM关键词: {atm_eligible_count}")
            logging.info(f"   - 已标记为现金: {atm_cash_marked_count}")
            logging.info(f"   - 未标记为现金: {atm_eligible_count - atm_cash_marked_count}")
            
            if atm_eligible_count - atm_cash_marked_count > 0:
                logging.warning(f"⚠️ 发现 {atm_eligible_count - atm_cash_marked_count} 条符合条件但未被标记为现金的ATM记录")
            
        except Exception as e:
            logging.error(f"❌ 检查现金匹配条件时出错: {e}")
    
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        report_file = f"cash_matching_diagnosis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("# 现金匹配诊断报告\n\n")
                f.write(f"**诊断时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**案件编号**: {self.case_id}\n\n")
                
                # ATM分析结果
                if 'atm_analysis' in self.diagnosis_results:
                    atm_data = self.diagnosis_results['atm_analysis']
                    f.write("## ATM记录分析\n\n")
                    f.write(f"- **总ATM记录数**: {atm_data['total_atm_records']}\n")
                    f.write(f"- **已标记为现金**: {atm_data['cash_marked']}\n")
                    f.write(f"- **未标记为现金**: {atm_data['not_cash_marked']}\n\n")
                    
                    if atm_data['analysis_details']:
                        f.write("### 未标记为现金的记录详情\n\n")
                        f.write("| ID | 摘要说明 | 交易类型 | 对手户名 | 未标记原因 |\n")
                        f.write("|----|---------|---------|---------|-----------|\n")
                        
                        for detail in atm_data['analysis_details']:
                            f.write(f"| {detail['id']} | {detail['summary'][:30]}... | {detail['transaction_type'][:20]}... | {detail['counterpart_name']} | {'; '.join(detail['reasons'])} |\n")
                
                # 关键词测试结果
                if 'keyword_test' in self.diagnosis_results:
                    keyword_data = self.diagnosis_results['keyword_test']
                    f.write("\n## 关键词匹配测试\n\n")
                    f.write("| 关键词 | 摘要匹配 | 类型匹配 | 已标记现金 | 总匹配数 |\n")
                    f.write("|--------|----------|----------|------------|----------|\n")
                    
                    for keyword, data in keyword_data.items():
                        f.write(f"| {keyword} | {data['summary_matches']} | {data['type_matches']} | {data['cash_marked']} | {data['total_matches']} |\n")
                
                # 条件检查结果
                if 'conditions_check' in self.diagnosis_results:
                    conditions_data = self.diagnosis_results['conditions_check']
                    f.write("\n## 现金匹配条件检查\n\n")
                    f.write(f"- **符合现金匹配条件的记录**: {conditions_data['eligible_for_cash_matching']}\n")
                    f.write(f"- **其中包含ATM关键词**: {conditions_data['atm_eligible']}\n")
                    f.write(f"- **已标记为现金**: {conditions_data['atm_cash_marked']}\n")
                    f.write(f"- **未标记为现金**: {conditions_data['atm_not_marked']}\n\n")
                
                # 问题分析和建议
                f.write("## 问题分析和建议\n\n")
                
                if 'atm_analysis' in self.diagnosis_results:
                    atm_data = self.diagnosis_results['atm_analysis']
                    if atm_data['not_cash_marked'] > 0:
                        f.write("### 发现的问题\n\n")
                        f.write(f"1. **有 {atm_data['not_cash_marked']} 条ATM记录未被标记为现金**\n")
                        f.write("   - 主要原因：对手户名、对手账号或对手卡号不为空\n")
                        f.write("   - 这些记录不符合现金匹配的前提条件\n\n")
                        
                        f.write("### 修复建议\n\n")
                        f.write("1. **检查数据质量**：确认这些ATM记录是否应该被标记为现金\n")
                        f.write("2. **调整匹配逻辑**：如果确实是现金交易，考虑放宽匹配条件\n")
                        f.write("3. **数据清洗**：在现金匹配前先清理对手信息字段\n")
                
            logging.info(f"📄 诊断报告已生成: {report_file}")
            
        except Exception as e:
            logging.error(f"生成诊断报告失败: {e}")
    
    def run_full_diagnosis(self):
        """运行完整的诊断"""
        logging.info("🚀 开始现金匹配诊断...")
        
        if not self.setup_database():
            return False
        
        try:
            # 执行各项诊断
            self.analyze_atm_records()
            self.test_cash_keywords()
            self.check_cash_matching_conditions()
            
            # 生成报告
            self.generate_diagnosis_report()
            
            logging.info("🎉 现金匹配诊断完成！")
            return True
            
        except Exception as e:
            logging.error(f"❌ 诊断过程中出错: {e}")
            return False
            
        finally:
            self.cleanup_database()

def main():
    """主函数"""
    print("🔍 现金匹配诊断工具")
    print("=" * 50)
    
    # 获取案件编号
    case_id = input("请输入案件编号: ").strip()
    if not case_id:
        print("❌ 案件编号不能为空")
        return 1
    
    # 创建诊断器
    diagnostic = CashMatchingDiagnostic(case_id)
    
    # 运行诊断
    success = diagnostic.run_full_diagnosis()
    
    if success:
        print("\n✅ 诊断完成！")
        print("📄 请查看生成的诊断报告文件")
        return 0
    else:
        print("\n❌ 诊断失败！")
        print("📄 请查看日志文件了解详细错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
