#data_import_thread.py
from PySide6.QtCore import QThread, Signal
import pandas as pd
import psycopg2  # 替换 sqlite3 为 psycopg2
from psycopg2 import extras  # 用于批量操作和字典游标
import logging
from database_setup import get_db_connection  # 导入数据库连接函数
from import_error_handler import get_error_handler, get_db_manager, safe_import_decorator

class DataImportThread(QThread):
    update_progress_signal = Signal(int)
    task_finished_signal = Signal(int, str, int)
    error_signal = Signal(str, str)

    def __init__(self, case_id, db_config, files):
        """
        初始化数据导入线程

        参数:
            case_id: 案例ID
            db_config: 数据库配置（已弃用db_path参数）
            files: 要导入的文件列表
        """
        super().__init__()
        self.case_id = case_id
        self.db_config = db_config  # 保留兼容性，但实际使用get_db_connection()
        self.files = files
        self.error_handler = get_error_handler()
        self.db_manager = get_db_manager()

    def run(self):
        """运行导入线程，处理所有文件"""
        total_files = len(self.files)
        total_imported = 0

        try:
            for index, (file_path, df, file_type, import_mapping) in enumerate(self.files):
                try:
                    # 添加内存检查，防止内存溢出
                    import psutil
                    memory_percent = psutil.virtual_memory().percent
                    if memory_percent > 85:
                        logging.warning(f"内存使用率过高 ({memory_percent}%)，暂停导入")
                        import time
                        time.sleep(2)  # 等待2秒让系统释放内存

                    # 数据预处理，确保数据格式正确
                    if df is None or df.empty:
                        logging.warning(f"跳过空数据文件: {file_path}")
                        continue

                    # 🚀 优化：使用新的分批导入机制，防止内存溢出和程序崩溃
                    try:
                        from optimized_batch_importer import OptimizedBatchImporter

                        # 创建优化的分批导入器
                        batch_importer = OptimizedBatchImporter(
                            case_id=getattr(self, 'case_id', 'unknown'),
                            import_batch=getattr(self, 'import_batch', 1)
                        )

                        # 检查数据量，决定使用哪种导入方式
                        if len(df) > 10000:  # 大数据量使用优化导入
                            logging.info(f"文件 {file_path} 数据量较大 ({len(df)} 行)，使用优化分批导入")

                            # 使用优化的分批导入（支持断点续传）
                            count = batch_importer.import_file_optimized(
                                file_path=file_path,
                                worksheet_name=getattr(self, 'current_worksheet', 'Sheet1'),
                                table_name=file_type,
                                field_mapping=import_mapping,
                                resume_from_checkpoint=True
                            )
                        else:
                            # 小数据量使用传统方式，但减少批次大小
                            max_rows_per_batch = 5000  # 🔧 优化：减少批次大小
                            if len(df) > max_rows_per_batch:
                                logging.info(f"文件 {file_path} 使用传统分批导入 ({len(df)} 行)")
                                batch_count = 0
                                for i in range(0, len(df), max_rows_per_batch):
                                    batch_df = df.iloc[i:i + max_rows_per_batch].copy()
                                    batch_count += self.import_to_db(batch_df, file_type, import_mapping)

                                    # 🔧 优化：每批次后进行内存清理
                                    if i % (max_rows_per_batch * 3) == 0:  # 每3批次清理一次
                                        import gc
                                        gc.collect()

                                count = batch_count
                            else:
                                count = self.import_to_db(df, file_type, import_mapping)

                    except ImportError:
                        # 如果优化导入器不可用，使用传统方式但减少批次大小
                        logging.warning("优化分批导入器不可用，使用传统方式")
                        max_rows_per_batch = 5000  # 🔧 优化：减少批次大小防止崩溃
                        if len(df) > max_rows_per_batch:
                            logging.info(f"文件 {file_path} 数据量过大 ({len(df)} 行)，将分批导入")
                            batch_count = 0
                            for i in range(0, len(df), max_rows_per_batch):
                                batch_df = df.iloc[i:i + max_rows_per_batch].copy()
                                batch_count += self.import_to_db(batch_df, file_type, import_mapping)

                                # 🔧 优化：每批次后进行内存清理
                                if i % (max_rows_per_batch * 3) == 0:
                                    import gc
                                    gc.collect()

                            count = batch_count
                        else:
                            count = self.import_to_db(df, file_type, import_mapping)

                    total_imported += count
                    self.task_finished_signal.emit(total_imported, file_path, count)
                    progress = int((index + 1) / total_files * 100)
                    self.update_progress_signal.emit(progress)

                except Exception as e:
                    logging.error(f"导入文件 {file_path} 时发生错误: {e}")
                    # 发送错误信号但不中断整个导入过程
                    self.error_signal.emit(file_path, str(e))
                    continue  # 继续处理下一个文件

        except Exception as e:
            logging.critical(f"导入线程发生严重错误: {e}")
            # 发送错误信号
            self.error_signal.emit("导入线程", str(e))
        finally:
            # 确保线程正常结束
            logging.info(f"导入线程结束，共处理 {total_files} 个文件，成功导入 {total_imported} 条记录")

    def import_to_db(self, df, table_name, import_mapping):
        """
        将DataFrame数据导入到PostgreSQL数据库

        参数:
            df: 要导入的DataFrame
            table_name: 目标表名
            import_mapping: 字段映射字典

        返回:
            导入的记录数量
        """
        conn = None
        cursor = None
        max_retries = 3
        retry_delay = 2

        for retry_count in range(max_retries):
            try:
                # 使用数据库管理器获取连接，带重试机制
                conn = self.db_manager.get_connection_with_retry(get_db_connection)
                if not conn:
                    raise Exception("无法连接到PostgreSQL数据库")

                # 设置连接超时和自动提交
                conn.autocommit = False
                cursor = conn.cursor()

                # 构建映射后的DataFrame，优化字段处理逻辑
                mapped_df = pd.DataFrame()
                field_mapping = {}

                for db_field, import_fields in import_mapping.items():
                    if import_fields and isinstance(import_fields, list):
                        # 获取有效的源字段
                        valid_fields = [field for field in import_fields if field and isinstance(field, str)]
                        if valid_fields:
                            source_field = valid_fields[0].strip()

                            # 支持大小写不敏感的字段匹配
                            matched_field = None
                            for col in df.columns:
                                if col.lower() == source_field.lower():
                                    matched_field = col
                                    break

                            if matched_field:
                                # 数据清理，防止PostgreSQL数组常量错误
                                cleaned_data = df[matched_field].apply(self._clean_data_value)
                                mapped_df[db_field] = cleaned_data
                                field_mapping[db_field] = matched_field

                if mapped_df.empty:
                    logging.warning(f"映射后的数据为空，跳过表 {table_name}")
                    return 0

                # 清理数据：处理NaN值和数据类型
                mapped_df = mapped_df.fillna('')  # 将NaN替换为空字符串

                # 使用PostgreSQL的批量插入优化性能
                columns = list(mapped_df.columns)
                quoted_columns = [f'"{col}"' for col in columns]  # PostgreSQL需要引号包围列名

                # 准备批量插入的数据
                values_list = []
                for _, row in mapped_df.iterrows():
                    values = []
                    for value in row:
                        # 处理特殊值，防止数据类型错误
                        if pd.isna(value) or value == '':
                            values.append(None)
                        else:
                            # 确保数据类型安全
                            cleaned_value = self._sanitize_value_for_postgres(value)
                            values.append(cleaned_value)
                    values_list.append(tuple(values))

                # 使用execute_values进行批量插入（PostgreSQL优化）
                if values_list:
                    try:
                        placeholders = ','.join(['%s'] * len(columns))
                        sql = f'INSERT INTO "{table_name}" ({",".join(quoted_columns)}) VALUES %s'

                        # 批量插入，提高性能
                        extras.execute_values(
                            cursor,
                            sql,
                            values_list,
                            template=f'({placeholders})',
                            page_size=1000  # 每次处理1000条记录
                        )

                        conn.commit()

                    except psycopg2.Error as db_error:
                        logging.error(f"数据库插入错误: {db_error}")
                        conn.rollback()

                        # 如果批量插入失败，尝试逐行插入
                        logging.info(f"批量插入失败，尝试逐行插入表 {table_name}")
                        count = self._single_row_insert(cursor, conn, table_name, quoted_columns, values_list)
                        return count

                # 获取实际插入的记录数
                count = len(values_list)
                logging.info(f"成功导入 {count} 条记录到表 {table_name}")

                return count

            except psycopg2.OperationalError as op_error:
                # 数据库连接错误，进行重试
                if retry_count < max_retries - 1:
                    logging.warning(f"数据库连接错误，{retry_delay}秒后重试 ({retry_count + 1}/{max_retries}): {op_error}")
                    import time
                    time.sleep(retry_delay)
                    continue
                else:
                    logging.error(f"数据库连接重试失败，跳过表 {table_name}: {op_error}")
                    return 0

            except Exception as e:
                if conn:
                    try:
                        conn.rollback()  # 发生错误时回滚事务
                    except:
                        pass
                logging.error(f"导入数据到PostgreSQL表 {table_name} 时发生错误: {e}")

                # 不再抛出异常，而是返回0，避免程序崩溃
                if retry_count < max_retries - 1:
                    logging.warning(f"导入失败，{retry_delay}秒后重试 ({retry_count + 1}/{max_retries})")
                    import time
                    time.sleep(retry_delay)
                    continue
                else:
                    logging.error(f"导入表 {table_name} 最终失败，跳过此表")
                    return 0

            finally:
                # 使用数据库管理器安全关闭连接
                self.db_manager.safe_close_connection(conn, cursor)

        return 0  # 如果所有重试都失败，返回0

    def _clean_data_value(self, value):
        """清理数据值，防止PostgreSQL数组常量错误"""
        if value is None or pd.isna(value):
            return None

        # 确保转换为字符串
        if not isinstance(value, str):
            value = str(value)

        value = value.strip()

        # 如果值是 'nan' 或类似的空值表示，返回 None
        if value.lower() in ['nan', 'none', 'null', '']:
            return None

        # 移除可能导致PostgreSQL数组常量错误的字符
        import re

        # 移除花括号（PostgreSQL数组语法标识符）
        value = value.replace('{', '').replace('}', '')

        # 移除方括号（可能被误认为是数组）
        value = value.replace('[', '').replace(']', '')

        # 移除控制字符
        value = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', value)

        # 规范化空白字符
        value = re.sub(r'\s+', ' ', value).strip()

        return str(value)

    def _sanitize_value_for_postgres(self, value):
        """为PostgreSQL安全地处理数据值"""
        if value is None or pd.isna(value):
            return None

        # 确保转换为字符串
        value = str(value).strip()

        if not value or value.lower() in ['nan', 'none', 'null']:
            return None

        # 移除可能导致问题的字符
        import re
        value = value.replace('{', '').replace('}', '')
        value = value.replace('[', '').replace(']', '')
        value = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', value)
        value = re.sub(r'\s+', ' ', value).strip()

        # 限制字符串长度，防止过长数据
        if len(value) > 2000:
            logging.warning(f"数据值过长，将截断: {value[:50]}...")
            value = value[:2000]

        return str(value)

    def _single_row_insert(self, cursor, conn, table_name, quoted_columns, values_list):
        """逐行插入数据，用于批量插入失败时的备用方案"""
        success_count = 0
        failed_count = 0

        for i, row_values in enumerate(values_list):
            try:
                placeholders = ','.join(['%s'] * len(quoted_columns))
                sql = f'INSERT INTO "{table_name}" ({",".join(quoted_columns)}) VALUES ({placeholders})'

                cursor.execute(sql, row_values)
                conn.commit()
                success_count += 1

            except Exception as row_error:
                logging.error(f"插入第 {i+1} 行数据失败: {row_error}")
                logging.error(f"问题数据: {row_values}")
                conn.rollback()
                failed_count += 1
                continue

        logging.info(f"逐行插入完成: 成功 {success_count} 行，失败 {failed_count} 行")
        return success_count
