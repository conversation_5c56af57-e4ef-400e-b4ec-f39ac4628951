2025-08-05 20:28:09.517 - INFO - [MainThread:26196] - enhanced_logging_patch.py:84 - setup_enhanced_logging() - 🔍 增强日志记录已启动
2025-08-05 20:28:09.517 - INFO - [MainThread:26196] - memory_optimizer.py:38 - __init__() - ✅ 内存监控模块已加载
2025-08-05 20:28:09.517 - INFO - [MainThread:26196] - memory_optimizer.py:42 - __init__() - 📊 系统内存信息: 总计 31.7GB, 可用 16.9GB, 使用率 46.7%
2025-08-05 20:28:09.662 - INFO - [MainThread:26196] - database_table_checker.py:417 - check_database_tables() - 开始数据库表格完整性检查...
2025-08-05 20:28:09.702 - INFO - [MainThread:26196] - database_table_checker.py:115 - load_excel_table_config() - 成功读取Excel文件，共94行数据
2025-08-05 20:28:09.712 - INFO - [MainThread:26196] - database_table_checker.py:169 - load_excel_table_config() - 从Excel文件解析出94个有效表配置（处理了94行数据）
2025-08-05 20:28:09.713 - INFO - [MainThread:26196] - database_table_checker.py:182 - load_excel_table_config() - Excel文件统计：总行数94，重复表名0个，有效表格94个
2025-08-05 20:28:09.789 - INFO - [MainThread:26196] - database_table_checker.py:444 - check_database_tables() - 数据库中现有表格总数：106
2025-08-05 20:28:09.789 - INFO - [MainThread:26196] - database_table_checker.py:456 - check_database_tables() - 核心系统表检查：9个必需，9个已存在
2025-08-05 20:28:16.833 - INFO - [MainThread:26196] - database_table_checker.py:507 - check_database_tables() - Excel配置表检查：94个定义，94个已存在
2025-08-05 20:28:16.833 - INFO - [MainThread:26196] - database_table_checker.py:511 - check_database_tables() - 所有Excel配置表都已存在，无需创建
2025-08-05 20:28:16.833 - INFO - [MainThread:26196] - database_table_checker.py:541 - check_database_tables() - 所有表都已存在，无需创建任何表
2025-08-05 20:28:16.833 - INFO - [MainThread:26196] - database_table_checker.py:344 - update_table_column_types() - 开始检查并更新表字段类型...
2025-08-05 20:28:17.143 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_建设用地宅基地' 的字段类型...
2025-08-05 20:28:17.144 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_房地产权表' 的字段类型...
2025-08-05 20:28:17.144 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_抵押权表' 的字段类型...
2025-08-05 20:28:17.145 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_查封登记表' 的字段类型...
2025-08-05 20:28:17.145 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_预告登记表' 的字段类型...
2025-08-05 20:28:17.145 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同乘三次以上同行人' 的字段类型...
2025-08-05 20:28:17.145 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同订单同行人已成行' 的字段类型...
2025-08-05 20:28:17.145 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同订单同行人未成行' 的字段类型...
2025-08-05 20:28:17.146 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班进出港_航班进出港已成行表' 的字段类型...
2025-08-05 20:28:17.146 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班进出港_航班进出港未成行表' 的字段类型...
2025-08-05 20:28:17.147 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券持有_持有信息' 的字段类型...
2025-08-05 20:28:17.147 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券持有变动_持' 的字段类型...
2025-08-05 20:28:17.148 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券账户_证券账户' 的字段类型...
2025-08-05 20:28:17.149 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_同订单同行人_同行人员信息表' 的字段类型...
2025-08-05 20:28:17.150 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_同订单同行人_同行人员客票' 的字段类型...
2025-08-05 20:28:17.151 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_用户注册_互联网注册信息表' 的字段类型...
2025-08-05 20:28:17.151 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_用户注册_常用联系人信息表' 的字段类型...
2025-08-05 20:28:17.151 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_铁路客票_交易信息表' 的字段类型...
2025-08-05 20:28:17.152 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_铁路客票_票面信息表' 的字段类型...
2025-08-05 20:28:17.153 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '临时账户交易明细表' 的字段类型...
2025-08-05 20:28:17.153 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_产品信息表' 的字段类型...
2025-08-05 20:28:17.154 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_受益人信息' 的字段类型...
2025-08-05 20:28:17.154 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_委托人信息' 的字段类型...
2025-08-05 20:28:17.154 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_登记信息_受益权结构' 的字段类型...
2025-08-05 20:28:17.154 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_登记信息_合同信息' 的字段类型...
2025-08-05 20:28:17.155 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_终止登记' 的字段类型...
2025-08-05 20:28:17.156 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_委托人或受益人变动信息表' 的字段类型...
2025-08-05 20:28:17.156 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_登记信息_受益权结构表' 的字段类型...
2025-08-05 20:28:17.156 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_登记信息_合同信息表' 的字段类型...
2025-08-05 20:28:17.156 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_终止登记表' 的字段类型...
2025-08-05 20:28:17.156 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_交通违法_机动车违章信息表' 的字段类型...
2025-08-05 20:28:17.157 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_出入境记录_出入境记录信息表' 的字段类型...
2025-08-05 20:28:17.157 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_出国_境_证件_出入境证件信息' 的字段类型...
2025-08-05 20:28:17.157 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同住址_同住址表' 的字段类型...
2025-08-05 20:28:17.157 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同户人_同户人表' 的字段类型...
2025-08-05 20:28:17.157 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同车违章_同车违章表' 的字段类型...
2025-08-05 20:28:17.157 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃人员_在逃人员登记信息' 的字段类型...
2025-08-05 20:28:17.157 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃同案撤销人员_在逃同案撤销人员' 的字段类型...
2025-08-05 20:28:17.157 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃撤销_在逃人员撤销信息' 的字段类型...
2025-08-05 20:28:17.158 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_户籍人口_基本人员信息表' 的字段类型...
2025-08-05 20:28:17.158 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_旅馆住宿_旅馆住宿人员信息表' 的字段类型...
2025-08-05 20:28:17.158 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_机动车_机动车信息' 的字段类型...
2025-08-05 20:28:17.158 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_驾驶证_驾驶证信息表' 的字段类型...
2025-08-05 20:28:17.158 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_住院结算数据' 的字段类型...
2025-08-05 20:28:17.158 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_参保信息' 的字段类型...
2025-08-05 20:28:17.158 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_普通门诊' 的字段类型...
2025-08-05 20:28:17.159 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_药店购药' 的字段类型...
2025-08-05 20:28:17.159 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_药店购药明细' 的字段类型...
2025-08-05 20:28:17.159 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '国家税务总局_纳税人登记信息_登记信息表' 的字段类型...
2025-08-05 20:28:17.159 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '国家税务总局_纳税信息_税务缴纳信息表' 的字段类型...
2025-08-05 20:28:17.159 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '增值税发票表' 的字段类型...
2025-08-05 20:28:17.159 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '字段匹配规则' 的字段类型...
2025-08-05 20:28:17.159 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '对手信息' 的字段类型...
2025-08-05 20:28:17.159 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '导入记录表' 的字段类型...
2025-08-05 20:28:17.160 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_主要人员表' 的字段类型...
2025-08-05 20:28:17.160 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_内资补充信息表' 的字段类型...
2025-08-05 20:28:17.160 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_农专补充信息表' 的字段类型...
2025-08-05 20:28:17.160 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_分支机构备案信息表' 的字段类型...
2025-08-05 20:28:17.160 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_变更备案信息表' 的字段类型...
2025-08-05 20:28:17.160 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_吊销信息表' 的字段类型...
2025-08-05 20:28:17.160 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_外资补充信息表' 的字段类型...
2025-08-05 20:28:17.160 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_注销信息表' 的字段类型...
2025-08-05 20:28:17.160 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_清算基本信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_清算成员信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_联络员信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_自然人出资信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_许可信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_财务负责人信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_非自然人出资信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业基本信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_统一社会信用代码_统一社会信用代码表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '开户信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '本地银行_客户信息本地表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '案件信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_投资行业信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_持有信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_理财产品信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '用户信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '电话_登记信息_运营商登记信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '电话_话单信息_运营商话单信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_专票货物或应税劳务名称表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_增值税专用发票表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_增值税普通发票表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_普票货物或应税劳务服务名' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '系统信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '系统配置表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '虚拟运营商_登记信息_虚拟运营商登记信息表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '表类型匹配规则_导出文件名分类表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '表类型匹配规则表' 的字段类型...
2025-08-05 20:28:17.161 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '证券登记结算_证券持有变动_持' 的字段类型...
2025-08-05 20:28:17.167 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '证券登记结算_证券持有变动_证券持有变动' 的字段类型...
2025-08-05 20:28:17.167 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '财付通交易明细表' 的字段类型...
2025-08-05 20:28:17.167 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户交易明细表' 的字段类型...
2025-08-05 20:28:17.168 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_共有权优先权信息表' 的字段类型...
2025-08-05 20:28:17.168 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_关联子账户信息表' 的字段类型...
2025-08-05 20:28:17.169 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_关联子账户信息表本地' 的字段类型...
2025-08-05 20:28:17.169 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_客户基本信息表' 的字段类型...
2025-08-05 20:28:17.170 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_强制措施信息表' 的字段类型...
2025-08-05 20:28:17.171 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息（本地）_优先权信息表' 的字段类型...
2025-08-05 20:28:17.171 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '金融理财_金融理财信息表' 的字段类型...
2025-08-05 20:28:17.171 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '金融理财_金融理财账户信息表' 的字段类型...
2025-08-05 20:28:17.171 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险人员信息表' 的字段类型...
2025-08-05 20:28:17.171 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险保单信息表' 的字段类型...
2025-08-05 20:28:17.171 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险赔案信息表' 的字段类型...
2025-08-05 20:28:17.171 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_家庭财产保险表' 的字段类型...
2025-08-05 20:28:17.171 - INFO - [MainThread:26196] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_航空延误保险表' 的字段类型...
2025-08-05 20:28:17.171 - INFO - [MainThread:26196] - database_table_checker.py:404 - update_table_column_types() - 字段类型更新完成: 已更新 0 个字段, 跳过 2702 个字段, 失败 0 个字段
2025-08-05 20:28:17.241 - INFO - [MainThread:26196] - database_table_checker.py:580 - check_database_tables() - === 数据库表格检查报告 ===
2025-08-05 20:28:17.241 - INFO - [MainThread:26196] - database_table_checker.py:581 - check_database_tables() - 总计检查表格：103个 (核心9个 + Excel94个)
2025-08-05 20:28:17.241 - INFO - [MainThread:26196] - database_table_checker.py:582 - check_database_tables() - 最终存在表格：103个
2025-08-05 20:28:17.241 - INFO - [MainThread:26196] - database_table_checker.py:589 - check_database_tables() - 本次未创建任何新表格（所有表都已存在）
2025-08-05 20:28:22.966 - WARNING - [MainThread:26196] - import_error_handler.py:476 - check_for_previous_crash() - 检测到可能的程序崩溃，上次心跳: {'timestamp': 1754396401.0054507, 'operation': '检查导入表类型', 'pid': 20292, 'readable_time': '2025-08-05 20:20:01'}
2025-08-05 20:28:22.966 - WARNING - [MainThread:26196] - import_data.py:5815 - __init__() - 检测到之前的程序崩溃，将启用增强监控
2025-08-05 20:28:24.412 - INFO - [MainThread:26196] - import_error_handler.py:523 - start_heartbeat_monitor() - 心跳监控已启动
2025-08-05 20:28:24.443 - INFO - [MainThread:26196] - import_data.py:460 - __init__() - ✅ 自动化管理器已初始化
2025-08-05 20:28:35.383 - INFO - [MainThread:26196] - import_data.py:134 - register_dialog() - ✅ 注册对话框: folder_scan
2025-08-05 20:28:35.396 - INFO - [MainThread:26196] - import_data.py:11839 - scan_folder_async() - ✅ 创建文件夹扫描进度对话框
2025-08-05 20:28:35.412 - INFO - [MainThread:26196] - import_data.py:11854 - scan_folder_async() - 🚀 启动异步文件夹扫描: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地
2025-08-05 20:28:35.423 - INFO - [Dummy-2:26192] - import_data.py:180 - run() - 🚀 开始后台扫描文件夹: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地
2025-08-05 20:28:35.474 - INFO - [Dummy-2:26192] - import_data.py:225 - run() - ✅ 文件夹扫描完成: 找到956个文件，跳过20个
2025-08-05 20:28:35.483 - INFO - [MainThread:26196] - import_data.py:143 - unregister_dialog() - ✅ 注销对话框: folder_scan
2025-08-05 20:28:36.342 - INFO - [MainThread:26196] - import_data.py:9075 - preload_table_type_rules() - 预加载了 113 条表类型匹配规则
2025-08-05 20:28:36.369 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300001.xls - 普通门诊 (行数: 6, 非空值: 60)
2025-08-05 20:28:36.385 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300001.xls - 普通门诊 (行数: 6, 非空值: 60)
2025-08-05 20:28:36.466 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:36.467 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:36.671 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB01-CXGZSSYBJ52000001-340000002025042319472300001', 工作表='普通门诊'
2025-08-05 20:28:36.681 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='普通门诊'
2025-08-05 20:28:36.682 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'普通门诊' → 医保_普通门诊
2025-08-05 20:28:36.683 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300001' → 表类型'医保_普通门诊'
2025-08-05 20:28:36.684 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300001' → 表类型'医保_普通门诊'
2025-08-05 20:28:36.684 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300001' → 表类型'医保_普通门诊'
2025-08-05 20:28:36.689 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300051.xls - 参保信息 (行数: 3, 非空值: 42)
2025-08-05 20:28:36.692 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300051.xls - 参保信息 (行数: 3, 非空值: 42)
2025-08-05 20:28:36.754 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:36.755 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:36.828 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB06-CXGZSSYBJ52000001-340000002025042319472300051', 工作表='参保信息'
2025-08-05 20:28:36.832 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='参保信息'
2025-08-05 20:28:36.833 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'参保信息' → 医保_参保信息
2025-08-05 20:28:36.834 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300051' → 表类型'医保_参保信息'
2025-08-05 20:28:36.834 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300051' → 表类型'医保_参保信息'
2025-08-05 20:28:36.835 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300051' → 表类型'医保_参保信息'
2025-08-05 20:28:36.844 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300041.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:36.849 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300041.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:36.921 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:36.922 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:36.982 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB05-CXGZSSYBJ52000001-340000002025042319472300041', 工作表='药店购药明细'
2025-08-05 20:28:36.983 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药明细'
2025-08-05 20:28:36.984 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药明细' → 医保_药店购药明细
2025-08-05 20:28:36.984 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300041' → 表类型'医保_药店购药明细'
2025-08-05 20:28:36.985 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300041' → 表类型'医保_药店购药明细'
2025-08-05 20:28:36.985 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300041' → 表类型'医保_药店购药明细'
2025-08-05 20:28:36.992 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300031.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:36.996 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300031.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:37.062 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:37.063 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:37.137 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB04-CXGZSSYBJ52000001-340000002025042319472300031', 工作表='药店购药'
2025-08-05 20:28:37.138 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药'
2025-08-05 20:28:37.139 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药' → 医保_药店购药
2025-08-05 20:28:37.140 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300031' → 表类型'医保_药店购药'
2025-08-05 20:28:37.140 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300031' → 表类型'医保_药店购药'
2025-08-05 20:28:37.140 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300031' → 表类型'医保_药店购药'
2025-08-05 20:28:37.144 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300003.xls - 普通门诊 (行数: 4, 非空值: 37)
2025-08-05 20:28:37.148 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300003.xls - 普通门诊 (行数: 4, 非空值: 37)
2025-08-05 20:28:37.225 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:37.226 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:37.303 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB01-CXGZSSYBJ52000001-340000002025042319472300003', 工作表='普通门诊'
2025-08-05 20:28:37.305 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='普通门诊'
2025-08-05 20:28:37.307 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'普通门诊' → 医保_普通门诊
2025-08-05 20:28:37.308 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300003' → 表类型'医保_普通门诊'
2025-08-05 20:28:37.308 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300003' → 表类型'医保_普通门诊'
2025-08-05 20:28:37.309 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300003' → 表类型'医保_普通门诊'
2025-08-05 20:28:37.314 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300053.xls - 参保信息 (行数: 3, 非空值: 42)
2025-08-05 20:28:37.317 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300053.xls - 参保信息 (行数: 3, 非空值: 42)
2025-08-05 20:28:37.396 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:37.399 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:37.459 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB06-CXGZSSYBJ52000001-340000002025042319472300053', 工作表='参保信息'
2025-08-05 20:28:37.461 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='参保信息'
2025-08-05 20:28:37.463 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'参保信息' → 医保_参保信息
2025-08-05 20:28:37.463 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300053' → 表类型'医保_参保信息'
2025-08-05 20:28:37.464 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300053' → 表类型'医保_参保信息'
2025-08-05 20:28:37.467 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300053' → 表类型'医保_参保信息'
2025-08-05 20:28:37.474 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300023.xls - 住院结算数据 (行数: 6, 非空值: 60)
2025-08-05 20:28:37.476 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300023.xls - 住院结算数据 (行数: 6, 非空值: 60)
2025-08-05 20:28:37.545 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:37.546 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:37.628 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB02-CXGZSSYBJ52000001-340000002025042319472300023', 工作表='住院结算数据'
2025-08-05 20:28:37.628 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='住院结算数据'
2025-08-05 20:28:37.630 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'住院结算数据' → 医保_住院结算数据
2025-08-05 20:28:37.630 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300023' → 表类型'医保_住院结算数据'
2025-08-05 20:28:37.630 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300023' → 表类型'医保_住院结算数据'
2025-08-05 20:28:37.630 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300023' → 表类型'医保_住院结算数据'
2025-08-05 20:28:37.638 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300043.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:37.642 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300043.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:37.705 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:37.708 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:37.771 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB05-CXGZSSYBJ52000001-340000002025042319472300043', 工作表='药店购药明细'
2025-08-05 20:28:37.772 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药明细'
2025-08-05 20:28:37.774 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药明细' → 医保_药店购药明细
2025-08-05 20:28:37.774 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300043' → 表类型'医保_药店购药明细'
2025-08-05 20:28:37.774 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300043' → 表类型'医保_药店购药明细'
2025-08-05 20:28:37.774 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300043' → 表类型'医保_药店购药明细'
2025-08-05 20:28:37.780 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300033.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:37.785 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300033.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:37.846 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:37.855 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:37.920 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB04-CXGZSSYBJ52000001-340000002025042319472300033', 工作表='药店购药'
2025-08-05 20:28:37.921 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药'
2025-08-05 20:28:37.922 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药' → 医保_药店购药
2025-08-05 20:28:37.924 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300033' → 表类型'医保_药店购药'
2025-08-05 20:28:37.930 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300033' → 表类型'医保_药店购药'
2025-08-05 20:28:37.931 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300033' → 表类型'医保_药店购药'
2025-08-05 20:28:37.937 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300004.xls - 普通门诊 (行数: 8, 非空值: 73)
2025-08-05 20:28:37.940 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300004.xls - 普通门诊 (行数: 8, 非空值: 73)
2025-08-05 20:28:38.010 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:38.021 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:38.083 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB01-CXGZSSYBJ52000001-340000002025042319472300004', 工作表='普通门诊'
2025-08-05 20:28:38.085 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='普通门诊'
2025-08-05 20:28:38.087 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'普通门诊' → 医保_普通门诊
2025-08-05 20:28:38.087 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300004' → 表类型'医保_普通门诊'
2025-08-05 20:28:38.087 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300004' → 表类型'医保_普通门诊'
2025-08-05 20:28:38.088 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300004' → 表类型'医保_普通门诊'
2025-08-05 20:28:38.111 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300054.xls - 参保信息 (行数: 5, 非空值: 65)
2025-08-05 20:28:38.114 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300054.xls - 参保信息 (行数: 5, 非空值: 65)
2025-08-05 20:28:38.173 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:38.174 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:38.272 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB06-CXGZSSYBJ52000001-340000002025042319472300054', 工作表='参保信息'
2025-08-05 20:28:38.276 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='参保信息'
2025-08-05 20:28:38.278 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'参保信息' → 医保_参保信息
2025-08-05 20:28:38.286 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300054' → 表类型'医保_参保信息'
2025-08-05 20:28:38.287 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300054' → 表类型'医保_参保信息'
2025-08-05 20:28:38.288 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300054' → 表类型'医保_参保信息'
2025-08-05 20:28:38.301 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300044.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:38.306 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300044.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:38.369 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:38.373 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:38.443 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB05-CXGZSSYBJ52000001-340000002025042319472300044', 工作表='药店购药明细'
2025-08-05 20:28:38.444 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药明细'
2025-08-05 20:28:38.445 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药明细' → 医保_药店购药明细
2025-08-05 20:28:38.446 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300044' → 表类型'医保_药店购药明细'
2025-08-05 20:28:38.447 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300044' → 表类型'医保_药店购药明细'
2025-08-05 20:28:38.448 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300044' → 表类型'医保_药店购药明细'
2025-08-05 20:28:38.455 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300034.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:38.459 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300034.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:38.527 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:38.532 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:38.602 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB04-CXGZSSYBJ52000001-340000002025042319472300034', 工作表='药店购药'
2025-08-05 20:28:38.605 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药'
2025-08-05 20:28:38.606 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药' → 医保_药店购药
2025-08-05 20:28:38.619 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300034' → 表类型'医保_药店购药'
2025-08-05 20:28:38.620 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300034' → 表类型'医保_药店购药'
2025-08-05 20:28:38.621 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300034' → 表类型'医保_药店购药'
2025-08-05 20:28:38.629 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300005.xls - 普通门诊 (行数: 10, 非空值: 91)
2025-08-05 20:28:38.633 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300005.xls - 普通门诊 (行数: 10, 非空值: 91)
2025-08-05 20:28:38.703 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:38.704 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:38.767 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB01-CXGZSSYBJ52000001-340000002025042319472300005', 工作表='普通门诊'
2025-08-05 20:28:38.768 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='普通门诊'
2025-08-05 20:28:38.769 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'普通门诊' → 医保_普通门诊
2025-08-05 20:28:38.769 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300005' → 表类型'医保_普通门诊'
2025-08-05 20:28:38.770 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300005' → 表类型'医保_普通门诊'
2025-08-05 20:28:38.770 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300005' → 表类型'医保_普通门诊'
2025-08-05 20:28:38.775 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300055.xls - 参保信息 (行数: 3, 非空值: 42)
2025-08-05 20:28:38.778 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300055.xls - 参保信息 (行数: 3, 非空值: 42)
2025-08-05 20:28:38.837 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:38.838 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:38.926 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB06-CXGZSSYBJ52000001-340000002025042319472300055', 工作表='参保信息'
2025-08-05 20:28:38.926 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='参保信息'
2025-08-05 20:28:38.927 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'参保信息' → 医保_参保信息
2025-08-05 20:28:38.928 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300055' → 表类型'医保_参保信息'
2025-08-05 20:28:38.928 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300055' → 表类型'医保_参保信息'
2025-08-05 20:28:38.929 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300055' → 表类型'医保_参保信息'
2025-08-05 20:28:38.935 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300025.xls - 住院结算数据 (行数: 1, 非空值: 10)
2025-08-05 20:28:38.938 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300025.xls - 住院结算数据 (行数: 1, 非空值: 10)
2025-08-05 20:28:39.009 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:39.011 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:39.070 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB02-CXGZSSYBJ52000001-340000002025042319472300025', 工作表='住院结算数据'
2025-08-05 20:28:39.071 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='住院结算数据'
2025-08-05 20:28:39.072 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'住院结算数据' → 医保_住院结算数据
2025-08-05 20:28:39.073 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300025' → 表类型'医保_住院结算数据'
2025-08-05 20:28:39.074 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300025' → 表类型'医保_住院结算数据'
2025-08-05 20:28:39.075 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300025' → 表类型'医保_住院结算数据'
2025-08-05 20:28:39.083 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300045.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:39.092 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300045.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:39.167 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:39.170 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:39.253 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB05-CXGZSSYBJ52000001-340000002025042319472300045', 工作表='药店购药明细'
2025-08-05 20:28:39.253 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药明细'
2025-08-05 20:28:39.254 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药明细' → 医保_药店购药明细
2025-08-05 20:28:39.255 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300045' → 表类型'医保_药店购药明细'
2025-08-05 20:28:39.255 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300045' → 表类型'医保_药店购药明细'
2025-08-05 20:28:39.255 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300045' → 表类型'医保_药店购药明细'
2025-08-05 20:28:39.263 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300035.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:39.268 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300035.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:39.363 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:39.367 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:39.517 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB04-CXGZSSYBJ52000001-340000002025042319472300035', 工作表='药店购药'
2025-08-05 20:28:39.517 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药'
2025-08-05 20:28:39.520 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药' → 医保_药店购药
2025-08-05 20:28:39.520 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300035' → 表类型'医保_药店购药'
2025-08-05 20:28:39.521 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300035' → 表类型'医保_药店购药'
2025-08-05 20:28:39.521 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300035' → 表类型'医保_药店购药'
2025-08-05 20:28:39.538 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300015.xls - 门诊慢特病 (行数: 10, 非空值: 100)
2025-08-05 20:28:39.550 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300015.xls - 门诊慢特病 (行数: 10, 非空值: 100)
2025-08-05 20:28:39.647 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:39.651 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:39.708 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB03-CXGZSSYBJ52000001-340000002025042319472300015', 工作表='门诊慢特病'
2025-08-05 20:28:39.712 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='门诊慢特病'
2025-08-05 20:28:39.713 - WARNING - [MainThread:26196] - import_data.py:12388 - suggest_table_type_by_name() - ⚠️ 未找到匹配的表类型: 文件名='YB03-CXGZSSYBJ52000001-340000002025042319472300015', 工作表='门诊慢特病'
2025-08-05 20:28:39.718 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300006.xls - 普通门诊 (行数: 2, 非空值: 18)
2025-08-05 20:28:39.720 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300006.xls - 普通门诊 (行数: 2, 非空值: 18)
2025-08-05 20:28:39.805 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:39.805 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:39.942 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB01-CXGZSSYBJ52000001-340000002025042319472300006', 工作表='普通门诊'
2025-08-05 20:28:39.944 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='普通门诊'
2025-08-05 20:28:39.947 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'普通门诊' → 医保_普通门诊
2025-08-05 20:28:39.950 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300006' → 表类型'医保_普通门诊'
2025-08-05 20:28:39.953 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300006' → 表类型'医保_普通门诊'
2025-08-05 20:28:39.954 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300006' → 表类型'医保_普通门诊'
2025-08-05 20:28:39.966 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300056.xls - 参保信息 (行数: 2, 非空值: 26)
2025-08-05 20:28:39.971 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300056.xls - 参保信息 (行数: 2, 非空值: 26)
2025-08-05 20:28:40.051 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:40.053 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:40.116 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB06-CXGZSSYBJ52000001-340000002025042319472300056', 工作表='参保信息'
2025-08-05 20:28:40.119 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='参保信息'
2025-08-05 20:28:40.120 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'参保信息' → 医保_参保信息
2025-08-05 20:28:40.121 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300056' → 表类型'医保_参保信息'
2025-08-05 20:28:40.121 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300056' → 表类型'医保_参保信息'
2025-08-05 20:28:40.121 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300056' → 表类型'医保_参保信息'
2025-08-05 20:28:40.127 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300046.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:40.130 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300046.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:40.201 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:40.202 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:40.274 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB05-CXGZSSYBJ52000001-340000002025042319472300046', 工作表='药店购药明细'
2025-08-05 20:28:40.276 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药明细'
2025-08-05 20:28:40.277 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药明细' → 医保_药店购药明细
2025-08-05 20:28:40.286 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300046' → 表类型'医保_药店购药明细'
2025-08-05 20:28:40.286 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300046' → 表类型'医保_药店购药明细'
2025-08-05 20:28:40.287 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300046' → 表类型'医保_药店购药明细'
2025-08-05 20:28:40.294 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300036.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:40.299 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300036.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:40.385 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:40.385 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:40.454 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB04-CXGZSSYBJ52000001-340000002025042319472300036', 工作表='药店购药'
2025-08-05 20:28:40.454 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药'
2025-08-05 20:28:40.455 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药' → 医保_药店购药
2025-08-05 20:28:40.456 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300036' → 表类型'医保_药店购药'
2025-08-05 20:28:40.456 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300036' → 表类型'医保_药店购药'
2025-08-05 20:28:40.456 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300036' → 表类型'医保_药店购药'
2025-08-05 20:28:40.461 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300057.xls - 参保信息 (行数: 3, 非空值: 45)
2025-08-05 20:28:40.463 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300057.xls - 参保信息 (行数: 3, 非空值: 45)
2025-08-05 20:28:40.526 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:40.534 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:40.591 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB06-CXGZSSYBJ52000001-340000002025042319472300057', 工作表='参保信息'
2025-08-05 20:28:40.605 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='参保信息'
2025-08-05 20:28:40.607 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'参保信息' → 医保_参保信息
2025-08-05 20:28:40.608 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300057' → 表类型'医保_参保信息'
2025-08-05 20:28:40.608 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300057' → 表类型'医保_参保信息'
2025-08-05 20:28:40.608 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300057' → 表类型'医保_参保信息'
2025-08-05 20:28:40.613 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300047.xls - 药店购药明细 (行数: 1, 非空值: 10)
2025-08-05 20:28:40.616 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300047.xls - 药店购药明细 (行数: 1, 非空值: 10)
2025-08-05 20:28:40.674 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:40.676 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:40.735 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB05-CXGZSSYBJ52000001-340000002025042319472300047', 工作表='药店购药明细'
2025-08-05 20:28:40.737 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药明细'
2025-08-05 20:28:40.738 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药明细' → 医保_药店购药明细
2025-08-05 20:28:40.758 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300047' → 表类型'医保_药店购药明细'
2025-08-05 20:28:40.758 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300047' → 表类型'医保_药店购药明细'
2025-08-05 20:28:40.759 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300047' → 表类型'医保_药店购药明细'
2025-08-05 20:28:40.767 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300037.xls - 药店购药 (行数: 5, 非空值: 55)
2025-08-05 20:28:40.774 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300037.xls - 药店购药 (行数: 5, 非空值: 55)
2025-08-05 20:28:41.013 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:41.014 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:41.085 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB04-CXGZSSYBJ52000001-340000002025042319472300037', 工作表='药店购药'
2025-08-05 20:28:41.088 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药'
2025-08-05 20:28:41.089 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药' → 医保_药店购药
2025-08-05 20:28:41.090 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300037' → 表类型'医保_药店购药'
2025-08-05 20:28:41.090 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300037' → 表类型'医保_药店购药'
2025-08-05 20:28:41.090 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300037' → 表类型'医保_药店购药'
2025-08-05 20:28:41.095 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300008.xls - 普通门诊 (行数: 10, 非空值: 90)
2025-08-05 20:28:41.099 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300008.xls - 普通门诊 (行数: 10, 非空值: 90)
2025-08-05 20:28:41.178 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:41.179 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:41.245 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB01-CXGZSSYBJ52000001-340000002025042319472300008', 工作表='普通门诊'
2025-08-05 20:28:41.246 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='普通门诊'
2025-08-05 20:28:41.247 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'普通门诊' → 医保_普通门诊
2025-08-05 20:28:41.247 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300008' → 表类型'医保_普通门诊'
2025-08-05 20:28:41.247 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300008' → 表类型'医保_普通门诊'
2025-08-05 20:28:41.248 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300008' → 表类型'医保_普通门诊'
2025-08-05 20:28:41.253 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300058.xls - 参保信息 (行数: 2, 非空值: 28)
2025-08-05 20:28:41.256 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300058.xls - 参保信息 (行数: 2, 非空值: 28)
2025-08-05 20:28:41.340 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:41.340 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:41.399 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB06-CXGZSSYBJ52000001-340000002025042319472300058', 工作表='参保信息'
2025-08-05 20:28:41.401 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='参保信息'
2025-08-05 20:28:41.402 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'参保信息' → 医保_参保信息
2025-08-05 20:28:41.403 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300058' → 表类型'医保_参保信息'
2025-08-05 20:28:41.404 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300058' → 表类型'医保_参保信息'
2025-08-05 20:28:41.407 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300058' → 表类型'医保_参保信息'
2025-08-05 20:28:41.418 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300028.xls - 住院结算数据 (行数: 4, 非空值: 40)
2025-08-05 20:28:41.423 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300028.xls - 住院结算数据 (行数: 4, 非空值: 40)
2025-08-05 20:28:41.496 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:41.497 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:41.585 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB02-CXGZSSYBJ52000001-340000002025042319472300028', 工作表='住院结算数据'
2025-08-05 20:28:41.586 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='住院结算数据'
2025-08-05 20:28:41.588 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'住院结算数据' → 医保_住院结算数据
2025-08-05 20:28:41.589 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300028' → 表类型'医保_住院结算数据'
2025-08-05 20:28:41.589 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300028' → 表类型'医保_住院结算数据'
2025-08-05 20:28:41.590 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300028' → 表类型'医保_住院结算数据'
2025-08-05 20:28:41.597 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300048.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:41.602 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300048.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:41.796 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:41.797 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:41.876 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB05-CXGZSSYBJ52000001-340000002025042319472300048', 工作表='药店购药明细'
2025-08-05 20:28:41.876 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药明细'
2025-08-05 20:28:41.877 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药明细' → 医保_药店购药明细
2025-08-05 20:28:41.878 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300048' → 表类型'医保_药店购药明细'
2025-08-05 20:28:41.878 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300048' → 表类型'医保_药店购药明细'
2025-08-05 20:28:41.879 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300048' → 表类型'医保_药店购药明细'
2025-08-05 20:28:41.884 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300038.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:41.888 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300038.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:41.946 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:41.947 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:42.004 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB04-CXGZSSYBJ52000001-340000002025042319472300038', 工作表='药店购药'
2025-08-05 20:28:42.005 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药'
2025-08-05 20:28:42.006 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药' → 医保_药店购药
2025-08-05 20:28:42.012 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300038' → 表类型'医保_药店购药'
2025-08-05 20:28:42.014 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300038' → 表类型'医保_药店购药'
2025-08-05 20:28:42.015 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300038' → 表类型'医保_药店购药'
2025-08-05 20:28:42.021 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300018.xls - 门诊慢特病 (行数: 3, 非空值: 30)
2025-08-05 20:28:42.024 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300018.xls - 门诊慢特病 (行数: 3, 非空值: 30)
2025-08-05 20:28:42.106 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:42.108 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:42.174 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB03-CXGZSSYBJ52000001-340000002025042319472300018', 工作表='门诊慢特病'
2025-08-05 20:28:42.174 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='门诊慢特病'
2025-08-05 20:28:42.175 - WARNING - [MainThread:26196] - import_data.py:12388 - suggest_table_type_by_name() - ⚠️ 未找到匹配的表类型: 文件名='YB03-CXGZSSYBJ52000001-340000002025042319472300018', 工作表='门诊慢特病'
2025-08-05 20:28:42.180 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300010.xls - 普通门诊 (行数: 5, 非空值: 45)
2025-08-05 20:28:42.183 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300010.xls - 普通门诊 (行数: 5, 非空值: 45)
2025-08-05 20:28:42.244 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:42.248 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:42.310 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB01-CXGZSSYBJ52000001-340000002025042319472300010', 工作表='普通门诊'
2025-08-05 20:28:42.311 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='普通门诊'
2025-08-05 20:28:42.312 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'普通门诊' → 医保_普通门诊
2025-08-05 20:28:42.313 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300010' → 表类型'医保_普通门诊'
2025-08-05 20:28:42.314 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300010' → 表类型'医保_普通门诊'
2025-08-05 20:28:42.314 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB01-CXGZSSYBJ52000001-340000002025042319472300010' → 表类型'医保_普通门诊'
2025-08-05 20:28:42.322 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300060.xls - 参保信息 (行数: 3, 非空值: 42)
2025-08-05 20:28:42.324 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300060.xls - 参保信息 (行数: 3, 非空值: 42)
2025-08-05 20:28:42.392 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:42.394 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:42.455 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB06-CXGZSSYBJ52000001-340000002025042319472300060', 工作表='参保信息'
2025-08-05 20:28:42.458 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='参保信息'
2025-08-05 20:28:42.459 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'参保信息' → 医保_参保信息
2025-08-05 20:28:42.460 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300060' → 表类型'医保_参保信息'
2025-08-05 20:28:42.460 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300060' → 表类型'医保_参保信息'
2025-08-05 20:28:42.460 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB06-CXGZSSYBJ52000001-340000002025042319472300060' → 表类型'医保_参保信息'
2025-08-05 20:28:42.465 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300030.xls - 住院结算数据 (行数: 2, 非空值: 20)
2025-08-05 20:28:42.467 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300030.xls - 住院结算数据 (行数: 2, 非空值: 20)
2025-08-05 20:28:42.545 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:42.547 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:42.630 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB02-CXGZSSYBJ52000001-340000002025042319472300030', 工作表='住院结算数据'
2025-08-05 20:28:42.644 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='住院结算数据'
2025-08-05 20:28:42.647 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'住院结算数据' → 医保_住院结算数据
2025-08-05 20:28:42.647 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300030' → 表类型'医保_住院结算数据'
2025-08-05 20:28:42.651 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300030' → 表类型'医保_住院结算数据'
2025-08-05 20:28:42.652 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB02-CXGZSSYBJ52000001-340000002025042319472300030' → 表类型'医保_住院结算数据'
2025-08-05 20:28:42.660 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300050.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:42.664 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300050.xls - 药店购药明细 (行数: 10, 非空值: 90)
2025-08-05 20:28:42.732 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:42.735 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:42.814 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB05-CXGZSSYBJ52000001-340000002025042319472300050', 工作表='药店购药明细'
2025-08-05 20:28:42.815 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药明细'
2025-08-05 20:28:42.816 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药明细' → 医保_药店购药明细
2025-08-05 20:28:42.817 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300050' → 表类型'医保_药店购药明细'
2025-08-05 20:28:42.821 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300050' → 表类型'医保_药店购药明细'
2025-08-05 20:28:42.822 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB05-CXGZSSYBJ52000001-340000002025042319472300050' → 表类型'医保_药店购药明细'
2025-08-05 20:28:42.842 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300040.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:42.848 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300040.xls - 药店购药 (行数: 10, 非空值: 100)
2025-08-05 20:28:42.911 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:42.912 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:42.975 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='YB04-CXGZSSYBJ52000001-340000002025042319472300040', 工作表='药店购药'
2025-08-05 20:28:42.986 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='药店购药'
2025-08-05 20:28:42.988 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'YB' + 工作表'药店购药' → 医保_药店购药
2025-08-05 20:28:43.003 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300040' → 表类型'医保_药店购药'
2025-08-05 20:28:43.004 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300040' → 表类型'医保_药店购药'
2025-08-05 20:28:43.004 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'YB04-CXGZSSYBJ52000001-340000002025042319472300040' → 表类型'医保_药店购药'
2025-08-05 20:28:43.043 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国人民银行_银行账户_001_吕庆_522101197203023219.xlsx - 开户信息 (行数: 10, 非空值: 140)
2025-08-05 20:28:43.068 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国人民银行_银行账户_001_吕庆_522101197203023219.xlsx - 开户信息 (行数: 10, 非空值: 140)
2025-08-05 20:28:43.143 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:43.144 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:43.218 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='中国人民银行_银行账户_001_吕庆_522101197203023219', 工作表='开户信息'
2025-08-05 20:28:43.218 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='开户信息'
2025-08-05 20:28:43.220 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'人民银行' → 开户信息表
2025-08-05 20:28:43.221 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国人民银行_银行账户_001_吕庆_522101197203023219' → 表类型'开户信息表'
2025-08-05 20:28:43.221 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国人民银行_银行账户_001_吕庆_522101197203023219' → 表类型'开户信息表'
2025-08-05 20:28:43.222 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国人民银行_银行账户_001_吕庆_522101197203023219' → 表类型'开户信息表'
2025-08-05 20:28:43.245 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219.xlsx - 同订单同行人(已成行) (行数: 10, 非空值: 374)
2025-08-05 20:28:43.260 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219.xlsx - 同订单同行人(未成行) (行数: 1, 非空值: 1)
2025-08-05 20:28:43.278 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219.xlsx - 同乘三次以上同行人 (行数: 10, 非空值: 378)
2025-08-05 20:28:43.311 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219.xlsx - 同订单同行人(已成行) (行数: 10, 非空值: 374)
2025-08-05 20:28:43.321 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219.xlsx - 同订单同行人(未成行) (行数: 1, 非空值: 1)
2025-08-05 20:28:43.341 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219.xlsx - 同乘三次以上同行人 (行数: 10, 非空值: 378)
2025-08-05 20:28:43.442 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:43.443 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:43.445 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219 - 同订单同行人(已成行)' → 表类型'中国航空_航班同行人信息_同订单同行人已成行'
2025-08-05 20:28:43.447 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219 - 同订单同行人(已成行)' → 表类型'中国航空_航班同行人信息_同订单同行人已成行'
2025-08-05 20:28:43.447 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219 - 同订单同行人(已成行)' → 表类型'中国航空_航班同行人信息_同订单同行人已成行'
2025-08-05 20:28:43.520 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:43.521 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:43.522 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219 - 同订单同行人(未成行)' → 表类型'中国航空_航班同行人信息_同订单同行人未成行'
2025-08-05 20:28:43.522 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219 - 同订单同行人(未成行)' → 表类型'中国航空_航班同行人信息_同订单同行人未成行'
2025-08-05 20:28:43.523 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219 - 同订单同行人(未成行)' → 表类型'中国航空_航班同行人信息_同订单同行人未成行'
2025-08-05 20:28:43.585 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:43.593 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:43.598 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219 - 同乘三次以上同行人' → 表类型'中国航空_航班同行人信息_同乘三次以上同行人'
2025-08-05 20:28:43.601 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219 - 同乘三次以上同行人' → 表类型'中国航空_航班同行人信息_同乘三次以上同行人'
2025-08-05 20:28:43.602 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国航空信息有限公司_航班同行人信息_001_吕庆_522101197203023219 - 同乘三次以上同行人' → 表类型'中国航空_航班同行人信息_同乘三次以上同行人'
2025-08-05 20:28:43.637 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国航空信息有限公司_航班进出港_001_吕庆_522101197203023219.xlsx - 航班进出港(已成行) (行数: 10, 非空值: 365)
2025-08-05 20:28:43.646 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国航空信息有限公司_航班进出港_001_吕庆_522101197203023219.xlsx - 航班进出港(未成行) (行数: 1, 非空值: 1)
2025-08-05 20:28:43.670 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国航空信息有限公司_航班进出港_001_吕庆_522101197203023219.xlsx - 航班进出港(已成行) (行数: 10, 非空值: 365)
2025-08-05 20:28:43.682 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国航空信息有限公司_航班进出港_001_吕庆_522101197203023219.xlsx - 航班进出港(未成行) (行数: 1, 非空值: 1)
2025-08-05 20:28:43.885 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:43.886 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:43.889 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班进出港_001_吕庆_522101197203023219 - 航班进出港(已成行)' → 表类型'中国航空_航班进出港_航班进出港已成行表'
2025-08-05 20:28:43.889 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班进出港_001_吕庆_522101197203023219 - 航班进出港(已成行)' → 表类型'中国航空_航班进出港_航班进出港已成行表'
2025-08-05 20:28:43.889 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国航空信息有限公司_航班进出港_001_吕庆_522101197203023219 - 航班进出港(已成行)' → 表类型'中国航空_航班进出港_航班进出港已成行表'
2025-08-05 20:28:43.950 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:43.951 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:43.953 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班进出港_001_吕庆_522101197203023219 - 航班进出港(未成行)' → 表类型'中国航空_航班进出港_航班进出港未成行表'
2025-08-05 20:28:43.954 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班进出港_001_吕庆_522101197203023219 - 航班进出港(未成行)' → 表类型'中国航空_航班进出港_航班进出港未成行表'
2025-08-05 20:28:43.955 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国航空信息有限公司_航班进出港_001_吕庆_522101197203023219 - 航班进出港(未成行)' → 表类型'中国航空_航班进出港_航班进出港未成行表'
2025-08-05 20:28:43.983 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国铁路总公司_同订单同行人_001_吕庆_522101197203023219.xlsx - 同行人员信息 (行数: 10, 非空值: 82)
2025-08-05 20:28:43.991 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国铁路总公司_同订单同行人_001_吕庆_522101197203023219.xlsx - 同行人员客票信息 (行数: 10, 非空值: 122)
2025-08-05 20:28:44.010 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国铁路总公司_同订单同行人_001_吕庆_522101197203023219.xlsx - 同行人员信息 (行数: 10, 非空值: 82)
2025-08-05 20:28:44.018 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国铁路总公司_同订单同行人_001_吕庆_522101197203023219.xlsx - 同行人员客票信息 (行数: 10, 非空值: 122)
2025-08-05 20:28:44.205 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:44.207 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:44.209 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_001_吕庆_522101197203023219 - 同行人员信息' → 表类型'中国铁路总公司_同订单同行人_同行人员信息表'
2025-08-05 20:28:44.209 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_001_吕庆_522101197203023219 - 同行人员信息' → 表类型'中国铁路总公司_同订单同行人_同行人员信息表'
2025-08-05 20:28:44.211 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_同订单同行人_001_吕庆_522101197203023219 - 同行人员信息' → 表类型'中国铁路总公司_同订单同行人_同行人员信息表'
2025-08-05 20:28:44.275 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:44.283 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:44.285 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_001_吕庆_522101197203023219 - 同行人员客票信息' → 表类型'中国铁路总公司_同订单同行人_同行人员客票'
2025-08-05 20:28:44.286 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_001_吕庆_522101197203023219 - 同行人员客票信息' → 表类型'中国铁路总公司_同订单同行人_同行人员客票'
2025-08-05 20:28:44.286 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_同订单同行人_001_吕庆_522101197203023219 - 同行人员客票信息' → 表类型'中国铁路总公司_同订单同行人_同行人员客票'
2025-08-05 20:28:44.307 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国铁路总公司_铁路客票_001_吕庆_522101197203023219.xlsx - 票面信息 (行数: 10, 非空值: 177)
2025-08-05 20:28:44.315 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国铁路总公司_铁路客票_001_吕庆_522101197203023219.xlsx - 交易信息 (行数: 10, 非空值: 56)
2025-08-05 20:28:44.329 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国铁路总公司_铁路客票_001_吕庆_522101197203023219.xlsx - 票面信息 (行数: 10, 非空值: 177)
2025-08-05 20:28:44.338 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国铁路总公司_铁路客票_001_吕庆_522101197203023219.xlsx - 交易信息 (行数: 10, 非空值: 56)
2025-08-05 20:28:44.400 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:44.400 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:44.402 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_001_吕庆_522101197203023219 - 票面信息' → 表类型'中国铁路总公司_铁路客票_票面信息表'
2025-08-05 20:28:44.402 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_001_吕庆_522101197203023219 - 票面信息' → 表类型'中国铁路总公司_铁路客票_票面信息表'
2025-08-05 20:28:44.403 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_铁路客票_001_吕庆_522101197203023219 - 票面信息' → 表类型'中国铁路总公司_铁路客票_票面信息表'
2025-08-05 20:28:44.582 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:44.584 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:44.586 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_001_吕庆_522101197203023219 - 交易信息' → 表类型'中国铁路总公司_铁路客票_交易信息表'
2025-08-05 20:28:44.586 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_001_吕庆_522101197203023219 - 交易信息' → 表类型'中国铁路总公司_铁路客票_交易信息表'
2025-08-05 20:28:44.588 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_铁路客票_001_吕庆_522101197203023219 - 交易信息' → 表类型'中国铁路总公司_铁路客票_交易信息表'
2025-08-05 20:28:44.603 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_交易流水_001_吕庆_522101197203023219.xlsx - 交易流水 (行数: 9, 非空值: 192)
2025-08-05 20:28:44.614 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_交易流水_001_吕庆_522101197203023219.xlsx - 交易流水 (行数: 9, 非空值: 192)
2025-08-05 20:28:44.673 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:44.679 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:44.755 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='中国银行_交易流水_001_吕庆_522101197203023219', 工作表='交易流水'
2025-08-05 20:28:44.756 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='交易流水'
2025-08-05 20:28:44.758 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'交易流水' → 临时账户交易明细表
2025-08-05 20:28:44.759 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_交易流水_001_吕庆_522101197203023219' → 表类型'临时账户交易明细表'
2025-08-05 20:28:44.759 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_交易流水_001_吕庆_522101197203023219' → 表类型'临时账户交易明细表'
2025-08-05 20:28:44.759 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国银行_交易流水_001_吕庆_522101197203023219' → 表类型'临时账户交易明细表'
2025-08-05 20:28:44.776 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 客户基本信息 (行数: 1, 非空值: 11)
2025-08-05 20:28:44.782 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 账号基本信息 (行数: 2, 非空值: 32)
2025-08-05 20:28:44.788 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:44.799 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:44.809 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:44.809 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:44.827 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息 (行数: 2, 非空值: 26)
2025-08-05 20:28:44.827 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 中国银行_账户信息_001_吕庆_522101197203023219.xlsx 跳过了 2 个空工作表
2025-08-05 20:28:44.840 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 客户基本信息 (行数: 1, 非空值: 11)
2025-08-05 20:28:44.847 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 账号基本信息 (行数: 2, 非空值: 32)
2025-08-05 20:28:44.855 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:44.855 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:44.863 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:44.864 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:44.873 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\中国银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息 (行数: 2, 非空值: 26)
2025-08-05 20:28:44.943 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:44.946 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:45.018 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='中国银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息', 工作表='客户基本信息'
2025-08-05 20:28:45.019 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='客户基本信息'
2025-08-05 20:28:45.020 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'客户基本信息' → 账户信息_客户基本信息表
2025-08-05 20:28:45.021 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:45.022 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:45.023 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:45.095 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:45.097 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:45.163 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='中国银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息', 工作表='账号基本信息'
2025-08-05 20:28:45.166 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='账号基本信息'
2025-08-05 20:28:45.168 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'账号基本信息' → 开户信息表
2025-08-05 20:28:45.168 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:45.169 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:45.169 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:45.246 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:45.246 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:45.315 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='中国银行_账户信息_001_吕庆_522101197203023219 - 关联子账户信息', 工作表='关联子账户信息'
2025-08-05 20:28:45.319 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='关联子账户信息'
2025-08-05 20:28:45.320 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'关联子账户信息' → 账户信息_关联子账户信息表
2025-08-05 20:28:45.320 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_账户信息_001_吕庆_522101197203023219 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:28:45.321 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_账户信息_001_吕庆_522101197203023219 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:28:45.321 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国银行_账户信息_001_吕庆_522101197203023219 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:28:45.331 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_交通违法_001_吕庆_522101197203023219.xlsx - 机动车违章信息 (行数: 3, 非空值: 27)
2025-08-05 20:28:45.338 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_交通违法_001_吕庆_522101197203023219.xlsx - 机动车违章信息 (行数: 3, 非空值: 27)
2025-08-05 20:28:45.411 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:45.412 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:45.415 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_交通违法_001_吕庆_522101197203023219' → 表类型'公安部_交通违法_机动车违章信息表'
2025-08-05 20:28:45.421 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_交通违法_001_吕庆_522101197203023219' → 表类型'公安部_交通违法_机动车违章信息表'
2025-08-05 20:28:45.421 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_交通违法_001_吕庆_522101197203023219' → 表类型'公安部_交通违法_机动车违章信息表'
2025-08-05 20:28:45.439 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_同住址_001_吕庆_522101197203023219.xlsx - 同住址 (行数: 3, 非空值: 53)
2025-08-05 20:28:45.446 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_同住址_001_吕庆_522101197203023219.xlsx - 同住址 (行数: 3, 非空值: 53)
2025-08-05 20:28:45.660 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:45.660 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:45.662 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同住址_001_吕庆_522101197203023219' → 表类型'公安部_同住址_同住址表'
2025-08-05 20:28:45.663 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同住址_001_吕庆_522101197203023219' → 表类型'公安部_同住址_同住址表'
2025-08-05 20:28:45.663 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_同住址_001_吕庆_522101197203023219' → 表类型'公安部_同住址_同住址表'
2025-08-05 20:28:45.676 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_同户人_001_吕庆_522101197203023219.xlsx - 同户人 (行数: 3, 非空值: 53)
2025-08-05 20:28:45.684 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_同户人_001_吕庆_522101197203023219.xlsx - 同户人 (行数: 3, 非空值: 53)
2025-08-05 20:28:45.745 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:45.746 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:45.748 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同户人_001_吕庆_522101197203023219' → 表类型'公安部_同户人_同户人表'
2025-08-05 20:28:45.748 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同户人_001_吕庆_522101197203023219' → 表类型'公安部_同户人_同户人表'
2025-08-05 20:28:45.749 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_同户人_001_吕庆_522101197203023219' → 表类型'公安部_同户人_同户人表'
2025-08-05 20:28:45.761 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_同车违章_001_吕庆_522101197203023219.xlsx - 同车违章 (行数: 2, 非空值: 10)
2025-08-05 20:28:45.773 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_同车违章_001_吕庆_522101197203023219.xlsx - 同车违章 (行数: 2, 非空值: 10)
2025-08-05 20:28:45.847 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:45.847 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:45.849 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同车违章_001_吕庆_522101197203023219' → 表类型'公安部_同车违章_同车违章表'
2025-08-05 20:28:45.860 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同车违章_001_吕庆_522101197203023219' → 表类型'公安部_同车违章_同车违章表'
2025-08-05 20:28:45.862 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_同车违章_001_吕庆_522101197203023219' → 表类型'公安部_同车违章_同车违章表'
2025-08-05 20:28:45.882 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_户籍人口_001_吕庆_522101197203023219.xlsx - 基本人员信息 (行数: 1, 非空值: 16)
2025-08-05 20:28:45.913 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_户籍人口_001_吕庆_522101197203023219.xlsx - 基本人员信息 (行数: 1, 非空值: 16)
2025-08-05 20:28:46.004 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:46.006 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:46.014 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_户籍人口_001_吕庆_522101197203023219' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:28:46.018 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_户籍人口_001_吕庆_522101197203023219' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:28:46.018 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_户籍人口_001_吕庆_522101197203023219' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:28:46.046 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_旅馆住宿_001_吕庆_522101197203023219.xlsx - 旅馆住宿人员信息 (行数: 10, 非空值: 114)
2025-08-05 20:28:46.075 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_旅馆住宿_001_吕庆_522101197203023219.xlsx - 旅馆住宿人员信息 (行数: 10, 非空值: 114)
2025-08-05 20:28:46.186 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:46.187 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:46.190 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_旅馆住宿_001_吕庆_522101197203023219' → 表类型'公安部_旅馆住宿_旅馆住宿人员信息表'
2025-08-05 20:28:46.190 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_旅馆住宿_001_吕庆_522101197203023219' → 表类型'公安部_旅馆住宿_旅馆住宿人员信息表'
2025-08-05 20:28:46.191 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_旅馆住宿_001_吕庆_522101197203023219' → 表类型'公安部_旅馆住宿_旅馆住宿人员信息表'
2025-08-05 20:28:46.207 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_驾驶证_001_吕庆_522101197203023219.xlsx - 驾驶证信息 (行数: 1, 非空值: 16)
2025-08-05 20:28:46.215 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\公安部_驾驶证_001_吕庆_522101197203023219.xlsx - 驾驶证信息 (行数: 1, 非空值: 16)
2025-08-05 20:28:46.279 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:46.290 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:46.293 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_驾驶证_001_吕庆_522101197203023219' → 表类型'公安部_驾驶证_驾驶证信息表'
2025-08-05 20:28:46.303 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_驾驶证_001_吕庆_522101197203023219' → 表类型'公安部_驾驶证_驾驶证信息表'
2025-08-05 20:28:46.305 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_驾驶证_001_吕庆_522101197203023219' → 表类型'公安部_驾驶证_驾驶证信息表'
2025-08-05 20:28:46.337 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\兴业银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财账户信息 (行数: 2, 非空值: 18)
2025-08-05 20:28:46.355 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\兴业银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财信息 (行数: 5, 非空值: 63)
2025-08-05 20:28:46.372 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\兴业银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财账户信息 (行数: 2, 非空值: 18)
2025-08-05 20:28:46.392 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\兴业银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财信息 (行数: 5, 非空值: 63)
2025-08-05 20:28:46.478 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:46.484 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:46.546 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='兴业银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息', 工作表='金融理财账户信息'
2025-08-05 20:28:46.553 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财账户信息'
2025-08-05 20:28:46.554 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财账户信息' → 金融理财_金融理财账户信息表
2025-08-05 20:28:46.558 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:46.561 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:46.561 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'兴业银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:46.623 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:46.623 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:46.822 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='兴业银行_金融理财_001_吕庆_522101197203023219 - 金融理财信息', 工作表='金融理财信息'
2025-08-05 20:28:46.822 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财信息'
2025-08-05 20:28:46.824 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财信息' → 金融理财_金融理财信息表
2025-08-05 20:28:46.826 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_001_吕庆_522101197203023219 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:28:46.826 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_001_吕庆_522101197203023219 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:28:46.827 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'兴业银行_金融理财_001_吕庆_522101197203023219 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:28:46.849 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 客户基本信息 (行数: 1, 非空值: 11)
2025-08-05 20:28:46.857 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 账号基本信息 (行数: 1, 非空值: 16)
2025-08-05 20:28:46.866 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:46.869 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:46.875 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:46.877 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:46.891 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息 (行数: 1, 非空值: 13)
2025-08-05 20:28:46.891 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 农业银行_账户信息_001_吕庆_522101197203023219.xlsx 跳过了 2 个空工作表
2025-08-05 20:28:46.908 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 客户基本信息 (行数: 1, 非空值: 11)
2025-08-05 20:28:46.917 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 账号基本信息 (行数: 1, 非空值: 16)
2025-08-05 20:28:46.925 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:46.926 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:46.934 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:46.935 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:46.945 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息 (行数: 1, 非空值: 13)
2025-08-05 20:28:47.042 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:47.046 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:47.125 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息', 工作表='客户基本信息'
2025-08-05 20:28:47.126 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='客户基本信息'
2025-08-05 20:28:47.128 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'客户基本信息' → 账户信息_客户基本信息表
2025-08-05 20:28:47.130 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:47.131 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:47.134 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:47.212 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:47.214 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:47.291 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息', 工作表='账号基本信息'
2025-08-05 20:28:47.292 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='账号基本信息'
2025-08-05 20:28:47.294 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'账号基本信息' → 开户信息表
2025-08-05 20:28:47.294 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:47.294 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:47.295 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:47.397 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:47.398 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:47.488 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_账户信息_001_吕庆_522101197203023219 - 关联子账户信息', 工作表='关联子账户信息'
2025-08-05 20:28:47.488 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='关联子账户信息'
2025-08-05 20:28:47.490 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'关联子账户信息' → 账户信息_关联子账户信息表
2025-08-05 20:28:47.491 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_001_吕庆_522101197203023219 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:28:47.491 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_001_吕庆_522101197203023219 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:28:47.492 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_账户信息_001_吕庆_522101197203023219 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:28:47.505 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财账户信息 (行数: 1, 非空值: 18)
2025-08-05 20:28:47.511 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财信息 (行数: 1, 非空值: 13)
2025-08-05 20:28:47.520 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财账户信息 (行数: 1, 非空值: 18)
2025-08-05 20:28:47.526 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\农业银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财信息 (行数: 1, 非空值: 13)
2025-08-05 20:28:47.591 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:47.592 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:47.656 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息', 工作表='金融理财账户信息'
2025-08-05 20:28:47.663 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财账户信息'
2025-08-05 20:28:47.664 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财账户信息' → 金融理财_金融理财账户信息表
2025-08-05 20:28:47.666 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:47.666 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:47.667 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:47.725 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:47.726 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:47.809 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_金融理财_001_吕庆_522101197203023219 - 金融理财信息', 工作表='金融理财信息'
2025-08-05 20:28:47.811 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财信息'
2025-08-05 20:28:47.813 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财信息' → 金融理财_金融理财信息表
2025-08-05 20:28:47.814 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_金融理财_001_吕庆_522101197203023219 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:28:47.814 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_金融理财_001_吕庆_522101197203023219 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:28:47.814 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_金融理财_001_吕庆_522101197203023219 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:28:47.825 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\国家税务总局_纳税人登记信息_001_吕庆_522101197203023219.xlsx - 登记信息 (行数: 1, 非空值: 6)
2025-08-05 20:28:47.835 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\国家税务总局_纳税人登记信息_001_吕庆_522101197203023219.xlsx - 登记信息 (行数: 1, 非空值: 6)
2025-08-05 20:28:47.920 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:47.921 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:47.923 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'国家税务总局_纳税人登记信息_001_吕庆_522101197203023219' → 表类型'国家税务总局_纳税人登记信息_登记信息表'
2025-08-05 20:28:47.924 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'国家税务总局_纳税人登记信息_001_吕庆_522101197203023219' → 表类型'国家税务总局_纳税人登记信息_登记信息表'
2025-08-05 20:28:47.924 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'国家税务总局_纳税人登记信息_001_吕庆_522101197203023219' → 表类型'国家税务总局_纳税人登记信息_登记信息表'
2025-08-05 20:28:47.936 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\国家税务总局_纳税信息_001_吕庆_522101197203023219.xlsx - 税务缴纳信息 (行数: 10, 非空值: 80)
2025-08-05 20:28:47.945 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\国家税务总局_纳税信息_001_吕庆_522101197203023219.xlsx - 税务缴纳信息 (行数: 10, 非空值: 80)
2025-08-05 20:28:48.037 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:48.037 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:48.039 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'国家税务总局_纳税信息_001_吕庆_522101197203023219' → 表类型'国家税务总局_纳税信息_税务缴纳信息表'
2025-08-05 20:28:48.039 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'国家税务总局_纳税信息_001_吕庆_522101197203023219' → 表类型'国家税务总局_纳税信息_税务缴纳信息表'
2025-08-05 20:28:48.039 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'国家税务总局_纳税信息_001_吕庆_522101197203023219' → 表类型'国家税务总局_纳税信息_税务缴纳信息表'
2025-08-05 20:28:48.100 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_交易流水_001_吕庆_522101197203023219.xlsx - 交易流水 (行数: 10, 非空值: 198)
2025-08-05 20:28:48.111 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_交易流水_001_吕庆_522101197203023219.xlsx - 交易流水 (行数: 10, 非空值: 198)
2025-08-05 20:28:48.194 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:48.196 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:48.278 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='工商银行_交易流水_001_吕庆_522101197203023219', 工作表='交易流水'
2025-08-05 20:28:48.279 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='交易流水'
2025-08-05 20:28:48.280 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'交易流水' → 临时账户交易明细表
2025-08-05 20:28:48.280 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_交易流水_001_吕庆_522101197203023219' → 表类型'临时账户交易明细表'
2025-08-05 20:28:48.281 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_交易流水_001_吕庆_522101197203023219' → 表类型'临时账户交易明细表'
2025-08-05 20:28:48.281 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'工商银行_交易流水_001_吕庆_522101197203023219' → 表类型'临时账户交易明细表'
2025-08-05 20:28:48.298 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 客户基本信息 (行数: 1, 非空值: 17)
2025-08-05 20:28:48.306 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 账号基本信息 (行数: 5, 非空值: 85)
2025-08-05 20:28:48.315 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:48.316 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:48.324 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:48.334 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:48.355 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息
2025-08-05 20:28:48.356 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息
2025-08-05 20:28:48.357 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 工商银行_账户信息_001_吕庆_522101197203023219.xlsx 跳过了 3 个空工作表
2025-08-05 20:28:48.378 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 客户基本信息 (行数: 1, 非空值: 17)
2025-08-05 20:28:48.387 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 账号基本信息 (行数: 5, 非空值: 85)
2025-08-05 20:28:48.392 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:48.392 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:48.398 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:48.398 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:48.404 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息
2025-08-05 20:28:48.405 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\工商银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息
2025-08-05 20:28:48.483 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:48.484 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:48.558 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='工商银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息', 工作表='客户基本信息'
2025-08-05 20:28:48.559 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='客户基本信息'
2025-08-05 20:28:48.561 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'客户基本信息' → 账户信息_客户基本信息表
2025-08-05 20:28:48.561 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:48.562 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:48.562 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'工商银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:48.620 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:48.621 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:48.681 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='工商银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息', 工作表='账号基本信息'
2025-08-05 20:28:48.685 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='账号基本信息'
2025-08-05 20:28:48.686 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'账号基本信息' → 开户信息表
2025-08-05 20:28:48.686 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:48.687 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:48.687 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'工商银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:48.710 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_交易流水_001_吕庆_522101197203023219.xlsx - 交易流水 (行数: 10, 非空值: 186)
2025-08-05 20:28:48.722 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_交易流水_001_吕庆_522101197203023219.xlsx - 交易流水 (行数: 10, 非空值: 186)
2025-08-05 20:28:48.798 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:48.798 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:48.904 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='建设银行_交易流水_001_吕庆_522101197203023219', 工作表='交易流水'
2025-08-05 20:28:48.905 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='交易流水'
2025-08-05 20:28:48.908 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'交易流水' → 临时账户交易明细表
2025-08-05 20:28:48.909 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_交易流水_001_吕庆_522101197203023219' → 表类型'临时账户交易明细表'
2025-08-05 20:28:48.909 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_交易流水_001_吕庆_522101197203023219' → 表类型'临时账户交易明细表'
2025-08-05 20:28:48.909 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'建设银行_交易流水_001_吕庆_522101197203023219' → 表类型'临时账户交易明细表'
2025-08-05 20:28:48.925 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 客户基本信息 (行数: 1, 非空值: 13)
2025-08-05 20:28:48.931 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 账号基本信息 (行数: 2, 非空值: 34)
2025-08-05 20:28:48.936 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:48.937 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:48.943 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:48.943 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:48.948 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息
2025-08-05 20:28:48.950 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息
2025-08-05 20:28:48.950 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 建设银行_账户信息_001_吕庆_522101197203023219.xlsx 跳过了 3 个空工作表
2025-08-05 20:28:48.960 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 客户基本信息 (行数: 1, 非空值: 13)
2025-08-05 20:28:48.970 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 账号基本信息 (行数: 2, 非空值: 34)
2025-08-05 20:28:48.975 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:48.976 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 强制措施信息
2025-08-05 20:28:48.981 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:48.981 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 共有权、优先权信息
2025-08-05 20:28:48.987 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息
2025-08-05 20:28:48.988 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_账户信息_001_吕庆_522101197203023219.xlsx - 关联子账户信息
2025-08-05 20:28:49.080 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:49.081 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:49.150 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='建设银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息', 工作表='客户基本信息'
2025-08-05 20:28:49.150 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='客户基本信息'
2025-08-05 20:28:49.152 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'客户基本信息' → 账户信息_客户基本信息表
2025-08-05 20:28:49.152 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:49.152 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:49.154 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'建设银行_账户信息_001_吕庆_522101197203023219 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:49.228 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:49.229 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:49.311 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='建设银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息', 工作表='账号基本信息'
2025-08-05 20:28:49.316 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='账号基本信息'
2025-08-05 20:28:49.319 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'账号基本信息' → 开户信息表
2025-08-05 20:28:49.320 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:49.320 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:49.321 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'建设银行_账户信息_001_吕庆_522101197203023219 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:49.341 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财账户信息 (行数: 2, 非空值: 36)
2025-08-05 20:28:49.347 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财信息
2025-08-05 20:28:49.350 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财信息
2025-08-05 20:28:49.352 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 建设银行_金融理财_001_吕庆_522101197203023219.xlsx 跳过了 1 个空工作表
2025-08-05 20:28:49.367 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财账户信息 (行数: 2, 非空值: 36)
2025-08-05 20:28:49.375 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财信息
2025-08-05 20:28:49.375 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\建设银行_金融理财_001_吕庆_522101197203023219.xlsx - 金融理财信息
2025-08-05 20:28:49.475 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:49.477 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:49.613 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='建设银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息', 工作表='金融理财账户信息'
2025-08-05 20:28:49.613 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财账户信息'
2025-08-05 20:28:49.613 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财账户信息' → 金融理财_金融理财账户信息表
2025-08-05 20:28:49.613 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:49.613 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:49.613 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'建设银行_金融理财_001_吕庆_522101197203023219 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:49.672 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\电信_登记信息_001_吕庆_522101197203023219.xlsx - 运营商登记信息 (行数: 1, 非空值: 26)
2025-08-05 20:28:49.704 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\电信_登记信息_001_吕庆_522101197203023219.xlsx - 运营商登记信息 (行数: 1, 非空值: 26)
2025-08-05 20:28:49.830 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:49.830 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:49.830 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'电信_登记信息_001_吕庆_522101197203023219' → 表类型'电话_登记信息_运营商登记信息表'
2025-08-05 20:28:49.830 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'电信_登记信息_001_吕庆_522101197203023219' → 表类型'电话_登记信息_运营商登记信息表'
2025-08-05 20:28:49.830 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'电信_登记信息_001_吕庆_522101197203023219' → 表类型'电话_登记信息_运营商登记信息表'
2025-08-05 20:28:49.893 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\电信_话单信息_001_吕庆_522101197203023219.xlsx - 运营商话单信息 (行数: 5, 非空值: 35)
2025-08-05 20:28:49.942 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\电信_话单信息_001_吕庆_522101197203023219.xlsx - 运营商话单信息 (行数: 5, 非空值: 35)
2025-08-05 20:28:50.038 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:50.055 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:50.055 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'电信_话单信息_001_吕庆_522101197203023219' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:50.055 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'电信_话单信息_001_吕庆_522101197203023219' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:50.069 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'电信_话单信息_001_吕庆_522101197203023219' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:50.135 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\电信_话单信息_001_吕庆_522101197203023219~1.xlsx - 运营商话单信息 (行数: 10, 非空值: 174)
2025-08-05 20:28:50.197 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\电信_话单信息_001_吕庆_522101197203023219~1.xlsx - 运营商话单信息 (行数: 10, 非空值: 174)
2025-08-05 20:28:50.292 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:50.292 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:50.308 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'电信_话单信息_001_吕庆_522101197203023219~1' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:50.308 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'电信_话单信息_001_吕庆_522101197203023219~1' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:50.308 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'电信_话单信息_001_吕庆_522101197203023219~1' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:50.372 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\移动_登记信息_001_吕庆_522101197203023219.xlsx - 运营商登记信息 (行数: 4, 非空值: 92)
2025-08-05 20:28:50.419 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\移动_登记信息_001_吕庆_522101197203023219.xlsx - 运营商登记信息 (行数: 4, 非空值: 92)
2025-08-05 20:28:50.515 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:50.515 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:50.531 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'移动_登记信息_001_吕庆_522101197203023219' → 表类型'电话_登记信息_运营商登记信息表'
2025-08-05 20:28:50.531 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'移动_登记信息_001_吕庆_522101197203023219' → 表类型'电话_登记信息_运营商登记信息表'
2025-08-05 20:28:50.531 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'移动_登记信息_001_吕庆_522101197203023219' → 表类型'电话_登记信息_运营商登记信息表'
2025-08-05 20:28:50.597 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\移动_话单信息_001_吕庆_522101197203023219.xlsx - 运营商话单信息 (行数: 10, 非空值: 140)
2025-08-05 20:28:50.658 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\移动_话单信息_001_吕庆_522101197203023219.xlsx - 运营商话单信息 (行数: 10, 非空值: 140)
2025-08-05 20:28:50.738 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:50.738 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:50.754 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'移动_话单信息_001_吕庆_522101197203023219' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:50.754 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'移动_话单信息_001_吕庆_522101197203023219' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:50.754 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'移动_话单信息_001_吕庆_522101197203023219' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:50.818 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\移动_话单信息_001_吕庆_522101197203023219~1.xlsx - 运营商话单信息 (行数: 10, 非空值: 70)
2025-08-05 20:28:50.881 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\移动_话单信息_001_吕庆_522101197203023219~1.xlsx - 运营商话单信息 (行数: 10, 非空值: 70)
2025-08-05 20:28:50.976 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:50.976 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:50.976 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'移动_话单信息_001_吕庆_522101197203023219~1' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:50.976 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'移动_话单信息_001_吕庆_522101197203023219~1' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:50.976 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'移动_话单信息_001_吕庆_522101197203023219~1' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-05 20:28:51.025 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\自然资源部不动产全国库查询_不动产全国总库_001_吕庆_522101197203023219.xlsx - 房地产权 (行数: 1, 非空值: 19)
2025-08-05 20:28:51.041 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\自然资源部不动产全国库查询_不动产全国总库_001_吕庆_522101197203023219.xlsx - 预告登记 (行数: 2, 非空值: 38)
2025-08-05 20:28:51.073 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\自然资源部不动产全国库查询_不动产全国总库_001_吕庆_522101197203023219.xlsx - 房地产权 (行数: 1, 非空值: 19)
2025-08-05 20:28:51.089 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\自然资源部不动产全国库查询_不动产全国总库_001_吕庆_522101197203023219.xlsx - 预告登记 (行数: 2, 非空值: 38)
2025-08-05 20:28:51.184 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:51.184 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:51.184 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'自然资源部不动产全国库查询_不动产全国总库_001_吕庆_522101197203023219 - 房地产权' → 表类型'不动产查询_不动产全国总库_房地产权表'
2025-08-05 20:28:51.184 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'自然资源部不动产全国库查询_不动产全国总库_001_吕庆_522101197203023219 - 房地产权' → 表类型'不动产查询_不动产全国总库_房地产权表'
2025-08-05 20:28:51.184 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'自然资源部不动产全国库查询_不动产全国总库_001_吕庆_522101197203023219 - 房地产权' → 表类型'不动产查询_不动产全国总库_房地产权表'
2025-08-05 20:28:51.264 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:51.264 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:51.280 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'自然资源部不动产全国库查询_不动产全国总库_001_吕庆_522101197203023219 - 预告登记' → 表类型'不动产查询_不动产全国总库_预告登记表'
2025-08-05 20:28:51.280 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'自然资源部不动产全国库查询_不动产全国总库_001_吕庆_522101197203023219 - 预告登记' → 表类型'不动产查询_不动产全国总库_预告登记表'
2025-08-05 20:28:51.280 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'自然资源部不动产全国库查询_不动产全国总库_001_吕庆_522101197203023219 - 预告登记' → 表类型'不动产查询_不动产全国总库_预告登记表'
2025-08-05 20:28:51.344 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\银保信_保险产品_001_吕庆_522101197203023219.xlsx - 保险保单信息 (行数: 10, 非空值: 176)
2025-08-05 20:28:51.377 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\银保信_保险产品_001_吕庆_522101197203023219.xlsx - 保险人员信息 (行数: 10, 非空值: 73)
2025-08-05 20:28:51.398 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\银保信_保险产品_001_吕庆_522101197203023219.xlsx - 家庭财产保险
2025-08-05 20:28:51.399 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\银保信_保险产品_001_吕庆_522101197203023219.xlsx - 家庭财产保险
2025-08-05 20:28:51.399 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 银保信_保险产品_001_吕庆_522101197203023219.xlsx 跳过了 1 个空工作表
2025-08-05 20:28:51.440 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\银保信_保险产品_001_吕庆_522101197203023219.xlsx - 保险保单信息 (行数: 10, 非空值: 176)
2025-08-05 20:28:51.470 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\银保信_保险产品_001_吕庆_522101197203023219.xlsx - 保险人员信息 (行数: 10, 非空值: 73)
2025-08-05 20:28:51.486 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\银保信_保险产品_001_吕庆_522101197203023219.xlsx - 家庭财产保险
2025-08-05 20:28:51.486 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\001_吕庆_522101197203023219\银保信_保险产品_001_吕庆_522101197203023219.xlsx - 家庭财产保险
2025-08-05 20:28:51.581 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:51.581 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:51.597 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_001_吕庆_522101197203023219 - 保险保单信息' → 表类型'银保信_保险产品_保险保单信息表'
2025-08-05 20:28:51.597 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_001_吕庆_522101197203023219 - 保险保单信息' → 表类型'银保信_保险产品_保险保单信息表'
2025-08-05 20:28:51.597 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'银保信_保险产品_001_吕庆_522101197203023219 - 保险保单信息' → 表类型'银保信_保险产品_保险保单信息表'
2025-08-05 20:28:51.709 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:51.709 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:51.719 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_001_吕庆_522101197203023219 - 保险人员信息' → 表类型'银保信_保险产品_保险人员信息表'
2025-08-05 20:28:51.724 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_001_吕庆_522101197203023219 - 保险人员信息' → 表类型'银保信_保险产品_保险人员信息表'
2025-08-05 20:28:51.724 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'银保信_保险产品_001_吕庆_522101197203023219 - 保险人员信息' → 表类型'银保信_保险产品_保险人员信息表'
2025-08-05 20:28:51.756 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\中国人民银行_银行账户_002_陈荣峥_52250119470423002X.xlsx - 开户信息 (行数: 2, 非空值: 28)
2025-08-05 20:28:51.788 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\中国人民银行_银行账户_002_陈荣峥_52250119470423002X.xlsx - 开户信息 (行数: 2, 非空值: 28)
2025-08-05 20:28:51.884 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:51.884 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:51.979 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='中国人民银行_银行账户_002_陈荣峥_52250119470423002X', 工作表='开户信息'
2025-08-05 20:28:51.979 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='开户信息'
2025-08-05 20:28:51.979 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'人民银行' → 开户信息表
2025-08-05 20:28:51.979 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国人民银行_银行账户_002_陈荣峥_52250119470423002X' → 表类型'开户信息表'
2025-08-05 20:28:51.979 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国人民银行_银行账户_002_陈荣峥_52250119470423002X' → 表类型'开户信息表'
2025-08-05 20:28:51.984 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国人民银行_银行账户_002_陈荣峥_52250119470423002X' → 表类型'开户信息表'
2025-08-05 20:28:52.021 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\公安部_户籍人口_002_陈荣峥_52250119470423002X.xlsx - 基本人员信息 (行数: 1, 非空值: 18)
2025-08-05 20:28:52.049 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\公安部_户籍人口_002_陈荣峥_52250119470423002X.xlsx - 基本人员信息 (行数: 1, 非空值: 18)
2025-08-05 20:28:52.138 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:52.138 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:52.138 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_户籍人口_002_陈荣峥_52250119470423002X' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:28:52.138 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_户籍人口_002_陈荣峥_52250119470423002X' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:28:52.138 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_户籍人口_002_陈荣峥_52250119470423002X' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:28:52.184 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\兴业银行_金融理财_002_陈荣峥_52250119470423002X.xlsx - 金融理财账户信息 (行数: 2, 非空值: 18)
2025-08-05 20:28:52.223 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\兴业银行_金融理财_002_陈荣峥_52250119470423002X.xlsx - 金融理财信息 (行数: 5, 非空值: 63)
2025-08-05 20:28:52.260 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\兴业银行_金融理财_002_陈荣峥_52250119470423002X.xlsx - 金融理财账户信息 (行数: 2, 非空值: 18)
2025-08-05 20:28:52.282 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\兴业银行_金融理财_002_陈荣峥_52250119470423002X.xlsx - 金融理财信息 (行数: 5, 非空值: 63)
2025-08-05 20:28:52.360 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:52.360 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:52.440 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='兴业银行_金融理财_002_陈荣峥_52250119470423002X - 金融理财账户信息', 工作表='金融理财账户信息'
2025-08-05 20:28:52.440 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财账户信息'
2025-08-05 20:28:52.455 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财账户信息' → 金融理财_金融理财账户信息表
2025-08-05 20:28:52.455 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_002_陈荣峥_52250119470423002X - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:52.455 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_002_陈荣峥_52250119470423002X - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:52.455 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'兴业银行_金融理财_002_陈荣峥_52250119470423002X - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:28:52.534 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:52.534 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:52.645 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='兴业银行_金融理财_002_陈荣峥_52250119470423002X - 金融理财信息', 工作表='金融理财信息'
2025-08-05 20:28:52.645 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财信息'
2025-08-05 20:28:52.661 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财信息' → 金融理财_金融理财信息表
2025-08-05 20:28:52.661 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_002_陈荣峥_52250119470423002X - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:28:52.661 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_002_陈荣峥_52250119470423002X - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:28:52.661 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'兴业银行_金融理财_002_陈荣峥_52250119470423002X - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:28:52.726 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_交易流水_002_陈荣峥_52250119470423002X.xlsx - 交易流水 (行数: 10, 非空值: 200)
2025-08-05 20:28:52.773 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_交易流水_002_陈荣峥_52250119470423002X.xlsx - 交易流水 (行数: 10, 非空值: 200)
2025-08-05 20:28:52.917 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:52.917 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:53.013 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='工商银行_交易流水_002_陈荣峥_52250119470423002X', 工作表='交易流水'
2025-08-05 20:28:53.013 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='交易流水'
2025-08-05 20:28:53.013 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'交易流水' → 临时账户交易明细表
2025-08-05 20:28:53.013 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_交易流水_002_陈荣峥_52250119470423002X' → 表类型'临时账户交易明细表'
2025-08-05 20:28:53.013 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_交易流水_002_陈荣峥_52250119470423002X' → 表类型'临时账户交易明细表'
2025-08-05 20:28:53.013 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'工商银行_交易流水_002_陈荣峥_52250119470423002X' → 表类型'临时账户交易明细表'
2025-08-05 20:28:53.077 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 客户基本信息 (行数: 1, 非空值: 9)
2025-08-05 20:28:53.099 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 账号基本信息 (行数: 2, 非空值: 33)
2025-08-05 20:28:53.125 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 强制措施信息
2025-08-05 20:28:53.125 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 强制措施信息
2025-08-05 20:28:53.142 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 共有权、优先权信息
2025-08-05 20:28:53.142 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 共有权、优先权信息
2025-08-05 20:28:53.174 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 关联子账户信息 (行数: 1, 非空值: 5)
2025-08-05 20:28:53.174 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx 跳过了 2 个空工作表
2025-08-05 20:28:53.221 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 客户基本信息 (行数: 1, 非空值: 9)
2025-08-05 20:28:53.253 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 账号基本信息 (行数: 2, 非空值: 33)
2025-08-05 20:28:53.269 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 强制措施信息
2025-08-05 20:28:53.269 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 强制措施信息
2025-08-05 20:28:53.301 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 共有权、优先权信息
2025-08-05 20:28:53.301 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 共有权、优先权信息
2025-08-05 20:28:53.317 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\工商银行_账户信息_002_陈荣峥_52250119470423002X.xlsx - 关联子账户信息 (行数: 1, 非空值: 5)
2025-08-05 20:28:53.428 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:53.428 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:53.523 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='工商银行_账户信息_002_陈荣峥_52250119470423002X - 客户基本信息', 工作表='客户基本信息'
2025-08-05 20:28:53.523 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='客户基本信息'
2025-08-05 20:28:53.523 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'客户基本信息' → 账户信息_客户基本信息表
2025-08-05 20:28:53.523 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_002_陈荣峥_52250119470423002X - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:53.523 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_002_陈荣峥_52250119470423002X - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:53.523 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'工商银行_账户信息_002_陈荣峥_52250119470423002X - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:53.618 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:53.618 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:53.699 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='工商银行_账户信息_002_陈荣峥_52250119470423002X - 账号基本信息', 工作表='账号基本信息'
2025-08-05 20:28:53.699 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='账号基本信息'
2025-08-05 20:28:53.699 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'账号基本信息' → 开户信息表
2025-08-05 20:28:53.699 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_002_陈荣峥_52250119470423002X - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:53.699 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_002_陈荣峥_52250119470423002X - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:53.699 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'工商银行_账户信息_002_陈荣峥_52250119470423002X - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:53.776 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:53.776 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:53.856 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='工商银行_账户信息_002_陈荣峥_52250119470423002X - 关联子账户信息', 工作表='关联子账户信息'
2025-08-05 20:28:53.856 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='关联子账户信息'
2025-08-05 20:28:53.856 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'关联子账户信息' → 账户信息_关联子账户信息表
2025-08-05 20:28:53.856 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_002_陈荣峥_52250119470423002X - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:28:53.856 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_002_陈荣峥_52250119470423002X - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:28:53.856 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'工商银行_账户信息_002_陈荣峥_52250119470423002X - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:28:53.903 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\自然资源部不动产全国库查询_不动产全国总库_002_陈荣峥_52250119470423002X.xlsx - 房地产权 (行数: 3, 非空值: 54)
2025-08-05 20:28:53.935 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\自然资源部不动产全国库查询_不动产全国总库_002_陈荣峥_52250119470423002X.xlsx - 房地产权 (行数: 3, 非空值: 54)
2025-08-05 20:28:54.031 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:54.031 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:54.047 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'自然资源部不动产全国库查询_不动产全国总库_002_陈荣峥_52250119470423002X' → 表类型'不动产查询_不动产全国总库_房地产权表'
2025-08-05 20:28:54.047 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'自然资源部不动产全国库查询_不动产全国总库_002_陈荣峥_52250119470423002X' → 表类型'不动产查询_不动产全国总库_房地产权表'
2025-08-05 20:28:54.047 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'自然资源部不动产全国库查询_不动产全国总库_002_陈荣峥_52250119470423002X' → 表类型'不动产查询_不动产全国总库_房地产权表'
2025-08-05 20:28:54.094 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\银保信_保险产品_002_陈荣峥_52250119470423002X.xlsx - 保险保单信息 (行数: 1, 非空值: 18)
2025-08-05 20:28:54.110 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\银保信_保险产品_002_陈荣峥_52250119470423002X.xlsx - 保险人员信息 (行数: 2, 非空值: 17)
2025-08-05 20:28:54.142 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\银保信_保险产品_002_陈荣峥_52250119470423002X.xlsx - 保险保单信息 (行数: 1, 非空值: 18)
2025-08-05 20:28:54.170 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\002_陈荣峥_52250119470423002X\银保信_保险产品_002_陈荣峥_52250119470423002X.xlsx - 保险人员信息 (行数: 2, 非空值: 17)
2025-08-05 20:28:54.251 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:54.251 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:54.260 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_002_陈荣峥_52250119470423002X - 保险保单信息' → 表类型'银保信_保险产品_保险保单信息表'
2025-08-05 20:28:54.261 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_002_陈荣峥_52250119470423002X - 保险保单信息' → 表类型'银保信_保险产品_保险保单信息表'
2025-08-05 20:28:54.262 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'银保信_保险产品_002_陈荣峥_52250119470423002X - 保险保单信息' → 表类型'银保信_保险产品_保险保单信息表'
2025-08-05 20:28:54.349 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:54.350 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:54.357 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_002_陈荣峥_52250119470423002X - 保险人员信息' → 表类型'银保信_保险产品_保险人员信息表'
2025-08-05 20:28:54.358 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_002_陈荣峥_52250119470423002X - 保险人员信息' → 表类型'银保信_保险产品_保险人员信息表'
2025-08-05 20:28:54.359 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'银保信_保险产品_002_陈荣峥_52250119470423002X - 保险人员信息' → 表类型'银保信_保险产品_保险人员信息表'
2025-08-05 20:28:54.406 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国人民银行_银行账户_003_韩莎莎_522501198306050822.xlsx - 开户信息 (行数: 10, 非空值: 145)
2025-08-05 20:28:54.454 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国人民银行_银行账户_003_韩莎莎_522501198306050822.xlsx - 开户信息 (行数: 10, 非空值: 145)
2025-08-05 20:28:54.581 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:54.581 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:54.690 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='中国人民银行_银行账户_003_韩莎莎_522501198306050822', 工作表='开户信息'
2025-08-05 20:28:54.690 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='开户信息'
2025-08-05 20:28:54.690 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'人民银行' → 开户信息表
2025-08-05 20:28:54.690 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国人民银行_银行账户_003_韩莎莎_522501198306050822' → 表类型'开户信息表'
2025-08-05 20:28:54.690 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国人民银行_银行账户_003_韩莎莎_522501198306050822' → 表类型'开户信息表'
2025-08-05 20:28:54.690 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国人民银行_银行账户_003_韩莎莎_522501198306050822' → 表类型'开户信息表'
2025-08-05 20:28:54.786 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822.xlsx - 同订单同行人(已成行) (行数: 10, 非空值: 330)
2025-08-05 20:28:54.802 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822.xlsx - 同订单同行人(未成行) (行数: 1, 非空值: 1)
2025-08-05 20:28:54.837 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822.xlsx - 同乘三次以上同行人 (行数: 1, 非空值: 1)
2025-08-05 20:28:54.898 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822.xlsx - 同订单同行人(已成行) (行数: 10, 非空值: 330)
2025-08-05 20:28:55.024 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822.xlsx - 同订单同行人(未成行) (行数: 1, 非空值: 1)
2025-08-05 20:28:55.053 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822.xlsx - 同乘三次以上同行人 (行数: 1, 非空值: 1)
2025-08-05 20:28:55.153 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:55.154 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:55.158 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822 - 同订单同行人(已成行)' → 表类型'中国航空_航班同行人信息_同订单同行人已成行'
2025-08-05 20:28:55.158 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822 - 同订单同行人(已成行)' → 表类型'中国航空_航班同行人信息_同订单同行人已成行'
2025-08-05 20:28:55.158 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822 - 同订单同行人(已成行)' → 表类型'中国航空_航班同行人信息_同订单同行人已成行'
2025-08-05 20:28:55.245 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:55.245 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:55.261 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822 - 同订单同行人(未成行)' → 表类型'中国航空_航班同行人信息_同订单同行人未成行'
2025-08-05 20:28:55.261 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822 - 同订单同行人(未成行)' → 表类型'中国航空_航班同行人信息_同订单同行人未成行'
2025-08-05 20:28:55.268 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822 - 同订单同行人(未成行)' → 表类型'中国航空_航班同行人信息_同订单同行人未成行'
2025-08-05 20:28:55.390 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:55.393 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:55.403 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822 - 同乘三次以上同行人' → 表类型'中国航空_航班同行人信息_同乘三次以上同行人'
2025-08-05 20:28:55.404 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822 - 同乘三次以上同行人' → 表类型'中国航空_航班同行人信息_同乘三次以上同行人'
2025-08-05 20:28:55.405 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国航空信息有限公司_航班同行人信息_003_韩莎莎_522501198306050822 - 同乘三次以上同行人' → 表类型'中国航空_航班同行人信息_同乘三次以上同行人'
2025-08-05 20:28:55.453 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国航空信息有限公司_航班进出港_003_韩莎莎_522501198306050822.xlsx - 航班进出港(已成行) (行数: 2, 非空值: 64)
2025-08-05 20:28:55.484 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国航空信息有限公司_航班进出港_003_韩莎莎_522501198306050822.xlsx - 航班进出港(未成行) (行数: 1, 非空值: 1)
2025-08-05 20:28:55.516 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国航空信息有限公司_航班进出港_003_韩莎莎_522501198306050822.xlsx - 航班进出港(已成行) (行数: 2, 非空值: 64)
2025-08-05 20:28:55.546 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国航空信息有限公司_航班进出港_003_韩莎莎_522501198306050822.xlsx - 航班进出港(未成行) (行数: 1, 非空值: 1)
2025-08-05 20:28:55.644 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:55.644 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:55.660 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班进出港_003_韩莎莎_522501198306050822 - 航班进出港(已成行)' → 表类型'中国航空_航班进出港_航班进出港已成行表'
2025-08-05 20:28:55.660 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班进出港_003_韩莎莎_522501198306050822 - 航班进出港(已成行)' → 表类型'中国航空_航班进出港_航班进出港已成行表'
2025-08-05 20:28:55.660 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国航空信息有限公司_航班进出港_003_韩莎莎_522501198306050822 - 航班进出港(已成行)' → 表类型'中国航空_航班进出港_航班进出港已成行表'
2025-08-05 20:28:55.746 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:55.756 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:55.756 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班进出港_003_韩莎莎_522501198306050822 - 航班进出港(未成行)' → 表类型'中国航空_航班进出港_航班进出港未成行表'
2025-08-05 20:28:55.756 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国航空信息有限公司_航班进出港_003_韩莎莎_522501198306050822 - 航班进出港(未成行)' → 表类型'中国航空_航班进出港_航班进出港未成行表'
2025-08-05 20:28:55.756 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国航空信息有限公司_航班进出港_003_韩莎莎_522501198306050822 - 航班进出港(未成行)' → 表类型'中国航空_航班进出港_航班进出港未成行表'
2025-08-05 20:28:55.821 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_同订单同行人_003_韩莎莎_522501198306050822.xlsx - 同行人员信息 (行数: 10, 非空值: 85)
2025-08-05 20:28:55.857 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_同订单同行人_003_韩莎莎_522501198306050822.xlsx - 同行人员客票信息 (行数: 10, 非空值: 121)
2025-08-05 20:28:55.883 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_同订单同行人_003_韩莎莎_522501198306050822.xlsx - 同行人员信息 (行数: 10, 非空值: 85)
2025-08-05 20:28:55.915 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_同订单同行人_003_韩莎莎_522501198306050822.xlsx - 同行人员客票信息 (行数: 10, 非空值: 121)
2025-08-05 20:28:56.016 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:56.016 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:56.025 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_003_韩莎莎_522501198306050822 - 同行人员信息' → 表类型'中国铁路总公司_同订单同行人_同行人员信息表'
2025-08-05 20:28:56.025 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_003_韩莎莎_522501198306050822 - 同行人员信息' → 表类型'中国铁路总公司_同订单同行人_同行人员信息表'
2025-08-05 20:28:56.025 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_同订单同行人_003_韩莎莎_522501198306050822 - 同行人员信息' → 表类型'中国铁路总公司_同订单同行人_同行人员信息表'
2025-08-05 20:28:56.136 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:56.136 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:56.136 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_003_韩莎莎_522501198306050822 - 同行人员客票信息' → 表类型'中国铁路总公司_同订单同行人_同行人员客票'
2025-08-05 20:28:56.136 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_003_韩莎莎_522501198306050822 - 同行人员客票信息' → 表类型'中国铁路总公司_同订单同行人_同行人员客票'
2025-08-05 20:28:56.136 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_同订单同行人_003_韩莎莎_522501198306050822 - 同行人员客票信息' → 表类型'中国铁路总公司_同订单同行人_同行人员客票'
2025-08-05 20:28:56.184 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_用户注册_003_韩莎莎_522501198306050822.xlsx - 互联网注册信息 (行数: 1, 非空值: 9)
2025-08-05 20:28:56.212 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_用户注册_003_韩莎莎_522501198306050822.xlsx - 常用联系人信息 (行数: 1, 非空值: 8)
2025-08-05 20:28:56.244 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_用户注册_003_韩莎莎_522501198306050822.xlsx - 互联网注册信息 (行数: 1, 非空值: 9)
2025-08-05 20:28:56.265 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_用户注册_003_韩莎莎_522501198306050822.xlsx - 常用联系人信息 (行数: 1, 非空值: 8)
2025-08-05 20:28:56.378 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:56.383 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:56.383 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_用户注册_003_韩莎莎_522501198306050822 - 互联网注册信息' → 表类型'中国铁路总公司_用户注册_互联网注册信息表'
2025-08-05 20:28:56.383 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_用户注册_003_韩莎莎_522501198306050822 - 互联网注册信息' → 表类型'中国铁路总公司_用户注册_互联网注册信息表'
2025-08-05 20:28:56.397 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_用户注册_003_韩莎莎_522501198306050822 - 互联网注册信息' → 表类型'中国铁路总公司_用户注册_互联网注册信息表'
2025-08-05 20:28:56.495 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:56.508 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:56.508 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_用户注册_003_韩莎莎_522501198306050822 - 常用联系人信息' → 表类型'中国铁路总公司_用户注册_常用联系人信息表'
2025-08-05 20:28:56.508 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_用户注册_003_韩莎莎_522501198306050822 - 常用联系人信息' → 表类型'中国铁路总公司_用户注册_常用联系人信息表'
2025-08-05 20:28:56.525 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_用户注册_003_韩莎莎_522501198306050822 - 常用联系人信息' → 表类型'中国铁路总公司_用户注册_常用联系人信息表'
2025-08-05 20:28:56.620 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_铁路客票_003_韩莎莎_522501198306050822.xlsx - 票面信息 (行数: 10, 非空值: 189)
2025-08-05 20:28:56.652 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_铁路客票_003_韩莎莎_522501198306050822.xlsx - 交易信息 (行数: 10, 非空值: 60)
2025-08-05 20:28:56.718 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_铁路客票_003_韩莎莎_522501198306050822.xlsx - 票面信息 (行数: 10, 非空值: 189)
2025-08-05 20:28:56.755 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国铁路总公司_铁路客票_003_韩莎莎_522501198306050822.xlsx - 交易信息 (行数: 10, 非空值: 60)
2025-08-05 20:28:56.889 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:56.889 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:56.898 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_003_韩莎莎_522501198306050822 - 票面信息' → 表类型'中国铁路总公司_铁路客票_票面信息表'
2025-08-05 20:28:56.908 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_003_韩莎莎_522501198306050822 - 票面信息' → 表类型'中国铁路总公司_铁路客票_票面信息表'
2025-08-05 20:28:56.921 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_铁路客票_003_韩莎莎_522501198306050822 - 票面信息' → 表类型'中国铁路总公司_铁路客票_票面信息表'
2025-08-05 20:28:57.032 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:57.032 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:57.048 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_003_韩莎莎_522501198306050822 - 交易信息' → 表类型'中国铁路总公司_铁路客票_交易信息表'
2025-08-05 20:28:57.055 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_003_韩莎莎_522501198306050822 - 交易信息' → 表类型'中国铁路总公司_铁路客票_交易信息表'
2025-08-05 20:28:57.064 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_铁路客票_003_韩莎莎_522501198306050822 - 交易信息' → 表类型'中国铁路总公司_铁路客票_交易信息表'
2025-08-05 20:28:57.142 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_交易流水_003_韩莎莎_522501198306050822.xlsx - 交易流水 (行数: 10, 非空值: 170)
2025-08-05 20:28:57.205 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_交易流水_003_韩莎莎_522501198306050822.xlsx - 交易流水 (行数: 10, 非空值: 170)
2025-08-05 20:28:57.316 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:57.316 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:57.444 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='中国银行_交易流水_003_韩莎莎_522501198306050822', 工作表='交易流水'
2025-08-05 20:28:57.444 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='交易流水'
2025-08-05 20:28:57.444 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'交易流水' → 临时账户交易明细表
2025-08-05 20:28:57.444 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:28:57.444 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:28:57.444 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:28:57.523 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 客户基本信息 (行数: 1, 非空值: 13)
2025-08-05 20:28:57.549 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 账号基本信息 (行数: 1, 非空值: 15)
2025-08-05 20:28:57.568 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:28:57.568 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:28:57.612 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:28:57.620 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:28:57.654 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:28:57.660 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:28:57.667 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx 跳过了 3 个空工作表
2025-08-05 20:28:57.730 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 客户基本信息 (行数: 1, 非空值: 13)
2025-08-05 20:28:57.759 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 账号基本信息 (行数: 1, 非空值: 15)
2025-08-05 20:28:57.784 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:28:57.790 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:28:57.826 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:28:57.830 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:28:57.866 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:28:57.876 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\中国银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:28:58.008 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:58.010 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:58.104 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='中国银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息', 工作表='客户基本信息'
2025-08-05 20:28:58.104 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='客户基本信息'
2025-08-05 20:28:58.104 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'客户基本信息' → 账户信息_客户基本信息表
2025-08-05 20:28:58.104 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:58.104 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:58.104 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:28:58.197 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:58.198 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:58.310 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='中国银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息', 工作表='账号基本信息'
2025-08-05 20:28:58.310 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='账号基本信息'
2025-08-05 20:28:58.317 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'账号基本信息' → 开户信息表
2025-08-05 20:28:58.317 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:58.317 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:58.317 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:28:58.358 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_交通违法_003_韩莎莎_522501198306050822.xlsx - 机动车违章信息 (行数: 3, 非空值: 27)
2025-08-05 20:28:58.392 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_交通违法_003_韩莎莎_522501198306050822.xlsx - 机动车违章信息 (行数: 3, 非空值: 27)
2025-08-05 20:28:58.485 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:58.500 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:58.500 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_交通违法_003_韩莎莎_522501198306050822' → 表类型'公安部_交通违法_机动车违章信息表'
2025-08-05 20:28:58.500 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_交通违法_003_韩莎莎_522501198306050822' → 表类型'公安部_交通违法_机动车违章信息表'
2025-08-05 20:28:58.500 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_交通违法_003_韩莎莎_522501198306050822' → 表类型'公安部_交通违法_机动车违章信息表'
2025-08-05 20:28:58.548 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_出国（境）证件_003_韩莎莎_522501198306050822.xlsx - 出入境证件信息 (行数: 1, 非空值: 14)
2025-08-05 20:28:58.580 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_出国（境）证件_003_韩莎莎_522501198306050822.xlsx - 出入境证件信息 (行数: 1, 非空值: 14)
2025-08-05 20:28:58.677 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:58.677 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:58.690 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_出国（境）证件_003_韩莎莎_522501198306050822' → 表类型'公安部_出国_境_证件_出入境证件信息'
2025-08-05 20:28:58.690 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_出国（境）证件_003_韩莎莎_522501198306050822' → 表类型'公安部_出国_境_证件_出入境证件信息'
2025-08-05 20:28:58.690 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_出国（境）证件_003_韩莎莎_522501198306050822' → 表类型'公安部_出国_境_证件_出入境证件信息'
2025-08-05 20:28:58.739 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_同住址_003_韩莎莎_522501198306050822.xlsx - 同住址 (行数: 3, 非空值: 53)
2025-08-05 20:28:58.786 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_同住址_003_韩莎莎_522501198306050822.xlsx - 同住址 (行数: 3, 非空值: 53)
2025-08-05 20:28:58.889 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:58.889 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:58.895 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同住址_003_韩莎莎_522501198306050822' → 表类型'公安部_同住址_同住址表'
2025-08-05 20:28:58.895 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同住址_003_韩莎莎_522501198306050822' → 表类型'公安部_同住址_同住址表'
2025-08-05 20:28:58.895 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_同住址_003_韩莎莎_522501198306050822' → 表类型'公安部_同住址_同住址表'
2025-08-05 20:28:58.956 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_同户人_003_韩莎莎_522501198306050822.xlsx - 同户人 (行数: 3, 非空值: 53)
2025-08-05 20:28:58.989 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_同户人_003_韩莎莎_522501198306050822.xlsx - 同户人 (行数: 3, 非空值: 53)
2025-08-05 20:28:59.107 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:59.108 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:59.115 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同户人_003_韩莎莎_522501198306050822' → 表类型'公安部_同户人_同户人表'
2025-08-05 20:28:59.118 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同户人_003_韩莎莎_522501198306050822' → 表类型'公安部_同户人_同户人表'
2025-08-05 20:28:59.119 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_同户人_003_韩莎莎_522501198306050822' → 表类型'公安部_同户人_同户人表'
2025-08-05 20:28:59.161 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_同车违章_003_韩莎莎_522501198306050822.xlsx - 同车违章 (行数: 2, 非空值: 10)
2025-08-05 20:28:59.189 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_同车违章_003_韩莎莎_522501198306050822.xlsx - 同车违章 (行数: 2, 非空值: 10)
2025-08-05 20:28:59.275 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:59.290 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:59.297 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同车违章_003_韩莎莎_522501198306050822' → 表类型'公安部_同车违章_同车违章表'
2025-08-05 20:28:59.298 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同车违章_003_韩莎莎_522501198306050822' → 表类型'公安部_同车违章_同车违章表'
2025-08-05 20:28:59.298 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_同车违章_003_韩莎莎_522501198306050822' → 表类型'公安部_同车违章_同车违章表'
2025-08-05 20:28:59.339 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_户籍人口_003_韩莎莎_522501198306050822.xlsx - 基本人员信息 (行数: 1, 非空值: 17)
2025-08-05 20:28:59.376 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_户籍人口_003_韩莎莎_522501198306050822.xlsx - 基本人员信息 (行数: 1, 非空值: 17)
2025-08-05 20:28:59.465 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:59.465 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:59.465 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_户籍人口_003_韩莎莎_522501198306050822' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:28:59.465 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_户籍人口_003_韩莎莎_522501198306050822' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:28:59.465 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_户籍人口_003_韩莎莎_522501198306050822' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:28:59.529 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_旅馆住宿_003_韩莎莎_522501198306050822.xlsx - 旅馆住宿人员信息 (行数: 10, 非空值: 117)
2025-08-05 20:28:59.579 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_旅馆住宿_003_韩莎莎_522501198306050822.xlsx - 旅馆住宿人员信息 (行数: 10, 非空值: 117)
2025-08-05 20:28:59.670 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:59.670 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:59.685 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_旅馆住宿_003_韩莎莎_522501198306050822' → 表类型'公安部_旅馆住宿_旅馆住宿人员信息表'
2025-08-05 20:28:59.685 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_旅馆住宿_003_韩莎莎_522501198306050822' → 表类型'公安部_旅馆住宿_旅馆住宿人员信息表'
2025-08-05 20:28:59.685 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_旅馆住宿_003_韩莎莎_522501198306050822' → 表类型'公安部_旅馆住宿_旅馆住宿人员信息表'
2025-08-05 20:28:59.733 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_机动车_003_韩莎莎_522501198306050822.xlsx - 机动车信息 (行数: 1, 非空值: 21)
2025-08-05 20:28:59.765 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_机动车_003_韩莎莎_522501198306050822.xlsx - 机动车信息 (行数: 1, 非空值: 21)
2025-08-05 20:28:59.876 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:28:59.876 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:28:59.893 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_机动车_003_韩莎莎_522501198306050822' → 表类型'公安部_机动车_机动车信息'
2025-08-05 20:28:59.893 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_机动车_003_韩莎莎_522501198306050822' → 表类型'公安部_机动车_机动车信息'
2025-08-05 20:28:59.893 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_机动车_003_韩莎莎_522501198306050822' → 表类型'公安部_机动车_机动车信息'
2025-08-05 20:28:59.941 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_驾驶证_003_韩莎莎_522501198306050822.xlsx - 驾驶证信息 (行数: 1, 非空值: 15)
2025-08-05 20:28:59.972 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\公安部_驾驶证_003_韩莎莎_522501198306050822.xlsx - 驾驶证信息 (行数: 1, 非空值: 15)
2025-08-05 20:29:00.067 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:00.067 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:00.067 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_驾驶证_003_韩莎莎_522501198306050822' → 表类型'公安部_驾驶证_驾驶证信息表'
2025-08-05 20:29:00.067 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_驾驶证_003_韩莎莎_522501198306050822' → 表类型'公安部_驾驶证_驾驶证信息表'
2025-08-05 20:29:00.067 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_驾驶证_003_韩莎莎_522501198306050822' → 表类型'公安部_驾驶证_驾驶证信息表'
2025-08-05 20:29:00.130 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\兴业银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财账户信息 (行数: 2, 非空值: 18)
2025-08-05 20:29:00.162 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\兴业银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财信息 (行数: 5, 非空值: 63)
2025-08-05 20:29:00.208 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\兴业银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财账户信息 (行数: 2, 非空值: 18)
2025-08-05 20:29:00.226 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\兴业银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财信息 (行数: 5, 非空值: 63)
2025-08-05 20:29:00.322 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:00.338 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:00.433 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='兴业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息', 工作表='金融理财账户信息'
2025-08-05 20:29:00.449 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财账户信息'
2025-08-05 20:29:00.450 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财账户信息' → 金融理财_金融理财账户信息表
2025-08-05 20:29:00.450 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:00.450 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:00.450 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'兴业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:00.529 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:00.529 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:00.608 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='兴业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财信息', 工作表='金融理财信息'
2025-08-05 20:29:00.608 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财信息'
2025-08-05 20:29:00.608 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财信息' → 金融理财_金融理财信息表
2025-08-05 20:29:00.608 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:29:00.608 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:29:00.608 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'兴业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:29:00.685 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_交易流水_003_韩莎莎_522501198306050822.xlsx - 交易流水 (行数: 10, 非空值: 229)
2025-08-05 20:29:00.735 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_交易流水_003_韩莎莎_522501198306050822.xlsx - 交易流水 (行数: 10, 非空值: 229)
2025-08-05 20:29:00.830 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:00.830 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:00.911 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_交易流水_003_韩莎莎_522501198306050822', 工作表='交易流水'
2025-08-05 20:29:00.911 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='交易流水'
2025-08-05 20:29:00.911 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'交易流水' → 临时账户交易明细表
2025-08-05 20:29:00.911 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:29:00.911 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:29:00.926 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:29:00.985 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 客户基本信息 (行数: 1, 非空值: 15)
2025-08-05 20:29:01.006 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 账号基本信息 (行数: 2, 非空值: 32)
2025-08-05 20:29:01.038 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:01.038 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:01.054 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:01.054 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:01.094 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息 (行数: 2, 非空值: 26)
2025-08-05 20:29:01.095 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx 跳过了 2 个空工作表
2025-08-05 20:29:01.136 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 客户基本信息 (行数: 1, 非空值: 15)
2025-08-05 20:29:01.167 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 账号基本信息 (行数: 2, 非空值: 32)
2025-08-05 20:29:01.192 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:01.192 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:01.213 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:01.213 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:01.251 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息 (行数: 2, 非空值: 26)
2025-08-05 20:29:01.340 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:01.340 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:01.436 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息', 工作表='客户基本信息'
2025-08-05 20:29:01.436 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='客户基本信息'
2025-08-05 20:29:01.436 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'客户基本信息' → 账户信息_客户基本信息表
2025-08-05 20:29:01.436 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:01.436 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:01.436 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:01.515 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:01.515 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:01.609 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息', 工作表='账号基本信息'
2025-08-05 20:29:01.609 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='账号基本信息'
2025-08-05 20:29:01.609 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'账号基本信息' → 开户信息表
2025-08-05 20:29:01.609 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:01.609 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:01.625 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:01.689 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:01.689 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:01.800 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_账户信息_003_韩莎莎_522501198306050822 - 关联子账户信息', 工作表='关联子账户信息'
2025-08-05 20:29:01.800 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='关联子账户信息'
2025-08-05 20:29:01.800 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'关联子账户信息' → 账户信息_关联子账户信息表
2025-08-05 20:29:01.816 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_003_韩莎莎_522501198306050822 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:29:01.816 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_账户信息_003_韩莎莎_522501198306050822 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:29:01.816 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_账户信息_003_韩莎莎_522501198306050822 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:29:01.863 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财账户信息 (行数: 2, 非空值: 36)
2025-08-05 20:29:01.895 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财信息 (行数: 2, 非空值: 26)
2025-08-05 20:29:01.927 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财账户信息 (行数: 2, 非空值: 36)
2025-08-05 20:29:01.959 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\农业银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财信息 (行数: 2, 非空值: 26)
2025-08-05 20:29:02.070 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:02.070 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:02.170 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息', 工作表='金融理财账户信息'
2025-08-05 20:29:02.170 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财账户信息'
2025-08-05 20:29:02.180 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财账户信息' → 金融理财_金融理财账户信息表
2025-08-05 20:29:02.182 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:02.184 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:02.185 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:02.281 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:02.281 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:02.388 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财信息', 工作表='金融理财信息'
2025-08-05 20:29:02.388 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财信息'
2025-08-05 20:29:02.388 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财信息' → 金融理财_金融理财信息表
2025-08-05 20:29:02.388 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:29:02.388 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:29:02.388 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:29:02.420 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\国家税务总局_纳税人登记信息_003_韩莎莎_522501198306050822.xlsx - 登记信息 (行数: 1, 非空值: 6)
2025-08-05 20:29:02.452 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\国家税务总局_纳税人登记信息_003_韩莎莎_522501198306050822.xlsx - 登记信息 (行数: 1, 非空值: 6)
2025-08-05 20:29:02.567 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:02.567 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:02.579 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'国家税务总局_纳税人登记信息_003_韩莎莎_522501198306050822' → 表类型'国家税务总局_纳税人登记信息_登记信息表'
2025-08-05 20:29:02.584 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'国家税务总局_纳税人登记信息_003_韩莎莎_522501198306050822' → 表类型'国家税务总局_纳税人登记信息_登记信息表'
2025-08-05 20:29:02.589 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'国家税务总局_纳税人登记信息_003_韩莎莎_522501198306050822' → 表类型'国家税务总局_纳税人登记信息_登记信息表'
2025-08-05 20:29:02.641 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\国家税务总局_纳税信息_003_韩莎莎_522501198306050822.xlsx - 税务缴纳信息 (行数: 10, 非空值: 80)
2025-08-05 20:29:02.677 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\国家税务总局_纳税信息_003_韩莎莎_522501198306050822.xlsx - 税务缴纳信息 (行数: 10, 非空值: 80)
2025-08-05 20:29:02.768 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:02.768 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:02.768 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'国家税务总局_纳税信息_003_韩莎莎_522501198306050822' → 表类型'国家税务总局_纳税信息_税务缴纳信息表'
2025-08-05 20:29:02.768 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'国家税务总局_纳税信息_003_韩莎莎_522501198306050822' → 表类型'国家税务总局_纳税信息_税务缴纳信息表'
2025-08-05 20:29:02.768 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'国家税务总局_纳税信息_003_韩莎莎_522501198306050822' → 表类型'国家税务总局_纳税信息_税务缴纳信息表'
2025-08-05 20:29:02.846 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_交易流水_003_韩莎莎_522501198306050822.xlsx - 交易流水 (行数: 10, 非空值: 180)
2025-08-05 20:29:02.894 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_交易流水_003_韩莎莎_522501198306050822.xlsx - 交易流水 (行数: 10, 非空值: 180)
2025-08-05 20:29:02.983 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:02.983 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:03.086 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='工商银行_交易流水_003_韩莎莎_522501198306050822', 工作表='交易流水'
2025-08-05 20:29:03.086 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='交易流水'
2025-08-05 20:29:03.086 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'交易流水' → 临时账户交易明细表
2025-08-05 20:29:03.086 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:29:03.086 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:29:03.086 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'工商银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:29:03.149 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 客户基本信息 (行数: 1, 非空值: 14)
2025-08-05 20:29:03.185 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 账号基本信息 (行数: 2, 非空值: 34)
2025-08-05 20:29:03.213 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:03.213 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:03.229 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:03.229 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:03.267 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:29:03.267 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:29:03.267 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx 跳过了 3 个空工作表
2025-08-05 20:29:03.316 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 客户基本信息 (行数: 1, 非空值: 14)
2025-08-05 20:29:03.340 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 账号基本信息 (行数: 2, 非空值: 34)
2025-08-05 20:29:03.365 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:03.368 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:03.389 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:03.389 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:03.422 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:29:03.423 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\工商银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:29:03.601 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:03.601 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:03.714 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='工商银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息', 工作表='客户基本信息'
2025-08-05 20:29:03.714 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='客户基本信息'
2025-08-05 20:29:03.714 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'客户基本信息' → 账户信息_客户基本信息表
2025-08-05 20:29:03.714 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:03.714 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:03.714 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'工商银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:03.806 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:03.806 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:03.886 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='工商银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息', 工作表='账号基本信息'
2025-08-05 20:29:03.886 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='账号基本信息'
2025-08-05 20:29:03.902 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'账号基本信息' → 开户信息表
2025-08-05 20:29:03.902 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:03.902 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'工商银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:03.902 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'工商银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:03.997 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 企业基本信息 (行数: 1, 非空值: 29)
2025-08-05 20:29:04.028 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 企业公示_许可信息
2025-08-05 20:29:04.028 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 企业公示_许可信息
2025-08-05 20:29:04.077 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 自然人出资信息 (行数: 2, 非空值: 40)
2025-08-05 20:29:04.109 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 非自然人出资信息
2025-08-05 20:29:04.109 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 非自然人出资信息
2025-08-05 20:29:04.156 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 主要人员 (行数: 4, 非空值: 69)
2025-08-05 20:29:04.187 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 变更备案信息
2025-08-05 20:29:04.187 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 变更备案信息
2025-08-05 20:29:04.223 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 财务负责人信息
2025-08-05 20:29:04.223 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 财务负责人信息
2025-08-05 20:29:04.250 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 联络员信息
2025-08-05 20:29:04.250 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 联络员信息
2025-08-05 20:29:04.281 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 分支机构备案信息
2025-08-05 20:29:04.281 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 分支机构备案信息
2025-08-05 20:29:04.313 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 清算基本信息
2025-08-05 20:29:04.313 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 清算基本信息
2025-08-05 20:29:04.344 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 清算成员信息
2025-08-05 20:29:04.344 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 清算成员信息
2025-08-05 20:29:04.380 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 吊销信息
2025-08-05 20:29:04.380 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 吊销信息
2025-08-05 20:29:04.407 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 注销信息 (行数: 1, 非空值: 16)
2025-08-05 20:29:04.454 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 外资补充信息
2025-08-05 20:29:04.454 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 外资补充信息
2025-08-05 20:29:04.485 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 内资补充信息 (行数: 1, 非空值: 13)
2025-08-05 20:29:04.517 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 农专补充信息
2025-08-05 20:29:04.517 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 农专补充信息
2025-08-05 20:29:04.517 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx 跳过了 11 个空工作表
2025-08-05 20:29:04.580 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 企业基本信息 (行数: 1, 非空值: 29)
2025-08-05 20:29:04.612 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 企业公示_许可信息
2025-08-05 20:29:04.612 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 企业公示_许可信息
2025-08-05 20:29:04.660 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 自然人出资信息 (行数: 2, 非空值: 40)
2025-08-05 20:29:04.692 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 非自然人出资信息
2025-08-05 20:29:04.692 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 非自然人出资信息
2025-08-05 20:29:04.726 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 主要人员 (行数: 4, 非空值: 69)
2025-08-05 20:29:04.900 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 变更备案信息
2025-08-05 20:29:04.900 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 变更备案信息
2025-08-05 20:29:04.933 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 财务负责人信息
2025-08-05 20:29:04.935 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 财务负责人信息
2025-08-05 20:29:04.963 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 联络员信息
2025-08-05 20:29:04.963 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 联络员信息
2025-08-05 20:29:04.995 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 分支机构备案信息
2025-08-05 20:29:04.995 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 分支机构备案信息
2025-08-05 20:29:05.027 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 清算基本信息
2025-08-05 20:29:05.027 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 清算基本信息
2025-08-05 20:29:05.061 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 清算成员信息
2025-08-05 20:29:05.061 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 清算成员信息
2025-08-05 20:29:05.091 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 吊销信息
2025-08-05 20:29:05.091 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 吊销信息
2025-08-05 20:29:05.123 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 注销信息 (行数: 1, 非空值: 16)
2025-08-05 20:29:05.171 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 外资补充信息
2025-08-05 20:29:05.172 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 外资补充信息
2025-08-05 20:29:05.206 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 内资补充信息 (行数: 1, 非空值: 13)
2025-08-05 20:29:05.235 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 农专补充信息
2025-08-05 20:29:05.237 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\市场监管总局_企业登记_003_韩莎莎_522501198306050822.xlsx - 农专补充信息
2025-08-05 20:29:05.314 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:05.330 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:05.330 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 企业基本信息' → 表类型'市监_企业登记_企业基本信息表'
2025-08-05 20:29:05.330 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 企业基本信息' → 表类型'市监_企业登记_企业基本信息表'
2025-08-05 20:29:05.330 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 企业基本信息' → 表类型'市监_企业登记_企业基本信息表'
2025-08-05 20:29:05.425 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:05.425 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:05.425 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 自然人出资信息' → 表类型'市监_企业登记_企业公示_自然人出资信息表'
2025-08-05 20:29:05.425 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 自然人出资信息' → 表类型'市监_企业登记_企业公示_自然人出资信息表'
2025-08-05 20:29:05.425 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 自然人出资信息' → 表类型'市监_企业登记_企业公示_自然人出资信息表'
2025-08-05 20:29:05.521 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:05.521 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:05.534 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 主要人员' → 表类型'市监_企业登记_企业公示_主要人员表'
2025-08-05 20:29:05.534 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 主要人员' → 表类型'市监_企业登记_企业公示_主要人员表'
2025-08-05 20:29:05.537 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 主要人员' → 表类型'市监_企业登记_企业公示_主要人员表'
2025-08-05 20:29:05.616 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:05.616 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:05.632 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 注销信息' → 表类型'市监_企业登记_企业公示_注销信息表'
2025-08-05 20:29:05.632 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 注销信息' → 表类型'市监_企业登记_企业公示_注销信息表'
2025-08-05 20:29:05.632 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 注销信息' → 表类型'市监_企业登记_企业公示_注销信息表'
2025-08-05 20:29:05.712 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:05.712 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:05.728 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 内资补充信息' → 表类型'市监_企业登记_企业公示_内资补充信息表'
2025-08-05 20:29:05.728 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 内资补充信息' → 表类型'市监_企业登记_企业公示_内资补充信息表'
2025-08-05 20:29:05.728 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'市场监管总局_企业登记_003_韩莎莎_522501198306050822 - 内资补充信息' → 表类型'市监_企业登记_企业公示_内资补充信息表'
2025-08-05 20:29:05.792 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_交易流水_003_韩莎莎_522501198306050822.xlsx - 交易流水 (行数: 10, 非空值: 188)
2025-08-05 20:29:05.856 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_交易流水_003_韩莎莎_522501198306050822.xlsx - 交易流水 (行数: 10, 非空值: 188)
2025-08-05 20:29:05.935 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:05.935 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:06.030 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='建设银行_交易流水_003_韩莎莎_522501198306050822', 工作表='交易流水'
2025-08-05 20:29:06.030 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='交易流水'
2025-08-05 20:29:06.030 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'交易流水' → 临时账户交易明细表
2025-08-05 20:29:06.030 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:29:06.030 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:29:06.030 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'建设银行_交易流水_003_韩莎莎_522501198306050822' → 表类型'临时账户交易明细表'
2025-08-05 20:29:06.094 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 客户基本信息 (行数: 1, 非空值: 13)
2025-08-05 20:29:06.126 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 账号基本信息 (行数: 3, 非空值: 51)
2025-08-05 20:29:06.143 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:06.143 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:06.175 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:06.175 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:06.205 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:29:06.206 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:29:06.206 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx 跳过了 3 个空工作表
2025-08-05 20:29:06.236 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 客户基本信息 (行数: 1, 非空值: 13)
2025-08-05 20:29:06.269 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 账号基本信息 (行数: 3, 非空值: 51)
2025-08-05 20:29:06.285 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:06.285 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 强制措施信息
2025-08-05 20:29:06.317 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:06.317 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 共有权、优先权信息
2025-08-05 20:29:06.333 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:29:06.333 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_账户信息_003_韩莎莎_522501198306050822.xlsx - 关联子账户信息
2025-08-05 20:29:06.444 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:06.444 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:06.539 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='建设银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息', 工作表='客户基本信息'
2025-08-05 20:29:06.539 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='客户基本信息'
2025-08-05 20:29:06.539 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'客户基本信息' → 账户信息_客户基本信息表
2025-08-05 20:29:06.555 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:06.555 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:06.555 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'建设银行_账户信息_003_韩莎莎_522501198306050822 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:06.668 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:06.668 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:06.776 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='建设银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息', 工作表='账号基本信息'
2025-08-05 20:29:06.776 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='账号基本信息'
2025-08-05 20:29:06.776 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'账号基本信息' → 开户信息表
2025-08-05 20:29:06.776 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:06.776 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:06.776 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'建设银行_账户信息_003_韩莎莎_522501198306050822 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:06.824 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财账户信息 (行数: 3, 非空值: 54)
2025-08-05 20:29:06.840 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财信息
2025-08-05 20:29:06.856 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财信息
2025-08-05 20:29:06.856 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 建设银行_金融理财_003_韩莎莎_522501198306050822.xlsx 跳过了 1 个空工作表
2025-08-05 20:29:06.887 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财账户信息 (行数: 3, 非空值: 54)
2025-08-05 20:29:06.919 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财信息
2025-08-05 20:29:06.919 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\建设银行_金融理财_003_韩莎莎_522501198306050822.xlsx - 金融理财信息
2025-08-05 20:29:07.031 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:07.031 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:07.127 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='建设银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息', 工作表='金融理财账户信息'
2025-08-05 20:29:07.127 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财账户信息'
2025-08-05 20:29:07.127 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财账户信息' → 金融理财_金融理财账户信息表
2025-08-05 20:29:07.127 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:07.127 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'建设银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:07.127 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'建设银行_金融理财_003_韩莎莎_522501198306050822 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:07.180 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\自然资源部不动产全国库查询_不动产全国总库_003_韩莎莎_522501198306050822.xlsx - 预告登记 (行数: 3, 非空值: 57)
2025-08-05 20:29:07.208 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\自然资源部不动产全国库查询_不动产全国总库_003_韩莎莎_522501198306050822.xlsx - 预告登记 (行数: 3, 非空值: 57)
2025-08-05 20:29:07.286 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:07.286 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:07.302 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'自然资源部不动产全国库查询_不动产全国总库_003_韩莎莎_522501198306050822' → 表类型'不动产查询_不动产全国总库_预告登记表'
2025-08-05 20:29:07.302 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'自然资源部不动产全国库查询_不动产全国总库_003_韩莎莎_522501198306050822' → 表类型'不动产查询_不动产全国总库_预告登记表'
2025-08-05 20:29:07.302 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'自然资源部不动产全国库查询_不动产全国总库_003_韩莎莎_522501198306050822' → 表类型'不动产查询_不动产全国总库_预告登记表'
2025-08-05 20:29:07.366 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\银保信_保险产品_003_韩莎莎_522501198306050822.xlsx - 保险保单信息 (行数: 10, 非空值: 165)
2025-08-05 20:29:07.398 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\银保信_保险产品_003_韩莎莎_522501198306050822.xlsx - 保险人员信息 (行数: 10, 非空值: 77)
2025-08-05 20:29:07.430 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\银保信_保险产品_003_韩莎莎_522501198306050822.xlsx - 航空延误保险
2025-08-05 20:29:07.430 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\银保信_保险产品_003_韩莎莎_522501198306050822.xlsx - 航空延误保险
2025-08-05 20:29:07.430 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 银保信_保险产品_003_韩莎莎_522501198306050822.xlsx 跳过了 1 个空工作表
2025-08-05 20:29:07.484 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\银保信_保险产品_003_韩莎莎_522501198306050822.xlsx - 保险保单信息 (行数: 10, 非空值: 165)
2025-08-05 20:29:07.510 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\银保信_保险产品_003_韩莎莎_522501198306050822.xlsx - 保险人员信息 (行数: 10, 非空值: 77)
2025-08-05 20:29:07.542 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\银保信_保险产品_003_韩莎莎_522501198306050822.xlsx - 航空延误保险
2025-08-05 20:29:07.542 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\003_韩莎莎_522501198306050822\银保信_保险产品_003_韩莎莎_522501198306050822.xlsx - 航空延误保险
2025-08-05 20:29:07.636 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:07.636 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:07.636 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_003_韩莎莎_522501198306050822 - 保险保单信息' → 表类型'银保信_保险产品_保险保单信息表'
2025-08-05 20:29:07.636 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_003_韩莎莎_522501198306050822 - 保险保单信息' → 表类型'银保信_保险产品_保险保单信息表'
2025-08-05 20:29:07.636 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'银保信_保险产品_003_韩莎莎_522501198306050822 - 保险保单信息' → 表类型'银保信_保险产品_保险保单信息表'
2025-08-05 20:29:07.715 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:07.715 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:07.731 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_003_韩莎莎_522501198306050822 - 保险人员信息' → 表类型'银保信_保险产品_保险人员信息表'
2025-08-05 20:29:07.731 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'银保信_保险产品_003_韩莎莎_522501198306050822 - 保险人员信息' → 表类型'银保信_保险产品_保险人员信息表'
2025-08-05 20:29:07.731 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'银保信_保险产品_003_韩莎莎_522501198306050822 - 保险人员信息' → 表类型'银保信_保险产品_保险人员信息表'
2025-08-05 20:29:07.779 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国人民银行_银行账户_004_黄佳琴_522728197611250026.xlsx - 开户信息 (行数: 10, 非空值: 142)
2025-08-05 20:29:07.828 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国人民银行_银行账户_004_黄佳琴_522728197611250026.xlsx - 开户信息 (行数: 10, 非空值: 142)
2025-08-05 20:29:07.923 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:07.923 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:08.018 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='中国人民银行_银行账户_004_黄佳琴_522728197611250026', 工作表='开户信息'
2025-08-05 20:29:08.018 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='开户信息'
2025-08-05 20:29:08.034 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'人民银行' → 开户信息表
2025-08-05 20:29:08.036 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国人民银行_银行账户_004_黄佳琴_522728197611250026' → 表类型'开户信息表'
2025-08-05 20:29:08.036 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国人民银行_银行账户_004_黄佳琴_522728197611250026' → 表类型'开户信息表'
2025-08-05 20:29:08.036 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国人民银行_银行账户_004_黄佳琴_522728197611250026' → 表类型'开户信息表'
2025-08-05 20:29:08.082 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_同订单同行人_004_黄佳琴_522728197611250026.xlsx - 同行人员信息 (行数: 5, 非空值: 39)
2025-08-05 20:29:08.117 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_同订单同行人_004_黄佳琴_522728197611250026.xlsx - 同行人员客票信息 (行数: 10, 非空值: 125)
2025-08-05 20:29:08.145 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_同订单同行人_004_黄佳琴_522728197611250026.xlsx - 同行人员信息 (行数: 5, 非空值: 39)
2025-08-05 20:29:08.184 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_同订单同行人_004_黄佳琴_522728197611250026.xlsx - 同行人员客票信息 (行数: 10, 非空值: 125)
2025-08-05 20:29:08.260 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:08.260 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:08.272 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_004_黄佳琴_522728197611250026 - 同行人员信息' → 表类型'中国铁路总公司_同订单同行人_同行人员信息表'
2025-08-05 20:29:08.272 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_004_黄佳琴_522728197611250026 - 同行人员信息' → 表类型'中国铁路总公司_同订单同行人_同行人员信息表'
2025-08-05 20:29:08.272 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_同订单同行人_004_黄佳琴_522728197611250026 - 同行人员信息' → 表类型'中国铁路总公司_同订单同行人_同行人员信息表'
2025-08-05 20:29:08.373 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:08.373 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:08.381 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_004_黄佳琴_522728197611250026 - 同行人员客票信息' → 表类型'中国铁路总公司_同订单同行人_同行人员客票'
2025-08-05 20:29:08.381 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_同订单同行人_004_黄佳琴_522728197611250026 - 同行人员客票信息' → 表类型'中国铁路总公司_同订单同行人_同行人员客票'
2025-08-05 20:29:08.381 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_同订单同行人_004_黄佳琴_522728197611250026 - 同行人员客票信息' → 表类型'中国铁路总公司_同订单同行人_同行人员客票'
2025-08-05 20:29:08.424 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_用户注册_004_黄佳琴_522728197611250026.xlsx - 互联网注册信息 (行数: 1, 非空值: 9)
2025-08-05 20:29:08.430 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_用户注册_004_黄佳琴_522728197611250026.xlsx - 常用联系人信息 (行数: 1, 非空值: 8)
2025-08-05 20:29:08.462 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_用户注册_004_黄佳琴_522728197611250026.xlsx - 互联网注册信息 (行数: 1, 非空值: 9)
2025-08-05 20:29:08.479 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_用户注册_004_黄佳琴_522728197611250026.xlsx - 常用联系人信息 (行数: 1, 非空值: 8)
2025-08-05 20:29:08.609 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:08.609 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:08.618 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_用户注册_004_黄佳琴_522728197611250026 - 互联网注册信息' → 表类型'中国铁路总公司_用户注册_互联网注册信息表'
2025-08-05 20:29:08.619 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_用户注册_004_黄佳琴_522728197611250026 - 互联网注册信息' → 表类型'中国铁路总公司_用户注册_互联网注册信息表'
2025-08-05 20:29:08.619 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_用户注册_004_黄佳琴_522728197611250026 - 互联网注册信息' → 表类型'中国铁路总公司_用户注册_互联网注册信息表'
2025-08-05 20:29:08.718 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:08.718 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:08.718 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_用户注册_004_黄佳琴_522728197611250026 - 常用联系人信息' → 表类型'中国铁路总公司_用户注册_常用联系人信息表'
2025-08-05 20:29:08.718 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_用户注册_004_黄佳琴_522728197611250026 - 常用联系人信息' → 表类型'中国铁路总公司_用户注册_常用联系人信息表'
2025-08-05 20:29:08.718 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_用户注册_004_黄佳琴_522728197611250026 - 常用联系人信息' → 表类型'中国铁路总公司_用户注册_常用联系人信息表'
2025-08-05 20:29:08.806 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_铁路客票_004_黄佳琴_522728197611250026.xlsx - 票面信息 (行数: 10, 非空值: 162)
2025-08-05 20:29:08.829 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_铁路客票_004_黄佳琴_522728197611250026.xlsx - 交易信息 (行数: 10, 非空值: 60)
2025-08-05 20:29:08.877 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_铁路客票_004_黄佳琴_522728197611250026.xlsx - 票面信息 (行数: 10, 非空值: 162)
2025-08-05 20:29:08.909 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\中国铁路总公司_铁路客票_004_黄佳琴_522728197611250026.xlsx - 交易信息 (行数: 10, 非空值: 60)
2025-08-05 20:29:09.020 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:09.020 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:09.020 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_004_黄佳琴_522728197611250026 - 票面信息' → 表类型'中国铁路总公司_铁路客票_票面信息表'
2025-08-05 20:29:09.020 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_004_黄佳琴_522728197611250026 - 票面信息' → 表类型'中国铁路总公司_铁路客票_票面信息表'
2025-08-05 20:29:09.020 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_铁路客票_004_黄佳琴_522728197611250026 - 票面信息' → 表类型'中国铁路总公司_铁路客票_票面信息表'
2025-08-05 20:29:09.115 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:09.115 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:09.115 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_004_黄佳琴_522728197611250026 - 交易信息' → 表类型'中国铁路总公司_铁路客票_交易信息表'
2025-08-05 20:29:09.115 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'中国铁路总公司_铁路客票_004_黄佳琴_522728197611250026 - 交易信息' → 表类型'中国铁路总公司_铁路客票_交易信息表'
2025-08-05 20:29:09.130 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'中国铁路总公司_铁路客票_004_黄佳琴_522728197611250026 - 交易信息' → 表类型'中国铁路总公司_铁路客票_交易信息表'
2025-08-05 20:29:09.194 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_交易流水_004_黄佳琴_522728197611250026.xlsx - 交易流水 (行数: 10, 非空值: 250)
2025-08-05 20:29:09.241 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_交易流水_004_黄佳琴_522728197611250026.xlsx - 交易流水 (行数: 10, 非空值: 250)
2025-08-05 20:29:09.330 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:09.330 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:09.414 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='交通银行_交易流水_004_黄佳琴_522728197611250026', 工作表='交易流水'
2025-08-05 20:29:09.414 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='交易流水'
2025-08-05 20:29:09.414 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'交易流水' → 临时账户交易明细表
2025-08-05 20:29:09.414 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'交通银行_交易流水_004_黄佳琴_522728197611250026' → 表类型'临时账户交易明细表'
2025-08-05 20:29:09.414 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'交通银行_交易流水_004_黄佳琴_522728197611250026' → 表类型'临时账户交易明细表'
2025-08-05 20:29:09.414 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'交通银行_交易流水_004_黄佳琴_522728197611250026' → 表类型'临时账户交易明细表'
2025-08-05 20:29:09.477 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 客户基本信息 (行数: 1, 非空值: 13)
2025-08-05 20:29:09.493 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 账号基本信息 (行数: 1, 非空值: 16)
2025-08-05 20:29:09.525 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 强制措施信息
2025-08-05 20:29:09.525 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 强制措施信息
2025-08-05 20:29:09.541 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 共有权、优先权信息
2025-08-05 20:29:09.541 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 共有权、优先权信息
2025-08-05 20:29:09.574 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 关联子账户信息 (行数: 1, 非空值: 13)
2025-08-05 20:29:09.574 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx 跳过了 2 个空工作表
2025-08-05 20:29:09.606 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 客户基本信息 (行数: 1, 非空值: 13)
2025-08-05 20:29:09.638 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 账号基本信息 (行数: 1, 非空值: 16)
2025-08-05 20:29:09.654 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 强制措施信息
2025-08-05 20:29:09.654 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 强制措施信息
2025-08-05 20:29:09.685 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 共有权、优先权信息
2025-08-05 20:29:09.685 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 共有权、优先权信息
2025-08-05 20:29:09.717 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_账户信息_004_黄佳琴_522728197611250026.xlsx - 关联子账户信息 (行数: 1, 非空值: 13)
2025-08-05 20:29:09.808 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:09.808 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:09.891 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='交通银行_账户信息_004_黄佳琴_522728197611250026 - 客户基本信息', 工作表='客户基本信息'
2025-08-05 20:29:09.891 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='客户基本信息'
2025-08-05 20:29:09.891 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'客户基本信息' → 账户信息_客户基本信息表
2025-08-05 20:29:09.891 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'交通银行_账户信息_004_黄佳琴_522728197611250026 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:09.891 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'交通银行_账户信息_004_黄佳琴_522728197611250026 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:09.891 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'交通银行_账户信息_004_黄佳琴_522728197611250026 - 客户基本信息' → 表类型'账户信息_客户基本信息表'
2025-08-05 20:29:09.970 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:09.970 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:10.063 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='交通银行_账户信息_004_黄佳琴_522728197611250026 - 账号基本信息', 工作表='账号基本信息'
2025-08-05 20:29:10.063 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='账号基本信息'
2025-08-05 20:29:10.064 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'账号基本信息' → 开户信息表
2025-08-05 20:29:10.066 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'交通银行_账户信息_004_黄佳琴_522728197611250026 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:10.066 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'交通银行_账户信息_004_黄佳琴_522728197611250026 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:10.066 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'交通银行_账户信息_004_黄佳琴_522728197611250026 - 账号基本信息' → 表类型'开户信息表'
2025-08-05 20:29:10.130 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:10.130 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:10.193 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='交通银行_账户信息_004_黄佳琴_522728197611250026 - 关联子账户信息', 工作表='关联子账户信息'
2025-08-05 20:29:10.193 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='关联子账户信息'
2025-08-05 20:29:10.194 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'账户信息' + 工作表'关联子账户信息' → 账户信息_关联子账户信息表
2025-08-05 20:29:10.194 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'交通银行_账户信息_004_黄佳琴_522728197611250026 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:29:10.195 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'交通银行_账户信息_004_黄佳琴_522728197611250026 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:29:10.195 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'交通银行_账户信息_004_黄佳琴_522728197611250026 - 关联子账户信息' → 表类型'账户信息_关联子账户信息表'
2025-08-05 20:29:10.206 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_金融理财_004_黄佳琴_522728197611250026.xlsx - 金融理财账户信息 (行数: 1, 非空值: 19)
2025-08-05 20:29:10.212 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_金融理财_004_黄佳琴_522728197611250026.xlsx - 金融理财信息
2025-08-05 20:29:10.212 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_金融理财_004_黄佳琴_522728197611250026.xlsx - 金融理财信息
2025-08-05 20:29:10.212 - INFO - [MainThread:26196] - import_data.py:11977 - add_files_to_tree() - 📋 文件 交通银行_金融理财_004_黄佳琴_522728197611250026.xlsx 跳过了 1 个空工作表
2025-08-05 20:29:10.220 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_金融理财_004_黄佳琴_522728197611250026.xlsx - 金融理财账户信息 (行数: 1, 非空值: 19)
2025-08-05 20:29:10.229 - INFO - [MainThread:26196] - import_data.py:8969 - is_worksheet_empty() - 📋 工作表无数据行: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_金融理财_004_黄佳琴_522728197611250026.xlsx - 金融理财信息
2025-08-05 20:29:10.229 - INFO - [MainThread:26196] - import_data.py:9018 - get_file_worksheets_fast() - ⏭️ 跳过空工作表: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\交通银行_金融理财_004_黄佳琴_522728197611250026.xlsx - 金融理财信息
2025-08-05 20:29:10.294 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:10.295 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:10.358 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='交通银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财账户信息', 工作表='金融理财账户信息'
2025-08-05 20:29:10.358 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财账户信息'
2025-08-05 20:29:10.359 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财账户信息' → 金融理财_金融理财账户信息表
2025-08-05 20:29:10.359 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'交通银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:10.359 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'交通银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:10.359 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'交通银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:10.370 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_交通违法_004_黄佳琴_522728197611250026.xlsx - 机动车违章信息 (行数: 6, 非空值: 54)
2025-08-05 20:29:10.377 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_交通违法_004_黄佳琴_522728197611250026.xlsx - 机动车违章信息 (行数: 6, 非空值: 54)
2025-08-05 20:29:10.441 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:10.442 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:10.444 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_交通违法_004_黄佳琴_522728197611250026' → 表类型'公安部_交通违法_机动车违章信息表'
2025-08-05 20:29:10.444 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_交通违法_004_黄佳琴_522728197611250026' → 表类型'公安部_交通违法_机动车违章信息表'
2025-08-05 20:29:10.444 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_交通违法_004_黄佳琴_522728197611250026' → 表类型'公安部_交通违法_机动车违章信息表'
2025-08-05 20:29:10.455 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_出国（境）证件_004_黄佳琴_522728197611250026.xlsx - 出入境证件信息 (行数: 3, 非空值: 44)
2025-08-05 20:29:10.462 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_出国（境）证件_004_黄佳琴_522728197611250026.xlsx - 出入境证件信息 (行数: 3, 非空值: 44)
2025-08-05 20:29:10.522 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:10.523 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:10.524 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_出国（境）证件_004_黄佳琴_522728197611250026' → 表类型'公安部_出国_境_证件_出入境证件信息'
2025-08-05 20:29:10.524 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_出国（境）证件_004_黄佳琴_522728197611250026' → 表类型'公安部_出国_境_证件_出入境证件信息'
2025-08-05 20:29:10.525 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_出国（境）证件_004_黄佳琴_522728197611250026' → 表类型'公安部_出国_境_证件_出入境证件信息'
2025-08-05 20:29:10.539 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_同住址_004_黄佳琴_522728197611250026.xlsx - 同住址 (行数: 3, 非空值: 54)
2025-08-05 20:29:10.550 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_同住址_004_黄佳琴_522728197611250026.xlsx - 同住址 (行数: 3, 非空值: 54)
2025-08-05 20:29:10.619 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:10.619 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:10.620 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同住址_004_黄佳琴_522728197611250026' → 表类型'公安部_同住址_同住址表'
2025-08-05 20:29:10.621 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同住址_004_黄佳琴_522728197611250026' → 表类型'公安部_同住址_同住址表'
2025-08-05 20:29:10.621 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_同住址_004_黄佳琴_522728197611250026' → 表类型'公安部_同住址_同住址表'
2025-08-05 20:29:10.641 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_同户人_004_黄佳琴_522728197611250026.xlsx - 同户人 (行数: 3, 非空值: 54)
2025-08-05 20:29:10.649 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_同户人_004_黄佳琴_522728197611250026.xlsx - 同户人 (行数: 3, 非空值: 54)
2025-08-05 20:29:10.721 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:10.721 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:10.723 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同户人_004_黄佳琴_522728197611250026' → 表类型'公安部_同户人_同户人表'
2025-08-05 20:29:10.723 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同户人_004_黄佳琴_522728197611250026' → 表类型'公安部_同户人_同户人表'
2025-08-05 20:29:10.723 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_同户人_004_黄佳琴_522728197611250026' → 表类型'公安部_同户人_同户人表'
2025-08-05 20:29:10.738 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_同车违章_004_黄佳琴_522728197611250026.xlsx - 同车违章 (行数: 5, 非空值: 25)
2025-08-05 20:29:10.746 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_同车违章_004_黄佳琴_522728197611250026.xlsx - 同车违章 (行数: 5, 非空值: 25)
2025-08-05 20:29:10.930 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:10.931 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:10.932 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同车违章_004_黄佳琴_522728197611250026' → 表类型'公安部_同车违章_同车违章表'
2025-08-05 20:29:10.933 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_同车违章_004_黄佳琴_522728197611250026' → 表类型'公安部_同车违章_同车违章表'
2025-08-05 20:29:10.933 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_同车违章_004_黄佳琴_522728197611250026' → 表类型'公安部_同车违章_同车违章表'
2025-08-05 20:29:10.944 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_户籍人口_004_黄佳琴_522728197611250026.xlsx - 基本人员信息 (行数: 1, 非空值: 17)
2025-08-05 20:29:10.951 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_户籍人口_004_黄佳琴_522728197611250026.xlsx - 基本人员信息 (行数: 1, 非空值: 17)
2025-08-05 20:29:11.018 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:11.018 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:11.020 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_户籍人口_004_黄佳琴_522728197611250026' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:29:11.020 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_户籍人口_004_黄佳琴_522728197611250026' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:29:11.020 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_户籍人口_004_黄佳琴_522728197611250026' → 表类型'公安部_户籍人口_基本人员信息表'
2025-08-05 20:29:11.033 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_旅馆住宿_004_黄佳琴_522728197611250026.xlsx - 旅馆住宿人员信息 (行数: 10, 非空值: 112)
2025-08-05 20:29:11.043 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_旅馆住宿_004_黄佳琴_522728197611250026.xlsx - 旅馆住宿人员信息 (行数: 10, 非空值: 112)
2025-08-05 20:29:11.105 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:11.106 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:11.107 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_旅馆住宿_004_黄佳琴_522728197611250026' → 表类型'公安部_旅馆住宿_旅馆住宿人员信息表'
2025-08-05 20:29:11.107 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_旅馆住宿_004_黄佳琴_522728197611250026' → 表类型'公安部_旅馆住宿_旅馆住宿人员信息表'
2025-08-05 20:29:11.108 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_旅馆住宿_004_黄佳琴_522728197611250026' → 表类型'公安部_旅馆住宿_旅馆住宿人员信息表'
2025-08-05 20:29:11.117 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_机动车_004_黄佳琴_522728197611250026.xlsx - 机动车信息 (行数: 1, 非空值: 21)
2025-08-05 20:29:11.125 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_机动车_004_黄佳琴_522728197611250026.xlsx - 机动车信息 (行数: 1, 非空值: 21)
2025-08-05 20:29:11.186 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:11.186 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:11.188 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_机动车_004_黄佳琴_522728197611250026' → 表类型'公安部_机动车_机动车信息'
2025-08-05 20:29:11.188 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_机动车_004_黄佳琴_522728197611250026' → 表类型'公安部_机动车_机动车信息'
2025-08-05 20:29:11.188 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_机动车_004_黄佳琴_522728197611250026' → 表类型'公安部_机动车_机动车信息'
2025-08-05 20:29:11.198 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_驾驶证_004_黄佳琴_522728197611250026.xlsx - 驾驶证信息 (行数: 1, 非空值: 16)
2025-08-05 20:29:11.204 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\公安部_驾驶证_004_黄佳琴_522728197611250026.xlsx - 驾驶证信息 (行数: 1, 非空值: 16)
2025-08-05 20:29:11.265 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:11.265 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:11.267 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_驾驶证_004_黄佳琴_522728197611250026' → 表类型'公安部_驾驶证_驾驶证信息表'
2025-08-05 20:29:11.267 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'公安部_驾驶证_004_黄佳琴_522728197611250026' → 表类型'公安部_驾驶证_驾驶证信息表'
2025-08-05 20:29:11.267 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'公安部_驾驶证_004_黄佳琴_522728197611250026' → 表类型'公安部_驾驶证_驾驶证信息表'
2025-08-05 20:29:11.281 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\兴业银行_金融理财_004_黄佳琴_522728197611250026.xlsx - 金融理财账户信息 (行数: 2, 非空值: 18)
2025-08-05 20:29:11.288 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\兴业银行_金融理财_004_黄佳琴_522728197611250026.xlsx - 金融理财信息 (行数: 5, 非空值: 63)
2025-08-05 20:29:11.321 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\兴业银行_金融理财_004_黄佳琴_522728197611250026.xlsx - 金融理财账户信息 (行数: 2, 非空值: 18)
2025-08-05 20:29:11.328 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\兴业银行_金融理财_004_黄佳琴_522728197611250026.xlsx - 金融理财信息 (行数: 5, 非空值: 63)
2025-08-05 20:29:11.387 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:11.387 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:11.447 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='兴业银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财账户信息', 工作表='金融理财账户信息'
2025-08-05 20:29:11.447 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财账户信息'
2025-08-05 20:29:11.449 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财账户信息' → 金融理财_金融理财账户信息表
2025-08-05 20:29:11.449 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:11.449 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:11.449 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'兴业银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财账户信息' → 表类型'金融理财_金融理财账户信息表'
2025-08-05 20:29:11.517 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:11.517 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:11.582 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='兴业银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财信息', 工作表='金融理财信息'
2025-08-05 20:29:11.582 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='金融理财信息'
2025-08-05 20:29:11.583 - INFO - [MainThread:26196] - import_data.py:12365 - suggest_table_type_by_name() - ✅ 精确匹配成功: 文件关键词'金融理财' + 工作表'金融理财信息' → 金融理财_金融理财信息表
2025-08-05 20:29:11.583 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:29:11.583 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'兴业银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:29:11.583 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'兴业银行_金融理财_004_黄佳琴_522728197611250026 - 金融理财信息' → 表类型'金融理财_金融理财信息表'
2025-08-05 20:29:11.605 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\农业银行_交易流水_004_黄佳琴_522728197611250026.xlsx - 交易流水 (行数: 10, 非空值: 220)
2025-08-05 20:29:11.623 - DEBUG - [MainThread:26196] - import_data.py:8989 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-央地\黔监查〔2025〕2343号-央地_按对象\004_黄佳琴_522728197611250026\农业银行_交易流水_004_黄佳琴_522728197611250026.xlsx - 交易流水 (行数: 10, 非空值: 220)
2025-08-05 20:29:11.686 - INFO - [MainThread:26196] - import_data.py:9360 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-05 20:29:11.686 - DEBUG - [MainThread:26196] - import_data.py:9376 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-05 20:29:11.753 - INFO - [MainThread:26196] - import_data.py:12346 - suggest_table_type_by_name() - 🔍 表类型匹配开始: 文件名='农业银行_交易流水_004_黄佳琴_522728197611250026', 工作表='交易流水'
2025-08-05 20:29:11.754 - DEBUG - [MainThread:26196] - import_data.py:12350 - suggest_table_type_by_name() - 尝试精确匹配: 文件关键词包含匹配 + 工作表名='交易流水'
2025-08-05 20:29:11.755 - INFO - [MainThread:26196] - import_data.py:12385 - suggest_table_type_by_name() - ✅ 固定规则匹配成功: 文件名包含'交易流水' → 临时账户交易明细表
2025-08-05 20:29:11.755 - INFO - [MainThread:26196] - import_data.py:12408 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_交易流水_004_黄佳琴_522728197611250026' → 表类型'临时账户交易明细表'
2025-08-05 20:29:11.755 - INFO - [MainThread:26196] - import_data.py:12414 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'农业银行_交易流水_004_黄佳琴_522728197611250026' → 表类型'临时账户交易明细表'
2025-08-05 20:29:11.755 - INFO - [MainThread:26196] - import_data.py:9140 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'农业银行_交易流水_004_黄佳琴_522728197611250026' → 表类型'临时账户交易明细表'
