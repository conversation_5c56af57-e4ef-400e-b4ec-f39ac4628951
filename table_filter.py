import sys
import pandas as pd
from functools import partial
from PySide6.QtWidgets import (QApplication, QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem,
                               QCheckBox, QLineEdit, QPushButton, QDialog,
                               QScrollArea, QDialogButtonBox, QHBoxLayout, QToolButton, QMenu, QLabel)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon


class FilterDialog(QDialog):
    def __init__(self, column_values, selected_values, parent=None, col=None):
        super().__init__(parent)
        self.setWindowTitle('筛选器')
        self.resize(350, 500)
        self.parent_widget = parent
        self.col = col

        self.layout = QVBoxLayout()
        self.checkbox_list = []

        self.input_field = QLineEdit(self)
        self.input_field.setPlaceholderText('输入筛选内容...')
        self.input_field.setStyleSheet("padding: 5px; font-size: 14px;")
        self.input_field.textChanged.connect(self.filter_checkboxes)
        self.layout.addWidget(self.input_field)

        button_layout = QHBoxLayout()
        select_all_button = QPushButton("全选")
        deselect_all_button = QPushButton("反选")
        select_all_button.setStyleSheet("padding: 5px 10px; font-size: 14px;")
        deselect_all_button.setStyleSheet("padding: 5px 10px; font-size: 14px;")
        select_all_button.clicked.connect(self.select_all)
        deselect_all_button.clicked.connect(self.deselect_all)
        button_layout.addWidget(select_all_button)
        button_layout.addWidget(deselect_all_button)
        self.layout.addLayout(button_layout)

        sort_layout = QHBoxLayout()
        sort_button = QToolButton()
        sort_button.setText("排序")
        sort_button.setPopupMode(QToolButton.InstantPopup)
        sort_button.setStyleSheet("font-size: 14px; padding: 5px;")
        self.sort_menu = QMenu()
        self.sort_by_name_asc = self.sort_menu.addAction("名称升序")
        self.sort_by_name_desc = self.sort_menu.addAction("名称降序")
        self.sort_by_count_asc = self.sort_menu.addAction("数量升序")
        self.sort_by_count_desc = self.sort_menu.addAction("数量降序")
        sort_button.setMenu(self.sort_menu)
        sort_layout.addWidget(sort_button)
        self.layout.addLayout(sort_layout)

        self.sort_by_name_asc.triggered.connect(lambda: self.sort_checkboxes('name', True))
        self.sort_by_name_desc.triggered.connect(lambda: self.sort_checkboxes('name', False))
        self.sort_by_count_asc.triggered.connect(lambda: self.sort_checkboxes('count', True))
        self.sort_by_count_desc.triggered.connect(lambda: self.sort_checkboxes('count', False))

        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(scroll_widget)
        self.update_checkboxes(column_values, selected_values)
        scroll.setWidget(scroll_widget)
        scroll.setStyleSheet("QScrollArea { border: none; }")
        self.layout.addWidget(scroll)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.button(QDialogButtonBox.Ok).setStyleSheet("padding: 5px 10px; font-size: 14px;")
        buttons.button(QDialogButtonBox.Cancel).setStyleSheet("padding: 5px 10px; font-size: 14px;")
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        self.layout.addWidget(buttons)

        self.setLayout(self.layout)

    def select_all(self):
        for checkbox in self.checkbox_list:
            checkbox.setChecked(True)
        self.parent_widget.clear_filter_marks(self.col)

    def deselect_all(self):
        for checkbox in self.checkbox_list:
            checkbox.setChecked(not checkbox.isChecked())
        if all(checkbox.isChecked() for checkbox in self.checkbox_list):
            self.parent_widget.clear_filter_marks(self.col)

    def filter_checkboxes(self, text):
        for checkbox in self.checkbox_list:
            if text.lower() in checkbox.text().lower():
                checkbox.show()
            else:
                checkbox.hide()

    def sort_checkboxes(self, key, ascending):
        if key == 'name':
            self.checkbox_list.sort(key=lambda x: x.text().split(" (")[0], reverse=not ascending)
        elif key == 'count':
            self.checkbox_list.sort(key=lambda x: int(x.text().split(" (")[1][:-1]), reverse=not ascending)

        for checkbox in self.checkbox_list:
            self.scroll_layout.removeWidget(checkbox)
            self.scroll_layout.addWidget(checkbox)

    def update_checkboxes(self, column_values, selected_values):
        all_selected = not selected_values
        for value, count in column_values.items():
            checkbox = QCheckBox(f"{value} ({count})")
            checkbox.value = value
            checkbox.setStyleSheet("font-size: 14px; padding: 5px;")
            if all_selected or value in selected_values:
                checkbox.setChecked(True)
            else:
                checkbox.setChecked(False)
            self.checkbox_list.append(checkbox)
            self.scroll_layout.addWidget(checkbox)

    def get_selected_values(self):
        selected_values = [checkbox.value for checkbox in self.checkbox_list if checkbox.isChecked()]
        input_text = self.input_field.text().strip()
        if input_text:
            selected_values.append(input_text)
        return selected_values


class FilterApp(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.filtered_columns = {}

    def initUI(self):
        self.setWindowTitle('筛选器示例')
        self.setGeometry(100, 100, 1200, 600)

        layout = QVBoxLayout()

        self.table = QTableWidget(self)
        layout.addWidget(self.table)
        self.setLayout(layout)

    def load_data(self, df):
        self.table.setRowCount(df.shape[0] + 1)  # 添加一行用于筛选框
        self.table.setColumnCount(df.shape[1])
        self.table.setHorizontalHeaderLabels(df.columns)

        for row in range(df.shape[0]):
            for col in range(df.shape[1]):
                self.table.setItem(row + 1, col, QTableWidgetItem(str(df.iat[row, col])))

        for col in range(self.table.columnCount()):
            self.add_filter(col)

    def add_filter(self, col):
        filter_button = QToolButton()
        filter_button.setIcon(QIcon('gui/images/icons/筛选器.svg'))
        filter_button.setStyleSheet("padding: 5px; font-size: 14px;")
        filter_button.clicked.connect(partial(self.show_filter_dialog, col, filter_button))
        self.table.setCellWidget(0, col, filter_button)

    def show_filter_dialog(self, col, button):
        column_values = pd.Series(
            [self.table.item(row, col).text() for row in range(1, self.table.rowCount())]).value_counts().to_dict()
        selected_values = self.filtered_columns.get(col, [])
        dialog = FilterDialog(column_values, selected_values, self, col)

        button_pos = button.mapToGlobal(button.rect().bottomRight())
        screen_width = QApplication.primaryScreen().geometry().width()
        if button_pos.x() + dialog.width() > screen_width:
            dialog.move(button_pos.x() - dialog.width(), button_pos.y())
        else:
            dialog.move(button_pos.x(), button_pos.y())

        if dialog.exec():
            selected_values = dialog.get_selected_values()
            self.filtered_columns[col] = selected_values
            self.apply_filter(col, selected_values)

            header_item = self.table.horizontalHeaderItem(col)
            if not selected_values or len(selected_values) == len(column_values):
                header_item.setBackground(Qt.white)
                self.clear_column_background(col)
            else:
                header_item.setBackground(Qt.blue)
                self.set_column_background(col, Qt.lightGray)

    def clear_filter_marks(self, col):
        header_item = self.table.horizontalHeaderItem(col)
        header_item.setBackground(Qt.white)
        self.clear_column_background(col)
        self.filtered_columns.pop(col, None)

    def clear_column_background(self, col):
        for row in range(1, self.table.rowCount()):
            item = self.table.item(row, col)
            if item:
                item.setBackground(Qt.white)

    def set_column_background(self, col, color):
        for row in range(1, self.table.rowCount()):
            item = self.table.item(row, col)
            if item:
                item.setBackground(color)

    def apply_filter(self, col, selected_values):
        all_selected = len(selected_values) == 0
        for row in range(1, self.table.rowCount()):
            item = self.table.item(row, col)
            self.table.setRowHidden(row, not all_selected and not any(
                item and item.text() == value for value in selected_values))

        self.update_filter_options(col)

    def update_filter_options(self, col):
        for other_col in range(self.table.columnCount()):
            if other_col == col:
                continue
            column_values = pd.Series(
                [self.table.item(row, other_col).text() for row in range(1, self.table.rowCount()) if not self.table.isRowHidden(row)]).value_counts().to_dict()
            self.filtered_columns[other_col] = list(column_values.keys())

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = FilterApp()
    ex.show()
    sys.exit(app.exec())
