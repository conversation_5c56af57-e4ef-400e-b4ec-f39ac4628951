#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的分批数据导入器

功能描述：
提供内存友好的分批数据导入功能，解决大量数据导入时程序意外退出的问题

实现逻辑：
1. 文件分块读取：避免大文件一次性加载到内存
2. 动态批处理大小：根据系统资源自动调整批处理大小
3. 内存监控：实时监控内存使用情况，及时清理
4. 断点续传：支持导入中断后从断点继续
5. 智能重试：针对不同错误类型采用不同重试策略
"""

import os
import time
import json
import logging
import gc
from typing import Generator, Tuple, Optional, Dict, Any
import pandas as pd
import psycopg2
from psycopg2.extras import execute_batch
from database_setup import get_db_connection

class MemoryManager:
    """内存管理器"""
    
    def __init__(self, warning_threshold=70, critical_threshold=85):
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        
        # 尝试导入psutil
        try:
            import psutil
            self.psutil_available = True
        except ImportError:
            self.psutil_available = False
            logging.warning("psutil不可用，将使用简化的内存监控")
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用率"""
        if self.psutil_available:
            import psutil
            return psutil.virtual_memory().percent
        else:
            # 简化的内存检查
            return 50.0  # 假设50%使用率
    
    def check_memory_status(self) -> str:
        """检查内存状态"""
        memory_percent = self.get_memory_usage()
        
        if memory_percent > self.critical_threshold:
            return "critical"
        elif memory_percent > self.warning_threshold:
            return "warning"
        else:
            return "normal"
    
    def force_cleanup(self) -> int:
        """强制内存清理"""
        # 强制垃圾回收
        collected = gc.collect()
        logging.info(f"垃圾回收释放了 {collected} 个对象")
        
        # 等待系统释放内存
        time.sleep(1)
        
        return collected

class BatchSizeCalculator:
    """批处理大小计算器"""
    
    def __init__(self, initial_size=5000, min_size=1000, max_size=20000):
        self.current_size = initial_size
        self.min_size = min_size
        self.max_size = max_size
        self.success_history = []
        self.memory_manager = MemoryManager()
    
    def calculate_optimal_size(self, total_rows: int, estimated_row_size_kb: float = 1.0) -> int:
        """计算最优批处理大小"""
        try:
            # 获取可用内存
            if self.memory_manager.psutil_available:
                import psutil
                available_memory_mb = psutil.virtual_memory().available / (1024 * 1024)
            else:
                available_memory_mb = 1024  # 假设1GB可用内存
            
            # 使用可用内存的5%作为批处理缓冲区
            buffer_memory_mb = available_memory_mb * 0.05
            
            # 计算可处理的最大行数
            max_rows = int((buffer_memory_mb * 1024) / estimated_row_size_kb)
            
            # 结合历史成功率调整
            if len(self.success_history) > 5:
                recent_success_rate = sum(self.success_history[-5:]) / 5
                if recent_success_rate > 0.9:
                    # 成功率高，可以增加批处理大小
                    self.current_size = min(self.current_size * 1.1, self.max_size)
                elif recent_success_rate < 0.7:
                    # 成功率低，减少批处理大小
                    self.current_size = max(self.current_size * 0.8, self.min_size)
            
            # 综合考虑各种因素
            optimal_size = max(
                self.min_size,
                min(max_rows, self.current_size, self.max_size, total_rows)
            )
            
            return int(optimal_size)
            
        except Exception as e:
            logging.warning(f"计算最优批处理大小时出错: {e}，使用默认值")
            return min(self.current_size, total_rows)
    
    def record_batch_result(self, success: bool):
        """记录批处理结果"""
        self.success_history.append(1 if success else 0)
        # 只保留最近20次的记录
        if len(self.success_history) > 20:
            self.success_history = self.success_history[-20:]

class ExcelChunkReader:
    """Excel文件分块读取器"""
    
    def __init__(self, chunk_size=10000):
        self.chunk_size = chunk_size
    
    def read_in_chunks(self, file_path: str, worksheet_name: str) -> Generator[Tuple[pd.DataFrame, int, int], None, None]:
        """分块读取Excel文件"""
        try:
            logging.info(f"开始分块读取文件: {file_path}, 工作表: {worksheet_name}")
            
            # 先读取少量数据获取列信息
            sample_df = pd.read_excel(file_path, sheet_name=worksheet_name, nrows=10)
            columns = sample_df.columns.tolist()
            
            # 使用openpyxl读取总行数（更高效）
            try:
                from openpyxl import load_workbook
                wb = load_workbook(file_path, read_only=True)
                ws = wb[worksheet_name]
                total_rows = ws.max_row - 1  # 减去标题行
                wb.close()
            except Exception:
                # 如果openpyxl失败，使用pandas估算
                total_rows = len(pd.read_excel(file_path, sheet_name=worksheet_name))
            
            logging.info(f"文件总行数: {total_rows}, 将分 {(total_rows + self.chunk_size - 1) // self.chunk_size} 块读取")
            
            # 分块读取
            for start_row in range(0, total_rows, self.chunk_size):
                try:
                    # 计算实际读取行数
                    actual_chunk_size = min(self.chunk_size, total_rows - start_row)
                    
                    # 读取数据块
                    chunk_df = pd.read_excel(
                        file_path,
                        sheet_name=worksheet_name,
                        skiprows=range(1, start_row + 1),  # 跳过已读取的行，但保留标题
                        nrows=actual_chunk_size
                    )
                    
                    if not chunk_df.empty:
                        end_row = start_row + len(chunk_df)
                        logging.debug(f"读取数据块: 行 {start_row+1}-{end_row}, 共 {len(chunk_df)} 行")
                        yield chunk_df, start_row, end_row
                    else:
                        logging.warning(f"数据块为空: 行 {start_row+1}")
                        break
                        
                except Exception as chunk_error:
                    logging.error(f"读取数据块失败 (行 {start_row+1}): {chunk_error}")
                    # 尝试读取更小的块
                    if self.chunk_size > 1000:
                        smaller_chunk_size = self.chunk_size // 2
                        logging.info(f"尝试使用更小的块大小: {smaller_chunk_size}")
                        for sub_start in range(start_row, min(start_row + self.chunk_size, total_rows), smaller_chunk_size):
                            try:
                                sub_chunk_df = pd.read_excel(
                                    file_path,
                                    sheet_name=worksheet_name,
                                    skiprows=range(1, sub_start + 1),
                                    nrows=min(smaller_chunk_size, total_rows - sub_start)
                                )
                                if not sub_chunk_df.empty:
                                    yield sub_chunk_df, sub_start, sub_start + len(sub_chunk_df)
                            except Exception as sub_error:
                                logging.error(f"读取子数据块也失败 (行 {sub_start+1}): {sub_error}")
                                continue
                    else:
                        # 如果块已经很小了，跳过这个块
                        continue
                        
        except Exception as e:
            logging.error(f"分块读取Excel文件失败: {e}")
            yield None, 0, 0

class ImportCheckpoint:
    """导入检查点管理器"""
    
    def __init__(self, case_id: str, import_batch: int):
        self.case_id = case_id
        self.import_batch = import_batch
        self.checkpoint_file = f"import_checkpoint_{case_id}_{import_batch}.json"
    
    def save_progress(self, file_path: str, worksheet_name: str, processed_rows: int):
        """保存导入进度"""
        checkpoint_data = {
            'case_id': self.case_id,
            'import_batch': self.import_batch,
            'file_path': file_path,
            'worksheet_name': worksheet_name,
            'processed_rows': processed_rows,
            'timestamp': time.time(),
            'readable_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        try:
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
            logging.debug(f"保存检查点: {processed_rows} 行已处理")
        except Exception as e:
            logging.error(f"保存检查点失败: {e}")
    
    def load_progress(self) -> Optional[Dict[str, Any]]:
        """加载导入进度"""
        try:
            if os.path.exists(self.checkpoint_file):
                with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint_data = json.load(f)
                logging.info(f"加载检查点: {checkpoint_data.get('processed_rows', 0)} 行已处理")
                return checkpoint_data
        except Exception as e:
            logging.error(f"加载检查点失败: {e}")
        return None
    
    def clear_checkpoint(self):
        """清理检查点文件"""
        try:
            if os.path.exists(self.checkpoint_file):
                os.remove(self.checkpoint_file)
                logging.info("检查点文件已清理")
        except Exception as e:
            logging.error(f"清理检查点文件失败: {e}")

class SmartRetryHandler:
    """智能重试处理器"""
    
    def __init__(self, max_retries=3):
        self.max_retries = max_retries
        self.retry_delays = [1, 3, 5]  # 递增延迟
    
    def execute_with_retry(self, func, *args, **kwargs):
        """带智能重试的函数执行"""
        last_exception = None
        
        for attempt in range(self.max_retries):
            try:
                return func(*args, **kwargs)
                
            except psycopg2.OperationalError as e:
                # 数据库连接错误，需要重试
                last_exception = e
                if attempt < self.max_retries - 1:
                    delay = self.retry_delays[min(attempt, len(self.retry_delays) - 1)]
                    logging.warning(f"数据库操作失败，{delay}秒后重试 (第{attempt+1}次): {e}")
                    time.sleep(delay)
                    continue
                    
            except MemoryError as e:
                # 内存错误，强制清理后重试
                last_exception = e
                if attempt < self.max_retries - 1:
                    logging.warning(f"内存不足，强制垃圾回收后重试: {e}")
                    gc.collect()
                    time.sleep(2)
                    continue
                    
            except Exception as e:
                # 其他错误，记录并跳过
                logging.error(f"执行函数时发生不可恢复的错误: {e}")
                return None
        
        # 所有重试都失败
        logging.error(f"函数执行最终失败: {last_exception}")
        return None

class OptimizedBatchImporter:
    """优化的分批导入器"""
    
    def __init__(self, case_id: str, import_batch: int):
        self.case_id = case_id
        self.import_batch = import_batch
        
        # 初始化组件
        self.memory_manager = MemoryManager()
        self.batch_calculator = BatchSizeCalculator()
        self.chunk_reader = ExcelChunkReader()
        self.checkpoint = ImportCheckpoint(case_id, import_batch)
        self.retry_handler = SmartRetryHandler()
        
        # 统计信息
        self.total_processed = 0
        self.total_errors = 0
        self.start_time = time.time()
    
    def import_file_optimized(self, file_path: str, worksheet_name: str, table_name: str, 
                            field_mapping: Dict[str, str], resume_from_checkpoint: bool = True) -> int:
        """优化的文件导入方法"""
        logging.info(f"开始优化导入: {file_path} -> {table_name}")
        
        # 检查是否需要从检查点恢复
        checkpoint_data = None
        skip_rows = 0
        
        if resume_from_checkpoint:
            checkpoint_data = self.checkpoint.load_progress()
            if checkpoint_data and checkpoint_data.get('file_path') == file_path:
                skip_rows = checkpoint_data.get('processed_rows', 0)
                logging.info(f"从检查点恢复，跳过前 {skip_rows} 行")
        
        total_imported = 0
        chunk_count = 0
        
        try:
            # 分块读取和导入
            for chunk_df, start_row, end_row in self.chunk_reader.read_in_chunks(file_path, worksheet_name):
                if chunk_df is None:
                    continue
                
                # 如果需要跳过已处理的行
                if start_row < skip_rows:
                    continue
                
                chunk_count += 1
                logging.info(f"处理数据块 {chunk_count}: 行 {start_row+1}-{end_row}")
                
                # 检查内存状态
                memory_status = self.memory_manager.check_memory_status()
                if memory_status == "critical":
                    logging.warning("内存使用率过高，强制清理")
                    self.memory_manager.force_cleanup()
                elif memory_status == "warning":
                    logging.info("内存使用率较高，进行轻量清理")
                    gc.collect()
                
                # 导入数据块
                imported_count = self._import_chunk_with_retry(
                    chunk_df, table_name, field_mapping
                )
                
                if imported_count > 0:
                    total_imported += imported_count
                    self.total_processed += imported_count
                    
                    # 保存检查点
                    self.checkpoint.save_progress(file_path, worksheet_name, end_row)
                    
                    # 记录成功
                    self.batch_calculator.record_batch_result(True)
                else:
                    # 记录失败
                    self.batch_calculator.record_batch_result(False)
                    self.total_errors += 1
                
                # 短暂休息，让系统有时间处理其他任务
                time.sleep(0.1)
        
        except Exception as e:
            logging.error(f"文件导入过程中发生错误: {e}")
            return total_imported
        
        # 导入完成，清理检查点
        if total_imported > 0:
            self.checkpoint.clear_checkpoint()
        
        elapsed_time = time.time() - self.start_time
        logging.info(f"文件导入完成: 共导入 {total_imported} 条记录，耗时 {elapsed_time:.2f} 秒")
        
        return total_imported
    
    def _import_chunk_with_retry(self, chunk_df: pd.DataFrame, table_name: str, 
                               field_mapping: Dict[str, str]) -> int:
        """带重试的数据块导入"""
        def import_chunk():
            return self._import_single_chunk(chunk_df, table_name, field_mapping)
        
        result = self.retry_handler.execute_with_retry(import_chunk)
        return result if result is not None else 0
    
    def _import_single_chunk(self, chunk_df: pd.DataFrame, table_name: str, 
                           field_mapping: Dict[str, str]) -> int:
        """导入单个数据块"""
        if chunk_df.empty:
            return 0
        
        # 应用字段映射
        mapped_df = chunk_df.rename(columns=field_mapping)
        
        # 数据清理
        mapped_df = mapped_df.fillna('')
        
        # 计算最优批处理大小
        batch_size = self.batch_calculator.calculate_optimal_size(len(mapped_df))
        
        # 获取数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 准备插入数据
            columns = list(mapped_df.columns)
            quoted_columns = [f'"{col}"' for col in columns]
            placeholders = ', '.join(['%s'] * len(columns))
            sql = f'INSERT INTO "{table_name}" ({", ".join(quoted_columns)}) VALUES ({placeholders})'
            
            # 转换为值列表
            values_list = []
            for _, row in mapped_df.iterrows():
                values = []
                for value in row:
                    if pd.isna(value) or value == '':
                        values.append(None)
                    else:
                        values.append(str(value))
                values_list.append(tuple(values))
            
            # 分批插入
            total_inserted = 0
            for i in range(0, len(values_list), batch_size):
                batch = values_list[i:i+batch_size]
                
                try:
                    execute_batch(cursor, sql, batch, page_size=min(1000, len(batch)))
                    conn.commit()
                    total_inserted += len(batch)
                    
                except Exception as batch_error:
                    logging.error(f"批量插入失败: {batch_error}")
                    conn.rollback()
                    
                    # 尝试单行插入
                    for single_row in batch:
                        try:
                            cursor.execute(sql, single_row)
                            conn.commit()
                            total_inserted += 1
                        except Exception as row_error:
                            logging.error(f"单行插入失败: {row_error}")
                            conn.rollback()
            
            return total_inserted
            
        finally:
            cursor.close()
            conn.close()

# 导出主要类
__all__ = ['OptimizedBatchImporter', 'MemoryManager', 'BatchSizeCalculator', 'ExcelChunkReader']
