from gui.pages.ui_pages import Ui_application_pages
from gui.widgets.py_push_button import PyPushButton
from qt_core import *

class UI_MainWindow(object):
    def setup_ui(self, parent):
        if not parent.objectName():
            parent.setObjectName("MainWindow")

        # 设置初始参数
        parent.resize(1200, 720)
        parent.setMinimumSize(960, 540)

        # 创建中央窗口
        self.central_frame = QFrame()

        # 创建主布局
        self.main_layout = QHBoxLayout(self.central_frame)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # 左侧菜单
        self.left_menu = QFrame()
        self.left_menu.setStyleSheet("background-color: #44475a")
        self.left_menu.setMaximumWidth(50)
        self.left_menu.setMinimumWidth(50)

        # 左侧菜单布局
        self.left_menu_layout = QVBoxLayout(self.left_menu)
        self.left_menu_layout.setContentsMargins(0, 0, 0, 0)
        self.left_menu_layout.setSpacing(0)

        # 顶部菜单框架
        self.left_menu_top_frame = QFrame()
        self.left_menu_top_frame.setMinimumHeight(40)
        self.left_menu_top_frame.setObjectName("left_menu_top_frame")

        # 顶部菜单布局
        self.left_menu_top_layout = QVBoxLayout(self.left_menu_top_frame)
        self.left_menu_top_layout.setContentsMargins(0, 0, 0, 0)
        self.left_menu_top_layout.setSpacing(0)

        # 顶部按钮
        self.toggle_button = PyPushButton(
            text="隐藏菜单",
            icon_path="icon_menu.svg"
        )
        self.btn_1 = PyPushButton(
            text="主页",
            is_active=True,
            icon_path="icon_home.svg"
        )
        self.btn_2 = PyPushButton(
            text="工具",
            icon_path="icon_widgets.svg"
        )

        # 将按钮添加到布局
        self.left_menu_top_layout.addWidget(self.toggle_button)
        self.left_menu_top_layout.addWidget(self.btn_1)
        self.left_menu_top_layout.addWidget(self.btn_2)

        # 菜单填充
        self.left_menu_spacer = QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding)

        # 底部菜单框架
        self.left_menu_bottom_frame = QFrame()
        self.left_menu_bottom_frame.setMinimumHeight(40)
        self.left_menu_bottom_frame.setObjectName("left_menu_bottom_frame")

        self.left_menu_bottom_layout = QVBoxLayout(self.left_menu_bottom_frame)
        self.left_menu_bottom_layout.setContentsMargins(0, 0, 0, 0)
        self.left_menu_bottom_layout.setSpacing(0)

        # 底部按钮
        self.settings_btn = PyPushButton(
            text="设置",
            icon_path="icon_settings.svg"
        )
        
        # 数据库配置按钮
        self.database_config_btn = PyPushButton(
            text="数据库配置",
            icon_path="icon_settings.svg"
        )

        # 将按钮添加到布局
        self.left_menu_bottom_layout.addWidget(self.settings_btn)
        self.left_menu_bottom_layout.addWidget(self.database_config_btn)

        # 版本标签
        self.left_menu_label_version = QLabel("v1.3.0")
        self.left_menu_label_version.setAlignment(Qt.AlignCenter)
        self.left_menu_label_version.setMinimumHeight(30)
        self.left_menu_label_version.setMaximumHeight(30)
        self.left_menu_label_version.setStyleSheet("color: #c3ccdf")

        # 将组件添加到布局
        self.left_menu_layout.addWidget(self.left_menu_top_frame)
        self.left_menu_layout.addItem(self.left_menu_spacer)
        self.left_menu_layout.addWidget(self.left_menu_bottom_frame)
        self.left_menu_layout.addWidget(self.left_menu_label_version)

        # 内容区域
        self.content = QFrame()
        self.content.setStyleSheet("background-color: #282a36")

        # 内容布局
        self.content_layout = QVBoxLayout(self.content)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(0)

        # 顶部栏
        self.top_bar = QFrame()
        self.top_bar.setMinimumHeight(30)
        self.top_bar.setMaximumHeight(30)
        self.top_bar.setStyleSheet("background-color: #21232d; color: #6272a4")
        self.top_bar_layout = QHBoxLayout(self.top_bar)
        self.top_bar_layout.setContentsMargins(10, 0, 10, 0)

        # 左侧标签
        self.top_label_left = QLabel("欢迎使用资金分析系统")

        # 顶部填充
        self.top_spacer = QSpacerItem(20, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        # 右侧标签
        self.top_label_right = QLabel("案件管理")
        self.top_label_right.setStyleSheet("font: 700 9pt 'Segoe UI'")

        # 添加到布局
        self.top_bar_layout.addWidget(self.top_label_left)
        self.top_bar_layout.addItem(self.top_spacer)
        self.top_bar_layout.addWidget(self.top_label_right)

        # 应用页面
        self.pages = QStackedWidget()
        self.pages.setStyleSheet("font-size: 12pt; color: #f8f8f2;")
        self.ui_pages = Ui_application_pages()
        self.ui_pages.setupUi(self.pages)
        self.pages.setCurrentWidget(self.ui_pages.page_1)

        # 底部栏
        self.bottom_bar = QFrame()
        self.bottom_bar.setMinimumHeight(30)
        self.bottom_bar.setMaximumHeight(30)
        self.bottom_bar.setStyleSheet("background-color: #21232d; color: #6272a4")

        self.bottom_bar_layout = QHBoxLayout(self.bottom_bar)
        self.bottom_bar_layout.setContentsMargins(10, 0, 10, 0)

        # 左侧标签
        self.bottom_label_left = QLabel("云岩王森")

        # 底部填充
        self.bottom_spacer = QSpacerItem(20, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        # 右侧标签
        self.bottom_label_right = QLabel("©2024")

        # 添加到布局
        self.bottom_bar_layout.addWidget(self.bottom_label_left)
        self.bottom_bar_layout.addItem(self.bottom_spacer)
        self.bottom_bar_layout.addWidget(self.bottom_label_right)

        # 添加到内容布局
        self.content_layout.addWidget(self.top_bar)
        self.content_layout.addWidget(self.pages)
        self.content_layout.addWidget(self.bottom_bar)

        # 添加组件到应用程序
        self.main_layout.addWidget(self.left_menu)
        self.main_layout.addWidget(self.content)

        # 设置中央窗口
        parent.setCentralWidget(self.central_frame)
