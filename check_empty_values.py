#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查空值和空字符串问题

本文件的功能和实现逻辑：
1. 检查对手户名、对手账号、对手卡号字段的空值情况
2. 区分NULL值、空字符串''、空白字符串'   '等情况
3. 分析现金识别匹配条件是否完善
4. 提供优化建议

重点检查：
- NULL值 vs 空字符串 vs 空白字符串
- 不同类型空值的数量分布
- 现有匹配条件的覆盖率
"""

import psycopg2
import configparser
import logging
from datetime import datetime

# 设置日志
log_file = f'check_empty_values_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class EmptyValueChecker:
    def __init__(self, case_id):
        self.case_id = case_id
        self.conn = None
        self.cursor = None
        
    def connect_database(self):
        try:
            config = configparser.ConfigParser()
            config.read('db_config.ini')
            
            self.conn = psycopg2.connect(
                host=config['PostgreSQL']['host'],
                port=config['PostgreSQL']['port'],
                database=config['PostgreSQL']['database'],
                user=config['PostgreSQL']['user'],
                password=config['PostgreSQL']['password']
            )
            self.cursor = self.conn.cursor()
            logging.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logging.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def check_empty_values_detailed(self):
        """详细检查空值情况"""
        logging.info("🔍 详细检查对手信息字段的空值情况...")
        
        try:
            # 总记录数
            self.cursor.execute(f'''
                SELECT COUNT(*) FROM "账户交易明细表" 
                WHERE "案件编号" = '{self.case_id}'
            ''')
            total_records = self.cursor.fetchone()[0]
            logging.info(f"📊 总记录数: {total_records:,}")
            
            # 检查每个字段的不同空值类型
            fields = ['对手户名', '对手账号', '对手卡号']
            
            for field in fields:
                logging.info(f"\n📋 检查字段: {field}")
                
                # 1. NULL值
                self.cursor.execute(f'''
                    SELECT COUNT(*) FROM "账户交易明细表" 
                    WHERE "案件编号" = '{self.case_id}' AND "{field}" IS NULL
                ''')
                null_count = self.cursor.fetchone()[0]
                
                # 2. 空字符串
                self.cursor.execute(f'''
                    SELECT COUNT(*) FROM "账户交易明细表" 
                    WHERE "案件编号" = '{self.case_id}' AND "{field}" = ''
                ''')
                empty_string_count = self.cursor.fetchone()[0]
                
                # 3. 只包含空白字符
                self.cursor.execute(f'''
                    SELECT COUNT(*) FROM "账户交易明细表" 
                    WHERE "案件编号" = '{self.case_id}' 
                    AND "{field}" IS NOT NULL 
                    AND "{field}" != ''
                    AND TRIM("{field}") = ''
                ''')
                whitespace_count = self.cursor.fetchone()[0]
                
                # 4. 特殊值（如'-', 'NULL', 'null'等）
                special_values = ['-', 'NULL', 'null', 'None', 'none', '无', '空']
                special_counts = {}
                
                for special_val in special_values:
                    self.cursor.execute(f'''
                        SELECT COUNT(*) FROM "账户交易明细表" 
                        WHERE "案件编号" = '{self.case_id}' AND "{field}" = %s
                    ''', (special_val,))
                    count = self.cursor.fetchone()[0]
                    if count > 0:
                        special_counts[special_val] = count
                
                # 5. 有值的记录
                self.cursor.execute(f'''
                    SELECT COUNT(*) FROM "账户交易明细表" 
                    WHERE "案件编号" = '{self.case_id}' 
                    AND "{field}" IS NOT NULL 
                    AND TRIM("{field}") != ''
                    AND "{field}" NOT IN ('-', 'NULL', 'null', 'None', 'none', '无', '空')
                ''')
                has_value_count = self.cursor.fetchone()[0]
                
                # 显示统计结果
                logging.info(f"   NULL值: {null_count:,} 条 ({null_count/total_records*100:.1f}%)")
                logging.info(f"   空字符串 '': {empty_string_count:,} 条 ({empty_string_count/total_records*100:.1f}%)")
                logging.info(f"   空白字符串: {whitespace_count:,} 条 ({whitespace_count/total_records*100:.1f}%)")
                
                if special_counts:
                    logging.info(f"   特殊空值:")
                    for val, count in special_counts.items():
                        logging.info(f"     '{val}': {count:,} 条 ({count/total_records*100:.1f}%)")
                
                logging.info(f"   有效值: {has_value_count:,} 条 ({has_value_count/total_records*100:.1f}%)")
                
                # 计算总的"空"值
                total_empty = null_count + empty_string_count + whitespace_count + sum(special_counts.values())
                logging.info(f"   总空值: {total_empty:,} 条 ({total_empty/total_records*100:.1f}%)")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 检查空值时出错: {e}")
            return False
    
    def check_current_matching_condition(self):
        """检查当前匹配条件的覆盖情况"""
        logging.info("\n🔍 检查当前匹配条件的覆盖情况...")
        
        try:
            # 当前匹配条件：(字段 IS NULL OR 字段 = '')
            logging.info("📋 当前匹配条件: (字段 IS NULL OR 字段 = '')")
            
            # 三个字段都满足当前条件的记录
            self.cursor.execute(f'''
                SELECT COUNT(*) FROM "账户交易明细表" 
                WHERE "案件编号" = '{self.case_id}'
                AND ("对手户名" IS NULL OR "对手户名" = '')
                AND ("对手账号" IS NULL OR "对手账号" = '')
                AND ("对手卡号" IS NULL OR "对手卡号" = '')
            ''')
            current_match_count = self.cursor.fetchone()[0]
            
            # 改进的匹配条件：包含空白字符串和特殊值
            self.cursor.execute(f'''
                SELECT COUNT(*) FROM "账户交易明细表" 
                WHERE "案件编号" = '{self.case_id}'
                AND ("对手户名" IS NULL OR TRIM("对手户名") = '' OR "对手户名" IN ('-', 'NULL', 'null', 'None', 'none', '无', '空'))
                AND ("对手账号" IS NULL OR TRIM("对手账号") = '' OR "对手账号" IN ('-', 'NULL', 'null', 'None', 'none', '无', '空'))
                AND ("对手卡号" IS NULL OR TRIM("对手卡号") = '' OR "对手卡号" IN ('-', 'NULL', 'null', 'None', 'none', '无', '空'))
            ''')
            improved_match_count = self.cursor.fetchone()[0]
            
            logging.info(f"📊 匹配条件覆盖情况:")
            logging.info(f"   当前条件匹配: {current_match_count:,} 条")
            logging.info(f"   改进条件匹配: {improved_match_count:,} 条")
            logging.info(f"   改进后增加: {improved_match_count - current_match_count:,} 条")
            
            if improved_match_count > current_match_count:
                logging.warning(f"⚠️ 当前匹配条件可能遗漏了 {improved_match_count - current_match_count:,} 条记录")
                
                # 显示被遗漏的记录样本
                logging.info("📄 被遗漏记录的样本:")
                self.cursor.execute(f'''
                    SELECT "摘要说明", "交易类型", "对手户名", "对手账号", "对手卡号"
                    FROM "账户交易明细表" 
                    WHERE "案件编号" = '{self.case_id}'
                    AND NOT (
                        ("对手户名" IS NULL OR "对手户名" = '')
                        AND ("对手账号" IS NULL OR "对手账号" = '')
                        AND ("对手卡号" IS NULL OR "对手卡号" = '')
                    )
                    AND (
                        ("对手户名" IS NULL OR TRIM("对手户名") = '' OR "对手户名" IN ('-', 'NULL', 'null', 'None', 'none', '无', '空'))
                        AND ("对手账号" IS NULL OR TRIM("对手账号") = '' OR "对手账号" IN ('-', 'NULL', 'null', 'None', 'none', '无', '空'))
                        AND ("对手卡号" IS NULL OR TRIM("对手卡号") = '' OR "对手卡号" IN ('-', 'NULL', 'null', 'None', 'none', '无', '空'))
                    )
                    LIMIT 5
                ''')
                
                missed_samples = self.cursor.fetchall()
                for i, sample in enumerate(missed_samples, 1):
                    summary, tx_type, name, account, card = sample
                    logging.info(f"   样本{i}: 摘要='{summary}', 类型='{tx_type}'")
                    logging.info(f"          对手户名='{name}', 对手账号='{account}', 对手卡号='{card}'")
            
            return current_match_count, improved_match_count
            
        except Exception as e:
            logging.error(f"❌ 检查匹配条件时出错: {e}")
            return 0, 0
    
    def suggest_optimized_condition(self):
        """建议优化的匹配条件"""
        logging.info("\n💡 建议优化的匹配条件:")
        
        optimized_condition = '''
        -- 优化的现金识别匹配条件
        WHERE "案件编号" = %s
        AND (
            "对手户名" IS NULL 
            OR TRIM("对手户名") = '' 
            OR "对手户名" IN ('-', 'NULL', 'null', 'None', 'none', '无', '空')
        )
        AND (
            "对手账号" IS NULL 
            OR TRIM("对手账号") = '' 
            OR "对手账号" IN ('-', 'NULL', 'null', 'None', 'none', '无', '空')
        )
        AND (
            "对手卡号" IS NULL 
            OR TRIM("对手卡号") = '' 
            OR "对手卡号" IN ('-', 'NULL', 'null', 'None', 'none', '无', '空')
        )
        '''
        
        logging.info(optimized_condition)
        
        # 或者更简洁的版本
        simplified_condition = '''
        -- 简化版本（使用函数）
        WHERE "案件编号" = %s
        AND COALESCE(NULLIF(TRIM("对手户名"), ''), NULLIF("对手户名", '-')) IS NULL
        AND COALESCE(NULLIF(TRIM("对手账号"), ''), NULLIF("对手账号", '-')) IS NULL
        AND COALESCE(NULLIF(TRIM("对手卡号"), ''), NULLIF("对手卡号", '-')) IS NULL
        '''
        
        logging.info("\n💡 简化版本:")
        logging.info(simplified_condition)
    
    def run_check(self):
        """运行检查"""
        if not self.connect_database():
            return False
        
        try:
            # 详细检查空值情况
            self.check_empty_values_detailed()
            
            # 检查匹配条件覆盖情况
            current_count, improved_count = self.check_current_matching_condition()
            
            # 建议优化条件
            self.suggest_optimized_condition()
            
            # 总结
            logging.info("\n" + "="*60)
            logging.info("📊 检查总结")
            logging.info("="*60)
            
            if improved_count > current_count:
                logging.warning(f"⚠️ 发现问题：当前匹配条件可能遗漏 {improved_count - current_count:,} 条记录")
                logging.info("💡 建议：优化匹配条件以包含更多类型的空值")
            else:
                logging.info("✅ 当前匹配条件覆盖良好")
            
            return True
            
        finally:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()

def main():
    case_id = "20250716182021"
    
    checker = EmptyValueChecker(case_id)
    
    print(f"🔍 开始检查空值情况 - 案件: {case_id}")
    print(f"📄 详细日志保存到: {log_file}")
    
    success = checker.run_check()
    
    if success:
        print("\n✅ 空值检查完成！")
        return 0
    else:
        print("\n❌ 空值检查发现问题")
        return 1

if __name__ == "__main__":
    exit(main())
