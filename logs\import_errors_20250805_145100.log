2025-08-05 15:03:18.508 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:18.512 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:18.512 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:19.755 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:19.755 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:19.755 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:21.010 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:21.010 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:21.010 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:22.255 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:22.255 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:22.255 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:23.556 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:23.556 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:23.556 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:24.824 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:24.824 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:24.824 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:24.992 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:24.992 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:24.992 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:25.122 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:25.122 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:25.122 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:25.257 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:25.257 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:25.258 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:26.526 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:26.526 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:26.527 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:27.752 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:27.752 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:27.752 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:29.009 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:29.009 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:29.009 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:30.247 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:30.247 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:30.247 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:31.471 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:31.471 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:31.471 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:32.722 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:32.723 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:32.723 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:33.950 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:33.950 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:33.950 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:35.176 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:35.176 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:35.176 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:36.428 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:36.428 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:36.428 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:37.690 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:37.691 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:37.691 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:37.821 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:37.821 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:37.821 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:37.945 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:37.945 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:37.946 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:38.077 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:38.077 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:38.078 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:39.309 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:39.309 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:39.309 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:39.440 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:39.440 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:39.440 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:39.548 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:39.549 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:39.549 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:40.751 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:40.751 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:40.751 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:40.864 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:40.864 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:40.864 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:42.059 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:42.059 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:42.059 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:43.280 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:43.280 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:43.280 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:44.478 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:44.478 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:44.478 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:45.686 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:45.686 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:45.686 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:46.907 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:46.907 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:46.907 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:48.138 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:48.138 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:48.138 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:49.349 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:49.349 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:49.349 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:50.549 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:50.549 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:50.549 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:50.662 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:50.662 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:50.662 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:50.767 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:50.767 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:50.767 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:51.946 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:51.946 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:51.946 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:53.113 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:53.113 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:53.113 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:54.326 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:54.327 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:54.327 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:55.521 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:55.521 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:55.521 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:56.717 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:56.717 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:56.717 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:57.925 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:57.925 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:57.925 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:03:59.107 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:03:59.107 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:03:59.107 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:00.311 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:00.311 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:00.312 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:01.496 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:01.496 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:01.496 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:02.686 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:02.686 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:02.686 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:03.867 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:03.868 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:03.868 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:05.040 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:05.040 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:05.040 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:06.242 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:06.242 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:06.242 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:07.417 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:07.417 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:07.417 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:08.616 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:08.616 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:08.616 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:09.820 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:09.820 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:09.820 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:10.996 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:10.997 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:10.997 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:12.179 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:12.179 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:12.180 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:13.348 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:13.348 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:13.348 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:14.578 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:14.578 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:14.578 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:15.780 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:15.780 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:15.780 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:16.990 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:16.990 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:16.990 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:18.192 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:18.192 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:18.193 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:19.412 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:19.412 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:19.412 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:20.588 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:20.588 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:20.588 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:21.783 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:21.783 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:21.783 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:22.975 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:22.975 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:22.975 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:24.182 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:24.183 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:24.183 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:25.349 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:25.349 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:25.349 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:26.533 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:26.533 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:26.533 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:27.736 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:27.736 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:27.736 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:28.933 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:28.933 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:28.933 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:30.133 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:30.134 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:30.134 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:31.319 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:31.319 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:31.327 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:32.521 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:32.521 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:32.521 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:33.704 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:33.705 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:33.705 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:34.882 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:34.882 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:34.882 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:36.091 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:36.091 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:36.091 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:37.291 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:37.291 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:37.291 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:38.488 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:38.488 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:38.488 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:39.671 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:39.671 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:39.671 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:04:40.861 - ERROR - [MainThread:30300] - import_data.py:7198 - import_next_table_type() - 创建导入线程时出错: 'tuple' object has no attribute 'get'
2025-08-05 15:04:40.861 - ERROR - [MainThread:30300] - import_data.py:7200 - import_next_table_type() - 详细错误堆栈: Traceback (most recent call last):
  File "g:\数据分析系统20250725\import_data.py", line 7116, in import_next_table_type
    import_logger.info(f"  文件{i+1}: {file_data.get('file_path', 'Unknown')}")
AttributeError: 'tuple' object has no attribute 'get'

2025-08-05 15:04:40.861 - ERROR - [MainThread:30300] - import_data.py:7309 - handle_import_thread_error() - 导入线程错误处理: 'tuple' object has no attribute 'get'
2025-08-05 15:09:00.597 - CRITICAL - [MainThread:30300] - enhanced_logging_patch.py:322 - monitored_exit() - 🚨 程序即将退出! 退出码: 0
2025-08-05 15:09:00.597 - CRITICAL - [MainThread:30300] - enhanced_logging_patch.py:323 - monitored_exit() - 退出时的堆栈跟踪:
2025-08-05 15:09:00.597 - CRITICAL - [MainThread:30300] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\main.py", line 525, in <module>
    main()
2025-08-05 15:09:00.597 - CRITICAL - [MainThread:30300] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\main.py", line 509, in main
    sys.exit(exit_code)
2025-08-05 15:09:00.597 - CRITICAL - [MainThread:30300] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\enhanced_logging_patch.py", line 326, in monitored_exit
    stack = traceback.format_stack()
