2025-08-01 09:34:46.816 - INFO - [MainThread:5712] - enhanced_logging_patch.py:84 - setup_enhanced_logging() - 🔍 增强日志记录已启动
2025-08-01 09:34:46.817 - WARNING - [MainThread:5712] - memory_optimizer.py:48 - __init__() - ⚠️ psutil模块未安装，无法进行详细内存监控
2025-08-01 09:34:46.818 - WARNING - [MainThread:5712] - memory_optimizer.py:49 - __init__() - 💡 建议安装: pip install psutil
2025-08-01 09:34:46.821 - WARNING - [MainThread:5712] - import_error_handler.py:33 - <module>() - psutil模块不可用，将使用替代的内存监控方案
2025-08-01 09:34:46.936 - INFO - [MainThread:5712] - database_table_checker.py:417 - check_database_tables() - 开始数据库表格完整性检查...
2025-08-01 09:34:46.972 - INFO - [MainThread:5712] - database_table_checker.py:115 - load_excel_table_config() - 成功读取Excel文件，共94行数据
2025-08-01 09:34:46.980 - INFO - [MainThread:5712] - database_table_checker.py:169 - load_excel_table_config() - 从Excel文件解析出94个有效表配置（处理了94行数据）
2025-08-01 09:34:46.981 - INFO - [MainThread:5712] - database_table_checker.py:182 - load_excel_table_config() - Excel文件统计：总行数94，重复表名0个，有效表格94个
2025-08-01 09:34:47.046 - INFO - [MainThread:5712] - database_table_checker.py:444 - check_database_tables() - 数据库中现有表格总数：106
2025-08-01 09:34:47.046 - INFO - [MainThread:5712] - database_table_checker.py:456 - check_database_tables() - 核心系统表检查：9个必需，9个已存在
2025-08-01 09:34:53.318 - INFO - [MainThread:5712] - database_table_checker.py:507 - check_database_tables() - Excel配置表检查：94个定义，94个已存在
2025-08-01 09:34:53.323 - INFO - [MainThread:5712] - database_table_checker.py:511 - check_database_tables() - 所有Excel配置表都已存在，无需创建
2025-08-01 09:34:53.324 - INFO - [MainThread:5712] - database_table_checker.py:541 - check_database_tables() - 所有表都已存在，无需创建任何表
2025-08-01 09:34:53.325 - INFO - [MainThread:5712] - database_table_checker.py:344 - update_table_column_types() - 开始检查并更新表字段类型...
2025-08-01 09:34:53.510 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_建设用地宅基地' 的字段类型...
2025-08-01 09:34:53.511 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_房地产权表' 的字段类型...
2025-08-01 09:34:53.512 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_抵押权表' 的字段类型...
2025-08-01 09:34:53.513 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_查封登记表' 的字段类型...
2025-08-01 09:34:53.513 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_预告登记表' 的字段类型...
2025-08-01 09:34:53.514 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同乘三次以上同行人' 的字段类型...
2025-08-01 09:34:53.515 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同订单同行人已成行' 的字段类型...
2025-08-01 09:34:53.517 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同订单同行人未成行' 的字段类型...
2025-08-01 09:34:53.518 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班进出港_航班进出港已成行表' 的字段类型...
2025-08-01 09:34:53.519 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班进出港_航班进出港未成行表' 的字段类型...
2025-08-01 09:34:53.520 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券持有_持有信息' 的字段类型...
2025-08-01 09:34:53.521 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券持有变动_持' 的字段类型...
2025-08-01 09:34:53.521 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券账户_证券账户' 的字段类型...
2025-08-01 09:34:53.522 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_同订单同行人_同行人员信息表' 的字段类型...
2025-08-01 09:34:53.523 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_同订单同行人_同行人员客票' 的字段类型...
2025-08-01 09:34:53.523 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_用户注册_互联网注册信息表' 的字段类型...
2025-08-01 09:34:53.524 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_用户注册_常用联系人信息表' 的字段类型...
2025-08-01 09:34:53.525 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_铁路客票_交易信息表' 的字段类型...
2025-08-01 09:34:53.525 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_铁路客票_票面信息表' 的字段类型...
2025-08-01 09:34:53.526 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '临时账户交易明细表' 的字段类型...
2025-08-01 09:34:53.527 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_产品信息表' 的字段类型...
2025-08-01 09:34:53.528 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_受益人信息' 的字段类型...
2025-08-01 09:34:53.530 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_委托人信息' 的字段类型...
2025-08-01 09:34:53.533 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_登记信息_受益权结构' 的字段类型...
2025-08-01 09:34:53.534 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_登记信息_合同信息' 的字段类型...
2025-08-01 09:34:53.535 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_终止登记' 的字段类型...
2025-08-01 09:34:53.536 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_委托人或受益人变动信息表' 的字段类型...
2025-08-01 09:34:53.537 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_登记信息_受益权结构表' 的字段类型...
2025-08-01 09:34:53.538 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_登记信息_合同信息表' 的字段类型...
2025-08-01 09:34:53.538 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_终止登记表' 的字段类型...
2025-08-01 09:34:53.539 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_交通违法_机动车违章信息表' 的字段类型...
2025-08-01 09:34:53.541 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_出入境记录_出入境记录信息表' 的字段类型...
2025-08-01 09:34:53.543 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_出国_境_证件_出入境证件信息' 的字段类型...
2025-08-01 09:34:53.544 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同住址_同住址表' 的字段类型...
2025-08-01 09:34:53.545 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同户人_同户人表' 的字段类型...
2025-08-01 09:34:53.547 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同车违章_同车违章表' 的字段类型...
2025-08-01 09:34:53.549 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃人员_在逃人员登记信息' 的字段类型...
2025-08-01 09:34:53.550 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃同案撤销人员_在逃同案撤销人员' 的字段类型...
2025-08-01 09:34:53.551 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃撤销_在逃人员撤销信息' 的字段类型...
2025-08-01 09:34:53.552 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_户籍人口_基本人员信息表' 的字段类型...
2025-08-01 09:34:53.554 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_旅馆住宿_旅馆住宿人员信息表' 的字段类型...
2025-08-01 09:34:53.555 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_机动车_机动车信息' 的字段类型...
2025-08-01 09:34:53.556 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_驾驶证_驾驶证信息表' 的字段类型...
2025-08-01 09:34:53.557 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_住院结算数据' 的字段类型...
2025-08-01 09:34:53.558 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_参保信息' 的字段类型...
2025-08-01 09:34:53.560 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_普通门诊' 的字段类型...
2025-08-01 09:34:53.561 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_药店购药' 的字段类型...
2025-08-01 09:34:53.562 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_药店购药明细' 的字段类型...
2025-08-01 09:34:53.565 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '国家税务总局_纳税人登记信息_登记信息表' 的字段类型...
2025-08-01 09:34:53.566 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '国家税务总局_纳税信息_税务缴纳信息表' 的字段类型...
2025-08-01 09:34:53.567 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '增值税发票表' 的字段类型...
2025-08-01 09:34:53.568 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '字段匹配规则' 的字段类型...
2025-08-01 09:34:53.569 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '对手信息' 的字段类型...
2025-08-01 09:34:53.571 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '导入记录表' 的字段类型...
2025-08-01 09:34:53.572 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_主要人员表' 的字段类型...
2025-08-01 09:34:53.574 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_内资补充信息表' 的字段类型...
2025-08-01 09:34:53.575 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_农专补充信息表' 的字段类型...
2025-08-01 09:34:53.577 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_分支机构备案信息表' 的字段类型...
2025-08-01 09:34:53.579 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_变更备案信息表' 的字段类型...
2025-08-01 09:34:53.580 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_吊销信息表' 的字段类型...
2025-08-01 09:34:53.581 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_外资补充信息表' 的字段类型...
2025-08-01 09:34:53.583 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_注销信息表' 的字段类型...
2025-08-01 09:34:53.585 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_清算基本信息表' 的字段类型...
2025-08-01 09:34:53.586 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_清算成员信息表' 的字段类型...
2025-08-01 09:34:53.587 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_联络员信息表' 的字段类型...
2025-08-01 09:34:53.589 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_自然人出资信息表' 的字段类型...
2025-08-01 09:34:53.591 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_许可信息表' 的字段类型...
2025-08-01 09:34:53.592 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_财务负责人信息表' 的字段类型...
2025-08-01 09:34:53.593 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_非自然人出资信息表' 的字段类型...
2025-08-01 09:34:53.595 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业基本信息表' 的字段类型...
2025-08-01 09:34:53.596 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_统一社会信用代码_统一社会信用代码表' 的字段类型...
2025-08-01 09:34:53.596 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '开户信息表' 的字段类型...
2025-08-01 09:34:53.598 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '本地银行_客户信息本地表' 的字段类型...
2025-08-01 09:34:53.599 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '案件信息表' 的字段类型...
2025-08-01 09:34:53.600 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_投资行业信息表' 的字段类型...
2025-08-01 09:34:53.601 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_持有信息表' 的字段类型...
2025-08-01 09:34:53.602 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_理财产品信息表' 的字段类型...
2025-08-01 09:34:53.603 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '用户信息表' 的字段类型...
2025-08-01 09:34:53.604 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '电话_登记信息_运营商登记信息表' 的字段类型...
2025-08-01 09:34:53.606 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '电话_话单信息_运营商话单信息表' 的字段类型...
2025-08-01 09:34:53.606 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_专票货物或应税劳务名称表' 的字段类型...
2025-08-01 09:34:53.608 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_增值税专用发票表' 的字段类型...
2025-08-01 09:34:53.609 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_增值税普通发票表' 的字段类型...
2025-08-01 09:34:53.611 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_普票货物或应税劳务服务名' 的字段类型...
2025-08-01 09:34:53.611 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '系统信息表' 的字段类型...
2025-08-01 09:34:53.613 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '系统配置表' 的字段类型...
2025-08-01 09:34:53.614 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '虚拟运营商_登记信息_虚拟运营商登记信息表' 的字段类型...
2025-08-01 09:34:53.616 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '表类型匹配规则_导出文件名分类表' 的字段类型...
2025-08-01 09:34:53.618 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '表类型匹配规则表' 的字段类型...
2025-08-01 09:34:53.619 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '证券登记结算_证券持有变动_持' 的字段类型...
2025-08-01 09:34:53.621 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '证券登记结算_证券持有变动_证券持有变动' 的字段类型...
2025-08-01 09:34:53.622 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '财付通交易明细表' 的字段类型...
2025-08-01 09:34:53.623 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户交易明细表' 的字段类型...
2025-08-01 09:34:53.624 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_共有权优先权信息表' 的字段类型...
2025-08-01 09:34:53.625 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_关联子账户信息表' 的字段类型...
2025-08-01 09:34:53.628 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_关联子账户信息表本地' 的字段类型...
2025-08-01 09:34:53.629 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_客户基本信息表' 的字段类型...
2025-08-01 09:34:53.634 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_强制措施信息表' 的字段类型...
2025-08-01 09:34:53.636 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息（本地）_优先权信息表' 的字段类型...
2025-08-01 09:34:53.638 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '金融理财_金融理财信息表' 的字段类型...
2025-08-01 09:34:53.640 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '金融理财_金融理财账户信息表' 的字段类型...
2025-08-01 09:34:53.642 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险人员信息表' 的字段类型...
2025-08-01 09:34:53.644 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险保单信息表' 的字段类型...
2025-08-01 09:34:53.645 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险赔案信息表' 的字段类型...
2025-08-01 09:34:53.646 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_家庭财产保险表' 的字段类型...
2025-08-01 09:34:53.647 - INFO - [MainThread:5712] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_航空延误保险表' 的字段类型...
2025-08-01 09:34:53.648 - INFO - [MainThread:5712] - database_table_checker.py:404 - update_table_column_types() - 字段类型更新完成: 已更新 0 个字段, 跳过 2702 个字段, 失败 0 个字段
2025-08-01 09:34:53.722 - INFO - [MainThread:5712] - database_table_checker.py:580 - check_database_tables() - === 数据库表格检查报告 ===
2025-08-01 09:34:53.723 - INFO - [MainThread:5712] - database_table_checker.py:581 - check_database_tables() - 总计检查表格：103个 (核心9个 + Excel94个)
2025-08-01 09:34:53.724 - INFO - [MainThread:5712] - database_table_checker.py:582 - check_database_tables() - 最终存在表格：103个
2025-08-01 09:34:53.725 - INFO - [MainThread:5712] - database_table_checker.py:589 - check_database_tables() - 本次未创建任何新表格（所有表都已存在）
2025-08-01 09:35:28.378 - DEBUG - [MainThread:5712] - import_error_handler.py:468 - check_for_previous_crash() - psutil不可用，跳过PID检查
2025-08-01 09:35:28.378 - WARNING - [MainThread:5712] - import_error_handler.py:476 - check_for_previous_crash() - 检测到可能的程序崩溃，上次心跳: {'timestamp': 1753974991.1077101, 'operation': '心跳监控', 'pid': 22624, 'readable_time': '2025-07-31 23:16:31'}
2025-08-01 09:35:28.384 - WARNING - [MainThread:5712] - import_data.py:5457 - __init__() - 检测到之前的程序崩溃，将启用增强监控
2025-08-01 09:35:29.433 - INFO - [MainThread:5712] - import_error_handler.py:523 - start_heartbeat_monitor() - 心跳监控已启动
2025-08-01 09:35:29.464 - INFO - [MainThread:5712] - import_data.py:148 - __init__() - ✅ 自动化管理器已初始化
2025-08-01 09:35:31.502 - INFO - [Dummy-2:26188] - data_cleaning.py:2045 - start_cleaning() - 用户选择了 15 个清洗步骤: ['clean_customer_basic_info', 'clean_transaction_details', 'clean_account_opening_info', 'clean_special_characters', 'complement_transaction_account_fields', 'clean_numeric_account_names', 'enrich_account_opening_info', 'preprocess_data', 'match_transaction_names', 'match_certificate_numbers', 'match_opponent_names', 'check_and_correct_shoufu', 'fill_counterparty_name_with_cash', 'finalize_cleaning', 'deduplicate_all_tables']
2025-08-01 09:35:31.503 - INFO - [Dummy-2:26188] - data_cleaning.py:2063 - start_cleaning() - 开始执行清洗步骤 1/15: clean_customer_basic_info
2025-08-01 09:35:31.568 - INFO - [Dummy-2:26188] - data_cleaning.py:1654 - clean_customer_basic_info() - 🔄 开始清洗账户信息_客户基本信息表...
2025-08-01 09:35:31.597 - INFO - [Dummy-2:26188] - data_cleaning.py:1696 - clean_customer_basic_info() - ✅ 账户信息_客户基本信息表清洗完成，更新 0 条记录
2025-08-01 09:35:31.599 - INFO - [Dummy-2:26188] - data_cleaning.py:1697 - clean_customer_basic_info() - 📝 处理内容：证件类型为'账号/卡号'时，查询对象名称→卡号，证件号码→账号
2025-08-01 09:35:31.600 - INFO - [Dummy-2:26188] - data_cleaning.py:2078 - start_cleaning() - 清洗步骤 clean_customer_basic_info 完成
2025-08-01 09:35:31.601 - INFO - [Dummy-2:26188] - data_cleaning.py:2063 - start_cleaning() - 开始执行清洗步骤 2/15: clean_transaction_details
2025-08-01 09:35:31.665 - INFO - [Dummy-2:26188] - data_cleaning.py:324 - clean_transaction_details() - 开始清洗账户交易明细表
2025-08-01 09:35:31.670 - INFO - [Dummy-2:26188] - data_cleaning.py:329 - clean_transaction_details() - 🔧 清理交易账卡号和交易账号中的'-'值
2025-08-01 09:35:39.657 - INFO - [Dummy-2:26188] - data_cleaning.py:343 - clean_transaction_details() - ✅ 已清理 0 条记录中的'-'值
2025-08-01 09:35:40.062 - INFO - [Dummy-2:26188] - data_cleaning.py:361 - clean_transaction_details() - 🔍 发现 631749 条记录需要清洗币种数据
2025-08-01 09:35:40.063 - INFO - [Dummy-2:26188] - data_cleaning.py:364 - clean_transaction_details() - 🌐 标准化交易币种格式
2025-08-01 09:35:40.774 - INFO - [Dummy-2:26188] - data_cleaning.py:392 - clean_transaction_details() - ✅ 币种标准化完成，处理 0 条记录
2025-08-01 09:35:41.145 - INFO - [Dummy-2:26188] - data_cleaning.py:406 - clean_transaction_details() - 📊 账户交易明细表清洗完成统计：
2025-08-01 09:35:41.145 - INFO - [Dummy-2:26188] - data_cleaning.py:407 - clean_transaction_details() -   • 清理'-'值记录数：0
2025-08-01 09:35:41.145 - INFO - [Dummy-2:26188] - data_cleaning.py:408 - clean_transaction_details() -   • 币种标准化记录数：0
2025-08-01 09:35:41.145 - INFO - [Dummy-2:26188] - data_cleaning.py:409 - clean_transaction_details() -   • 总处理记录数：0
2025-08-01 09:35:41.146 - INFO - [Dummy-2:26188] - data_cleaning.py:2078 - start_cleaning() - 清洗步骤 clean_transaction_details 完成
2025-08-01 09:35:41.146 - INFO - [Dummy-2:26188] - data_cleaning.py:2063 - start_cleaning() - 开始执行清洗步骤 3/15: clean_account_opening_info
2025-08-01 09:35:41.203 - INFO - [Dummy-2:26188] - data_cleaning.py:426 - clean_account_opening_info() - 开始清洗开户信息表
2025-08-01 09:35:41.218 - INFO - [Dummy-2:26188] - data_cleaning.py:436 - clean_account_opening_info() - 🗑️ 删除交易账号和交易卡号都为空的记录
2025-08-01 09:35:41.232 - INFO - [Dummy-2:26188] - data_cleaning.py:446 - clean_account_opening_info() - ✅ 已删除 0 条交易账号和交易卡号都为空的记录
2025-08-01 09:35:41.234 - INFO - [Dummy-2:26188] - data_cleaning.py:456 - clean_account_opening_info() - 🔧 标准化剩余数据格式
2025-08-01 09:35:42.374 - INFO - [Dummy-2:26188] - data_cleaning.py:471 - clean_account_opening_info() - 📊 开户信息表清洗完成统计：
2025-08-01 09:35:42.375 - INFO - [Dummy-2:26188] - data_cleaning.py:472 - clean_account_opening_info() -   • 清洗前记录数：2467
2025-08-01 09:35:42.376 - INFO - [Dummy-2:26188] - data_cleaning.py:473 - clean_account_opening_info() -   • 删除无效记录：0
2025-08-01 09:35:42.376 - INFO - [Dummy-2:26188] - data_cleaning.py:474 - clean_account_opening_info() -   • 清洗后记录数：2467
2025-08-01 09:35:42.377 - INFO - [Dummy-2:26188] - data_cleaning.py:475 - clean_account_opening_info() -   • 标准化记录数：2467
2025-08-01 09:35:42.382 - INFO - [Dummy-2:26188] - data_cleaning.py:2078 - start_cleaning() - 清洗步骤 clean_account_opening_info 完成
2025-08-01 09:35:42.385 - INFO - [Dummy-2:26188] - data_cleaning.py:2063 - start_cleaning() - 开始执行清洗步骤 4/15: clean_special_characters
2025-08-01 09:35:42.453 - INFO - [Dummy-2:26188] - data_cleaning.py:1052 - clean_special_characters() - 开始清理特殊字符和无效值
