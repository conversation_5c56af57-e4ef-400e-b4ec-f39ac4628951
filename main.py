#main.py 文件
from database_setup import initialize_database
from database_table_checker import check_database_tables, is_system_ready  # 引入数据库表检查器
from gui.windows.main_window.ui_main_window import UI_MainWindow
from login_window import LoginWindow
from qt_core import *
from settings_controller import SettingsController
from tools import ToolsWindow  # 引入工具窗口
from PySide6.QtWidgets import QApplication, QMainWindow, QMessageBox
from PySide6.QtGui import QIcon
from PySide6.QtCore import QPropertyAnimation, QEasingCurve
import sys
import logging
import os
import glob
import traceback
import signal
import atexit
from datetime import datetime
from cases_controller import CasesController

# 🔧 增强：设置详细的日志系统
def setup_comprehensive_logging():
    """设置全面的日志记录系统"""
    try:
        # 创建logs目录
        os.makedirs('logs', exist_ok=True)

        # 设置日志格式
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s'

        # 配置根日志记录器
        logging.basicConfig(
            level=logging.DEBUG,
            format=log_format,
            handlers=[
                # 文件处理器 - 所有日志
                logging.FileHandler(f'logs/main_app_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8'),
                # 控制台处理器 - INFO及以上级别
                logging.StreamHandler(sys.stdout)
            ]
        )

        # 设置控制台处理器只显示INFO及以上级别
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

        # 创建专门的导入日志记录器
        import_logger = logging.getLogger('import_process')
        import_handler = logging.FileHandler(f'logs/import_process_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
        import_handler.setFormatter(logging.Formatter(log_format))
        import_logger.addHandler(import_handler)
        import_logger.setLevel(logging.DEBUG)

        # 创建错误日志记录器
        error_logger = logging.getLogger('error_tracking')
        error_handler = logging.FileHandler(f'logs/error_tracking_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
        error_handler.setFormatter(logging.Formatter(log_format))
        error_logger.addHandler(error_handler)
        error_logger.setLevel(logging.ERROR)

        logging.info("✅ 全面日志系统初始化完成")
        return True

    except Exception as e:
        print(f"❌ 日志系统初始化失败: {e}")
        return False

# 🔧 增强：异常处理和程序退出监控
def setup_exit_monitoring():
    """设置程序退出监控"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        if issubclass(exc_type, KeyboardInterrupt):
            logging.info("程序被用户中断 (Ctrl+C)")
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        error_logger = logging.getLogger('error_tracking')
        error_logger.critical("未捕获的异常导致程序退出", exc_info=(exc_type, exc_value, exc_traceback))

        # 记录详细的错误信息
        error_details = {
            'exception_type': exc_type.__name__,
            'exception_message': str(exc_value),
            'traceback': ''.join(traceback.format_tb(exc_traceback)),
            'timestamp': datetime.now().isoformat()
        }

        logging.critical(f"🚨 程序异常退出: {error_details}")

        # 尝试保存错误信息到文件
        try:
            with open(f'logs/crash_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt', 'w', encoding='utf-8') as f:
                f.write(f"程序崩溃报告\n")
                f.write(f"时间: {error_details['timestamp']}\n")
                f.write(f"异常类型: {error_details['exception_type']}\n")
                f.write(f"异常消息: {error_details['exception_message']}\n")
                f.write(f"堆栈跟踪:\n{error_details['traceback']}\n")
        except:
            pass

    def handle_signal(signum, frame):
        """信号处理器"""
        signal_names = {
            signal.SIGTERM: 'SIGTERM',
            signal.SIGINT: 'SIGINT'
        }
        signal_name = signal_names.get(signum, f'Signal {signum}')
        logging.warning(f"收到信号 {signal_name}，程序即将退出")
        sys.exit(0)

    def cleanup_on_exit():
        """程序退出时的清理函数"""
        logging.info("程序正常退出，执行清理操作")

        # 记录程序运行时间等信息
        try:
            if hasattr(cleanup_on_exit, 'start_time'):
                runtime = datetime.now() - cleanup_on_exit.start_time
                logging.info(f"程序运行时间: {runtime}")
        except:
            pass

    # 设置全局异常处理器
    sys.excepthook = handle_exception

    # 设置信号处理器
    try:
        signal.signal(signal.SIGTERM, handle_signal)
        signal.signal(signal.SIGINT, handle_signal)
    except AttributeError:
        # Windows上可能不支持某些信号
        pass

    # 设置退出时的清理函数
    atexit.register(cleanup_on_exit)
    cleanup_on_exit.start_time = datetime.now()

    logging.info("✅ 程序退出监控系统初始化完成")

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()  
        self.setWindowTitle("资金分析系统V2.3.1")
        self.setWindowIcon(QIcon('gui/images/icons/icon.ico'))  # 设置窗口图标
        # 设置主窗口
        self.ui = UI_MainWindow()
        self.ui.setup_ui(self)
        # 确保 ui_pages 被正确初始化
        self.ui_pages = self.ui.ui_pages
        # 创建设置控制器
        self.settings_controller = SettingsController(self, self.ui.ui_pages.btn_select_theme)
        # 创建案件控制器
        self.cases_controller = CasesController(self)
        # 切换按钮
        self.ui.toggle_button.clicked.connect(self.toggle_button)
        # 主页按钮
        self.ui.btn_1.clicked.connect(self.show_page_1)
        # 工具按钮
        self.ui.btn_2.clicked.connect(self.show_page_2)
        # 设置按钮
        self.ui.settings_btn.clicked.connect(self.show_page_3)
        # 选择主题按钮
        self.ui.ui_pages.btn_select_theme.setText("开灯")  # 设置初始标签
        self.ui.ui_pages.btn_select_theme.clicked.connect(self.settings_controller.toggle_theme)
        
        # 数据库配置按钮
        self.ui.database_config_btn.clicked.connect(self.show_database_config)
        
        # 退出按钮
        self.ui.ui_pages.btn_exit.clicked.connect(self.settings_controller.exit_application)
        # 创建工具窗口实例
        self.tools = ToolsWindow(self)
        # 连接工具页面按钮到工具功能
        self.ui.ui_pages.btn_merge_files.clicked.connect(self.tools.merge_files)
        self.ui.ui_pages.btn_split_table.clicked.connect(self.tools.split_table)
        self.ui.ui_pages.btn_pdf_paginate.clicked.connect(self.tools.pdf_split)
        self.ui.ui_pages.btn_pdf_to_word_excel.clicked.connect(self.tools.pdf_convert)
        # 连接数据库搜索按钮
        self.ui.ui_pages.btn_db_search.clicked.connect(self.tools.database_search)
        # 🔧 修复：恢复导出数据按钮连接
        self.ui.ui_pages.btn_export_data.clicked.connect(self.export_case_data)
        # 显示应用程序
        self.show()
        self.showMaximized()  # 自动最大化窗口

    # 重置按钮选择
    def reset_selection(self):
        for btn in self.ui.left_menu.findChildren(QPushButton):
            try:
                btn.set_active(False)
            except AttributeError:
                pass

    # 显示主页
    def show_page_1(self):
        self.reset_selection()
        self.ui.pages.setCurrentWidget(self.ui.ui_pages.page_1)
        self.ui.btn_1.set_active(True)

    # 显示工具页
    def show_page_2(self):
        self.reset_selection()
        self.ui.pages.setCurrentWidget(self.ui.ui_pages.page_2)
        self.ui.btn_2.set_active(True)

    # 显示设置页
    def show_page_3(self):
        self.reset_selection()
        self.ui.pages.setCurrentWidget(self.ui.ui_pages.page_3)
        self.ui.settings_btn.set_active(True)

    # 切换按钮
    def toggle_button(self):
        # 获取菜单宽度
        menu_width = self.ui.left_menu.width()
        # 检查宽度
        width = 50
        if menu_width == 50:
            width = 240
        # 开始动画
        self.animation = QPropertyAnimation(self.ui.left_menu, b"minimumWidth")
        self.animation.setStartValue(menu_width)
        self.animation.setEndValue(width)
        self.animation.setDuration(500)
        self.animation.setEasingCurve(QEasingCurve.InOutCirc)
        self.animation.start()

    def show_database_config(self):
        """
        显示数据库配置对话框
        """
        try:
            from database_config import show_database_config_dialog
            result = show_database_config_dialog()
            if result:
                QMessageBox.information(None, "配置完成",
                    "数据库配置已保存！\n请重新启动系统以使配置生效。")
        except Exception as config_error:
            QMessageBox.critical(None, "配置错误", f"数据库配置过程中发生错误：{config_error}")

    def export_case_data(self):
        """导出案件数据 - 按分类导出"""
        try:
            # 检查是否选择了案件
            if not hasattr(self.cases_controller, 'selected_case_id') or not self.cases_controller.selected_case_id:
                QMessageBox.warning(self, "警告", "请先在主界面选择一个案件，然后再进行导出操作")
                return

            case_id = self.cases_controller.selected_case_id
            case_name = self.cases_controller.get_case_name(case_id) or '案件'

            # 调用按分类导出功能
            from data_cleaning import export_case_data
            export_case_data(case_id, case_name)

        except Exception as e:
            QMessageBox.critical(self, "导出错误", f"导出数据时发生错误: {str(e)}")


def cleanup_temp_mapping_files():
    """
    清理临时MAPP映射文件
    
    功能描述：
    - 清理根目录下的旧版 temp_mapping.json 文件
    - 清理 mapp 文件夹中的所有 .json 临时文件
    - 在程序启动时和导入完成时调用
    """
    try:
        # 清理根目录下的旧版临时文件
        old_temp_files = [
            'temp_mapping.json',
            'temp_mapping_*.json'
        ]
        
        cleaned_count = 0
        
        for pattern in old_temp_files:
            for file_path in glob.glob(pattern):
                try:
                    os.remove(file_path)
                    print(f"✅ 删除旧版临时文件: {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"⚠️ 删除文件失败 {file_path}: {e}")
        
        # 清理 mapp 文件夹中的临时文件
        mapp_dir = 'mapp'
        if os.path.exists(mapp_dir):
            mapp_files = glob.glob(os.path.join(mapp_dir, '*.json'))
            for file_path in mapp_files:
                try:
                    os.remove(file_path)
                    print(f"✅ 删除mapp临时文件: {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"⚠️ 删除文件失败 {file_path}: {e}")
        else:
            # 如果mapp文件夹不存在，创建它
            os.makedirs(mapp_dir, exist_ok=True)
            print(f"✅ 创建mapp文件夹: {mapp_dir}")
        
        if cleaned_count > 0:
            print(f"✅ 临时文件清理完成，共清理 {cleaned_count} 个文件")
        else:
            print("✅ 无需清理临时文件")
            
    except Exception as e:
        print(f"⚠️ 清理临时文件时发生错误: {e}")

def main():
    # 🔧 增强：首先设置日志系统
    print("正在初始化日志系统...")
    setup_comprehensive_logging()
    setup_exit_monitoring()

    logging.info("🚀 程序启动开始")
    logging.info(f"Python版本: {sys.version}")
    logging.info(f"工作目录: {os.getcwd()}")
    logging.info(f"命令行参数: {sys.argv}")

    try:
        app = QApplication(sys.argv)
        app.setWindowIcon(QIcon('gui/images/icons/icon.ico'))  # 设置应用程序图标
        logging.info("✅ QApplication创建成功")

        # 🔧 增强：监控Qt应用程序事件
        def qt_message_handler(mode, context, message):
            """Qt消息处理器"""
            qt_logger = logging.getLogger('qt_messages')
            if mode == 0:  # QtDebugMsg
                qt_logger.debug(f"Qt Debug: {message}")
            elif mode == 1:  # QtWarningMsg
                qt_logger.warning(f"Qt Warning: {message}")
            elif mode == 2:  # QtCriticalMsg
                qt_logger.error(f"Qt Critical: {message}")
            elif mode == 3:  # QtFatalMsg
                qt_logger.critical(f"Qt Fatal: {message}")

        # 安装Qt消息处理器
        try:
            from PySide6.QtCore import qInstallMessageHandler
            qInstallMessageHandler(qt_message_handler)
            logging.info("✅ Qt消息处理器安装成功")
        except Exception as qt_handler_error:
            logging.warning(f"Qt消息处理器安装失败: {qt_handler_error}")

        # 清理临时MAPP文件
        logging.info("正在清理临时文件...")
        cleanup_temp_mapping_files()

    except Exception as app_init_error:
        logging.critical(f"❌ QApplication初始化失败: {app_init_error}")
        logging.critical(f"错误详情: {traceback.format_exc()}")
        sys.exit(1)
    
    # 🔧 增强：检查数据库连接状态
    logging.info("正在检查数据库连接...")
    try:
        from database_config import test_database_connection
        logging.debug("导入database_config模块成功")

        db_connected, db_message = test_database_connection()
        logging.info(f"数据库连接测试结果: connected={db_connected}, message={db_message}")

        if not db_connected:
            logging.error(f"数据库连接检查失败：{db_message}")

            # 询问用户是否配置数据库
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Question)
            msg_box.setWindowTitle("数据库连接失败")
            msg_box.setText("未找到有效的数据库连接配置。")
            msg_box.setInformativeText(f"错误信息：{db_message}\n\n是否现在配置本机数据库信息？")
            msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            msg_box.setDefaultButton(QMessageBox.Yes)

            logging.info("显示数据库配置询问对话框")
            user_choice = msg_box.exec()
            logging.info(f"用户选择: {user_choice}")

            if user_choice == QMessageBox.Yes:
                # 显示数据库配置对话框（系统启动时无需密码验证）
                try:
                    logging.info("开始数据库配置流程")
                    from database_config import show_database_config_dialog_without_password
                    result = show_database_config_dialog_without_password()
                    logging.info(f"数据库配置结果: {result}")

                    if not result:  # 用户取消配置
                        logging.warning("用户取消数据库配置，系统退出")
                        sys.exit(1)
                    else:
                        logging.info("数据库配置完成，继续系统启动")
                except Exception as config_error:
                    logging.critical(f"数据库配置过程中发生错误: {config_error}")
                    logging.critical(f"配置错误详情: {traceback.format_exc()}")
                    QMessageBox.critical(None, "配置错误", f"数据库配置过程中发生错误：{config_error}")
                    sys.exit(1)
            else:
                logging.warning("用户选择不配置数据库，系统退出")
                sys.exit(1)
        else:
            logging.info(f"✓ 数据库连接正常：{db_message}")

    except Exception as db_check_error:
        logging.error(f"数据库连接检查过程发生异常：{db_check_error}")
        logging.error(f"数据库检查异常详情: {traceback.format_exc()}")
        # 即使连接检查失败，也继续执行原有的数据库表格检查逻辑
    
    # 在系统启动时检查数据库表格完整性
    print("正在检查数据库表格完整性...")
    
    try:
        # 执行数据库表格检查
        check_results = check_database_tables()
        
        if not check_results['check_success']:
            # 数据库检查失败，提供配置数据库的选项
            error_msg = f"""
数据库表格检查失败！

检查结果：
- 必需表格总数：{check_results['total_required']}
- 已存在表格数：{len(check_results['existing_tables'])}
- 缺失表格数：{len(check_results['missing_tables'])}

错误信息：{check_results.get('error_message', '未知错误')}
"""
            
            # 创建错误对话框，提供配置选项
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("数据库初始化失败")
            msg_box.setText("数据库表格检查失败，系统无法正常启动。")
            msg_box.setDetailedText(error_msg)
            
            # 添加自定义按钮
            config_button = msg_box.addButton("配置数据库", QMessageBox.ActionRole)
            exit_button = msg_box.addButton("退出系统", QMessageBox.RejectRole)
            msg_box.setDefaultButton(config_button)
            
            msg_box.exec()
            
            if msg_box.clickedButton() == config_button:
                # 显示数据库配置对话框（系统启动时无需密码验证）
                try:
                    from database_config import show_database_config_dialog_without_password
                    result = show_database_config_dialog_without_password()
                    if result:
                        QMessageBox.information(None, "配置完成", 
                            "数据库配置已保存！\n请重新启动系统以使配置生效。")
                except Exception as config_error:
                    QMessageBox.critical(None, "配置错误", f"数据库配置过程中发生错误：{config_error}")
            
            print("数据库检查失败，系统退出。")
            sys.exit(1)
        
        else:
            # 数据库检查成功
            print(f"✓ 数据库表格检查成功！已验证{check_results['total_required']}个必需表格。")
            print(f"  - 核心系统表：{check_results['core_tables_count']}个")
            print(f"  - Excel配置表：{check_results['excel_tables_count']}个")
            if check_results['created_tables']:
                print(f"✓ 自动创建了{len(check_results['created_tables'])}个缺失表格。")
    
    except Exception as e:
        # 检查过程出现异常
        error_msg = f"数据库检查过程中发生异常：{str(e)}"
        print(f"✗ {error_msg}")
        
        # 显示错误对话框
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle("系统启动错误")
        msg_box.setText("数据库检查过程中发生异常。")
        msg_box.setDetailedText(error_msg)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec()
        
        sys.exit(1)
    
    # 数据库检查通过后，继续原有的初始化流程
    try:
        # 初始化数据库（保留原有功能，用于创建索引等）
        initialize_database()
        print("✓ 数据库初始化完成。")
    except Exception as e:
        print(f"数据库初始化警告：{e}")
        logging.warning(f"数据库初始化过程中出现警告：{e}")
    
    # 🔧 增强：显示登录界面
    try:
        logging.info("创建登录窗口")
        login_window = LoginWindow()
        logging.info("显示登录窗口")
        login_window.show()

        logging.info("系统启动完成，显示登录界面")

        # 🔧 增强：监控应用程序执行
        logging.info("开始Qt事件循环")
        exit_code = app.exec()
        logging.info(f"Qt事件循环结束，退出码: {exit_code}")

        sys.exit(exit_code)

    except Exception as startup_error:
        logging.critical(f"❌ 系统启动失败: {startup_error}")
        logging.critical(f"启动错误详情: {traceback.format_exc()}")

        # 显示错误对话框
        try:
            QMessageBox.critical(None, "系统启动失败",
                f"系统启动过程中发生错误：\n{startup_error}\n\n请查看日志文件获取详细信息。")
        except:
            pass

        sys.exit(1)

if __name__ == "__main__":
    main() 