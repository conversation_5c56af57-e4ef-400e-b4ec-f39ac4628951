#main.py 文件
from database_setup import initialize_database
from database_table_checker import check_database_tables, is_system_ready  # 引入数据库表检查器
from gui.windows.main_window.ui_main_window import UI_MainWindow
from login_window import LoginWindow
from qt_core import *
from settings_controller import SettingsController
from tools import ToolsWindow  # 引入工具窗口
from PySide6.QtWidgets import QApplication, QMainWindow, QMessageBox
from PySide6.QtGui import QIcon
from PySide6.QtCore import QPropertyAnimation, QEasingCurve
import sys
import logging
import os
import glob
from cases_controller import CasesController

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()  
        self.setWindowTitle("资金分析系统V2.3.1")
        self.setWindowIcon(QIcon('gui/images/icons/icon.ico'))  # 设置窗口图标
        # 设置主窗口
        self.ui = UI_MainWindow()
        self.ui.setup_ui(self)
        # 确保 ui_pages 被正确初始化
        self.ui_pages = self.ui.ui_pages
        # 创建设置控制器
        self.settings_controller = SettingsController(self, self.ui.ui_pages.btn_select_theme)
        # 创建案件控制器
        self.cases_controller = CasesController(self)
        # 切换按钮
        self.ui.toggle_button.clicked.connect(self.toggle_button)
        # 主页按钮
        self.ui.btn_1.clicked.connect(self.show_page_1)
        # 工具按钮
        self.ui.btn_2.clicked.connect(self.show_page_2)
        # 设置按钮
        self.ui.settings_btn.clicked.connect(self.show_page_3)
        # 选择主题按钮
        self.ui.ui_pages.btn_select_theme.setText("开灯")  # 设置初始标签
        self.ui.ui_pages.btn_select_theme.clicked.connect(self.settings_controller.toggle_theme)
        
        # 数据库配置按钮
        self.ui.database_config_btn.clicked.connect(self.show_database_config)
        
        # 退出按钮
        self.ui.ui_pages.btn_exit.clicked.connect(self.settings_controller.exit_application)
        # 创建工具窗口实例
        self.tools = ToolsWindow(self)
        # 连接工具页面按钮到工具功能
        self.ui.ui_pages.btn_merge_files.clicked.connect(self.tools.merge_files)
        self.ui.ui_pages.btn_split_table.clicked.connect(self.tools.split_table)
        self.ui.ui_pages.btn_pdf_paginate.clicked.connect(self.tools.pdf_split)
        self.ui.ui_pages.btn_pdf_to_word_excel.clicked.connect(self.tools.pdf_convert)
        # 连接数据库搜索按钮
        self.ui.ui_pages.btn_db_search.clicked.connect(self.tools.database_search)
        # 🔧 修复：恢复导出数据按钮连接
        self.ui.ui_pages.btn_export_data.clicked.connect(self.export_case_data)
        # 显示应用程序
        self.show()
        self.showMaximized()  # 自动最大化窗口

    # 重置按钮选择
    def reset_selection(self):
        for btn in self.ui.left_menu.findChildren(QPushButton):
            try:
                btn.set_active(False)
            except AttributeError:
                pass

    # 显示主页
    def show_page_1(self):
        self.reset_selection()
        self.ui.pages.setCurrentWidget(self.ui.ui_pages.page_1)
        self.ui.btn_1.set_active(True)

    # 显示工具页
    def show_page_2(self):
        self.reset_selection()
        self.ui.pages.setCurrentWidget(self.ui.ui_pages.page_2)
        self.ui.btn_2.set_active(True)

    # 显示设置页
    def show_page_3(self):
        self.reset_selection()
        self.ui.pages.setCurrentWidget(self.ui.ui_pages.page_3)
        self.ui.settings_btn.set_active(True)

    # 切换按钮
    def toggle_button(self):
        # 获取菜单宽度
        menu_width = self.ui.left_menu.width()
        # 检查宽度
        width = 50
        if menu_width == 50:
            width = 240
        # 开始动画
        self.animation = QPropertyAnimation(self.ui.left_menu, b"minimumWidth")
        self.animation.setStartValue(menu_width)
        self.animation.setEndValue(width)
        self.animation.setDuration(500)
        self.animation.setEasingCurve(QEasingCurve.InOutCirc)
        self.animation.start()

    def show_database_config(self):
        """
        显示数据库配置对话框
        """
        try:
            from database_config import show_database_config_dialog
            result = show_database_config_dialog()
            if result:
                QMessageBox.information(None, "配置完成",
                    "数据库配置已保存！\n请重新启动系统以使配置生效。")
        except Exception as config_error:
            QMessageBox.critical(None, "配置错误", f"数据库配置过程中发生错误：{config_error}")

    def export_case_data(self):
        """导出案件数据 - 按分类导出"""
        try:
            # 检查是否选择了案件
            if not hasattr(self.cases_controller, 'selected_case_id') or not self.cases_controller.selected_case_id:
                QMessageBox.warning(self, "警告", "请先在主界面选择一个案件，然后再进行导出操作")
                return

            case_id = self.cases_controller.selected_case_id
            case_name = self.cases_controller.get_case_name(case_id) or '案件'

            # 调用按分类导出功能
            from data_cleaning import export_case_data
            export_case_data(case_id, case_name)

        except Exception as e:
            QMessageBox.critical(self, "导出错误", f"导出数据时发生错误: {str(e)}")


def cleanup_temp_mapping_files():
    """
    清理临时MAPP映射文件
    
    功能描述：
    - 清理根目录下的旧版 temp_mapping.json 文件
    - 清理 mapp 文件夹中的所有 .json 临时文件
    - 在程序启动时和导入完成时调用
    """
    try:
        # 清理根目录下的旧版临时文件
        old_temp_files = [
            'temp_mapping.json',
            'temp_mapping_*.json'
        ]
        
        cleaned_count = 0
        
        for pattern in old_temp_files:
            for file_path in glob.glob(pattern):
                try:
                    os.remove(file_path)
                    print(f"✅ 删除旧版临时文件: {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"⚠️ 删除文件失败 {file_path}: {e}")
        
        # 清理 mapp 文件夹中的临时文件
        mapp_dir = 'mapp'
        if os.path.exists(mapp_dir):
            mapp_files = glob.glob(os.path.join(mapp_dir, '*.json'))
            for file_path in mapp_files:
                try:
                    os.remove(file_path)
                    print(f"✅ 删除mapp临时文件: {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"⚠️ 删除文件失败 {file_path}: {e}")
        else:
            # 如果mapp文件夹不存在，创建它
            os.makedirs(mapp_dir, exist_ok=True)
            print(f"✅ 创建mapp文件夹: {mapp_dir}")
        
        if cleaned_count > 0:
            print(f"✅ 临时文件清理完成，共清理 {cleaned_count} 个文件")
        else:
            print("✅ 无需清理临时文件")
            
    except Exception as e:
        print(f"⚠️ 清理临时文件时发生错误: {e}")

def main():
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon('gui/images/icons/icon.ico'))  # 设置应用程序图标
    
    # 清理临时MAPP文件
    print("正在清理临时文件...")
    cleanup_temp_mapping_files()
    
    # 检查数据库连接状态
    print("正在检查数据库连接...")
    try:
        from database_config import test_database_connection
        db_connected, db_message = test_database_connection()
        
        if not db_connected:
            print(f"数据库连接检查失败：{db_message}")
            
            # 询问用户是否配置数据库
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Question)
            msg_box.setWindowTitle("数据库连接失败")
            msg_box.setText("未找到有效的数据库连接配置。")
            msg_box.setInformativeText(f"错误信息：{db_message}\n\n是否现在配置本机数据库信息？")
            msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            msg_box.setDefaultButton(QMessageBox.Yes)
            
            if msg_box.exec() == QMessageBox.Yes:
                # 显示数据库配置对话框（系统启动时无需密码验证）
                try:
                    from database_config import show_database_config_dialog_without_password
                    result = show_database_config_dialog_without_password()
                    if not result:  # 用户取消配置
                        print("用户取消数据库配置，系统退出。")
                        sys.exit(1)
                    else:
                        print("数据库配置完成，继续系统启动。")
                except Exception as config_error:
                    QMessageBox.critical(None, "配置错误", f"数据库配置过程中发生错误：{config_error}")
                    sys.exit(1)
            else:
                print("用户选择不配置数据库，系统退出。")
                sys.exit(1)
        else:
            print(f"✓ 数据库连接正常：{db_message}")
    
    except Exception as db_check_error:
        print(f"数据库连接检查过程发生异常：{db_check_error}")
        # 即使连接检查失败，也继续执行原有的数据库表格检查逻辑
    
    # 在系统启动时检查数据库表格完整性
    print("正在检查数据库表格完整性...")
    
    try:
        # 执行数据库表格检查
        check_results = check_database_tables()
        
        if not check_results['check_success']:
            # 数据库检查失败，提供配置数据库的选项
            error_msg = f"""
数据库表格检查失败！

检查结果：
- 必需表格总数：{check_results['total_required']}
- 已存在表格数：{len(check_results['existing_tables'])}
- 缺失表格数：{len(check_results['missing_tables'])}

错误信息：{check_results.get('error_message', '未知错误')}
"""
            
            # 创建错误对话框，提供配置选项
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("数据库初始化失败")
            msg_box.setText("数据库表格检查失败，系统无法正常启动。")
            msg_box.setDetailedText(error_msg)
            
            # 添加自定义按钮
            config_button = msg_box.addButton("配置数据库", QMessageBox.ActionRole)
            exit_button = msg_box.addButton("退出系统", QMessageBox.RejectRole)
            msg_box.setDefaultButton(config_button)
            
            msg_box.exec()
            
            if msg_box.clickedButton() == config_button:
                # 显示数据库配置对话框（系统启动时无需密码验证）
                try:
                    from database_config import show_database_config_dialog_without_password
                    result = show_database_config_dialog_without_password()
                    if result:
                        QMessageBox.information(None, "配置完成", 
                            "数据库配置已保存！\n请重新启动系统以使配置生效。")
                except Exception as config_error:
                    QMessageBox.critical(None, "配置错误", f"数据库配置过程中发生错误：{config_error}")
            
            print("数据库检查失败，系统退出。")
            sys.exit(1)
        
        else:
            # 数据库检查成功
            print(f"✓ 数据库表格检查成功！已验证{check_results['total_required']}个必需表格。")
            print(f"  - 核心系统表：{check_results['core_tables_count']}个")
            print(f"  - Excel配置表：{check_results['excel_tables_count']}个")
            if check_results['created_tables']:
                print(f"✓ 自动创建了{len(check_results['created_tables'])}个缺失表格。")
    
    except Exception as e:
        # 检查过程出现异常
        error_msg = f"数据库检查过程中发生异常：{str(e)}"
        print(f"✗ {error_msg}")
        
        # 显示错误对话框
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle("系统启动错误")
        msg_box.setText("数据库检查过程中发生异常。")
        msg_box.setDetailedText(error_msg)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec()
        
        sys.exit(1)
    
    # 数据库检查通过后，继续原有的初始化流程
    try:
        # 初始化数据库（保留原有功能，用于创建索引等）
        initialize_database()
        print("✓ 数据库初始化完成。")
    except Exception as e:
        print(f"数据库初始化警告：{e}")
        logging.warning(f"数据库初始化过程中出现警告：{e}")
    
    # 显示登录界面
    login_window = LoginWindow()
    login_window.show()
    
    print("系统启动完成，显示登录界面。")
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 