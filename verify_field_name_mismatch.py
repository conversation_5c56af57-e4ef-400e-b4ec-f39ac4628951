#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证字段名称不匹配问题

本文件的功能和实现逻辑：
1. 验证临时表和正式表中IP地址、MAC地址字段的名称差异
2. 检查正式表中ip地址、mac地址字段的数据
3. 确认问题的根本原因
"""

import logging
from datetime import datetime
from database_setup import get_db_connection

# 设置日志
log_file = f'verify_field_name_mismatch_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def check_field_names():
    """检查字段名称"""
    logging.info("🔍 检查字段名称...")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查临时表字段
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = '临时账户交易明细表'
            AND column_name IN ('IP地址', 'MAC地址', 'ip地址', 'mac地址')
            ORDER BY column_name
        """)
        temp_fields = [row[0] for row in cursor.fetchall()]
        
        # 检查正式表字段
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = '账户交易明细表'
            AND column_name IN ('IP地址', 'MAC地址', 'ip地址', 'mac地址')
            ORDER BY column_name
        """)
        main_fields = [row[0] for row in cursor.fetchall()]
        
        logging.info("=" * 50)
        logging.info("字段名称对比:")
        logging.info("=" * 50)
        logging.info(f"临时表字段: {temp_fields}")
        logging.info(f"正式表字段: {main_fields}")
        
        cursor.close()
        conn.close()
        
        return temp_fields, main_fields
        
    except Exception as e:
        logging.error(f"❌ 检查字段名称时出错: {e}")
        return [], []

def check_main_table_data():
    """检查正式表中的数据"""
    logging.info("\n🔍 检查正式表中的数据...")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查ip地址字段的数据
        cursor.execute("""
            SELECT "ip地址", COUNT(*) as count
            FROM 账户交易明细表
            WHERE "ip地址" IS NOT NULL AND "ip地址" != ''
            GROUP BY "ip地址"
            ORDER BY count DESC
            LIMIT 10
        """)
        ip_data = cursor.fetchall()
        
        # 检查mac地址字段的数据
        cursor.execute("""
            SELECT "mac地址", COUNT(*) as count
            FROM 账户交易明细表
            WHERE "mac地址" IS NOT NULL AND "mac地址" != ''
            GROUP BY "mac地址"
            ORDER BY count DESC
            LIMIT 10
        """)
        mac_data = cursor.fetchall()
        
        # 检查交易发生地字段的数据
        cursor.execute("""
            SELECT "交易发生地", COUNT(*) as count
            FROM 账户交易明细表
            WHERE "交易发生地" IS NOT NULL AND "交易发生地" != ''
            GROUP BY "交易发生地"
            ORDER BY count DESC
            LIMIT 10
        """)
        location_data = cursor.fetchall()
        
        logging.info("=" * 50)
        logging.info("正式表数据样本:")
        logging.info("=" * 50)
        
        logging.info("ip地址字段数据 (前10个):")
        for ip, count in ip_data:
            logging.info(f"  {ip}: {count} 条")
        
        logging.info("\nmac地址字段数据 (前10个):")
        for mac, count in mac_data:
            logging.info(f"  {mac}: {count} 条")
        
        logging.info("\n交易发生地字段数据 (前10个):")
        for location, count in location_data:
            logging.info(f"  {location}: {count} 条")
        
        cursor.close()
        conn.close()
        
        return ip_data, mac_data, location_data
        
    except Exception as e:
        logging.error(f"❌ 检查正式表数据时出错: {e}")
        return [], [], []

def analyze_data_content(ip_data, mac_data, location_data):
    """分析数据内容"""
    logging.info("\n🔍 分析数据内容...")
    
    # 检查交易发生地中是否包含IP地址或MAC地址格式的数据
    ip_like_in_location = []
    mac_like_in_location = []
    
    for location, count in location_data:
        location_str = str(location)
        
        # 检查是否像IP地址 (x.x.x.x格式)
        if '.' in location_str and len(location_str.split('.')) == 4:
            try:
                parts = location_str.split('.')
                if all(part.isdigit() and 0 <= int(part) <= 255 for part in parts):
                    ip_like_in_location.append((location, count))
            except:
                pass
        
        # 检查是否像MAC地址 (xx:xx:xx:xx:xx:xx或xx-xx-xx-xx-xx-xx格式)
        if (':' in location_str or '-' in location_str) and len(location_str) >= 12:
            if ':' in location_str:
                parts = location_str.split(':')
            else:
                parts = location_str.split('-')
            
            if len(parts) == 6 and all(len(part) == 2 for part in parts):
                try:
                    # 检查是否都是十六进制
                    all(int(part, 16) for part in parts)
                    mac_like_in_location.append((location, count))
                except:
                    pass
    
    logging.info("=" * 50)
    logging.info("数据内容分析:")
    logging.info("=" * 50)
    
    if ip_like_in_location:
        logging.error("❌ 在交易发生地字段中发现疑似IP地址数据:")
        for location, count in ip_like_in_location:
            logging.error(f"  {location}: {count} 条")
    else:
        logging.info("✅ 交易发生地字段中未发现IP地址格式数据")
    
    if mac_like_in_location:
        logging.error("❌ 在交易发生地字段中发现疑似MAC地址数据:")
        for location, count in mac_like_in_location:
            logging.error(f"  {location}: {count} 条")
    else:
        logging.info("✅ 交易发生地字段中未发现MAC地址格式数据")
    
    return ip_like_in_location, mac_like_in_location

def main():
    """主函数"""
    print(f"🔍 开始验证字段名称不匹配问题")
    print(f"📄 详细日志保存到: {log_file}")
    
    # 检查字段名称
    logging.info("=" * 60)
    logging.info("步骤1: 检查字段名称")
    logging.info("=" * 60)
    temp_fields, main_fields = check_field_names()
    
    # 检查正式表数据
    logging.info("=" * 60)
    logging.info("步骤2: 检查正式表数据")
    logging.info("=" * 60)
    ip_data, mac_data, location_data = check_main_table_data()
    
    # 分析数据内容
    logging.info("=" * 60)
    logging.info("步骤3: 分析数据内容")
    logging.info("=" * 60)
    ip_like_in_location, mac_like_in_location = analyze_data_content(ip_data, mac_data, location_data)
    
    # 总结
    print("\\n" + "=" * 60)
    print("🎯 验证结果总结")
    print("=" * 60)
    
    print("📊 字段名称对比:")
    print(f"  临时表: {temp_fields}")
    print(f"  正式表: {main_fields}")
    
    if 'IP地址' in temp_fields and 'ip地址' in main_fields:
        print("❌ 发现字段名称大小写不匹配: IP地址 vs ip地址")
    
    if 'MAC地址' in temp_fields and 'mac地址' in main_fields:
        print("❌ 发现字段名称大小写不匹配: MAC地址 vs mac地址")
    
    print(f"\\n📊 数据分析:")
    print(f"  ip地址字段有数据: {len(ip_data) > 0}")
    print(f"  mac地址字段有数据: {len(mac_data) > 0}")
    print(f"  交易发生地中疑似IP数据: {len(ip_like_in_location)} 种")
    print(f"  交易发生地中疑似MAC数据: {len(mac_like_in_location)} 种")
    
    print("\\n🎯 结论:")
    if 'IP地址' in temp_fields and 'ip地址' in main_fields:
        print("✅ 确认问题根源: 字段名称大小写不匹配导致转存失败")
        print("   - 临时表: IP地址、MAC地址 (大写)")
        print("   - 正式表: ip地址、mac地址 (小写)")
        print("   - 结果: 这些字段不在common_columns中，未被转存")
    
    if ip_like_in_location or mac_like_in_location:
        print("❌ 在交易发生地字段中发现了IP/MAC格式的数据")
        print("   这证实了用户报告的问题")
    
    return 0

if __name__ == "__main__":
    exit(main())
