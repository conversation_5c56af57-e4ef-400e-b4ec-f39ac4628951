# import_error_handler.py
"""
数据导入错误处理和内存管理模块

功能描述：
1. 提供统一的异常处理机制
2. 内存监控和管理
3. 数据库连接池管理
4. 导入过程的错误恢复
5. 日志记录和错误报告

实现逻辑：
- 使用装饰器模式提供异常处理
- 监控内存使用情况，防止内存溢出
- 提供数据库连接重试机制
- 记录详细的错误信息用于调试
"""

import logging
import traceback
import gc
import os
import time
import functools
from typing import Optional, Callable, Any

# 尝试导入psutil，如果不存在则使用替代方案
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logging.warning("psutil模块不可用，将使用替代的内存监控方案")

# 尝试导入PySide6，如果不存在则跳过GUI相关功能
try:
    from PySide6.QtWidgets import QMessageBox
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    logging.warning("PySide6模块不可用，将跳过GUI错误对话框功能")


class ImportErrorHandler:
    """数据导入错误处理器"""
    
    def __init__(self):
        self.error_count = 0
        self.max_errors = 10  # 最大允许错误数
        self.memory_threshold = 85  # 内存使用率阈值（百分比）
        
    def safe_execute(self, func: Callable, *args, **kwargs) -> tuple[bool, Any]:
        """
        安全执行函数，捕获所有异常
        
        返回:
            (success: bool, result: Any) - 执行结果
        """
        try:
            result = func(*args, **kwargs)
            return True, result
        except Exception as e:
            self.error_count += 1
            logging.error(f"执行函数 {func.__name__} 时发生错误: {e}")
            logging.error(f"详细错误堆栈: {traceback.format_exc()}")
            return False, str(e)
    
    def check_memory_usage(self) -> bool:
        """
        检查内存使用情况

        返回:
            bool - 内存使用是否正常
        """
        try:
            if PSUTIL_AVAILABLE:
                memory_percent = psutil.virtual_memory().percent
                if memory_percent > self.memory_threshold:
                    logging.warning(f"内存使用率过高: {memory_percent}%")
                    # 尝试释放内存
                    gc.collect()
                    time.sleep(1)  # 等待垃圾回收完成

                    # 再次检查
                    memory_percent = psutil.virtual_memory().percent
                    if memory_percent > self.memory_threshold:
                        logging.error(f"内存释放后仍然过高: {memory_percent}%")
                        return False
                    else:
                        logging.info(f"内存释放成功，当前使用率: {memory_percent}%")

                return True
            else:
                # 如果psutil不可用，使用简单的垃圾回收
                gc.collect()
                logging.info("执行了垃圾回收（psutil不可用，无法检查具体内存使用率）")
                return True

        except Exception as e:
            logging.error(f"检查内存使用时出错: {e}")
            return True  # 如果检查失败，假设内存正常
    
    def force_memory_cleanup(self):
        """强制内存清理"""
        try:
            logging.info("开始强制内存清理...")
            
            # 多次垃圾回收
            for i in range(3):
                collected = gc.collect()
                logging.info(f"垃圾回收第 {i+1} 次，回收对象数: {collected}")
                time.sleep(0.5)
            
            # 检查清理效果
            if PSUTIL_AVAILABLE:
                memory_percent = psutil.virtual_memory().percent
                logging.info(f"内存清理完成，当前使用率: {memory_percent}%")
            else:
                logging.info("内存清理完成（psutil不可用，无法显示具体使用率）")
            
        except Exception as e:
            logging.error(f"强制内存清理时出错: {e}")
    
    def should_continue_import(self) -> bool:
        """
        判断是否应该继续导入
        
        返回:
            bool - 是否继续导入
        """
        # 检查错误数量
        if self.error_count >= self.max_errors:
            logging.error(f"错误数量过多 ({self.error_count})，停止导入")
            return False
        
        # 检查内存使用
        if not self.check_memory_usage():
            logging.error("内存使用率过高，停止导入")
            return False
        
        return True
    
    def reset_error_count(self):
        """重置错误计数"""
        self.error_count = 0
        logging.info("错误计数已重置")


def safe_import_decorator(error_handler: ImportErrorHandler):
    """
    安全导入装饰器
    
    用法:
        @safe_import_decorator(error_handler)
        def import_function():
            # 导入逻辑
            pass
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 检查是否应该继续导入
            if not error_handler.should_continue_import():
                logging.error(f"跳过函数 {func.__name__} 的执行")
                return None
            
            # 执行前检查内存
            if not error_handler.check_memory_usage():
                error_handler.force_memory_cleanup()
            
            # 安全执行函数
            success, result = error_handler.safe_execute(func, *args, **kwargs)
            
            if not success:
                logging.error(f"函数 {func.__name__} 执行失败: {result}")
                return None
            
            return result
        return wrapper
    return decorator


class DatabaseConnectionManager:
    """数据库连接管理器"""
    
    def __init__(self, max_retries: int = 3, retry_delay: float = 2.0):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.connection_errors = 0
    
    def get_connection_with_retry(self, get_connection_func: Callable):
        """
        带重试机制的数据库连接获取
        
        参数:
            get_connection_func: 获取数据库连接的函数
            
        返回:
            数据库连接对象或None
        """
        for attempt in range(self.max_retries):
            try:
                conn = get_connection_func()
                if conn:
                    self.connection_errors = 0  # 重置错误计数
                    return conn
                else:
                    raise Exception("数据库连接返回None")
                    
            except Exception as e:
                self.connection_errors += 1
                logging.warning(f"数据库连接失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                
                if attempt < self.max_retries - 1:
                    wait_time = self.retry_delay * (attempt + 1)
                    logging.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logging.error("数据库连接重试失败，已达到最大重试次数")
        
        return None
    
    def safe_close_connection(self, conn, cursor=None):
        """安全关闭数据库连接"""
        try:
            if cursor:
                cursor.close()
        except Exception as e:
            logging.warning(f"关闭游标时出错: {e}")
        
        try:
            if conn:
                conn.close()
        except Exception as e:
            logging.warning(f"关闭数据库连接时出错: {e}")


def show_error_dialog(parent, title: str, message: str, detailed_error: Optional[str] = None):
    """
    显示错误对话框

    参数:
        parent: 父窗口
        title: 对话框标题
        message: 错误消息
        detailed_error: 详细错误信息
    """
    try:
        if PYSIDE6_AVAILABLE:
            msg_box = QMessageBox(parent)
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)

            if detailed_error:
                msg_box.setDetailedText(detailed_error)

            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec()
        else:
            # 如果PySide6不可用，只记录到日志
            logging.error(f"错误对话框: {title} - {message}")
            if detailed_error:
                logging.error(f"详细错误: {detailed_error}")

    except Exception as e:
        logging.error(f"显示错误对话框时出错: {e}")


# 全局错误处理器实例
global_error_handler = ImportErrorHandler()
global_db_manager = DatabaseConnectionManager()


def get_error_handler() -> ImportErrorHandler:
    """获取全局错误处理器"""
    return global_error_handler


def get_db_manager() -> DatabaseConnectionManager:
    """获取全局数据库管理器"""
    return global_db_manager


class ProcessMonitor:
    """进程监控器，用于监控导入过程是否正常运行"""

    def __init__(self):
        self.last_activity_time = time.time()
        self.activity_timeout = 300  # 5分钟超时
        self.is_monitoring = False
        self.current_operation = "初始化"

    def update_activity(self, operation: str = None):
        """更新活动时间"""
        self.last_activity_time = time.time()
        if operation:
            self.current_operation = operation
            logging.info(f"进程监控: 当前操作 - {operation}")

    def check_timeout(self) -> bool:
        """检查是否超时"""
        if not self.is_monitoring:
            return False

        current_time = time.time()
        elapsed = current_time - self.last_activity_time

        if elapsed > self.activity_timeout:
            logging.error(f"进程监控: 操作超时! 当前操作: {self.current_operation}, 已等待: {elapsed:.1f}秒")
            return True

        return False

    def start_monitoring(self):
        """开始监控"""
        self.is_monitoring = True
        self.last_activity_time = time.time()
        logging.info("进程监控: 开始监控导入过程")

    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        logging.info("进程监控: 停止监控导入过程")


class UIResponseChecker:
    """UI响应检查器"""

    def __init__(self):
        self.ui_freeze_threshold = 30  # 30秒UI无响应阈值

    def check_ui_response(self, app) -> bool:
        """检查UI是否响应"""
        try:
            if PYSIDE6_AVAILABLE and app:
                # 处理待处理的事件
                app.processEvents()
                return True
            return True
        except Exception as e:
            logging.error(f"UI响应检查失败: {e}")
            return False

    def force_ui_update(self, app):
        """强制UI更新"""
        try:
            if PYSIDE6_AVAILABLE and app:
                for _ in range(10):  # 多次处理事件
                    app.processEvents()
                    time.sleep(0.01)
                logging.info("强制UI更新完成")
        except Exception as e:
            logging.error(f"强制UI更新失败: {e}")


# 全局监控器实例
global_process_monitor = ProcessMonitor()
global_ui_checker = UIResponseChecker()


def get_process_monitor() -> ProcessMonitor:
    """获取全局进程监控器"""
    return global_process_monitor


def get_ui_checker() -> UIResponseChecker:
    """获取全局UI检查器"""
    return global_ui_checker


class CrashDetector:
    """程序崩溃检测器"""

    def __init__(self):
        self.heartbeat_file = "import_heartbeat.txt"
        self.last_heartbeat = time.time()
        self.heartbeat_interval = 10  # 每10秒更新一次心跳
        self.crash_threshold = 30  # 30秒无心跳认为崩溃

    def update_heartbeat(self, operation: str = "运行中"):
        """更新心跳文件"""
        try:
            current_time = time.time()

            # 🔧 修复：验证时间戳是否合理
            import datetime
            if current_time < 1600000000 or current_time > 2000000000:  # 大约2020-2033年范围
                logging.warning(f"检测到异常的系统时间戳: {current_time}, 跳过心跳更新")
                return

            self.last_heartbeat = current_time

            heartbeat_info = {
                'timestamp': current_time,
                'operation': operation,
                'pid': os.getpid() if hasattr(os, 'getpid') else 'unknown',
                'readable_time': datetime.datetime.fromtimestamp(current_time).strftime('%Y-%m-%d %H:%M:%S')
            }

            with open(self.heartbeat_file, 'w', encoding='utf-8') as f:
                import json
                json.dump(heartbeat_info, f, indent=2)

        except Exception as e:
            logging.error(f"更新心跳文件失败: {e}")

    def check_for_previous_crash(self) -> bool:
        """检查是否有之前的崩溃"""
        try:
            if not os.path.exists(self.heartbeat_file):
                return False

            with open(self.heartbeat_file, 'r', encoding='utf-8') as f:
                import json
                heartbeat_info = json.load(f)

            last_time = heartbeat_info.get('timestamp', 0)
            current_time = time.time()

            # 🔧 修复：检查时间戳是否合理（避免异常时间戳）
            # 检查时间戳是否在合理范围内（2020-2033年）
            if last_time < 1600000000 or last_time > 2000000000:
                logging.warning(f"检测到异常的心跳时间戳，清理心跳文件: {heartbeat_info}")
                self.cleanup_heartbeat()
                return False

            # 如果时间戳比当前时间大（未来时间），也认为异常
            if last_time > current_time + 3600:  # 超过1小时的未来时间
                logging.warning(f"检测到未来的心跳时间戳，清理心跳文件: {heartbeat_info}")
                self.cleanup_heartbeat()
                return False

            # 如果时间戳太老（超过7天），也认为异常
            if current_time - last_time > 86400 * 7:  # 超过7天
                logging.warning(f"检测到过期的心跳时间戳，清理心跳文件: {heartbeat_info}")
                self.cleanup_heartbeat()
                return False

            # 🔧 修复：更智能的崩溃判断逻辑
            time_diff = current_time - last_time

            # 如果时间差在合理范围内，不认为是崩溃
            if time_diff <= self.crash_threshold:
                return False

            # 如果时间差过大（超过24小时），可能是正常的长时间关闭，清理文件
            if time_diff > 86400:  # 24小时
                logging.info(f"检测到长时间未使用（{time_diff/3600:.1f}小时），清理心跳文件")
                self.cleanup_heartbeat()
                return False

            # 如果时间差在崩溃阈值和24小时之间，才认为可能是崩溃
            if time_diff > self.crash_threshold:
                # 🔧 修复：检查PID是否仍在运行，避免误报
                try:
                    old_pid = heartbeat_info.get('pid')
                    if old_pid and isinstance(old_pid, (int, str)):
                        try:
                            import psutil
                            if psutil.pid_exists(int(old_pid)):
                                # 如果进程仍在运行，可能是多实例，清理文件
                                logging.info(f"检测到PID {old_pid} 仍在运行，清理心跳文件避免冲突")
                                self.cleanup_heartbeat()
                                return False
                        except ImportError:
                            # 如果psutil不可用，继续原有逻辑
                            logging.debug("psutil不可用，跳过PID检查")
                        except Exception as pid_error:
                            # 如果PID检查出错，继续原有逻辑
                            logging.debug(f"PID检查时出错: {pid_error}")
                except (ValueError, Exception) as e:
                    # 如果PID无效或其他错误，继续原有逻辑
                    logging.debug(f"PID处理时出错: {e}")

                logging.warning(f"检测到可能的程序崩溃，上次心跳: {heartbeat_info}")
                return True

            return False

        except Exception as e:
            logging.error(f"检查崩溃状态失败: {e}")
            # 🔧 修复：如果检查失败，清理可能损坏的心跳文件
            try:
                self.cleanup_heartbeat()
            except:
                pass
            return False

    def cleanup_heartbeat(self):
        """清理心跳文件"""
        try:
            if os.path.exists(self.heartbeat_file):
                os.remove(self.heartbeat_file)
                logging.info("心跳文件已清理")
        except Exception as e:
            logging.error(f"清理心跳文件失败: {e}")

    def force_cleanup_invalid_heartbeat(self):
        """强制清理无效的心跳文件"""
        try:
            if os.path.exists(self.heartbeat_file):
                logging.info("检测到残留的心跳文件，正在清理...")
                os.remove(self.heartbeat_file)
                logging.info("✅ 残留心跳文件已清理")
        except Exception as e:
            logging.error(f"强制清理心跳文件失败: {e}")

    def start_heartbeat_monitor(self):
        """启动心跳监控"""
        def heartbeat_worker():
            while True:
                try:
                    self.update_heartbeat("心跳监控")
                    time.sleep(self.heartbeat_interval)
                except Exception as e:
                    logging.error(f"心跳监控出错: {e}")
                    break

        import threading
        heartbeat_thread = threading.Thread(target=heartbeat_worker, daemon=True)
        heartbeat_thread.start()
        logging.info("心跳监控已启动")


class SystemResourceMonitor:
    """系统资源监控器"""

    def __init__(self):
        self.cpu_threshold = 90  # CPU使用率阈值
        self.memory_threshold = 85  # 内存使用率阈值
        self.disk_threshold = 95  # 磁盘使用率阈值

    def check_system_resources(self) -> dict:
        """检查系统资源使用情况"""
        resources = {
            'cpu_percent': 0,
            'memory_percent': 0,
            'disk_percent': 0,
            'warnings': []
        }

        try:
            if PSUTIL_AVAILABLE:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                resources['cpu_percent'] = cpu_percent
                if cpu_percent > self.cpu_threshold:
                    resources['warnings'].append(f"CPU使用率过高: {cpu_percent}%")

                # 内存使用率
                memory = psutil.virtual_memory()
                resources['memory_percent'] = memory.percent
                if memory.percent > self.memory_threshold:
                    resources['warnings'].append(f"内存使用率过高: {memory.percent}%")

                # 磁盘使用率
                disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100
                resources['disk_percent'] = disk_percent
                if disk_percent > self.disk_threshold:
                    resources['warnings'].append(f"磁盘使用率过高: {disk_percent:.1f}%")
            else:
                resources['warnings'].append("psutil不可用，无法检查系统资源")

        except Exception as e:
            resources['warnings'].append(f"检查系统资源时出错: {e}")

        return resources

    def suggest_optimizations(self, resources: dict) -> list:
        """根据资源使用情况提供优化建议"""
        suggestions = []

        if resources['cpu_percent'] > self.cpu_threshold:
            suggestions.append("🔧 关闭其他占用CPU的程序")
            suggestions.append("🔧 减少并发导入的文件数量")

        if resources['memory_percent'] > self.memory_threshold:
            suggestions.append("🔧 关闭其他占用内存的程序")
            suggestions.append("🔧 分批导入数据，减少单次处理的数据量")

        if resources['disk_percent'] > self.disk_threshold:
            suggestions.append("🔧 清理磁盘空间")
            suggestions.append("🔧 检查是否有足够的临时文件空间")

        return suggestions


# 全局实例
global_crash_detector = CrashDetector()
global_resource_monitor = SystemResourceMonitor()


def get_crash_detector() -> CrashDetector:
    """获取全局崩溃检测器"""
    return global_crash_detector


def get_resource_monitor() -> SystemResourceMonitor:
    """获取全局资源监控器"""
    return global_resource_monitor
