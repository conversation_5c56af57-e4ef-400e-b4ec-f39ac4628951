import math
import os
import re
import psycopg2
from psycopg2 import sql
from datetime import time, datetime
import pandas as pd
from PySide6.QtCore import Qt, QObject, Signal, QThread
from PySide6.QtWidgets import (
    QMainWindow, QProgressBar, QPushButton, QVBoxLayout, QWidget, QMessageBox,
    QHBoxLayout, QTableWidgetItem, QTableWidget, QHeaderView, QFileDialog, 
    QDialog, QCheckBox, QLabel, QScrollArea, QFrame, QGroupBox, QGridLayout)
from styles import BUTTON_STYLE
import time
import logging
import configparser
from concurrent.futures import ProcessPoolExecutor, as_completed
from database_setup import get_db_connection
from logger_config import get_logger
import traceback

# 获取日志记录器
logger = get_logger()

class CleaningStepsSelectionDialog(QDialog):
    """
    数据清洗步骤选择对话框
    
    功能说明：
    - 本文件的功能和实现逻辑：允许用户选择要执行的数据清洗步骤，提供灵活的清洗配置
    - 显示所有可用的清洗步骤，用户可以通过勾选来选择要执行的步骤
    - 默认全选所有步骤，用户可以根据需要取消某些步骤
    - 提供全选和取消全选的快捷操作
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择数据清洗步骤")
        self.setModal(True)
        self.resize(600, 500)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333;
            }
            QCheckBox {
                spacing: 8px;
                font-size: 13px;
                color: #333;
                padding: 4px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #ccc;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #4CAF50;
                background-color: #4CAF50;
                border-radius: 3px;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton#cancelButton {
                background-color: #757575;
            }
            QPushButton#cancelButton:hover {
                background-color: #616161;
            }
            QPushButton#selectAllButton, QPushButton#deselectAllButton {
                background-color: #FF9800;
                min-width: 80px;
                padding: 8px 16px;
                font-size: 12px;
            }
            QPushButton#selectAllButton:hover, QPushButton#deselectAllButton:hover {
                background-color: #F57C00;
            }
        """)
        
        # 定义清洗步骤信息
        self.cleaning_steps_info = [
            ('clean_customer_basic_info', '清洗客户基本信息', '清洗账户信息_客户基本信息表的数据质量'),
            ('clean_transaction_details', '清洗交易明细', '清洗账户交易明细表，包括字段值清理和标准化'),
            ('clean_account_opening_info', '清洗开户信息', '清洗开户信息表，删除无效记录和标准化数据格式'),
            ('clean_special_characters', '清理特殊字符', '清理表中的特殊字符和无效值'),
            ('standardize_cash_counterparty_names', '标准化库存现金对手户名', '将"库存现金"更新为"现金"'),
            ('fill_counterparty_name_by_cash_flag', '根据现金标志填充对手户名', '当现金标志为"现金"时，将对手户名填充为"现金"'),
            ('complement_transaction_account_fields', '账户字段互补', '账户交易明细表字段互补，完善账号信息'),
            ('clean_numeric_account_names', '清理数字账户名称', '清理开户信息表中的纯数字账户名称'),
            ('enrich_account_opening_info', '增强开户信息', '通过匹配增强开户信息表数据'),
            ('preprocess_data', '数据预处理', '对数据进行预处理，为后续匹配做准备'),
            ('match_transaction_names', '匹配交易户名', '匹配交易户名与开户信息'),
            ('match_certificate_numbers', '匹配证件号码', '匹配证件号码信息'),
            ('match_opponent_names', '匹配对手户名', '匹配对手户名信息'),
            ('check_and_correct_shoufu', '检查收付标志', '检查和修正收付标志'),
            ('fill_counterparty_name_with_cash', '填充现金交易', '识别和填充现金交易对手信息'),
            ('finalize_cleaning', '最终清理', '完成最终的数据验证和清理'),
            ('deduplicate_all_tables', '全表去重', '对所有数据表进行去重操作（推荐最后执行）')
        ]
        
        self.setup_ui()
        
        # 存储复选框引用
        self.checkboxes = {}
        self.create_step_checkboxes()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("🔧 选择要执行的数据清洗步骤")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #333;
                padding: 10px;
                background-color: #E3F2FD;
                border-radius: 8px;
                border: 1px solid #BBDEFB;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明文本
        info_label = QLabel("💡 默认选择所有步骤。您可以取消不需要的步骤，系统将按顺序执行选中的步骤。")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #666;
                padding: 8px;
                background-color: #FFF3E0;
                border-radius: 5px;
                border: 1px solid #FFCC02;
            }
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 快捷操作按钮
        button_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("✅ 全选")
        select_all_btn.setObjectName("selectAllButton")
        select_all_btn.clicked.connect(self.select_all)
        
        deselect_all_btn = QPushButton("❌ 取消全选")
        deselect_all_btn.setObjectName("deselectAllButton")  
        deselect_all_btn.clicked.connect(self.deselect_all)
        
        button_layout.addWidget(select_all_btn)
        button_layout.addWidget(deselect_all_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }
        """)
        
        # 创建步骤容器
        self.steps_widget = QWidget()
        self.steps_layout = QVBoxLayout(self.steps_widget)
        self.steps_layout.setSpacing(8)
        self.steps_layout.setContentsMargins(15, 15, 15, 15)
        
        scroll_area.setWidget(self.steps_widget)
        layout.addWidget(scroll_area)
        
        # 底部按钮
        bottom_layout = QHBoxLayout()
        bottom_layout.addStretch()
        
        ok_button = QPushButton("🚀 开始清洗")
        ok_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("❌ 取消")
        cancel_button.setObjectName("cancelButton")
        cancel_button.clicked.connect(self.reject)
        
        bottom_layout.addWidget(ok_button)
        bottom_layout.addWidget(cancel_button)
        
        layout.addLayout(bottom_layout)
        
    def create_step_checkboxes(self):
        """创建步骤复选框"""
        # 创建分组
        basic_group = QGroupBox("🧹 基础清洗步骤")
        advanced_group = QGroupBox("🔗 高级匹配步骤") 
        final_group = QGroupBox("✨ 最终处理步骤")
        
        basic_layout = QVBoxLayout(basic_group)
        advanced_layout = QVBoxLayout(advanced_group)
        final_layout = QVBoxLayout(final_group)
        
        # 基础清洗步骤（前7步）
        basic_steps = self.cleaning_steps_info[:7]
        for step_id, step_name, step_desc in basic_steps:
            checkbox = QCheckBox(f"{step_name}")
            checkbox.setToolTip(step_desc)
            checkbox.setChecked(True)  # 默认选中
            self.checkboxes[step_id] = checkbox
            basic_layout.addWidget(checkbox)
            
        # 高级匹配步骤（第8-13步）
        advanced_steps = self.cleaning_steps_info[7:13]
        for step_id, step_name, step_desc in advanced_steps:
            checkbox = QCheckBox(f"{step_name}")
            checkbox.setToolTip(step_desc)
            checkbox.setChecked(True)  # 默认选中
            self.checkboxes[step_id] = checkbox
            advanced_layout.addWidget(checkbox)
            
        # 最终处理步骤（最后2步）
        final_steps = self.cleaning_steps_info[13:]
        for step_id, step_name, step_desc in final_steps:
            checkbox = QCheckBox(f"{step_name}")
            checkbox.setToolTip(step_desc)
            checkbox.setChecked(True)  # 默认选中
            self.checkboxes[step_id] = checkbox
            final_layout.addWidget(checkbox)
            
        # 添加分组到主布局
        self.steps_layout.addWidget(basic_group)
        self.steps_layout.addWidget(advanced_group)
        self.steps_layout.addWidget(final_group)
        self.steps_layout.addStretch()
        
    def select_all(self):
        """全选所有步骤"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(True)
            
    def deselect_all(self):
        """取消选择所有步骤"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(False)
            
    def get_selected_steps(self):
        """获取选中的步骤"""
        selected_steps = []
        for step_id, checkbox in self.checkboxes.items():
            if checkbox.isChecked():
                selected_steps.append(step_id)
        return selected_steps

# 定义清洗步骤
def run_cleaning_step(case_id, step_name, step_func):
    """
    在独立进程中运行清洗步骤的函数
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        result = step_func(cursor, case_id)
        conn.commit()
        return {'step': step_name, 'result': result, 'success': True}
    except Exception as e:
        conn.rollback()
        logging.error(f"步骤 {step_name} 执行失败: {e}")
        return {'step': step_name, 'error': str(e), 'success': False}
    finally:
        cursor.close()
        conn.close()

# 清洗步骤函数定义
def clean_transaction_details(cursor, case_id):
    """
    清洗账户交易明细表 - 优化版本
    
    功能说明：
    - 本文件的功能和实现逻辑：清洗账户交易明细表的数据质量，包括字段值清理和标准化
    - 将交易账卡号和交易账号中的"-"值更新为空
    - 标准化交易币种格式，将英文简写转换为中文
    """
    logging.info("开始清洗账户交易明细表")
    try:
        total_cleaned = 0
        
        # 1. 清理交易账卡号和交易账号中的"-"值
        logging.info("🔧 清理交易账卡号和交易账号中的'-'值")
        cursor.execute("""
            UPDATE "账户交易明细表" 
            SET 
                "交易账卡号" = CASE WHEN TRIM("交易账卡号") = '-' THEN '' ELSE "交易账卡号" END,
                "交易账号" = CASE WHEN TRIM("交易账号") = '-' THEN '' ELSE "交易账号" END
            WHERE "案件编号" = %s 
            AND (
                TRIM("交易账卡号") = '-' OR 
                TRIM("交易账号") = '-'
            )
        """, (case_id,))
        dash_cleaned_count = cursor.rowcount or 0
        total_cleaned += dash_cleaned_count
        logging.info(f"✅ 已清理 {dash_cleaned_count} 条记录中的'-'值")
        
        # 2. 先查询需要清洗的币种记录数量
        cursor.execute("""
            SELECT COUNT(*) FROM "账户交易明细表" 
            WHERE "案件编号" = %s 
            AND (
                "交易币种" IS NULL 
                OR "交易币种" = '' 
                OR "交易币种" IN ('人民币', 'RMB', 'rmb', 'CNY', 'cny', '美元', 'USD', 'usd', '欧元', 'EUR', 'eur')
            )
        """, (case_id,))
        need_clean_result = cursor.fetchone()
        need_clean_count = need_clean_result[0] if need_clean_result else 0
        
        if need_clean_count == 0:
            logging.info("📊 账户交易明细表无需清洗的币种数据")
        else:
            logging.info(f"🔍 发现 {need_clean_count} 条记录需要清洗币种数据")
            
            # 3. 标准化交易币种格式 - 将英文简写转换为中文
            logging.info("🌐 标准化交易币种格式")
            cursor.execute("""
                -- 标准化交易币种格式 - 将英文简写转换为中文
                UPDATE "账户交易明细表"
                SET "交易币种" = CASE
                    WHEN "交易币种" IN ('CNY', 'cny', 'RMB', 'rmb', '人民币', '人民币元') THEN '人民币'
                    WHEN "交易币种" IN ('USD', 'usd', '美元') THEN '美元'
                    WHEN "交易币种" IN ('EUR', 'eur', '欧元') THEN '欧元'
                    WHEN "交易币种" IN ('GBP', 'gbp', '英镑') THEN '英镑'
                    WHEN "交易币种" IN ('JPY', 'jpy', '日元') THEN '日元'
                    WHEN "交易币种" IN ('CAD', 'cad', '加元', '加拿大元') THEN '加元'
                    WHEN "交易币种" IN ('HKD', 'hkd', '港币', '港元') THEN '港币'
                    WHEN "交易币种" IN ('AUD', 'aud', '澳元', '澳币') THEN '澳元'
                    WHEN "交易币种" IN ('SGD', 'sgd', '新加坡元', '新元') THEN '新加坡元'
                    WHEN "交易币种" IS NULL OR "交易币种" = '' THEN '人民币'  -- 处理NULL值，默认人民币
                    ELSE TRIM("交易币种")  -- 其他币种保持原样但去掉空格
                END
                WHERE "案件编号" = %s
                AND (
                    "交易币种" IS NULL 
                    OR "交易币种" = '' 
                    OR "交易币种" IN ('CNY', 'cny', 'RMB', 'rmb', 'USD', 'usd', 'EUR', 'eur', 'GBP', 'gbp', 'JPY', 'jpy', 'CAD', 'cad', 'HKD', 'hkd', 'AUD', 'aud', 'SGD', 'sgd')
                    OR "交易币种" != TRIM("交易币种")  -- 需要去空格的数据
                );
            """, (case_id,))
            
            currency_cleaned_count = cursor.rowcount or 0
            total_cleaned += currency_cleaned_count
            logging.info(f"✅ 币种标准化完成，处理 {currency_cleaned_count} 条记录")
            
            # 验证清洗结果
            cursor.execute("""
                SELECT COUNT(*) FROM "账户交易明细表" 
                WHERE "案件编号" = %s 
                AND ("交易币种" IS NULL OR "交易币种" = '')
            """, (case_id,))
            null_result = cursor.fetchone()
            null_count = null_result[0] if null_result else 0
            
            if null_count > 0:
                logging.warning(f"⚠️ 仍有 {null_count} 条记录的交易币种为空")
        
        logging.info(f"📊 账户交易明细表清洗完成统计：")
        logging.info(f"  • 清理'-'值记录数：{dash_cleaned_count}")
        logging.info(f"  • 币种标准化记录数：{currency_cleaned_count if need_clean_count > 0 else 0}")
        logging.info(f"  • 总处理记录数：{total_cleaned}")
        
        return total_cleaned
    except Exception as e:
        logging.error(f"清洗账户交易明细表时发生错误: {e}")
        raise e

def clean_account_opening_info(cursor, case_id):
    """
    清洗开户信息表
    
    功能说明：
    - 本文件的功能和实现逻辑：清洗开户信息表的数据质量，包括删除无效记录和标准化数据格式
    - 删除交易账号和交易卡号都为空的行（无效记录）
    - 标准化开户人证件号码格式（移除非字母数字字符）
    - 标准化账户开户名称（去除首尾空格）
    """
    logging.info("开始清洗开户信息表")
    try:
        # 先统计删除前的记录数
        cursor.execute("""
            SELECT COUNT(*) FROM "开户信息表" WHERE "案件编号" = %s
        """, (case_id,))
        total_before_result = cursor.fetchone()
        total_before = total_before_result[0] if total_before_result else 0
        
        # 1. 删除交易账号和交易卡号都为空的行
        logging.info("🗑️ 删除交易账号和交易卡号都为空的记录")
        cursor.execute("""
            DELETE FROM "开户信息表" 
            WHERE "案件编号" = %s 
            AND (
                ("交易账号" IS NULL OR TRIM("交易账号") = '') 
                AND ("交易卡号" IS NULL OR TRIM("交易卡号") = '')
            )
        """, (case_id,))
        deleted_count = cursor.rowcount or 0
        logging.info(f"✅ 已删除 {deleted_count} 条交易账号和交易卡号都为空的记录")
        
        # 统计删除后的记录数
        cursor.execute("""
            SELECT COUNT(*) FROM "开户信息表" WHERE "案件编号" = %s
        """, (case_id,))
        total_after_result = cursor.fetchone()
        total_after = total_after_result[0] if total_after_result else 0
        
        # 2. 标准化剩余数据
        logging.info("🔧 标准化剩余数据格式")
        cursor.execute("""
            -- 标准化开户人证件号码格式（移除非字母数字字符）
            UPDATE "开户信息表" 
            SET "开户人证件号码" = regexp_replace("开户人证件号码", '[^A-Za-z0-9]', '', 'g')
            WHERE "案件编号" = %s AND "开户人证件号码" IS NOT NULL;
            
            -- 标准化账户开户名称（去除首尾空格）
            UPDATE "开户信息表" 
            SET "账户开户名称" = TRIM("账户开户名称")
            WHERE "案件编号" = %s AND "账户开户名称" IS NOT NULL;
        """, (case_id, case_id))
        
        standardized_count = cursor.rowcount or 0
        
        logging.info(f"📊 开户信息表清洗完成统计：")
        logging.info(f"  • 清洗前记录数：{total_before}")
        logging.info(f"  • 删除无效记录：{deleted_count}")
        logging.info(f"  • 清洗后记录数：{total_after}")
        logging.info(f"  • 标准化记录数：{standardized_count}")
        
        return deleted_count + standardized_count
    except Exception as e:
        logging.error(f"清洗开户信息表时发生错误: {e}")
        raise e

def preprocess_data(cursor, case_id):
    """数据预处理"""
    logging.info("开始数据预处理")
    try:
        # 简化版本：确保所有必要的列存在
        cursor.execute("""
            -- 确保账户交易明细表有必要的列
            ALTER TABLE "账户交易明细表" 
            ADD COLUMN IF NOT EXISTS "交易户名" VARCHAR,
            ADD COLUMN IF NOT EXISTS "交易证件号码" VARCHAR,
            ADD COLUMN IF NOT EXISTS "对手户名" VARCHAR,
            ADD COLUMN IF NOT EXISTS "对手身份证号" VARCHAR,
            ADD COLUMN IF NOT EXISTS "收付标志" VARCHAR,
            ADD COLUMN IF NOT EXISTS "交易币种" VARCHAR;
            
            -- 确保开户信息表有必要的列
            ALTER TABLE "开户信息表" 
            ADD COLUMN IF NOT EXISTS "账户开户名称" VARCHAR,
            ADD COLUMN IF NOT EXISTS "开户人证件号码" VARCHAR;
        """)
        
        logging.info("所有必要的列已成功添加并填充。")
        return 0  # 返回0而不是True
    except Exception as e:
        logging.error(f"数据预处理时发生错误: {e}")
        raise e

def match_transaction_names(cursor, case_id):
    """
    批量匹配交易户名 - 按照正确顺序进行匹配
    
    匹配顺序：
    1. 使用交易账号_digits匹配开户信息表中的交易账号_digits（账号对账号 - 最精确匹配）
    2. 使用交易账号_digits匹配开户信息表中的交易卡号_digits（账号对卡号）
    3. 使用交易账卡号_digits匹配开户信息表中的交易账号_digits（卡号对账号）
    4. 使用交易账卡号_digits匹配开户信息表中的交易卡号_digits（卡号对卡号）
    
    每一步只处理前面步骤未匹配成功的记录，避免重复匹配
    """
    logging.info("开始批量匹配交易户名")
    try:
        total_matched = 0
        
        # 步骤1：使用交易账号_digits匹配开户信息表中的交易账号_digits（账号对账号 - 最精确匹配）
        logging.info("步骤1：交易账号_digits → 开户信息表.交易账号_digits")
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易户名" = (
                SELECT k."账户开户名称" 
                FROM "开户信息表" k 
                WHERE k."交易账号_digits" = "账户交易明细表"."交易账号_digits" 
                  AND k."案件编号" = "账户交易明细表"."案件编号"
                  AND k."账户开户名称" IS NOT NULL 
                  AND k."账户开户名称" != ''
                LIMIT 1
            )
            WHERE "案件编号" = %s
              AND ("交易户名" IS NULL OR "交易户名" = '')
              AND ("交易账号_digits" IS NOT NULL AND "交易账号_digits" != '')
        """, (case_id,))
        step1_count = cursor.rowcount or 0
        total_matched += step1_count
        logging.info(f"步骤1完成：通过交易账号_digits精确匹配，成功匹配 {step1_count} 条记录")

        # 步骤2：使用交易账号_digits匹配开户信息表中的交易卡号_digits（账号对卡号）
        logging.info("步骤2：交易账号_digits → 开户信息表.交易卡号_digits")
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易户名" = (
                SELECT k."账户开户名称" 
                FROM "开户信息表" k 
                WHERE k."交易卡号_digits" = "账户交易明细表"."交易账号_digits" 
                  AND k."案件编号" = "账户交易明细表"."案件编号"
                  AND k."账户开户名称" IS NOT NULL 
                  AND k."账户开户名称" != ''
                LIMIT 1
            )
            WHERE "案件编号" = %s
              AND ("交易户名" IS NULL OR "交易户名" = '')
              AND ("交易账号_digits" IS NOT NULL AND "交易账号_digits" != '')
        """, (case_id,))
        step2_count = cursor.rowcount or 0
        total_matched += step2_count
        logging.info(f"步骤2完成：通过交易账号_digits匹配交易卡号_digits，成功匹配 {step2_count} 条记录")

        # 步骤3：使用交易账卡号_digits匹配开户信息表中的交易账号_digits（卡号对账号）
        logging.info("步骤3：交易账卡号_digits → 开户信息表.交易账号_digits")
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易户名" = (
                SELECT k."账户开户名称" 
                FROM "开户信息表" k 
                WHERE k."交易账号_digits" = "账户交易明细表"."交易账卡号_digits" 
                  AND k."案件编号" = "账户交易明细表"."案件编号"
                  AND k."账户开户名称" IS NOT NULL 
                  AND k."账户开户名称" != ''
                LIMIT 1
            )
            WHERE "案件编号" = %s
              AND ("交易户名" IS NULL OR "交易户名" = '')
              AND ("交易账卡号_digits" IS NOT NULL AND "交易账卡号_digits" != '')
        """, (case_id,))
        step3_count = cursor.rowcount or 0
        total_matched += step3_count
        logging.info(f"步骤3完成：通过交易账卡号_digits匹配交易账号_digits，成功匹配 {step3_count} 条记录")

        # 步骤4：使用交易账卡号_digits匹配开户信息表中的交易卡号_digits（卡号对卡号）
        logging.info("步骤4：交易账卡号_digits → 开户信息表.交易卡号_digits")
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易户名" = (
                SELECT k."账户开户名称" 
                FROM "开户信息表" k 
                WHERE k."交易卡号_digits" = "账户交易明细表"."交易账卡号_digits" 
                  AND k."案件编号" = "账户交易明细表"."案件编号"
                  AND k."账户开户名称" IS NOT NULL 
                  AND k."账户开户名称" != ''
                LIMIT 1
            )
            WHERE "案件编号" = %s
              AND ("交易户名" IS NULL OR "交易户名" = '')
              AND ("交易账卡号_digits" IS NOT NULL AND "交易账卡号_digits" != '')
        """, (case_id,))
        step4_count = cursor.rowcount or 0
        total_matched += step4_count
        logging.info(f"步骤4完成：通过交易账卡号_digits匹配交易卡号_digits，成功匹配 {step4_count} 条记录")

        # 输出详细统计信息
        logging.info(f"🎯 交易户名匹配完成！总计匹配 {total_matched} 条记录")
        logging.info(f"📊 详细统计：")
        logging.info(f"   - 步骤1（账号对账号）：{step1_count} 条")
        logging.info(f"   - 步骤2（账号对卡号）：{step2_count} 条")
        logging.info(f"   - 步骤3（卡号对账号）：{step3_count} 条")
        logging.info(f"   - 步骤4（卡号对卡号）：{step4_count} 条")

        return total_matched
    except Exception as e:
        logging.error(f"批量匹配交易户名时发生错误: {e}")
        raise e

def match_certificate_numbers(cursor, case_id):
    """
    批量匹配交易证件号码 - 按照正确顺序进行匹配
    
    匹配顺序：
    1. 使用交易账号_digits匹配开户信息表中的交易账号_digits（账号对账号）
    2. 使用交易账号_digits匹配开户信息表中的交易卡号_digits（账号对卡号）
    3. 使用交易账卡号_digits匹配开户信息表中的交易账号_digits（卡号对账号）
    4. 使用交易账卡号_digits匹配开户信息表中的交易卡号_digits（卡号对卡号）
    
    每一步只处理前面步骤未匹配成功的记录，避免重复匹配
    """
    logging.info("开始批量匹配交易证件号码")
    try:
        total_matched = 0
        
        # 步骤1：使用交易账号_digits匹配开户信息表中的交易账号_digits（账号对账号 - 最精确匹配）
        logging.info("步骤1：交易账号_digits → 开户信息表.交易账号_digits")
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易证件号码" = (
                SELECT k."开户人证件号码" 
                FROM "开户信息表" k 
                WHERE k."交易账号_digits" = "账户交易明细表"."交易账号_digits" 
                  AND k."案件编号" = "账户交易明细表"."案件编号"
                  AND k."开户人证件号码" IS NOT NULL 
                  AND k."开户人证件号码" != ''
                LIMIT 1
            )
            WHERE "案件编号" = %s
              AND ("交易证件号码" IS NULL OR "交易证件号码" = '')
              AND ("交易账号_digits" IS NOT NULL AND "交易账号_digits" != '')
        """, (case_id,))
        step1_count = cursor.rowcount or 0
        total_matched += step1_count
        logging.info(f"步骤1完成：通过交易账号_digits精确匹配，成功匹配 {step1_count} 条记录")

        # 步骤2：使用交易账号_digits匹配开户信息表中的交易卡号_digits（账号对卡号）
        logging.info("步骤2：交易账号_digits → 开户信息表.交易卡号_digits")
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易证件号码" = (
                SELECT k."开户人证件号码" 
                FROM "开户信息表" k 
                WHERE k."交易卡号_digits" = "账户交易明细表"."交易账号_digits" 
                  AND k."案件编号" = "账户交易明细表"."案件编号"
                  AND k."开户人证件号码" IS NOT NULL 
                  AND k."开户人证件号码" != ''
                LIMIT 1
            )
            WHERE "案件编号" = %s
              AND ("交易证件号码" IS NULL OR "交易证件号码" = '')
              AND ("交易账号_digits" IS NOT NULL AND "交易账号_digits" != '')
        """, (case_id,))
        step2_count = cursor.rowcount or 0
        total_matched += step2_count
        logging.info(f"步骤2完成：通过交易账号_digits匹配交易卡号_digits，成功匹配 {step2_count} 条记录")

        # 步骤3：使用交易账卡号_digits匹配开户信息表中的交易账号_digits（卡号对账号）
        logging.info("步骤3：交易账卡号_digits → 开户信息表.交易账号_digits")
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易证件号码" = (
                SELECT k."开户人证件号码" 
                FROM "开户信息表" k 
                WHERE k."交易账号_digits" = "账户交易明细表"."交易账卡号_digits" 
                  AND k."案件编号" = "账户交易明细表"."案件编号"
                  AND k."开户人证件号码" IS NOT NULL 
                  AND k."开户人证件号码" != ''
                LIMIT 1
            )
            WHERE "案件编号" = %s
              AND ("交易证件号码" IS NULL OR "交易证件号码" = '')
              AND ("交易账卡号_digits" IS NOT NULL AND "交易账卡号_digits" != '')
        """, (case_id,))
        step3_count = cursor.rowcount or 0
        total_matched += step3_count
        logging.info(f"步骤3完成：通过交易账卡号_digits匹配交易账号_digits，成功匹配 {step3_count} 条记录")

        # 步骤4：使用交易账卡号_digits匹配开户信息表中的交易卡号_digits（卡号对卡号）
        logging.info("步骤4：交易账卡号_digits → 开户信息表.交易卡号_digits")
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易证件号码" = (
                SELECT k."开户人证件号码" 
                FROM "开户信息表" k 
                WHERE k."交易卡号_digits" = "账户交易明细表"."交易账卡号_digits" 
                  AND k."案件编号" = "账户交易明细表"."案件编号"
                  AND k."开户人证件号码" IS NOT NULL 
                  AND k."开户人证件号码" != ''
                LIMIT 1
            )
            WHERE "案件编号" = %s
              AND ("交易证件号码" IS NULL OR "交易证件号码" = '')
              AND ("交易账卡号_digits" IS NOT NULL AND "交易账卡号_digits" != '')
        """, (case_id,))
        step4_count = cursor.rowcount or 0
        total_matched += step4_count
        logging.info(f"步骤4完成：通过交易账卡号_digits匹配交易卡号_digits，成功匹配 {step4_count} 条记录")

        # 输出详细统计信息
        logging.info(f"🎯 交易证件号码匹配完成！总计匹配 {total_matched} 条记录")
        logging.info(f"📊 详细统计：")
        logging.info(f"   - 步骤1（账号对账号）：{step1_count} 条")
        logging.info(f"   - 步骤2（账号对卡号）：{step2_count} 条")
        logging.info(f"   - 步骤3（卡号对账号）：{step3_count} 条")
        logging.info(f"   - 步骤4（卡号对卡号）：{step4_count} 条")

        return total_matched
    except Exception as e:
        logging.error(f"批量匹配交易证件号码时发生错误: {e}")
        raise e

def match_opponent_names(cursor, case_id):
    """批量匹配对手户名"""
    logging.info("开始批量匹配对手户名")
    try:
        total_matched = 0
        # 步骤1：使用对手卡号_digits匹配开户信息表中的交易卡号_digits
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "对手户名" = (
                SELECT k."账户开户名称" 
                FROM "开户信息表" k 
                WHERE k."交易卡号_digits" = "账户交易明细表"."对手卡号_digits" 
                  AND k."案件编号" = "账户交易明细表"."案件编号" 
                LIMIT 1
            )
            WHERE "案件编号" = %s
              AND ("对手户名" IS NULL OR "对手户名" = '')
              AND ("对手卡号_digits" IS NOT NULL AND "对手卡号_digits" != '')
        """, (case_id,))
        total_matched += cursor.rowcount or 0

        logging.info(f"对手户名匹配完成，共匹配 {total_matched} 条记录")
        return total_matched
    except Exception as e:
        logging.error(f"批量匹配对手户名时发生错误: {e}")
        raise e

def check_and_correct_shoufu(cursor, case_id):
    """修正收付标志 - 使用完整的自动匹配规则"""
    logging.info("开始修正收付标志")
    try:
        # 完整的自动匹配规则
        auto_corrections = {
            "借": "出",
            "贷": "进",
            "入": "进",
            "0-借": "出",
            "1-贷": "进",
            "借方": "出",
            "贷方": "进",
            "0": "出",
            "1": "进",
            "-": "出",
            "+": "进",
            "D - 借": "出",
            "C - 贷": "进",
            "D": "出",
            "C": "进",
            "取": "出",
            "存": "进",
            "收入": "进",
            "支出": "出"
        }
        
        # 统计修正信息
        total_corrected_count = 0
        correction_details = {}
        
        logging.info(f"开始自动修正收付标志，共有 {len(auto_corrections)} 个匹配规则")
        
        # 逐个处理每个匹配规则
        for old_value, new_value in auto_corrections.items():
            cursor.execute("""
                UPDATE "账户交易明细表"
                SET "收付标志" = %s
                WHERE "案件编号" = %s 
                AND TRIM(COALESCE("收付标志", '')) = %s
            """, (new_value, case_id, old_value))
            
            corrected_count = cursor.rowcount or 0
            if corrected_count > 0:
                correction_details[old_value] = {
                    'new_value': new_value,
                    'count': corrected_count
                }
                total_corrected_count += corrected_count
                logging.info(f"'{old_value}' → '{new_value}': 修正了 {corrected_count} 条记录")

        # 记录详细统计信息
        if correction_details:
            logging.info("收付标志修正详情:")
            for old_val, details in correction_details.items():
                logging.info(f"  '{old_val}' → '{details['new_value']}': {details['count']} 条")
        
        # 检查是否还有未处理的收付标志值
        cursor.execute("""
            SELECT DISTINCT TRIM(COALESCE("收付标志", '')) as shoufu, COUNT(*) as count
            FROM "账户交易明细表"
            WHERE "案件编号" = %s 
            AND TRIM(COALESCE("收付标志", '')) NOT IN ('进', '出', '', NULL)
            AND TRIM(COALESCE("收付标志", '')) != ''
            GROUP BY TRIM(COALESCE("收付标志", ''))
            ORDER BY count DESC
        """, (case_id,))
        
        unmatched_values = cursor.fetchall()
        if unmatched_values:
            logging.warning(f"发现 {len(unmatched_values)} 种未匹配的收付标志值:")
            for shoufu, count in unmatched_values:
                logging.warning(f"  '{shoufu}': {count} 条记录")
            
            # 记录到日志供后续分析
            with open('收付标志未匹配值.log', 'a', encoding='utf-8') as f:
                f.write(f"\n=== 案件 {case_id} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n")
                for shoufu, count in unmatched_values:
                    f.write(f"'{shoufu}': {count} 条记录\n")
        
        logging.info(f"收付标志修正完成，共自动修复了 {total_corrected_count} 条记录")
        if unmatched_values:
            logging.info(f"仍有 {sum(count for _, count in unmatched_values)} 条记录的收付标志需要手动处理")
            
        return total_corrected_count
        
    except Exception as e:
        logging.error(f"修正收付标志时发生错误: {e}")
        raise e

def fill_counterparty_name_with_cash(cursor, case_id):
    """
    根据摘要说明和交易类型填充对手户名为现金 - 增加例外关键词过滤
    """
    logging.info("开始根据摘要说明和交易类型填充对手户名（含例外关键词过滤）")
    try:
        # 定义现金关键词列表 - 精确匹配版本（只匹配真正的现金交易）
        cash_keywords = [
            # 明确的现金关键词
            'cash', '现金', '现金存入', '现金支取', '现金取款', '现金存款',
            '现金开户', '现金销户', '现金支票', '现金业务', '现金交易',
            '现金缴存', '现金支取业务', '现金存取', '现金缴款', '现金提取',
            '现金支付', '现金收入', '现金汇款',

            # 现钞相关
            '现钞', '现钞存入', '现钞支取', '现钞取款', '现钞存款',
            '钞票', '纸币', '硬币',

            # ATM现金交易（明确包含现金的）
            'ATM现金存入', 'ATM现金取出', 'ATM现金支取', 'ATM存入现金',

            # 柜台现金业务
            '柜台现金', '柜面现金', '现金柜台',

            # 传统现金业务术语
            '现存', '现取', '取现', '存现', '提现',
            '无卡存现', '有卡存现', 'ATMD',

            # ATM取款/存款（传统银行术语，通常指现金）
            'ATM取款', 'ATM存款', 'ATM取现', 'ATM存现',
            'atm取款', 'atm存款', 'atm取现', 'atm存入',
            'ATM 取款', 'ATM 存款', 'ATM 取现', 'ATM 存入',
            '网络ATM取款', '他行ATM取款',

            # 银行现金业务
            '跨网点ATM取现借卡账户', '自助取款', '自动取款机', '自动存款机',
            'ATM机', '卡取', '卡存', '跨取', '现开',

            # 新增ATM相关关键词（2025-07-20用户反馈）
            'ATMT', 'ATM', '银联ATM取款', 'ATM本代本取款', 'ATM刷脸取款',

            # 英文变体
            '现 金', 'CASH', 'Cash',
            'ATM WITHDRAWAL', 'ATM DEPOSIT', 'CASH DEPOSIT', 'CASH WITHDRAWAL'
        ]

        logging.info(f"💰 现金关键词数量: {len(cash_keywords)}")

        # 验证用户反馈的关键词是否都包含在内
        user_feedback_keywords = ['现金存入', '现金支取', '网络ATM取款', 'ATM取款', 'ATM存款', 'ATM取现', '存款:ATM']
        logging.info("🔍 用户反馈关键词检查:")
        for keyword in user_feedback_keywords:
            if keyword in cash_keywords:
                logging.info(f"   ✅ {keyword}")
            else:
                logging.warning(f"   ❌ {keyword} - 缺失")

        # 转义特殊字符并构建SQL条件
        escaped_case_id = case_id.replace("'", "''")

        # 构建现金关键词条件 - 简化版本（只匹配现金，不使用例外关键词）
        cash_like_conditions = []
        for keyword in cash_keywords:
            escaped_kw = keyword.replace("'", "''")
            cash_like_conditions.append(f"\"摘要说明\" ILIKE '%{escaped_kw}%'")
            cash_like_conditions.append(f"\"交易类型\" ILIKE '%{escaped_kw}%'")

        cash_like_sql = ' OR '.join(cash_like_conditions)

        sql_query = f'''
            UPDATE "账户交易明细表"
            SET "对手户名" = '现金'
            WHERE "案件编号" = '{escaped_case_id}'
            AND "对手户名" IS NULL
            AND "对手账号" IS NULL
            AND "对手卡号" IS NULL
            AND (
                {cash_like_sql}
            )
        '''

        cursor.execute(sql_query)
        updated_count = cursor.rowcount or 0

        logging.info(f"现金交易识别完成，共更新 {updated_count} 条记录")
        logging.info("📊 现金交易识别统计：")
        logging.info("   - 匹配条件：对手户名、对手账号、对手卡号均为空")
        logging.info("   - 匹配字段：摘要说明 或 交易类型")
        logging.info(f"   - ✅ 识别为现金：{updated_count} 条记录")
        logging.info("   - 💡 使用精确现金关键词匹配，无例外关键词排除")

        return {
            'cash_identified': updated_count,
            'total_processed': updated_count
        }

    except Exception as e:
        logging.error(f"根据摘要说明填充对手户名时发生错误: {e}")
        raise e


def finalize_cleaning(cursor, case_id):
    """最终清理 - 完成其他必要的清理操作，不进行去重（去重统一在deduplicate_all_tables中执行）"""
    logging.info("开始最终清理")
    try:
        # 这里可以添加其他最终清理逻辑
        # 例如：数据验证、索引优化、统计信息更新等
        
        # 执行数据验证（示例）
        cursor.execute("""
            SELECT COUNT(*) FROM "账户交易明细表" 
            WHERE "案件编号" = %s AND "交易户名" IS NOT NULL
        """, (case_id,))
        transaction_result = cursor.fetchone()
        transaction_count = transaction_result[0] if transaction_result else 0
        
        logging.info(f"最终清理完成，案件 {case_id} 共有 {transaction_count} 条有效交易记录")
        
        return {
            'verified_transactions': transaction_count,
            'final_cleaning_completed': True
        }
    except Exception as e:
        logging.error(f"最终清理时发生错误: {e}")
        raise e

def clean_transaction_account_fields(cursor, case_id):
    """
    清洗账户交易明细表中的交易账号和交易账卡号字段：
    1. 若交易账卡号为空，优先使用查询卡号，其次使用本方卡号
    2. 若交易账号为空，优先使用查询账号，其次使用本方账号
    3. 若所有相关字段都为空，则不处理
    """
    logging.info("开始清洗交易账号和交易账卡号字段")
    try:
        # 更新交易账卡号
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易账卡号" = CASE
                WHEN "查询卡号" IS NOT NULL AND "查询卡号" != '' THEN "查询卡号"
                WHEN "本方卡号" IS NOT NULL AND "本方卡号" != '' THEN "本方卡号"
                ELSE "交易账卡号"
            END
            WHERE "案件编号" = %s
            AND ("交易账卡号" IS NULL OR "交易账卡号" = '')
            AND (
                ("查询卡号" IS NOT NULL AND "查询卡号" != '') OR
                ("本方卡号" IS NOT NULL AND "本方卡号" != '')
            )
        """, (case_id,))
        card_updated = cursor.rowcount or 0
        logging.info(f"已更新 {card_updated} 条记录的交易账卡号")

        # 更新交易账号
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易账号" = CASE
                WHEN "查询账号" IS NOT NULL AND "查询账号" != '' THEN "查询账号"
                WHEN "本方账号" IS NOT NULL AND "本方账号" != '' THEN "本方账号"
                ELSE "交易账号"
            END
            WHERE "案件编号" = %s
            AND ("交易账号" IS NULL OR "交易账号" = '')
            AND (
                ("查询账号" IS NOT NULL AND "查询账号" != '') OR
                ("本方账号" IS NOT NULL AND "本方账号" != '')
            )
        """, (case_id,))
        account_updated = cursor.rowcount or 0
        logging.info(f"已更新 {account_updated} 条记录的交易账号")

        return card_updated + account_updated
    except Exception as e:
        logging.error(f"清洗交易账号和交易账卡号字段时发生错误: {e}")
        raise e

def clean_special_characters(cursor, case_id):
    """
    清理表中的特殊字符和无效值
    
    功能说明：
    1. 清理账户交易明细表中的特殊字符：交易户名、对手户名、交易证件号码、对手身份证号、对手账号、对手卡号
    2. 清理开户信息表中的特殊字符：账户开户名称、开户人证件号码
    3. 处理特殊值：\\N、-、空值等无效值
    
    注意：_digits字段已在导入阶段生成，此处不再重复生成
    
    处理的无效值：
    - 文字类：无、空、null、NULL、None、未知、不详、不明、不清楚等
    - 符号类：N/A、n/a、/A、-、--、---等
    - 特殊值：\\N（修复转义问题）
    
    Args:
        cursor: 数据库游标
        case_id: 案件编号
        
    Returns:
        int: 清理的总记录数
    """
    try:
        logging.info("开始清理特殊字符和无效值")
        total_cleaned_count = 0
        
        # 🔧 修复：定义需要清理的无效值（移除过度清理的数字值）
        invalid_values = [
            # 原有无效值
            "无", "空", "null", "NULL", "None", "未知", "不详", "不明", "不清楚",
            "N/A", "n/a", "/A", "-", "--", "---", "暂无", "无资料", "无信息",
            "待定", "待查", "不确定", "不明确", "缺失", "空白", "空值",
            "无效", "错误", "异常", "未填写", "未提供", "未录入",
            # 🔧 修复：特殊值处理（移除可能有效的数字值）
            "\\N",  # 双反斜杠N，常见于数据库导出
            r"\N",  # 单反斜杠N，文本文件中的空值标记
            "\\n",  # 双反斜杠小写n
            r"\n",  # 单反斜杠小写n
            "##", "###", "****", "....", "----",      # 符号形式的无效值
            "NA", "na", "nil", "NIL", "void", "VOID",  # 英文无效值
            "empty", "EMPTY", "blank", "BLANK",        # 英文空值
            "missing", "MISSING", "unknown", "UNKNOWN" # 英文未知值
        ]
        
        # 🔧 修复：使用参数化查询避免SQL注入风险
        # 构建安全的PostgreSQL数组格式
        placeholders = ','.join(['%s'] * len(invalid_values))
        
        # 清理账户交易明细表
        update_query = f"""
            UPDATE "账户交易明细表" 
            SET 
                "交易户名" = CASE 
                    WHEN TRIM(COALESCE("交易户名", '')) = ANY(ARRAY[{placeholders}]) 
                         OR TRIM(COALESCE("交易户名", '')) = '' 
                    THEN NULL
                    ELSE TRIM("交易户名")
                END,
                "对手户名" = CASE 
                    WHEN TRIM(COALESCE("对手户名", '')) = ANY(ARRAY[{placeholders}]) 
                         OR TRIM(COALESCE("对手户名", '')) = '' 
                    THEN NULL
                    ELSE TRIM("对手户名")
                END,
                "交易证件号码" = CASE 
                    WHEN TRIM(COALESCE("交易证件号码", '')) = ANY(ARRAY[{placeholders}]) 
                         OR TRIM(COALESCE("交易证件号码", '')) = '' 
                    THEN NULL
                    ELSE TRIM("交易证件号码")
                END,
                "对手身份证号" = CASE 
                    WHEN TRIM(COALESCE("对手身份证号", '')) = ANY(ARRAY[{placeholders}]) 
                         OR TRIM(COALESCE("对手身份证号", '')) = '' 
                    THEN NULL
                    ELSE TRIM("对手身份证号")
                END,
                "对手账号" = CASE 
                    WHEN TRIM(COALESCE("对手账号", '')) = ANY(ARRAY[{placeholders}]) 
                         OR TRIM(COALESCE("对手账号", '')) = '' 
                    THEN NULL
                    ELSE TRIM("对手账号")
                END,
                "对手卡号" = CASE 
                    WHEN TRIM(COALESCE("对手卡号", '')) = ANY(ARRAY[{placeholders}]) 
                         OR TRIM(COALESCE("对手卡号", '')) = '' 
                    THEN NULL
                    ELSE TRIM("对手卡号")
                END
            WHERE "案件编号" = %s
            AND (
                TRIM(COALESCE("交易户名", '')) = ANY(ARRAY[{placeholders}]) OR
                TRIM(COALESCE("对手户名", '')) = ANY(ARRAY[{placeholders}]) OR
                TRIM(COALESCE("交易证件号码", '')) = ANY(ARRAY[{placeholders}]) OR
                TRIM(COALESCE("对手身份证号", '')) = ANY(ARRAY[{placeholders}]) OR
                TRIM(COALESCE("对手账号", '')) = ANY(ARRAY[{placeholders}]) OR
                TRIM(COALESCE("对手卡号", '')) = ANY(ARRAY[{placeholders}]) OR
                TRIM(COALESCE("交易户名", '')) = '' OR
                TRIM(COALESCE("对手户名", '')) = '' OR
                TRIM(COALESCE("交易证件号码", '')) = '' OR
                TRIM(COALESCE("对手身份证号", '')) = '' OR
                TRIM(COALESCE("对手账号", '')) = '' OR
                TRIM(COALESCE("对手卡号", '')) = ''
            );
        """
        
        # 🔧 修复：使用参数化查询，重复invalid_values 6次（对应6个字段的检查）
        query_params = invalid_values * 6 + [case_id] + invalid_values * 6
        cursor.execute(update_query, query_params)
        
        jiaoyimingxi_count = cursor.rowcount or 0
        total_cleaned_count += jiaoyimingxi_count
        logging.info(f"账户交易明细表清理完成，处理了 {jiaoyimingxi_count} 条记录")
        
        # 清理开户信息表
        update_query_kaihu = f"""
            UPDATE "开户信息表" 
            SET 
                "账户开户名称" = CASE 
                    WHEN TRIM(COALESCE("账户开户名称", '')) = ANY(ARRAY[{placeholders}]) 
                         OR TRIM(COALESCE("账户开户名称", '')) = '' 
                    THEN NULL
                    ELSE TRIM("账户开户名称")
                END,
                "开户人证件号码" = CASE 
                    WHEN TRIM(COALESCE("开户人证件号码", '')) = ANY(ARRAY[{placeholders}]) 
                         OR TRIM(COALESCE("开户人证件号码", '')) = '' 
                    THEN NULL
                    ELSE TRIM("开户人证件号码")
                END
            WHERE "案件编号" = %s
            AND (
                TRIM(COALESCE("账户开户名称", '')) = ANY(ARRAY[{placeholders}]) OR
                TRIM(COALESCE("开户人证件号码", '')) = ANY(ARRAY[{placeholders}]) OR
                TRIM(COALESCE("账户开户名称", '')) = '' OR
                TRIM(COALESCE("开户人证件号码", '')) = ''
            );
        """
        
        # 🔧 修复：使用参数化查询，重复invalid_values 2次（对应2个字段的检查）
        query_params_kaihu = invalid_values * 2 + [case_id] + invalid_values * 2
        cursor.execute(update_query_kaihu, query_params_kaihu)
        
        kaihuxinxi_count = cursor.rowcount or 0
        total_cleaned_count += kaihuxinxi_count
        logging.info(f"开户信息表清理完成，处理了 {kaihuxinxi_count} 条记录")
        
        # 🔢 生成_digits字段：提取账号/卡号中的数字
        logging.info("开始生成_digits字段...")
        
        # 生成账户交易明细表的_digits字段
        cursor.execute("""
            UPDATE "账户交易明细表" 
            SET 
                "交易账卡号_digits" = regexp_replace(COALESCE("交易账卡号", ''), '[^0-9]', '', 'g'),
                "交易账号_digits" = regexp_replace(COALESCE("交易账号", ''), '[^0-9]', '', 'g'),
                "对手账号_digits" = regexp_replace(COALESCE("对手账号", ''), '[^0-9]', '', 'g'),
                "对手卡号_digits" = regexp_replace(COALESCE("对手卡号", ''), '[^0-9]', '', 'g')
            WHERE "案件编号" = %s
            AND (
                ("交易账卡号" IS NOT NULL AND ("交易账卡号_digits" IS NULL OR "交易账卡号_digits" = '')) OR
                ("交易账号" IS NOT NULL AND ("交易账号_digits" IS NULL OR "交易账号_digits" = '')) OR
                ("对手账号" IS NOT NULL AND ("对手账号_digits" IS NULL OR "对手账号_digits" = '')) OR
                ("对手卡号" IS NOT NULL AND ("对手卡号_digits" IS NULL OR "对手卡号_digits" = ''))
            );
        """, (case_id,))
        
        transaction_digits_count = cursor.rowcount or 0
        logging.info(f"账户交易明细表_digits字段生成完成，处理了 {transaction_digits_count} 条记录")
        
        # 生成开户信息表的_digits字段
        cursor.execute("""
            UPDATE "开户信息表" 
            SET 
                "交易卡号_digits" = regexp_replace(COALESCE("交易卡号", ''), '[^0-9]', '', 'g'),
                "交易账号_digits" = regexp_replace(COALESCE("交易账号", ''), '[^0-9]', '', 'g')
            WHERE "案件编号" = %s
            AND (
                ("交易卡号" IS NOT NULL AND ("交易卡号_digits" IS NULL OR "交易卡号_digits" = '')) OR
                ("交易账号" IS NOT NULL AND ("交易账号_digits" IS NULL OR "交易账号_digits" = ''))
            );
        """, (case_id,))
        
        kaihu_digits_count = cursor.rowcount or 0
        logging.info(f"开户信息表_digits字段生成完成，处理了 {kaihu_digits_count} 条记录")
        
        total_digits_generated = transaction_digits_count + kaihu_digits_count
        logging.info(f"🔢 _digits字段生成总计完成，共处理 {total_digits_generated} 条记录")

        # 🔧 创建_digits字段索引（从导入阶段移动到此处）
        # 原因：导入时_digits字段为空，现在有数据了，创建索引更有效
        if total_digits_generated > 0:
            logging.info("🔧 开始创建_digits字段索引...")
            try:
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_jyzhdigits
                    ON 账户交易明细表 (交易账号_digits)
                    WHERE 交易账号_digits IS NOT NULL AND 交易账号_digits != '';

                    CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_jykahdigits
                    ON 账户交易明细表 (交易账卡号_digits)
                    WHERE 交易账卡号_digits IS NOT NULL AND 交易账卡号_digits != '';

                    CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_dshzhdigits
                    ON 账户交易明细表 (对手账号_digits)
                    WHERE 对手账号_digits IS NOT NULL AND 对手账号_digits != '';

                    CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_dskahdigits
                    ON 账户交易明细表 (对手卡号_digits)
                    WHERE 对手卡号_digits IS NOT NULL AND 对手卡号_digits != '';

                    CREATE INDEX IF NOT EXISTS idx_kaihuxinxi_jykahdigits
                    ON 开户信息表 (交易卡号_digits)
                    WHERE 交易卡号_digits IS NOT NULL AND 交易卡号_digits != '';

                    CREATE INDEX IF NOT EXISTS idx_kaihuxinxi_jyzhdigits
                    ON 开户信息表 (交易账号_digits)
                    WHERE 交易账号_digits IS NOT NULL AND 交易账号_digits != '';
                """)
                logging.info("✅ _digits字段索引创建完成")
            except Exception as e:
                logging.warning(f"⚠️ _digits字段索引创建失败（不影响清洗功能）: {e}")
        else:
            logging.info("⏭️ 无_digits数据，跳过索引创建")

        # 不再清理财付通交易明细表，按用户要求跳过
        logging.info("跳过财付通交易明细表清理（按用户要求）")
        
        logging.info(f"特殊字符清理总计完成，共处理 {total_cleaned_count} 条记录")
        return total_cleaned_count

    except Exception as e:
        logging.error(f"清理特殊字符时发生错误: {e}")
        raise e

def standardize_cash_counterparty_names(cursor, case_id):
    """
    标准化现金对手户名：将"库存现金"更新为"现金"

    功能说明：
    - 本文件的功能和实现逻辑：在数据清洗过程中标准化"库存现金"对手户名
    - 如果对手户名为"库存现金"，不论对手账号、对手卡号是否为空，以及摘要说明、交易类型为任何值
    - 此时对手户名都更新为"现金"

    处理规则：
    - 只处理"库存现金"这一个关键词
    - 对手户名为"库存现金" → 更新为"现金"
    - 对手户名为"库存现金 " (带空格) → 更新为"现金"

    Args:
        cursor: 数据库游标
        case_id: 案件编号

    Returns:
        int: 更新的记录数
    """
    logging.info("开始标准化库存现金对手户名")
    try:
        # 只处理"库存现金"这一个关键词
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "对手户名" = '现金'
            WHERE "案件编号" = %s
            AND (
                "对手户名" = '库存现金' OR
                TRIM("对手户名") = '库存现金'
            )
        """, (case_id,))

        updated_count = cursor.rowcount or 0

        if updated_count > 0:
            logging.info(f"✅ 将对手户名'库存现金' → '现金'：{updated_count} 条记录")
            logging.info(f"🎯 库存现金对手户名标准化完成，共更新 {updated_count} 条记录")
        else:
            logging.info("ℹ️ 未发现对手户名为'库存现金'的记录")

        return updated_count

    except Exception as e:
        logging.error(f"标准化库存现金对手户名时发生错误: {e}")
        raise e

def fill_counterparty_name_by_cash_flag(cursor, case_id):
    """
    根据现金标志字段填充对手户名为现金

    功能说明：
    - 本文件的功能和实现逻辑：根据现金标志字段来填充对手户名
    - 只有当现金标志为"现金"且对手户名为空时，才填充对手户名为"现金"
    - 其他的现金标志值不进行处理

    处理规则：
    - 现金标志 = "现金" 且 对手户名为空 → 对手户名填充为"现金"
    - 其他值不处理

    Args:
        cursor: 数据库游标
        case_id: 案件编号

    Returns:
        int: 更新的记录数
    """
    logging.info("开始根据现金标志字段填充对手户名")
    try:
        # 只处理现金标志为"现金"的情况
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "对手户名" = '现金'
            WHERE "案件编号" = %s
            AND ("对手户名" IS NULL OR "对手户名" = '')
            AND (
                "现金标志" = '现金' OR
                TRIM("现金标志") = '现金'
            )
        """, (case_id,))

        updated_count = cursor.rowcount or 0

        if updated_count > 0:
            logging.info(f"✅ 现金标志'现金' → 对手户名'现金'：{updated_count} 条记录")
            logging.info(f"🎯 根据现金标志填充对手户名完成，共更新 {updated_count} 条记录")
        else:
            logging.info("ℹ️ 未发现现金标志为'现金'且对手户名为空的记录")

        return updated_count

    except Exception as e:
        logging.error(f"根据现金标志填充对手户名时发生错误: {e}")
        raise e

def complement_transaction_account_fields(cursor, case_id):
    """
    账户交易明细表的交易账号和交易账卡号字段的互补
    
    功能说明：
    1. 若交易账号为空，则将交易账卡号的值复制到交易账号
    2. 若交易账卡号为空，则将交易账号的值复制到交易账卡号
    3. 若都有值，则跳过
    4. 若都为空，也跳过
    
    注意：_digits字段已在导入阶段生成，此处不再重复生成
    
    Args:
        cursor: 数据库游标
        case_id: 案件编号
        
    Returns:
        int: 更新的记录数
    """
    try:
        logging.info("开始执行账户交易明细表的交易账号和交易账卡号字段互补")
        
        total_updated_count = 0
        
        # 1. 交易账号为空时，用交易账卡号填充
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易账号" = "交易账卡号"
            WHERE "案件编号" = %s
            AND (
                "交易账号" IS NULL OR 
                TRIM("交易账号") = ''
            )
            AND "交易账卡号" IS NOT NULL 
            AND TRIM("交易账卡号") != ''
        """, (case_id,))
        
        account_updated = cursor.rowcount or 0
        total_updated_count += account_updated
        logging.info(f"交易账号字段互补完成，更新了 {account_updated} 条记录")
        
        # 2. 交易账卡号为空时，用交易账号填充
        cursor.execute("""
            UPDATE "账户交易明细表"
            SET "交易账卡号" = "交易账号"
            WHERE "案件编号" = %s
            AND (
                "交易账卡号" IS NULL OR 
                TRIM("交易账卡号") = ''
            )
            AND "交易账号" IS NOT NULL 
            AND TRIM("交易账号") != ''
        """, (case_id,))
        
        card_updated = cursor.rowcount or 0
        total_updated_count += card_updated
        logging.info(f"交易账卡号字段互补完成，更新了 {card_updated} 条记录")
        
        # 🔢 在字段互补后，重新生成_digits字段
        logging.info("字段互补完成后，重新生成_digits字段...")
        
        cursor.execute("""
            UPDATE "账户交易明细表" 
            SET 
                "交易账号_digits" = regexp_replace("交易账号", '[^0-9]', '', 'g'),
                "交易账卡号_digits" = regexp_replace("交易账卡号", '[^0-9]', '', 'g')
            WHERE "案件编号" = %s
            AND (
                ("交易账号" IS NOT NULL AND ("交易账号_digits" IS NULL OR "交易账号_digits" = '')) OR
                ("交易账卡号" IS NOT NULL AND ("交易账卡号_digits" IS NULL OR "交易账卡号_digits" = ''))
            );
        """, (case_id,))
        
        digits_updated = cursor.rowcount or 0
        logging.info(f"账户交易明细表_digits字段重新生成完成，更新了 {digits_updated} 条记录")
        
        logging.info(f"账户交易明细表字段互补总计完成，共更新 {total_updated_count} 条记录")
        return total_updated_count
        
    except Exception as e:
        logging.error(f"账户交易明细表字段互补时发生错误: {e}")
        raise e

def clean_numeric_account_names(cursor, case_id):
    """
    清理开户信息表中的数字账户开户名称：
    检测账户开户名称字段是否为纯数字，若为数字则将该字段及开户人证件号码字段设为空值
    
    功能说明：
    1. 检测账户开户名称是否为纯数字（去除空格后全部为数字字符）
    2. 如果是纯数字，将账户开户名称设为空值
    3. 同时将对应行的开户人证件号码也设为空值
    4. 为后续的数据增强步骤准备干净的数据
    
    Args:
        cursor: 数据库游标
        case_id: 案件编号
        
    Returns:
        int: 清理的记录数
    """
    try:
        logging.info("🔄 开始清理开户信息表中的数字账户开户名称...")
        
        # 检查开户信息表是否存在
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = '开户信息表'
            )
        """)
        table_exists_result = cursor.fetchone()
        table_exists = table_exists_result[0] if table_exists_result else False
        
        if not table_exists:
            logging.warning("⚠️ 开户信息表不存在，跳过数字账户名称清理步骤")
            return 0
        
        # 清理纯数字的账户开户名称
        clean_sql = '''
            UPDATE "开户信息表"
            SET 
                "账户开户名称" = NULL,
                "开户人证件号码" = NULL
            WHERE "案件编号" = %s
              AND "账户开户名称" IS NOT NULL 
              AND "账户开户名称" != ''
              AND TRIM("账户开户名称") ~ '^[0-9]+$'
        '''
        
        cursor.execute(clean_sql, (case_id,))
        cleaned_count = cursor.rowcount
        
        if cleaned_count > 0:
            logging.info(f"✅ 已清理 {cleaned_count} 条纯数字账户开户名称记录")
            logging.info(f"📝 清理内容：将纯数字的账户开户名称及对应的开户人证件号码设为空值")
        else:
            logging.info("✅ 未发现纯数字账户开户名称，无需清理")
        
        return cleaned_count
        
    except Exception as e:
        logging.error(f"清理数字账户开户名称时发生错误: {e}")
        raise e

def enrich_account_opening_info(cursor, case_id):
    """
    增强开户信息表数据：多渠道查询客户名称和证件号码
    
    功能说明：
    1. 当账户开户名称为空时，使用交易卡号/交易账号从账户信息_客户基本信息表查询客户名称并填充
    2. 当开户人证件号码为空时，使用交易卡号/交易账号从账户信息_客户基本信息表查询证件号码并填充
    3. 对于账户开户名称仍为空的记录，使用交易卡号/交易账号从账户交易明细表的对手信息中查询非空中文户名并填充
    4. 删除最终账户开户名称仍为空的开户信息表记录
    
    数据验证：
    - 对手户名必须包含中文字符（使用正则表达式 [\u4e00-\u9fa5]）
    - 对手户名长度至少2个字符
    - 排除"现金"等特殊值
    
    Args:
        cursor: 数据库游标
        case_id: 案件编号
        
    Returns:
        dict: 详细的统计信息，包括更新和删除的记录数
    """
    try:
        logging.info("🔄 开始增强开户信息表数据...")
        
        updated_count = 0
        
        # 1. 处理账户开户名称为空的情况
        logging.info("📝 处理账户开户名称为空的记录...")
        
        # 1.1 使用交易卡号查询客户名称
        update_name_by_card_sql = '''
            UPDATE "开户信息表" 
            SET "账户开户名称" = sub."客户名称"
            FROM (
                SELECT DISTINCT "卡号", "客户名称"
                FROM "账户信息_客户基本信息表"
                WHERE "案件编号" = %s 
                  AND "客户名称" IS NOT NULL 
                  AND "客户名称" != ''
                  AND "卡号" IS NOT NULL 
                  AND "卡号" != ''
            ) AS sub
            WHERE "开户信息表"."案件编号" = %s
              AND ("开户信息表"."账户开户名称" IS NULL OR "开户信息表"."账户开户名称" = '')
              AND "开户信息表"."交易卡号" = sub."卡号"
              AND "开户信息表"."交易卡号" IS NOT NULL 
              AND "开户信息表"."交易卡号" != ''
        '''
        
        cursor.execute(update_name_by_card_sql, (case_id, case_id))
        card_name_count = cursor.rowcount
        updated_count += card_name_count
        logging.info(f"✅ 通过交易卡号更新账户开户名称：{card_name_count} 条记录")
        
        # 1.2 使用交易账号查询客户名称（对于仍然为空的记录）
        update_name_by_account_sql = '''
            UPDATE "开户信息表" 
            SET "账户开户名称" = sub."客户名称"
            FROM (
                SELECT DISTINCT "账号", "客户名称"
                FROM "账户信息_客户基本信息表"
                WHERE "案件编号" = %s 
                  AND "客户名称" IS NOT NULL 
                  AND "客户名称" != ''
                  AND "账号" IS NOT NULL 
                  AND "账号" != ''
            ) AS sub
            WHERE "开户信息表"."案件编号" = %s
              AND ("开户信息表"."账户开户名称" IS NULL OR "开户信息表"."账户开户名称" = '')
              AND "开户信息表"."交易账号" = sub."账号"
              AND "开户信息表"."交易账号" IS NOT NULL 
              AND "开户信息表"."交易账号" != ''
        '''
        
        cursor.execute(update_name_by_account_sql, (case_id, case_id))
        account_name_count = cursor.rowcount
        updated_count += account_name_count
        logging.info(f"✅ 通过交易账号更新账户开户名称：{account_name_count} 条记录")
        
        # 2. 处理开户人证件号码为空的情况
        logging.info("📝 处理开户人证件号码为空的记录...")
        
        # 2.1 使用交易卡号查询证件号码
        update_cert_by_card_sql = '''
            UPDATE "开户信息表" 
            SET "开户人证件号码" = sub."证件号码_1"
            FROM (
                SELECT DISTINCT "卡号", "证件号码_1"
                FROM "账户信息_客户基本信息表"
                WHERE "案件编号" = %s 
                  AND "证件号码_1" IS NOT NULL 
                  AND "证件号码_1" != ''
                  AND "卡号" IS NOT NULL 
                  AND "卡号" != ''
            ) AS sub
            WHERE "开户信息表"."案件编号" = %s
              AND ("开户信息表"."开户人证件号码" IS NULL OR "开户信息表"."开户人证件号码" = '')
              AND "开户信息表"."交易卡号" = sub."卡号"
              AND "开户信息表"."交易卡号" IS NOT NULL 
              AND "开户信息表"."交易卡号" != ''
        '''
        
        cursor.execute(update_cert_by_card_sql, (case_id, case_id))
        card_cert_count = cursor.rowcount
        updated_count += card_cert_count
        logging.info(f"✅ 通过交易卡号更新开户人证件号码：{card_cert_count} 条记录")
        
        # 2.2 使用交易账号查询证件号码（对于仍然为空的记录）
        update_cert_by_account_sql = '''
            UPDATE "开户信息表" 
            SET "开户人证件号码" = sub."证件号码_1"
            FROM (
                SELECT DISTINCT "账号", "证件号码_1"
                FROM "账户信息_客户基本信息表"
                WHERE "案件编号" = %s 
                  AND "证件号码_1" IS NOT NULL 
                  AND "证件号码_1" != ''
                  AND "账号" IS NOT NULL 
                  AND "账号" != ''
            ) AS sub
            WHERE "开户信息表"."案件编号" = %s
              AND ("开户信息表"."开户人证件号码" IS NULL OR "开户信息表"."开户人证件号码" = '')
              AND "开户信息表"."交易账号" = sub."账号"
              AND "开户信息表"."交易账号" IS NOT NULL 
              AND "开户信息表"."交易账号" != ''
        '''
        
        cursor.execute(update_cert_by_account_sql, (case_id, case_id))
        account_cert_count = cursor.rowcount
        updated_count += account_cert_count
        logging.info(f"✅ 通过交易账号更新开户人证件号码：{account_cert_count} 条记录")
        
        # 3. 最后一步：从账户交易明细表的对手信息中补充账户开户名称
        logging.info("📝 从账户交易明细表的对手信息中补充账户开户名称...")
        
        # 3.1 通过交易卡号匹配对手卡号，获取对手户名
        update_name_by_opponent_card_sql = '''
            UPDATE "开户信息表" 
            SET "账户开户名称" = sub."对手户名"
            FROM (
                SELECT DISTINCT "对手卡号", "对手户名"
                FROM "账户交易明细表"
                WHERE "案件编号" = %s 
                  AND "对手户名" IS NOT NULL 
                  AND "对手户名" != ''
                  AND "对手户名" != '现金'
                  AND "对手卡号" IS NOT NULL 
                  AND "对手卡号" != ''
                  AND "对手户名" ~ '[\u4e00-\u9fa5]'  -- 包含中文字符
                  AND LENGTH("对手户名") >= 2  -- 至少2个字符
            ) AS sub
            WHERE "开户信息表"."案件编号" = %s
              AND ("开户信息表"."账户开户名称" IS NULL OR "开户信息表"."账户开户名称" = '')
              AND "开户信息表"."交易卡号" = sub."对手卡号"
              AND "开户信息表"."交易卡号" IS NOT NULL 
              AND "开户信息表"."交易卡号" != ''
        '''
        
        cursor.execute(update_name_by_opponent_card_sql, (case_id, case_id))
        opponent_card_count = cursor.rowcount
        updated_count += opponent_card_count
        logging.info(f"✅ 通过对手卡号更新账户开户名称：{opponent_card_count} 条记录")
        
        # 3.2 通过交易账号匹配对手账号，获取对手户名（对于仍然为空的记录）
        update_name_by_opponent_account_sql = '''
            UPDATE "开户信息表" 
            SET "账户开户名称" = sub."对手户名"
            FROM (
                SELECT DISTINCT "对手账号", "对手户名"
                FROM "账户交易明细表"
                WHERE "案件编号" = %s 
                  AND "对手户名" IS NOT NULL 
                  AND "对手户名" != ''
                  AND "对手户名" != '现金'
                  AND "对手账号" IS NOT NULL 
                  AND "对手账号" != ''
                  AND "对手户名" ~ '[\u4e00-\u9fa5]'  -- 包含中文字符
                  AND LENGTH("对手户名") >= 2  -- 至少2个字符
            ) AS sub
            WHERE "开户信息表"."案件编号" = %s
              AND ("开户信息表"."账户开户名称" IS NULL OR "开户信息表"."账户开户名称" = '')
              AND "开户信息表"."交易账号" = sub."对手账号"
              AND "开户信息表"."交易账号" IS NOT NULL 
              AND "开户信息表"."交易账号" != ''
        '''
        
        cursor.execute(update_name_by_opponent_account_sql, (case_id, case_id))
        opponent_account_count = cursor.rowcount
        updated_count += opponent_account_count
        logging.info(f"✅ 通过对手账号更新账户开户名称：{opponent_account_count} 条记录")
        
        # 4. 删除最终仍为空的账户开户名称记录
        logging.info("🗑️ 删除账户开户名称仍为空的开户信息表记录...")
        
        delete_empty_names_sql = '''
            DELETE FROM "开户信息表"
            WHERE "案件编号" = %s
              AND ("账户开户名称" IS NULL OR "账户开户名称" = '')
        '''
        
        cursor.execute(delete_empty_names_sql, (case_id,))
        deleted_count = cursor.rowcount
        logging.info(f"🗑️ 删除账户开户名称为空的记录：{deleted_count} 条")
        
        # 输出总计信息
        logging.info(f"🎯 开户信息表数据增强完成，总计更新 {updated_count} 条记录，删除 {deleted_count} 条记录")
        logging.info(f"📊 详细统计：")
        logging.info(f"   - 通过交易卡号更新客户名称：{card_name_count} 条")
        logging.info(f"   - 通过交易账号更新客户名称：{account_name_count} 条")
        logging.info(f"   - 通过交易卡号更新证件号码：{card_cert_count} 条")
        logging.info(f"   - 通过交易账号更新证件号码：{account_cert_count} 条")
        logging.info(f"   - 通过对手卡号更新账户开户名称：{opponent_card_count} 条")
        logging.info(f"   - 通过对手账号更新账户开户名称：{opponent_account_count} 条")
        logging.info(f"   - 删除账户开户名称为空的记录：{deleted_count} 条")
        
        # 返回详细的统计信息
        return {
            'total_updated': updated_count,
            'total_deleted': deleted_count,
            'card_name_updates': card_name_count,
            'account_name_updates': account_name_count,
            'card_cert_updates': card_cert_count,
            'account_cert_updates': account_cert_count,
            'opponent_card_name_updates': opponent_card_count,
            'opponent_account_name_updates': opponent_account_count,
            'empty_name_deletions': deleted_count
        }
        
    except Exception as e:
        logging.error(f"增强开户信息表数据时发生错误: {e}")
        raise e

def clean_customer_basic_info(cursor, case_id):
    """
    清洗账户信息_客户基本信息表：
    当证件类型为"账号/卡号"时，将查询对象名称复制到卡号字段，证件号码复制到账号字段
    
    Args:
        cursor: 数据库游标
        case_id: 案件编号
        
    Returns:
        int: 更新的记录数
    """
    try:
        logging.info("🔄 开始清洗账户信息_客户基本信息表...")
        
        # 检查表是否存在
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = '账户信息_客户基本信息表'
            )
        """)
        table_exists_result = cursor.fetchone()
        table_exists = table_exists_result[0] if table_exists_result else False
        
        if not table_exists:
            logging.warning("⚠️ 账户信息_客户基本信息表不存在，跳过清洗步骤")
            return 0
        
        # 处理证件类型为"账号/卡号"的记录
        update_sql = '''
            UPDATE "账户信息_客户基本信息表"
            SET 
                "卡号" = CASE 
                    WHEN ("卡号" IS NULL OR "卡号" = '') AND "查询对象名称" IS NOT NULL AND "查询对象名称" != ''
                    THEN "查询对象名称"
                    ELSE "卡号"
                END,
                "账号" = CASE 
                    WHEN ("账号" IS NULL OR "账号" = '') AND "证件号码" IS NOT NULL AND "证件号码" != ''
                    THEN "证件号码"
                    ELSE "账号"
                END
            WHERE "案件编号" = %s 
              AND ("证件类型" = '账号/卡号' OR "证件类型" = '账号' OR "证件类型" = '卡号')
              AND (
                  (("卡号" IS NULL OR "卡号" = '') AND "查询对象名称" IS NOT NULL AND "查询对象名称" != '')
                  OR 
                  (("账号" IS NULL OR "账号" = '') AND "证件号码" IS NOT NULL AND "证件号码" != '')
              )
        '''
        
        cursor.execute(update_sql, (case_id,))
        updated_count = cursor.rowcount
        
        logging.info(f"✅ 账户信息_客户基本信息表清洗完成，更新 {updated_count} 条记录")
        logging.info(f"📝 处理内容：证件类型为'账号/卡号'时，查询对象名称→卡号，证件号码→账号")
        
        return updated_count
        
    except Exception as e:
        logging.error(f"清洗账户信息_客户基本信息表时发生错误: {e}")
        raise e

def deduplicate_all_tables(cursor, case_id):
    """
    对本案件下的所有数据表进行去重操作
    
    Args:
        cursor: 数据库游标
        case_id: 案件编号
        
    Returns:
        dict: 各表去重统计信息
    """
    try:
        logging.info("🔄 开始对所有数据表进行去重操作...")
        
        # 定义需要去重的表和其排除列（扩展到92种数据表）
        tables_to_deduplicate = {
            # 账户交易明细表特殊配置（排除更多字段）
            "账户交易明细表": ["id", "ID", "导入时间", "导入批次", "现金标志", "交易场所", "交易是否成功", "交易方开户银行", "源文件位置", "查询账号", "查询卡号", "本方账号", "本方卡号", "交易账卡号_digits", "交易账号_digits", "对手账号_digits", "对手卡号_digits"],
            
            # 其他所有表统一配置（只排除基本字段）
            "开户信息表": ["id", "ID", "导入批次", "源文件位置"],
            "临时账户交易明细表": ["id", "ID", "导入批次", "源文件位置"],
            "财付通交易明细表": ["id", "ID", "导入批次", "源文件位置"],
            "账户信息_客户基本信息表": ["id", "ID", "导入批次", "源文件位置"],
            "增值税发票表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 公安部相关表
            "公安部_户籍人口_基本人员信息表": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_驾驶证_驾驶证信息表": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_交通违法_机动车违章信息表": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_旅馆住宿_旅馆住宿人员信息表": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_同车违章_同车违章表": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_同户人_同户人表": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_同住址_同住址表": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_出国_境_证件_出入境证件信息": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_机动车_机动车信息": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_在逃撤销_在逃人员撤销信息": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_在逃同案撤销人员_在逃同案撤销人员": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_在逃人员_在逃人员登记信息": ["id", "ID", "导入批次", "源文件位置"],
            "公安部_出入境记录_出入境记录信息表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 国家税务总局相关表
            "国家税务总局_纳税人登记信息_登记信息表": ["id", "ID", "导入批次", "源文件位置"],
            "国家税务总局_纳税信息_税务缴纳信息表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 金融理财相关表
            "金融理财_金融理财账户信息表": ["id", "ID", "导入批次", "源文件位置"],
            "金融理财_金融理财信息表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 账户信息相关表
            "账户信息_强制措施信息表": ["id", "ID", "导入批次", "源文件位置"],
            "账户信息_共有权优先权信息表": ["id", "ID", "导入批次", "源文件位置"],
            "账户信息_关联子账户信息表": ["id", "ID", "导入批次", "源文件位置"],
            "账户信息_关联子账户信息表本地": ["id", "ID", "导入批次", "源文件位置"],
            "账户信息（本地）_优先权信息表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 理财登记中心相关表
            "理财登记中心_理财产品_理财产品信息表": ["id", "ID", "导入批次", "源文件位置"],
            "理财登记中心_理财产品_投资行业信息表": ["id", "ID", "导入批次", "源文件位置"],
            "理财登记中心_理财产品_持有信息表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 电话相关表
            "电话_登记信息_运营商登记信息表": ["id", "ID", "导入批次", "源文件位置"],
            "电话_话单信息_运营商话单信息表": ["id", "ID", "导入批次", "源文件位置"],
            "虚拟运营商_登记信息_虚拟运营商登记信息表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 银保信相关表
            "银保信_保险产品_保险保单信息表": ["id", "ID", "导入批次", "源文件位置"],
            "银保信_保险产品_保险人员信息表": ["id", "ID", "导入批次", "源文件位置"],
            "银保信_保险产品_家庭财产保险表": ["id", "ID", "导入批次", "源文件位置"],
            "银保信_保险产品_航空延误保险表": ["id", "ID", "导入批次", "源文件位置"],
            "银保信_保险产品_保险赔案信息表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 中国航空相关表
            "中国航空_航班进出港_航班进出港已成行表": ["id", "ID", "导入批次", "源文件位置"],
            "中国航空_航班进出港_航班进出港未成行表": ["id", "ID", "导入批次", "源文件位置"],
            "中国航空_航班同行人信息_同订单同行人已成行": ["id", "ID", "导入批次", "源文件位置"],
            "中国航空_航班同行人信息_同订单同行人未成行": ["id", "ID", "导入批次", "源文件位置"],
            "中国航空_航班同行人信息_同乘三次以上同行人": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 信托登记公司相关表
            "信托登记公司_产品信息表": ["id", "ID", "导入批次", "源文件位置"],
            "信托登记公司_登记信息_受益权结构表": ["id", "ID", "导入批次", "源文件位置"],
            "信托登记公司_登记信息_合同信息表": ["id", "ID", "导入批次", "源文件位置"],
            "信托登记公司_委托人或受益人变动信息表": ["id", "ID", "导入批次", "源文件位置"],
            "信托登记公司_终止登记表": ["id", "ID", "导入批次", "源文件位置"],
            "信托登记公司_信托产品_委托人信息": ["id", "ID", "导入批次", "源文件位置"],
            "信托登记公司_信托产品_受益人信息": ["id", "ID", "导入批次", "源文件位置"],
            "信托登记公司_信托产品_登记信息_受益权结构": ["id", "ID", "导入批次", "源文件位置"],
            "信托登记公司_信托产品_登记信息_合同信息": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 市监相关表
            "市监_企业登记_企业基本信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_许可信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_自然人出资信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_非自然人出资信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_主要人员表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_变更备案信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_财务负责人信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_联络员信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_分支机构备案信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_清算基本信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_清算成员信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_吊销信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_注销信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_外资补充信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_内资补充信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_企业登记_企业公示_农专补充信息表": ["id", "ID", "导入批次", "源文件位置"],
            "市监_统一社会信用代码_统一社会信用代码表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 不动产查询相关表
            "不动产查询_不动产全国总库_房地产权表": ["id", "ID", "导入批次", "源文件位置"],
            "不动产查询_不动产全国总库_抵押权表": ["id", "ID", "导入批次", "源文件位置"],
            "不动产查询_不动产全国总库_预告登记表": ["id", "ID", "导入批次", "源文件位置"],
            "不动产查询_不动产全国总库_建设用地宅基地": ["id", "ID", "导入批次", "源文件位置"],
            "不动产查询_不动产全国总库_查封登记表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 中国铁路总公司相关表
            "中国铁路总公司_铁路客票_票面信息表": ["id", "ID", "导入批次", "源文件位置"],
            "中国铁路总公司_铁路客票_交易信息表": ["id", "ID", "导入批次", "源文件位置"],
            "中国铁路总公司_同订单同行人_同行人员信息表": ["id", "ID", "导入批次", "源文件位置"],
            "中国铁路总公司_同订单同行人_同行人员客票": ["id", "ID", "导入批次", "源文件位置"],
            "中国铁路总公司_用户注册_互联网注册信息表": ["id", "ID", "导入批次", "源文件位置"],
            "中国铁路总公司_用户注册_常用联系人信息表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 税务相关表
            "税务_增值税发票_增值税普通发票表": ["id", "ID", "导入批次", "源文件位置"],
            "税务_增值税发票_普票货物或应税劳务服务名": ["id", "ID", "导入批次", "源文件位置"],
            "税务_增值税发票_增值税专用发票表": ["id", "ID", "导入批次", "源文件位置"],
            "税务_增值税发票_专票货物或应税劳务名称表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 本地银行相关表
            "本地银行_客户信息本地表": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 医保相关表
            "医保_参保信息": ["id", "ID", "导入批次", "源文件位置"],
            "医保_药店购药": ["id", "ID", "导入批次", "源文件位置"],
            "医保_药店购药明细": ["id", "ID", "导入批次", "源文件位置"],
            "医保_普通门诊": ["id", "ID", "导入批次", "源文件位置"],
            "医保_住院结算数据": ["id", "ID", "导入批次", "源文件位置"],
            
            # 🆕 中国证券登记结算有限公司相关表
            "中国证券登记结算有限公司_证券账户_证券账户": ["id", "ID", "导入批次", "源文件位置"],
            "中国证券登记结算有限公司_证券持有变动_持": ["id", "ID", "导入批次", "源文件位置"],
            "中国证券登记结算有限公司_证券持有_持有信息": ["id", "ID", "导入批次", "源文件位置"]
        }
        
        dedup_stats = {}
        total_deduplicated = 0
        successful_dedup_count = 0
        failed_dedup_count = 0
        non_existent_tables = 0
        empty_tables = 0
        
        logging.info(f"📊 准备对 {len(tables_to_deduplicate)} 个数据表进行去重操作...")
        
        for table_name, exclude_columns in tables_to_deduplicate.items():
            try:
                # 检查表是否存在
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    )
                """, (table_name,))
                table_exists_result = cursor.fetchone()
                table_exists = table_exists_result[0] if table_exists_result else False
                
                if not table_exists:
                    logging.debug(f"⚠️ 表 '{table_name}' 不存在，跳过去重")
                    dedup_stats[table_name] = 0
                    non_existent_tables += 1
                    continue
                
                # 检查表中是否有当前案件的数据
                cursor.execute(f'SELECT COUNT(*) FROM "{table_name}" WHERE "案件编号" = %s', (case_id,))
                record_count_result = cursor.fetchone()
                record_count = record_count_result[0] if record_count_result else 0
                
                if record_count == 0:
                    logging.debug(f"ℹ️ 表 '{table_name}' 中没有案件 {case_id} 的数据，跳过去重")
                    dedup_stats[table_name] = 0
                    empty_tables += 1
                    continue
                
                # 执行去重操作
                logging.info(f"🔄 开始去重表 '{table_name}'（原有 {record_count} 条记录）...")
                deleted_count = deduplicate_data(cursor, table_name, case_id, exclude_columns)
                dedup_stats[table_name] = deleted_count
                total_deduplicated += deleted_count
                successful_dedup_count += 1
                
                if deleted_count > 0:
                    logging.info(f"✅ 表 '{table_name}' 去重完成，删除 {deleted_count} 条重复记录")
                else:
                    logging.debug(f"✅ 表 '{table_name}' 去重完成，无重复记录")
                
            except Exception as table_error:
                logging.error(f"❌ 去重表 '{table_name}' 时发生错误: {table_error}")
                dedup_stats[table_name] = 0
                failed_dedup_count += 1
                continue
        
        # 输出总计信息
        logging.info(f"🎯 所有表去重操作完成")
        logging.info(f"📊 去重统计总览：")
        logging.info(f"   - 总表数量: {len(tables_to_deduplicate)} 个")
        logging.info(f"   - 成功去重: {successful_dedup_count} 个表")
        logging.info(f"   - 不存在的表: {non_existent_tables} 个")
        logging.info(f"   - 无数据的表: {empty_tables} 个")
        logging.info(f"   - 去重失败: {failed_dedup_count} 个表")
        logging.info(f"   - 总计删除重复记录: {total_deduplicated} 条")
        
        # 只显示有去重记录的表
        logging.info(f"📋 有效去重详情：")
        for table_name, count in dedup_stats.items():
            if count > 0:
                logging.info(f"   - {table_name}：删除 {count} 条重复记录")
        
        dedup_stats['total_deduplicated'] = total_deduplicated
        dedup_stats['successful_dedup_count'] = successful_dedup_count
        dedup_stats['non_existent_tables'] = non_existent_tables
        dedup_stats['empty_tables'] = empty_tables
        dedup_stats['failed_dedup_count'] = failed_dedup_count
        
        return dedup_stats
        
    except Exception as e:
        logging.error(f"执行全表去重操作时发生错误: {e}")
        raise e

# 定义清洗步骤列表（按照业务逻辑顺序执行）
CLEANING_STEPS = [
    ('clean_customer_basic_info', clean_customer_basic_info),  # 🔧 第1步：清洗账户信息_客户基本信息表
    ('clean_transaction_details', clean_transaction_details),  # 第2步：清洗交易明细表
    ('clean_account_opening_info', clean_account_opening_info),  # 第3步：清洗开户信息表
    ('clean_special_characters', clean_special_characters),  # 第4步：特殊字符清理
    ('complement_transaction_account_fields', complement_transaction_account_fields),  # 🔧 第5步：账户交易明细表字段互补
    ('clean_numeric_account_names', clean_numeric_account_names),  # 🔧 第6步：清理数字账户名称
    ('enrich_account_opening_info', enrich_account_opening_info),  # 🔧 第7步：增强开户信息表数据
    ('preprocess_data', preprocess_data),  # 第8步：数据预处理
    ('match_transaction_names', match_transaction_names),  # 第9步：匹配交易户名
    ('match_certificate_numbers', match_certificate_numbers),  # 第10步：匹配证件号码
    ('match_opponent_names', match_opponent_names),  # 第11步：匹配对手户名
    ('check_and_correct_shoufu', check_and_correct_shoufu),  # 第12步：检查和修正收付
    ('fill_counterparty_name_with_cash', fill_counterparty_name_with_cash),  # 第13步：现金交易填充
    ('finalize_cleaning', finalize_cleaning),  # 第14步：最终清理
    ('deduplicate_all_tables', deduplicate_all_tables)  # 🔧 第15步：全表去重（最后执行）
]

def clean_data_step(case_id, step_name):
    """
    执行单个清洗步骤的纯函数。
    不依赖类成员变量，所有需要的参数都通过函数参数传递。
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        if step_name == 'clean_customer_basic_info':
            result = clean_customer_basic_info(cursor, case_id)
        elif step_name == 'clean_transaction_details':
            result = clean_transaction_details(cursor, case_id)
        elif step_name == 'clean_account_opening_info':
            result = clean_account_opening_info(cursor, case_id)
        elif step_name == 'clean_special_characters':
            result = clean_special_characters(cursor, case_id)
        elif step_name == 'standardize_cash_counterparty_names':
            result = standardize_cash_counterparty_names(cursor, case_id)
        elif step_name == 'fill_counterparty_name_by_cash_flag':
            result = fill_counterparty_name_by_cash_flag(cursor, case_id)
        elif step_name == 'complement_transaction_account_fields':
            result = complement_transaction_account_fields(cursor, case_id)
        elif step_name == 'clean_numeric_account_names':
            result = clean_numeric_account_names(cursor, case_id)
        elif step_name == 'enrich_account_opening_info':
            result = enrich_account_opening_info(cursor, case_id)
        elif step_name == 'preprocess_data':
            result = preprocess_data(cursor, case_id)
        elif step_name == 'match_transaction_names':
            result = match_transaction_names(cursor, case_id)
        elif step_name == 'match_certificate_numbers':
            result = match_certificate_numbers(cursor, case_id)
        elif step_name == 'match_opponent_names':
            result = match_opponent_names(cursor, case_id)
        elif step_name == 'check_and_correct_shoufu':
            result = check_and_correct_shoufu(cursor, case_id)
        elif step_name == 'fill_counterparty_name_with_cash':
            result = fill_counterparty_name_with_cash(cursor, case_id)
        elif step_name == 'finalize_cleaning':
            result = finalize_cleaning(cursor, case_id)
        elif step_name == 'deduplicate_all_tables':
            result = deduplicate_all_tables(cursor, case_id)
        
        conn.commit()
        return {'step': step_name, 'result': result, 'success': True}
    except Exception as e:
        conn.rollback()
        logging.error(f"步骤 {step_name} 执行失败: {e}")
        return {'step': step_name, 'error': str(e), 'success': False}
    finally:
        cursor.close()
        conn.close()

class DataCleaner(QObject):
    # 信号定义
    cleaning_finished_signal = Signal(dict)
    error_signal = Signal(Exception)
    show_dialog_signal = Signal(dict)
    shoufu_corrected_signal = Signal()
    show_manual_input_dialog_signal = Signal(str, int)
    progress_signal = Signal(int)
    step_progress_signal = Signal(str, int, int)  # 步骤名称, 当前步骤, 总步骤

    def __init__(self, case_id, login_user=None, selected_steps=None):
        super().__init__()
        self.case_id = case_id
        self.login_user = login_user
        self.selected_steps = selected_steps  # 用户选择的清洗步骤
        self.stats = {
            'total_cleaned_records': 0,
            'cleaned_customer_basic_info_count': 0,  # 🔧 新增：客户基本信息表清洗计数
            'cleaned_kaihuxinxi_count': 0,
            'cleaned_jiaoyimingxi_count': 0,
            'cleaned_special_characters_count': 0,
            'complemented_transaction_account_fields_count': 0,  # 🔧 新增：账户交易明细表字段互补计数
            'cleaned_numeric_account_names_count': 0,  # 🔧 新增：数字账户名称清理计数
            'enriched_account_opening_info_count': 0,  # 🔧 新增：开户信息表增强计数
            'matched_transaction_names': 0,
            'matched_certificate_numbers': 0,
            'matched_counterparty_names': 0,
            'filled_counterparty_names_with_cash': 0,
            'verified_transactions': 0,  # 🔧 新增：最终清理验证的交易记录数
            'total_deduplicated_records': 0  # 🔧 新增：去重删除记录计数
        }

    def start_cleaning(self):
        """
        按顺序执行清洗步骤，每个步骤完成后再执行下一个步骤
        支持选择性执行用户指定的步骤
        """
        try:
            # 根据用户选择过滤清洗步骤
            if self.selected_steps:
                # 只执行用户选中的步骤，保持原有顺序
                steps_to_execute = [(step_name, step_func) for step_name, step_func in CLEANING_STEPS 
                                   if step_name in self.selected_steps]
                logging.info(f"用户选择了 {len(steps_to_execute)} 个清洗步骤: {[step[0] for step in steps_to_execute]}")
            else:
                # 如果没有选择，执行所有步骤
                steps_to_execute = CLEANING_STEPS
                logging.info("执行所有清洗步骤")
            
            total_steps = len(steps_to_execute)
            
            if total_steps == 0:
                logging.warning("没有选择任何清洗步骤")
                self.cleaning_finished_signal.emit(self.stats)
                return
            
            # 逐个执行清洗步骤
            for i, (step_name, step_func) in enumerate(steps_to_execute):
                # 发送步骤进度信号
                self.step_progress_signal.emit(step_name, i + 1, total_steps)
                
                logging.info(f"开始执行清洗步骤 {i+1}/{total_steps}: {step_name}")
                
                try:
                    # 执行当前步骤
                    result = run_cleaning_step(self.case_id, step_name, step_func)
                    
                    if result['success']:
                        # 更新进度
                        progress = int(((i + 1) / total_steps) * 100)
                        self.progress_signal.emit(progress)
                        
                        # 更新统计信息
                        if 'result' in result:
                            self._update_stats(step_name, result['result'])
                            
                        logging.info(f"清洗步骤 {step_name} 完成")
                    else:
                        error_msg = f"步骤 {step_name} 失败: {result.get('error')}"
                        logging.error(error_msg)
                        self.error_signal.emit(Exception(error_msg))
                        # 如果步骤失败，停止后续步骤
                        return
                        
                except Exception as e:
                    error_msg = f"处理步骤 {step_name} 时发生错误: {e}"
                    logging.error(error_msg)
                    self.error_signal.emit(Exception(error_msg))
                    # 如果步骤出错，停止后续步骤
                    return

            # 所有步骤完成后发送完成信号
            self.cleaning_finished_signal.emit(self.stats)
            logging.info(f"选中的 {total_steps} 个清洗步骤已完成")
            
        except Exception as e:
            error_msg = f"数据清洗过程中发生错误: {e}"
            logging.error(error_msg)
            self.error_signal.emit(Exception(error_msg))
            raise

    def _update_stats(self, step_name, result):
        """更新清洗统计信息"""
        if isinstance(result, dict):
            # 特殊处理去重操作的结果
            if step_name == 'deduplicate_all_tables':
                # 从去重结果中提取总计数量
                if 'total_deduplicated' in result:
                    self.stats['total_deduplicated_records'] = result['total_deduplicated']
                # 将其他统计信息也合并到主统计中
                for key, value in result.items():
                    if key != 'total_deduplicated':
                        self.stats[f'dedup_{key}'] = value
            # 特殊处理开户信息表增强操作的结果
            elif step_name == 'enrich_account_opening_info':
                # 记录总更新数到标准字段
                self.stats['enriched_account_opening_info_count'] = result.get('total_updated', 0)
                # 记录详细统计信息
                for key, value in result.items():
                    self.stats[f'enrich_{key}'] = value
                # 更新总清洗记录数（只计算更新操作，不计算删除操作）
                self.stats['total_cleaned_records'] += result.get('total_updated', 0)
            # 特殊处理现金交易识别操作的结果
            elif step_name == 'fill_counterparty_name_with_cash':
                # 记录识别为现金的数量到标准字段
                self.stats['filled_counterparty_names_with_cash'] = result.get('cash_identified', 0)
                # 记录详细统计信息
                for key, value in result.items():
                    self.stats[f'cash_{key}'] = value
                # 更新总清洗记录数（只计算实际更新的记录）
                self.stats['total_cleaned_records'] += result.get('cash_identified', 0)
            else:
                # 其他字典类型结果直接合并
                self.stats.update(result)
        else:
            # 确保结果是数字类型
            result = 0 if result is None else int(result)
            # 根据步骤名称更新对应的统计数据
            stat_mapping = {
                'clean_customer_basic_info': 'cleaned_customer_basic_info_count',  # 🔧 新增
                'clean_transaction_details': 'cleaned_jiaoyimingxi_count',
                'clean_account_opening_info': 'cleaned_kaihuxinxi_count',
                'clean_special_characters': 'cleaned_special_characters_count',
                'complement_transaction_account_fields': 'complemented_transaction_account_fields_count',  # 🔧 新增
                'clean_numeric_account_names': 'cleaned_numeric_account_names_count',  # 🔧 新增
                'enrich_account_opening_info': 'enriched_account_opening_info_count',  # 🔧 新增
                'match_transaction_names': 'matched_transaction_names',
                'match_certificate_numbers': 'matched_certificate_numbers',
                'match_opponent_names': 'matched_counterparty_names',
                'fill_counterparty_name_with_cash': 'filled_counterparty_names_with_cash',
                'finalize_cleaning': 'verified_transactions'  # 🔧 修改：最终清理不再进行去重
            }
            if step_name in stat_mapping:
                self.stats[stat_mapping[step_name]] = result
                # 去重操作不计入总清洗记录数，因为它是删除操作
                if step_name != 'deduplicate_all_tables':
                    self.stats['total_cleaned_records'] += result

# 注意：实际的清洗函数已经定义在上面，这里不需要额外定义

# ... (其他辅助函数保持不变)

# 导出这些函数供其他模块使用
__all__ = ['DataCleaner', 'deduplicate_data', 'export_case_data']

def deduplicate_data(cursor, table_name, case_id, exclude_columns):
    """
    对指定表进行去重操作，排除指定的列。

    参数:
        cursor (psycopg2.cursor): 数据库游标。
        table_name (str): 表名。
        case_id (str): 案件编号。
        exclude_columns (list): 在去重时排除的列名。

    返回:
        int: 去重删除的行数。
    """
    try:
        # 获取所有列名并排除指定列 - PostgreSQL语法
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = %s
        """, (table_name,))
        columns_info = cursor.fetchall()
        all_columns = [info[0] for info in columns_info]
        
        # 🔧 修复：ID字段大小写问题 - 统一处理大小写
        exclude_columns_lower = [col.lower() for col in exclude_columns]
        dedup_columns = []
        
        for col in all_columns:
            # 检查字段是否在排除列表中（不区分大小写）
            if col.lower() not in exclude_columns_lower:
                dedup_columns.append(col)
            else:
                logging.debug(f"表 '{table_name}' 排除字段: {col}")

        if not dedup_columns:
            logging.warning(f"表 '{table_name}' 没有需要去重的列。")
            return 0  # 没有进行去重

        logging.info(f"表 '{table_name}' 去重字段: {dedup_columns}")
        group_by_str = ', '.join([f'"{col}"' for col in dedup_columns])
        temp_table = f"{table_name}_temp_{case_id.replace('-', '_')}"

        # 执行去重操作，无需显式管理事务
        cursor.execute(f"DROP TABLE IF EXISTS {temp_table}")
        
        # 🔧 优化：使用更高效的去重SQL
        cursor.execute(f"""
            CREATE TABLE {temp_table} AS
            WITH ranked AS (
                SELECT *, ROW_NUMBER() OVER (
                    PARTITION BY {group_by_str}
                    ORDER BY ctid
                ) as rn
                FROM "{table_name}"
                WHERE "案件编号" = %s
            )
            SELECT * FROM ranked WHERE rn = 1
        """, (case_id,))

        # 获取去重前的记录数
        cursor.execute(f'SELECT COUNT(*) FROM "{table_name}" WHERE "案件编号" = %s', (case_id,))
        before_count_result = cursor.fetchone()
        before_count = before_count_result[0] if before_count_result else 0
        logging.info(f"表 '{table_name}' 去重前记录数: {before_count}")

        # 删除原表中的相关记录并插入去重后的数据
        cursor.execute(f'DELETE FROM "{table_name}" WHERE "案件编号" = %s', (case_id,))
        
        # 获取列名以正确插入数据
        cursor.execute(f"""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = %s
            ORDER BY ordinal_position
        """, (table_name,))
        columns = [col[0] for col in cursor.fetchall()]
        columns_str = ', '.join([f'"{col}"' for col in columns])
        
        cursor.execute(f'INSERT INTO "{table_name}" ({columns_str}) SELECT {columns_str} FROM {temp_table}')
        cursor.execute(f"DROP TABLE {temp_table}")

        # 获取去重后的记录数
        cursor.execute(f'SELECT COUNT(*) FROM "{table_name}" WHERE "案件编号" = %s', (case_id,))
        after_count_result = cursor.fetchone()
        after_count = after_count_result[0] if after_count_result else 0
        logging.info(f"表 '{table_name}' 去重后记录数: {after_count}")

        deduplicated_rows = before_count - after_count
        logging.info(f"表 '{table_name}' 去重删除了 {deduplicated_rows} 条记录。")
        return deduplicated_rows

    except Exception as e:
        logging.error(f"执行去重操作时发生错误: {e}")
        raise e

def export_case_data(case_id, case_name):
    """
    导出案件数据 - 按分类导出
    
    功能说明：
    - 本文件的功能和实现逻辑：直接使用按分类导出功能
    - 按分类导出：将相同类别的表放在同一个Excel文件中，不同表作为不同工作表
    - 涵盖15个主要分类，支持103个数据表的智能分类导出
    """
    try:
        from pivot_export import export_data_by_category
        
        # 直接使用按分类导出功能
        logging.info(f"开始按分类导出案件数据: {case_name}")
        export_data_by_category(case_id, case_name)
        
    except Exception as e:
        logging.error(f"导出数据时发生错误: {e}")
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.critical(None, "导出错误", f"导出数据时发生错误: {str(e)}")
