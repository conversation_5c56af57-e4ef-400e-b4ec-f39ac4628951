def export_case_data(self):
    """导出案件数据"""
    try:
        from data_cleaning import export_case_data
        export_case_data(self.case_id, self.case_name)
    except Exception as e:
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.critical(self, "导出错误", f"导出数据时发生错误: {str(e)}")

# 在初始化UI的地方添加导出按钮的连接
self.export_button.clicked.connect(self.export_case_data)

# 或者在菜单项中添加
self.export_action = self.menu.addAction("导出数据")
self.export_action.triggered.connect(self.export_case_data) 