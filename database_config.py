"""
数据库配置模块

功能描述：
- 提供数据库配置对话框界面
- 支持PostgreSQL数据库连接参数配置
- 记录计算机硬件信息用于系统识别
- 配置信息保存到INI文件和数据库
- 密码保护功能，防止非授权用户修改数据库配置

实现逻辑：
1. PasswordDialog类实现密码验证对话框
2. DatabaseConfigDialog类实现可视化配置界面
3. 包含数据库连接参数输入框（主机、端口、数据库名、用户名、密码）
4. 自动获取计算机硬件信息（CPU、内存、操作系统等）
5. 测试数据库连接功能
6. 保存配置到db_config.ini文件
7. 将计算机信息保存到数据库系统信息表
"""

import os
import sys
import platform
import socket
import psutil
import uuid
from datetime import datetime
import configparser
import psycopg2
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, 
                              QLineEdit, QPushButton, QLabel, QMessageBox, 
                              QTextEdit, QGroupBox, QProgressBar, QCheckBox, QWidget)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont, QIcon


class PasswordDialog(QDialog):
    """密码验证对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("权限验证")
        self.setWindowIcon(QIcon('gui/images/icons/icon.ico'))
        self.setFixedSize(500, 280)
        self.setModal(True)
        
        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #2C313C;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 12px;
            }
            QLineEdit {
                background-color: #44475a;
                border: 1px solid #6272a4;
                border-radius: 6px;
                padding: 8px 12px;
                color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 2px solid #8be9fd;
            }
            QPushButton {
                background-color: #44475a;
                color: white;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
                border: none;
            }
            QPushButton:hover {
                background-color: #6272a4;
            }
            QPushButton:pressed {
                background-color: #282a36;
            }
            QPushButton#ok_button {
                background-color: #50fa7b;
                color: #2C313C;
            }
            QPushButton#ok_button:hover {
                background-color: #8be9fd;
                color: #2C313C;
            }
            QPushButton#cancel_button {
                background-color: #ff5555;
                color: white;
            }
            QPushButton#cancel_button:hover {
                background-color: #ff7777;
            }
            QWidget {
                background-color: transparent;
            }
        """)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("🔐 数据库配置权限验证")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #8be9fd; margin: 10px 0px;")
        layout.addWidget(title_label)
        
        # 说明文字
        info_label = QLabel("为了保护系统安全，修改数据库配置需要管理员权限。\n\n请输入管理员密码：")
        info_label.setFont(QFont("Microsoft YaHei", 12))
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #f8f8f2; margin: 10px 0px; line-height: 1.5;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 密码输入框区域
        password_widget = QWidget()
        password_layout = QHBoxLayout(password_widget)
        password_layout.setContentsMargins(0, 10, 0, 10)
        
        password_label = QLabel("管理员密码：")
        password_label.setFont(QFont("Microsoft YaHei", 12))
        password_label.setMinimumWidth(100)
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFont(QFont("Microsoft YaHei", 12))
        self.password_input.setPlaceholderText("请输入管理员密码")
        self.password_input.setMinimumHeight(35)
        self.password_input.returnPressed.connect(self.verify_password)
        
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        layout.addWidget(password_widget)
        
        # 添加一些垂直间距
        layout.addSpacing(20)
        
        # 按钮组
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)
        
        self.ok_button = QPushButton("确定")
        self.ok_button.setObjectName("ok_button")
        self.ok_button.setMinimumSize(80, 35)
        self.ok_button.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        self.ok_button.clicked.connect(self.verify_password)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setObjectName("cancel_button")
        self.cancel_button.setMinimumSize(80, 35)
        self.cancel_button.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addSpacing(10)
        button_layout.addWidget(self.cancel_button)
        button_layout.addStretch()
        layout.addWidget(button_widget)
        
        self.setLayout(layout)
        
        # 设置焦点到密码输入框
        self.password_input.setFocus()
    
    def verify_password(self):
        """验证密码"""
        entered_password = self.password_input.text()
        correct_password = "admin"  # 管理员密码
        
        if entered_password == correct_password:
            self.accept()  # 密码正确，关闭对话框并返回成功
        else:
            # 密码错误，显示错误提示
            QMessageBox.warning(self, "密码错误", 
                "❌ 密码错误！\n请联系系统管理员获取正确的管理员密码。")
            self.password_input.clear()
            self.password_input.setFocus()


class DatabaseTestThread(QThread):
    """数据库连接测试线程"""
    test_finished = Signal(bool, str)  # 测试完成信号：成功/失败，消息
    
    def __init__(self, db_params):
        super().__init__()
        self.db_params = db_params
    
    def run(self):
        try:
            # 尝试连接数据库
            conn = psycopg2.connect(
                host=self.db_params['host'],
                port=self.db_params['port'],
                database=self.db_params['database'],
                user=self.db_params['user'],
                password=self.db_params['password'],
                connect_timeout=10  # 10秒超时
            )
            
            # 测试基本查询
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            self.test_finished.emit(True, f"连接成功！\n数据库版本：{version}")
            
        except Exception as e:
            error_msg = f"连接失败：{str(e)}"
            self.test_finished.emit(False, error_msg)


class DatabaseConfigDialog(QDialog):
    """数据库配置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("数据库配置")
        self.setWindowIcon(QIcon('gui/images/icons/icon.ico'))
        self.setFixedSize(600, 700)
        self.setModal(True)
        
        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #2C313C;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #44475a;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit {
                background-color: #44475a;
                border: 1px solid #6272a4;
                border-radius: 4px;
                padding: 5px;
                color: white;
            }
            QLineEdit:focus {
                border: 2px solid #8be9fd;
            }
            QPushButton {
                background-color: #44475a;
                color: white;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #6272a4;
            }
            QPushButton:pressed {
                background-color: #282a36;
            }
            QPushButton:disabled {
                background-color: #3e4451;
                color: #6c7086;
            }
            QTextEdit {
                background-color: #44475a;
                border: 1px solid #6272a4;
                border-radius: 4px;
                color: white;
            }
            QProgressBar {
                border: 1px solid #6272a4;
                border-radius: 4px;
                text-align: center;
                background-color: #44475a;
            }
            QProgressBar::chunk {
                background-color: #50fa7b;
                border-radius: 4px;
            }
        """)
        
        self.init_ui()
        self.load_current_config()
        self.get_computer_info()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("数据库连接配置")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #8be9fd; margin: 10px;")
        layout.addWidget(title_label)
        
        # 数据库配置组
        db_group = QGroupBox("PostgreSQL数据库配置")
        db_layout = QFormLayout()
        
        # 数据库连接参数
        self.host_input = QLineEdit("localhost")
        self.port_input = QLineEdit("5432")
        self.database_input = QLineEdit("case_management")
        self.user_input = QLineEdit("postgres")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        
        # 显示密码复选框
        self.show_password_cb = QCheckBox("显示密码")
        self.show_password_cb.stateChanged.connect(self.toggle_password_visibility)
        
        db_layout.addRow("主机地址:", self.host_input)
        db_layout.addRow("端口:", self.port_input)
        db_layout.addRow("数据库名:", self.database_input)
        db_layout.addRow("用户名:", self.user_input)
        db_layout.addRow("密码:", self.password_input)
        db_layout.addRow("", self.show_password_cb)
        
        db_group.setLayout(db_layout)
        layout.addWidget(db_group)
        
        # 测试连接按钮和进度条
        test_layout = QHBoxLayout()
        self.test_button = QPushButton("测试数据库连接")
        self.test_button.clicked.connect(self.test_connection)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        test_layout.addWidget(self.test_button)
        test_layout.addWidget(self.progress_bar)
        layout.addLayout(test_layout)
        
        # 计算机信息组
        info_group = QGroupBox("计算机信息")
        info_layout = QVBoxLayout()
        
        self.computer_info_text = QTextEdit()
        self.computer_info_text.setMaximumHeight(200)
        self.computer_info_text.setReadOnly(True)
        
        info_layout.addWidget(self.computer_info_text)
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存配置")
        self.save_button.clicked.connect(self.save_config)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def toggle_password_visibility(self, state):
        """切换密码显示/隐藏"""
        if state == Qt.Checked:
            self.password_input.setEchoMode(QLineEdit.Normal)
        else:
            self.password_input.setEchoMode(QLineEdit.Password)
    
    def load_current_config(self):
        """加载当前配置"""
        try:
            config = configparser.ConfigParser()
            config_file = 'db_config.ini'
            
            if os.path.exists(config_file):
                config.read(config_file, encoding='utf-8')
                
                if 'PostgreSQL' in config:
                    self.host_input.setText(config['PostgreSQL'].get('host', 'localhost'))
                    self.port_input.setText(config['PostgreSQL'].get('port', '5432'))
                    self.database_input.setText(config['PostgreSQL'].get('database', 'case_management'))
                    self.user_input.setText(config['PostgreSQL'].get('user', 'postgres'))
                    self.password_input.setText(config['PostgreSQL'].get('password', ''))
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    def get_computer_info(self):
        """获取计算机硬件和系统信息"""
        try:
            # 获取系统信息
            system_info = {
                "计算机名称": socket.gethostname(),
                "操作系统": f"{platform.system()} {platform.release()}",
                "系统版本": platform.version(),
                "处理器架构": platform.machine(),
                "处理器": platform.processor() or "未知",
                "Python版本": platform.python_version(),
            }
            
            # 获取硬件信息
            try:
                # CPU信息
                system_info["CPU核心数"] = str(psutil.cpu_count(logical=False))
                system_info["CPU线程数"] = str(psutil.cpu_count(logical=True))
                
                # 内存信息
                memory = psutil.virtual_memory()
                system_info["总内存"] = f"{memory.total / (1024**3):.1f} GB"
                system_info["可用内存"] = f"{memory.available / (1024**3):.1f} GB"
                
                # 磁盘信息
                disk_usage = psutil.disk_usage('/')
                system_info["磁盘总容量"] = f"{disk_usage.total / (1024**3):.1f} GB"
                system_info["磁盘可用空间"] = f"{disk_usage.free / (1024**3):.1f} GB"
                
            except Exception as e:
                system_info["硬件信息获取错误"] = str(e)
            
            # 获取网络信息
            try:
                hostname = socket.gethostname()
                local_ip = socket.gethostbyname(hostname)
                system_info["本机IP地址"] = local_ip
            except Exception:
                system_info["本机IP地址"] = "获取失败"
            
            # 获取MAC地址
            try:
                mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> i) & 0xff) 
                                      for i in range(0, 48, 8)][::-1])
                system_info["MAC地址"] = mac_address
            except Exception:
                system_info["MAC地址"] = "获取失败"
            
            # 添加时间信息
            system_info["配置时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 格式化显示
            info_text = "系统将记录以下计算机信息：\n\n"
            for key, value in system_info.items():
                info_text += f"{key}: {value}\n"
            
            self.computer_info_text.setPlainText(info_text)
            self.computer_info = system_info
            
        except Exception as e:
            error_text = f"获取计算机信息时发生错误：{str(e)}"
            self.computer_info_text.setPlainText(error_text)
            self.computer_info = {"错误": str(e)}
    
    def test_connection(self):
        """测试数据库连接"""
        # 获取输入的连接参数
        db_params = {
            'host': self.host_input.text().strip(),
            'port': self.port_input.text().strip(),
            'database': self.database_input.text().strip(),
            'user': self.user_input.text().strip(),
            'password': self.password_input.text()
        }
        
        # 验证输入
        if not all([db_params['host'], db_params['port'], 
                   db_params['database'], db_params['user']]):
            QMessageBox.warning(self, "输入错误", "请填写所有必需的连接参数！")
            return
        
        try:
            port = int(db_params['port'])
            if port < 1 or port > 65535:
                raise ValueError("端口号无效")
            db_params['port'] = str(port)
        except ValueError:
            QMessageBox.warning(self, "输入错误", "端口号必须是1-65535之间的数字！")
            return
        
        # 显示进度条，禁用测试按钮
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        self.test_button.setEnabled(False)
        
        # 启动测试线程
        self.test_thread = DatabaseTestThread(db_params)
        self.test_thread.test_finished.connect(self.on_test_finished)
        self.test_thread.start()
    
    def on_test_finished(self, success, message):
        """数据库测试完成回调"""
        # 隐藏进度条，启用测试按钮
        self.progress_bar.setVisible(False)
        self.test_button.setEnabled(True)
        
        # 显示测试结果
        if success:
            QMessageBox.information(self, "连接测试", f"✅ {message}")
            # 连接成功后启用保存按钮
            self.save_button.setEnabled(True)
        else:
            QMessageBox.critical(self, "连接测试", f"❌ {message}")
    
    def save_config(self):
        """保存配置到文件和数据库"""
        try:
            # 获取配置参数
            config_data = {
                'host': self.host_input.text().strip(),
                'port': self.port_input.text().strip(),
                'database': self.database_input.text().strip(),
                'user': self.user_input.text().strip(),
                'password': self.password_input.text()
            }
            
            # 验证输入
            if not all([config_data['host'], config_data['port'], 
                       config_data['database'], config_data['user']]):
                QMessageBox.warning(self, "保存失败", "请填写所有必需的配置参数！")
                return
            
            # 验证端口号
            try:
                port = int(config_data['port'])
                if port < 1 or port > 65535:
                    raise ValueError("端口号无效")
            except ValueError:
                QMessageBox.warning(self, "保存失败", "端口号必须是1-65535之间的数字！")
                return
            
            # 保存到INI文件
            config = configparser.ConfigParser()
            config['PostgreSQL'] = config_data
            
            with open('db_config.ini', 'w', encoding='utf-8') as f:
                config.write(f)
            
            # 尝试连接数据库并保存计算机信息
            try:
                conn = psycopg2.connect(**config_data)
                cursor = conn.cursor()
                
                # 创建计算机信息表（如果不存在）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS 计算机信息表 (
                        id SERIAL PRIMARY KEY,
                        配置时间 TEXT,
                        计算机名称 TEXT,
                        操作系统 TEXT,
                        处理器 TEXT,
                        总内存 TEXT,
                        本机IP地址 TEXT,
                        MAC地址 TEXT,
                        系统详细信息 TEXT
                    )
                ''')
                
                # 插入计算机信息
                detail_info = "\n".join([f"{k}: {v}" for k, v in self.computer_info.items()])
                
                cursor.execute('''
                    INSERT INTO 计算机信息表 
                    (配置时间, 计算机名称, 操作系统, 处理器, 总内存, 本机IP地址, MAC地址, 系统详细信息)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ''', (
                    self.computer_info.get('配置时间', ''),
                    self.computer_info.get('计算机名称', ''),
                    self.computer_info.get('操作系统', ''),
                    self.computer_info.get('处理器', ''),
                    self.computer_info.get('总内存', ''),
                    self.computer_info.get('本机IP地址', ''),
                    self.computer_info.get('MAC地址', ''),
                    detail_info
                ))
                
                conn.commit()
                cursor.close()
                conn.close()
                
                QMessageBox.information(self, "保存成功", 
                    "✅ 数据库配置已保存成功！\n✅ 计算机信息已记录到数据库！")
                
            except Exception as db_error:
                # 即使数据库操作失败，配置文件已保存
                QMessageBox.warning(self, "部分保存成功", 
                    f"✅ 配置文件已保存\n⚠️ 数据库信息记录失败：{str(db_error)}")
            
            self.accept()  # 关闭对话框
            
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置时发生错误：{str(e)}")


def show_database_config_dialog(parent=None):
    """显示数据库配置对话框（带密码保护）"""
    # 首先显示密码验证对话框
    password_dialog = PasswordDialog(parent)
    if password_dialog.exec() != QDialog.Accepted:
        return False  # 密码验证失败或用户取消
    
    # 密码验证成功，显示数据库配置对话框
    dialog = DatabaseConfigDialog(parent)
    return dialog.exec()


def show_database_config_dialog_without_password(parent=None):
    """显示数据库配置对话框（无密码保护，用于系统启动时的配置）"""
    dialog = DatabaseConfigDialog(parent)
    return dialog.exec()


def test_database_connection():
    """测试当前配置的数据库连接"""
    try:
        config = configparser.ConfigParser()
        config_file = 'db_config.ini'
        
        if not os.path.exists(config_file):
            return False, "配置文件不存在"
        
        config.read(config_file, encoding='utf-8')
        
        if 'PostgreSQL' not in config:
            return False, "配置文件格式错误"
        
        db_params = {
            'host': config['PostgreSQL']['host'],
            'port': config['PostgreSQL']['port'],
            'database': config['PostgreSQL']['database'],
            'user': config['PostgreSQL']['user'],
            'password': config['PostgreSQL']['password']
        }
        
        # 尝试连接
        conn = psycopg2.connect(**db_params, connect_timeout=5)
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.close()
        conn.close()
        
        return True, "数据库连接正常"
        
    except Exception as e:
        return False, f"数据库连接失败：{str(e)}"


if __name__ == "__main__":
    # 测试代码
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    result = show_database_config_dialog()
    if result == QDialog.Accepted:
        print("配置已保存")
    else:
        print("配置已取消")
    
    sys.exit() 