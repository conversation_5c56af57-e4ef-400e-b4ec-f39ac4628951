#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确现金识别修复脚本

本文件的功能和实现逻辑：
1. 基于实际数据分析，精确识别真正的现金交易
2. 区分ATM转账和ATM现金交易
3. 正确标记现金存取款交易

修复策略：
- ATM取款：已经正确标记，无需修改
- 跨网点ATM取现借卡账户：这些是现金取现，需要标记
- 存款：只标记对手信息为空的现金存款
- ATM：只标记对手信息为空的ATM现金交易
"""

import psycopg2
import configparser
import logging
from datetime import datetime

# 设置日志
log_file = f'precise_cash_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class PreciseCashFixer:
    def __init__(self, case_id):
        self.case_id = case_id
        self.conn = None
        self.cursor = None
        
    def connect_database(self):
        try:
            config = configparser.ConfigParser()
            config.read('db_config.ini')
            
            self.conn = psycopg2.connect(
                host=config['PostgreSQL']['host'],
                port=config['PostgreSQL']['port'],
                database=config['PostgreSQL']['database'],
                user=config['PostgreSQL']['user'],
                password=config['PostgreSQL']['password']
            )
            self.cursor = self.conn.cursor()
            logging.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logging.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def analyze_cash_transactions(self):
        """分析现金交易类型"""
        logging.info("🔍 分析各种交易类型的现金特征...")
        
        try:
            # 分析各种交易类型
            transaction_types = [
                'ATM取款', 'ATM', '跨网点ATM取现借卡账户', '存款', 
                '现金存入', '现金支取', '网络ATM取款'
            ]
            
            for tx_type in transaction_types:
                logging.info(f"\n📊 分析交易类型: {tx_type}")
                
                # 总数
                self.cursor.execute(f'''
                    SELECT COUNT(*) FROM "账户交易明细表" 
                    WHERE "案件编号" = '{self.case_id}' AND "交易类型" = '{tx_type}'
                ''')
                total = self.cursor.fetchone()[0]
                
                if total == 0:
                    logging.info(f"   总数: 0 (跳过)")
                    continue
                
                # 已标记为现金的
                self.cursor.execute(f'''
                    SELECT COUNT(*) FROM "账户交易明细表" 
                    WHERE "案件编号" = '{self.case_id}' 
                    AND "交易类型" = '{tx_type}'
                    AND "对手户名" = '现金'
                ''')
                cash_marked = self.cursor.fetchone()[0]
                
                # 对手信息为空的
                self.cursor.execute(f'''
                    SELECT COUNT(*) FROM "账户交易明细表" 
                    WHERE "案件编号" = '{self.case_id}' 
                    AND "交易类型" = '{tx_type}'
                    AND ("对手户名" IS NULL OR "对手户名" = '')
                    AND ("对手账号" IS NULL OR "对手账号" = '')
                    AND ("对手卡号" IS NULL OR "对手卡号" = '')
                ''')
                empty_info = self.cursor.fetchone()[0]
                
                # 有对手信息的
                has_counterparty = total - empty_info
                
                logging.info(f"   总数: {total}")
                logging.info(f"   已标记现金: {cash_marked}")
                logging.info(f"   对手信息为空: {empty_info}")
                logging.info(f"   有对手信息: {has_counterparty}")
                
                # 显示样本
                if empty_info > 0 and cash_marked < empty_info:
                    logging.info(f"   ⚠️ 有 {empty_info - cash_marked} 条对手信息为空但未标记为现金")
                    
                    # 显示未标记的样本
                    self.cursor.execute(f'''
                        SELECT "摘要说明", "对手户名", "交易金额"
                        FROM "账户交易明细表" 
                        WHERE "案件编号" = '{self.case_id}' 
                        AND "交易类型" = '{tx_type}'
                        AND ("对手户名" IS NULL OR "对手户名" = '')
                        AND ("对手账号" IS NULL OR "对手账号" = '')
                        AND ("对手卡号" IS NULL OR "对手卡号" = '')
                        AND "对手户名" != '现金'
                        LIMIT 3
                    ''')
                    
                    samples = self.cursor.fetchall()
                    for i, (summary, counterparty, amount) in enumerate(samples, 1):
                        logging.info(f"     样本{i}: 摘要='{summary}', 对手户名='{counterparty}', 金额={amount}")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 分析现金交易时出错: {e}")
            return False
    
    def fix_cash_transactions_precisely(self):
        """精确修复现金交易"""
        logging.info("🔧 开始精确修复现金交易...")
        
        try:
            total_fixed = 0
            
            # 1. 修复跨网点ATM取现借卡账户（这些明显是现金取现）
            logging.info("🔧 修复跨网点ATM取现借卡账户...")
            self.cursor.execute(f'''
                UPDATE "账户交易明细表"
                SET "对手户名" = '现金'
                WHERE "案件编号" = '{self.case_id}'
                AND "交易类型" LIKE '%ATM%取现%'
                AND ("对手户名" IS NULL OR "对手户名" = '')
                AND ("对手账号" IS NULL OR "对手账号" = '')
                AND ("对手卡号" IS NULL OR "对手卡号" = '')
                AND "对手户名" != '现金'
            ''')
            atm_cross_fixed = self.cursor.rowcount
            total_fixed += atm_cross_fixed
            logging.info(f"   跨网点ATM取现: 标记了 {atm_cross_fixed} 条记录")
            
            # 2. 修复存款（只标记对手信息为空的）
            logging.info("🔧 修复存款交易...")
            self.cursor.execute(f'''
                UPDATE "账户交易明细表"
                SET "对手户名" = '现金'
                WHERE "案件编号" = '{self.case_id}'
                AND ("交易类型" LIKE '%存款%' OR "交易类型" LIKE '%存现%')
                AND ("对手户名" IS NULL OR "对手户名" = '')
                AND ("对手账号" IS NULL OR "对手账号" = '')
                AND ("对手卡号" IS NULL OR "对手卡号" = '')
                AND "对手户名" != '现金'
                AND NOT (
                    LOWER("摘要说明") LIKE '%转账%' OR
                    LOWER("交易类型") LIKE '%转账%'
                )
            ''')
            deposit_fixed = self.cursor.rowcount
            total_fixed += deposit_fixed
            logging.info(f"   存款: 标记了 {deposit_fixed} 条记录")
            
            # 3. 修复ATM（只标记对手信息为空的）
            logging.info("🔧 修复ATM交易...")
            self.cursor.execute(f'''
                UPDATE "账户交易明细表"
                SET "对手户名" = '现金'
                WHERE "案件编号" = '{self.case_id}'
                AND ("交易类型" LIKE '%ATM%' OR "交易类型" LIKE '%取款%' OR "交易类型" LIKE '%取现%')
                AND ("对手户名" IS NULL OR "对手户名" = '')
                AND ("对手账号" IS NULL OR "对手账号" = '')
                AND ("对手卡号" IS NULL OR "对手卡号" = '')
                AND "对手户名" != '现金'
                AND NOT (
                    LOWER("摘要说明") LIKE '%转账%' OR
                    LOWER("交易类型") LIKE '%转账%'
                )
            ''')
            atm_fixed = self.cursor.rowcount
            total_fixed += atm_fixed
            logging.info(f"   ATM: 标记了 {atm_fixed} 条记录")
            
            # 4. 修复明确的现金关键词
            logging.info("🔧 修复明确的现金关键词...")
            cash_keywords = ['现金存入', '现金支取', '现金取款', '现金存款', '取现', '存现']
            
            for keyword in cash_keywords:
                self.cursor.execute(f'''
                    UPDATE "账户交易明细表"
                    SET "对手户名" = '现金'
                    WHERE "案件编号" = '{self.case_id}' 
                    AND ("对手户名" IS NULL OR "对手户名" = '')
                    AND ("对手账号" IS NULL OR "对手账号" = '')
                    AND ("对手卡号" IS NULL OR "对手卡号" = '')
                    AND "对手户名" != '现金'
                    AND (
                        LOWER("摘要说明") LIKE '%{keyword.lower()}%' OR
                        LOWER("交易类型") LIKE '%{keyword.lower()}%'
                    )
                    AND NOT (
                        LOWER("摘要说明") LIKE '%转账%' OR
                        LOWER("交易类型") LIKE '%转账%'
                    )
                ''')
                keyword_fixed = self.cursor.rowcount
                if keyword_fixed > 0:
                    total_fixed += keyword_fixed
                    logging.info(f"   关键词'{keyword}': 标记了 {keyword_fixed} 条记录")
            
            # 提交更改
            self.conn.commit()
            logging.info(f"✅ 总共标记了 {total_fixed} 条记录为现金")
            
            # 验证结果
            self.cursor.execute(f'''
                SELECT COUNT(*) FROM "账户交易明细表" 
                WHERE "案件编号" = '{self.case_id}' AND "对手户名" = '现金'
            ''')
            total_cash = self.cursor.fetchone()[0]
            logging.info(f"📊 现在总共有 {total_cash} 条现金记录")
            
            # 显示各类型的现金记录统计
            logging.info("📊 各交易类型的现金记录统计:")
            cash_types = ['ATM取款', 'ATM', '跨网点ATM取现借卡账户', '存款']
            
            for cash_type in cash_types:
                self.cursor.execute(f'''
                    SELECT COUNT(*) FROM "账户交易明细表" 
                    WHERE "案件编号" = '{self.case_id}' 
                    AND "交易类型" = '{cash_type}'
                    AND "对手户名" = '现金'
                ''')
                count = self.cursor.fetchone()[0]
                logging.info(f"   {cash_type}: {count} 条现金记录")
            
            return total_fixed > 0
            
        except Exception as e:
            logging.error(f"❌ 精确修复现金交易时出错: {e}")
            return False
    
    def run_precise_fix(self):
        """运行精确修复"""
        if not self.connect_database():
            return False
        
        try:
            # 分析现金交易
            self.analyze_cash_transactions()
            
            # 精确修复
            fixed = self.fix_cash_transactions_precisely()
            
            if fixed:
                logging.info("🎉 现金识别问题已精确修复！")
            else:
                logging.info("ℹ️ 没有找到需要修复的现金交易")
            
            return True
            
        finally:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()

def main():
    case_id = "20250715175620"
    
    fixer = PreciseCashFixer(case_id)
    
    print(f"🔧 开始精确修复现金识别 - 案件: {case_id}")
    print(f"📄 详细日志保存到: {log_file}")
    
    success = fixer.run_precise_fix()
    
    if success:
        print("\n✅ 精确修复完成！")
        return 0
    else:
        print("\n❌ 精确修复发现问题")
        return 1

if __name__ == "__main__":
    exit(main())
