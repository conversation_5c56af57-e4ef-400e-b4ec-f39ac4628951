# 功能：同步开户信息表的交易卡号_digits和交易账号_digits字段，只处理未同步的行
import re
from database_setup import get_db_connection

def only_digits(s):
    return re.sub(r'\D', '', s) if s else None

def sync_kaihu_digits():
    conn = get_db_connection()
    cursor = conn.cursor()
    # 查询需要同步的记录
    cursor.execute('''
        SELECT id, "交易卡号", "交易账号", "交易卡号_digits", "交易账号_digits"
        FROM "开户信息表"
        WHERE (("交易卡号" IS NOT NULL AND ("交易卡号_digits" IS NULL OR "交易卡号_digits" = ''))
            OR ("交易账号" IS NOT NULL AND ("交易账号_digits" IS NULL OR "交易账号_digits" = '')))
    ''')
    rows = cursor.fetchall()
    update_count = 0
    for row in rows:
        id_, card, account, card_digits, account_digits = row
        updates = []
        values = []
        if card and (not card_digits or card_digits.strip() == ''):
            digits = only_digits(card)
            updates.append('"交易卡号_digits" = %s')
            values.append(digits)
        if account and (not account_digits or account_digits.strip() == ''):
            digits = only_digits(account)
            updates.append('"交易账号_digits" = %s')
            values.append(digits)
        if updates:
            sql = f'UPDATE "开户信息表" SET {", ".join(updates)} WHERE id = %s'
            values.append(id_)
            cursor.execute(sql, values)
            update_count += 1
    conn.commit()
    cursor.close()
    conn.close()
    print(f"开户信息表同步完成，共更新 {update_count} 条记录。")

if __name__ == "__main__":
    sync_kaihu_digits()