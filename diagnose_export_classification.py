#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出分类问题诊断脚本

本文件的功能和实现逻辑：
1. 检查导出分类规则文件是否存在和格式是否正确
2. 分析数据库中的表类型和导出规则的匹配情况
3. 诊断为什么导出时不能按分类生成文件
4. 提供修复建议和解决方案

主要检查项目：
- 规则文件存在性和格式
- 数据库表类型统计
- 规则映射完整性
- 导出逻辑问题
"""

import os
import pandas as pd
import logging
import configparser
from sqlalchemy import create_engine, text
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class ExportClassificationDiagnostic:
    """导出分类诊断器"""
    
    def __init__(self, case_id):
        self.case_id = case_id
        self.rule_file_path = "表类型匹配规则_导出文件名分类.xlsx"
        self.db_engine = None
        self.diagnosis_results = {}
    
    def setup_database(self):
        """设置数据库连接"""
        try:
            config = configparser.ConfigParser()
            config.read('db_config.ini')
            
            db_url = f"postgresql://{config['PostgreSQL']['user']}:{config['PostgreSQL']['password']}@" \
                    f"{config['PostgreSQL']['host']}:{config['PostgreSQL']['port']}/{config['PostgreSQL']['database']}"
            
            self.db_engine = create_engine(db_url, echo=False)
            logging.info("✅ 数据库连接建立成功")
            return True
            
        except Exception as e:
            logging.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def check_rule_file(self):
        """检查规则文件"""
        logging.info("🔍 检查导出分类规则文件...")
        
        result = {
            'file_exists': False,
            'file_readable': False,
            'columns': [],
            'row_count': 0,
            'sample_data': [],
            'issues': []
        }
        
        try:
            # 检查文件是否存在
            if not os.path.exists(self.rule_file_path):
                result['issues'].append(f"规则文件不存在: {self.rule_file_path}")
                logging.error(f"❌ 规则文件不存在: {self.rule_file_path}")
                return result
            
            result['file_exists'] = True
            logging.info(f"✅ 规则文件存在: {self.rule_file_path}")
            
            # 尝试读取文件
            df = pd.read_excel(self.rule_file_path)
            result['file_readable'] = True
            result['columns'] = df.columns.tolist()
            result['row_count'] = len(df)
            result['sample_data'] = df.head(10).to_dict('records')
            
            logging.info(f"✅ 规则文件读取成功")
            logging.info(f"   - 行数: {result['row_count']}")
            logging.info(f"   - 列名: {result['columns']}")
            
            # 检查必要的列
            required_columns = ['数据库表', '工作表', '导出文件']
            missing_columns = []
            
            for req_col in required_columns:
                found = False
                for col in result['columns']:
                    if req_col in col:
                        found = True
                        break
                if not found:
                    missing_columns.append(req_col)
            
            if missing_columns:
                result['issues'].append(f"缺少必要列: {missing_columns}")
                logging.warning(f"⚠️ 缺少必要列: {missing_columns}")
            else:
                logging.info("✅ 包含所有必要列")
            
            # 检查数据质量
            empty_rows = 0
            for _, row in df.iterrows():
                if pd.isna(row).all():
                    empty_rows += 1
            
            if empty_rows > 0:
                result['issues'].append(f"发现 {empty_rows} 行空数据")
                logging.warning(f"⚠️ 发现 {empty_rows} 行空数据")
            
        except Exception as e:
            result['issues'].append(f"读取文件时出错: {e}")
            logging.error(f"❌ 读取规则文件时出错: {e}")
        
        self.diagnosis_results['rule_file'] = result
        return result
    
    def get_database_table_types(self):
        """获取数据库中的表类型统计"""
        logging.info("📊 统计数据库中的表类型...")
        
        result = {
            'total_tables': 0,
            'table_types': {},
            'sample_tables': [],
            'issues': []
        }
        
        try:
            # 获取所有表名
            query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            """
            
            with self.db_engine.connect() as conn:
                tables_df = pd.read_sql(query, conn)
            
            result['total_tables'] = len(tables_df)
            result['sample_tables'] = tables_df['table_name'].head(20).tolist()
            
            logging.info(f"✅ 数据库中共有 {result['total_tables']} 个表")
            
            # 统计每个表的记录数（针对当前案件）
            table_stats = {}
            
            for table_name in tables_df['table_name']:
                try:
                    count_query = f'SELECT COUNT(*) as count FROM "{table_name}" WHERE "案件编号" = %s'
                    with self.db_engine.connect() as conn:
                        count_result = conn.execute(text(count_query), (self.case_id,))
                        count = count_result.fetchone()[0]
                    
                    if count > 0:
                        table_stats[table_name] = count
                        
                        # 按表类型分类
                        table_type = self.classify_table_type(table_name)
                        if table_type not in result['table_types']:
                            result['table_types'][table_type] = []
                        result['table_types'][table_type].append({
                            'table_name': table_name,
                            'record_count': count
                        })
                
                except Exception as e:
                    logging.debug(f"跳过表 {table_name}: {e}")
                    continue
            
            logging.info(f"✅ 找到 {len(table_stats)} 个有数据的表")
            logging.info(f"✅ 表类型分布: {list(result['table_types'].keys())}")
            
        except Exception as e:
            result['issues'].append(f"获取数据库表信息时出错: {e}")
            logging.error(f"❌ 获取数据库表信息时出错: {e}")
        
        self.diagnosis_results['database_tables'] = result
        return result
    
    def classify_table_type(self, table_name):
        """根据表名分类表类型"""
        if "医保" in table_name:
            return "医保信息"
        elif "电话" in table_name or "虚拟运营商" in table_name:
            return "通讯信息"
        elif "公安" in table_name:
            return "公安信息"
        elif "账户" in table_name or "开户" in table_name or "银行" in table_name:
            return "账户信息"
        elif "税务" in table_name or "纳税" in table_name:
            return "税务纳税信息"
        elif "发票" in table_name:
            return "增值税发票信息"
        elif "理财" in table_name:
            return "理财信息"
        elif "工商" in table_name:
            return "工商信息"
        elif "保险" in table_name:
            return "保险信息"
        else:
            return "其他"
    
    def analyze_mapping_coverage(self):
        """分析映射覆盖率"""
        logging.info("🔍 分析规则映射覆盖率...")
        
        result = {
            'rule_tables': set(),
            'db_tables': set(),
            'matched_tables': set(),
            'unmatched_db_tables': set(),
            'unmatched_rule_tables': set(),
            'coverage_rate': 0.0,
            'issues': []
        }
        
        try:
            # 从规则文件获取表名
            if self.diagnosis_results.get('rule_file', {}).get('file_readable', False):
                rule_df = pd.read_excel(self.rule_file_path)
                
                # 找到数据库表列
                db_col = None
                for col in rule_df.columns:
                    if '数据库表' in col:
                        db_col = col
                        break
                
                if db_col:
                    rule_tables = set(rule_df[db_col].dropna().astype(str).str.strip())
                    result['rule_tables'] = rule_tables
                    logging.info(f"✅ 规则文件中包含 {len(rule_tables)} 个表")
                else:
                    result['issues'].append("规则文件中未找到数据库表列")
            
            # 从数据库获取有数据的表名
            if 'database_tables' in self.diagnosis_results:
                db_tables = set()
                for table_type, tables in self.diagnosis_results['database_tables']['table_types'].items():
                    for table_info in tables:
                        db_tables.add(table_info['table_name'])
                result['db_tables'] = db_tables
                logging.info(f"✅ 数据库中有数据的表: {len(db_tables)} 个")
            
            # 计算匹配情况
            result['matched_tables'] = result['rule_tables'] & result['db_tables']
            result['unmatched_db_tables'] = result['db_tables'] - result['rule_tables']
            result['unmatched_rule_tables'] = result['rule_tables'] - result['db_tables']
            
            if len(result['db_tables']) > 0:
                result['coverage_rate'] = len(result['matched_tables']) / len(result['db_tables']) * 100
            
            logging.info(f"📊 映射覆盖率分析:")
            logging.info(f"   - 匹配的表: {len(result['matched_tables'])} 个")
            logging.info(f"   - 数据库中未匹配的表: {len(result['unmatched_db_tables'])} 个")
            logging.info(f"   - 规则中多余的表: {len(result['unmatched_rule_tables'])} 个")
            logging.info(f"   - 覆盖率: {result['coverage_rate']:.1f}%")
            
            if result['coverage_rate'] < 80:
                result['issues'].append(f"映射覆盖率较低: {result['coverage_rate']:.1f}%")
            
        except Exception as e:
            result['issues'].append(f"分析映射覆盖率时出错: {e}")
            logging.error(f"❌ 分析映射覆盖率时出错: {e}")
        
        self.diagnosis_results['mapping_coverage'] = result
        return result
    
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        report_file = f"export_classification_diagnosis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("# 导出分类问题诊断报告\n\n")
                f.write(f"**诊断时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**案件编号**: {self.case_id}\n\n")
                
                # 规则文件检查结果
                if 'rule_file' in self.diagnosis_results:
                    rule_data = self.diagnosis_results['rule_file']
                    f.write("## 规则文件检查\n\n")
                    f.write(f"- **文件存在**: {'✅' if rule_data['file_exists'] else '❌'}\n")
                    f.write(f"- **文件可读**: {'✅' if rule_data['file_readable'] else '❌'}\n")
                    f.write(f"- **行数**: {rule_data['row_count']}\n")
                    f.write(f"- **列名**: {rule_data['columns']}\n")
                    
                    if rule_data['issues']:
                        f.write("\n### 发现的问题\n\n")
                        for issue in rule_data['issues']:
                            f.write(f"- ❌ {issue}\n")
                
                # 数据库表统计
                if 'database_tables' in self.diagnosis_results:
                    db_data = self.diagnosis_results['database_tables']
                    f.write("\n## 数据库表统计\n\n")
                    f.write(f"- **总表数**: {db_data['total_tables']}\n")
                    f.write(f"- **有数据的表类型**: {len(db_data['table_types'])}\n")
                    
                    f.write("\n### 表类型分布\n\n")
                    for table_type, tables in db_data['table_types'].items():
                        f.write(f"#### {table_type} ({len(tables)}个表)\n\n")
                        for table_info in tables[:5]:  # 只显示前5个
                            f.write(f"- {table_info['table_name']}: {table_info['record_count']} 条记录\n")
                        if len(tables) > 5:
                            f.write(f"- ... 还有 {len(tables)-5} 个表\n")
                        f.write("\n")
                
                # 映射覆盖率分析
                if 'mapping_coverage' in self.diagnosis_results:
                    mapping_data = self.diagnosis_results['mapping_coverage']
                    f.write("## 映射覆盖率分析\n\n")
                    f.write(f"- **覆盖率**: {mapping_data['coverage_rate']:.1f}%\n")
                    f.write(f"- **匹配的表**: {len(mapping_data['matched_tables'])} 个\n")
                    f.write(f"- **未匹配的数据库表**: {len(mapping_data['unmatched_db_tables'])} 个\n")
                    f.write(f"- **规则中多余的表**: {len(mapping_data['unmatched_rule_tables'])} 个\n")
                    
                    if mapping_data['unmatched_db_tables']:
                        f.write("\n### 未匹配的数据库表\n\n")
                        for table in list(mapping_data['unmatched_db_tables'])[:10]:
                            f.write(f"- {table}\n")
                
                # 问题总结和建议
                f.write("\n## 问题总结和修复建议\n\n")
                
                all_issues = []
                for section, data in self.diagnosis_results.items():
                    if 'issues' in data:
                        all_issues.extend(data['issues'])
                
                if all_issues:
                    f.write("### 发现的问题\n\n")
                    for i, issue in enumerate(all_issues, 1):
                        f.write(f"{i}. {issue}\n")
                
                f.write("\n### 修复建议\n\n")
                f.write("1. **检查规则文件**：确保表类型匹配规则_导出文件名分类.xlsx文件存在且格式正确\n")
                f.write("2. **更新规则映射**：将未匹配的数据库表添加到规则文件中\n")
                f.write("3. **验证导出逻辑**：检查CategoryExportWorker的实现是否正确\n")
                f.write("4. **测试导出功能**：使用小数据集测试分类导出功能\n")
            
            logging.info(f"📄 诊断报告已生成: {report_file}")
            
        except Exception as e:
            logging.error(f"生成诊断报告失败: {e}")
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        logging.info("🚀 开始导出分类问题诊断...")
        
        if not self.setup_database():
            return False
        
        try:
            # 执行各项诊断
            self.check_rule_file()
            self.get_database_table_types()
            self.analyze_mapping_coverage()
            
            # 生成报告
            self.generate_diagnosis_report()
            
            logging.info("🎉 导出分类诊断完成！")
            return True
            
        except Exception as e:
            logging.error(f"❌ 诊断过程中出错: {e}")
            return False

def main():
    """主函数"""
    print("🔍 导出分类问题诊断工具")
    print("=" * 50)
    
    # 获取案件编号
    case_id = input("请输入案件编号: ").strip()
    if not case_id:
        print("❌ 案件编号不能为空")
        return 1
    
    # 创建诊断器
    diagnostic = ExportClassificationDiagnostic(case_id)
    
    # 运行诊断
    success = diagnostic.run_full_diagnosis()
    
    if success:
        print("\n✅ 诊断完成！")
        print("📄 请查看生成的诊断报告文件")
        return 0
    else:
        print("\n❌ 诊断失败！")
        print("📄 请查看日志文件了解详细错误信息")
        return 1

if __name__ == "__main__":
    exit(main())
