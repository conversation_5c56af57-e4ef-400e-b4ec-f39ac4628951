2025-08-05 12:37:56,429 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 12:37:56,429 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 12:37:56,429 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 12:37:56,429 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_123756.log
2025-08-05 12:37:57,133 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_123757.log
2025-08-05 12:37:57,262 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 13:24:05,170 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 13:24:05,170 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 13:24:05,170 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 13:24:05,170 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_132405.log
2025-08-05 13:24:05,703 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_132405.log
2025-08-05 13:24:05,758 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 13:52:57,301 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 13:52:57,301 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 13:52:57,301 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 13:52:57,302 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_135257.log
2025-08-05 13:52:59,223 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_135259.log
2025-08-05 13:52:59,266 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 14:11:54,632 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 14:11:54,633 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 14:11:54,633 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 14:11:54,634 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_141154.log
2025-08-05 14:11:56,751 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_141156.log
2025-08-05 14:11:56,791 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 14:44:27,707 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 14:44:27,708 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: G:\数据分析系统20250725\LOGS
2025-08-05 14:44:27,708 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 14:44:27,709 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\database_table_checker_20250805_144427.log
2025-08-05 14:44:28,989 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\import_data_20250805_144428.log
2025-08-05 14:44:29,223 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 14:50:59,593 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 14:50:59,594 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 14:50:59,594 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 14:50:59,595 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_145059.log
2025-08-05 14:51:00,187 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_145100.log
2025-08-05 14:51:00,255 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 15:18:09,956 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 15:18:09,956 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 15:18:09,956 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 15:18:09,958 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_151809.log
2025-08-05 15:18:10,530 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_151810.log
2025-08-05 15:18:10,594 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 16:02:02,964 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 16:02:02,964 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 16:02:02,965 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 16:02:02,966 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_160202.log
2025-08-05 16:02:03,458 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_160203.log
2025-08-05 16:02:03,547 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 18:01:31,706 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 18:01:31,706 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 18:01:31,707 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 18:01:31,708 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_180131.log
2025-08-05 18:01:32,969 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_180132.log
2025-08-05 18:01:33,130 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 18:50:04,858 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 18:50:04,860 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 18:50:04,860 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 18:50:04,861 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_185004.log
2025-08-05 18:50:05,714 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_185005.log
2025-08-05 18:50:05,809 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 19:18:29,493 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 19:18:29,493 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 19:18:29,493 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 19:18:29,495 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_191829.log
2025-08-05 19:18:30,175 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_191830.log
2025-08-05 19:18:30,256 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 19:56:42,048 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 19:56:42,049 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 19:56:42,049 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 19:56:42,049 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_195642.log
2025-08-05 19:56:42,809 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_195642.log
2025-08-05 19:56:42,887 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 20:27:35,168 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 20:27:35,168 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 20:27:35,168 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 20:27:35,168 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_202735.log
2025-08-05 20:27:35,915 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_202735.log
2025-08-05 20:27:35,996 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 20:28:09,060 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 20:28:09,061 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 20:28:09,062 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 20:28:09,063 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_202809.log
2025-08-05 20:28:09,474 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_202809.log
2025-08-05 20:28:09,501 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 20:54:28,000 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 20:54:28,000 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 20:54:28,000 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 20:54:28,001 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_205428.log
2025-08-05 21:00:38,801 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 21:00:38,801 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 21:00:38,801 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 21:00:38,801 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_210038.log
2025-08-05 21:00:40,237 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_210040.log
2025-08-05 21:00:40,361 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
