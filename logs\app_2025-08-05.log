2025-08-05 12:37:56,429 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 12:37:56,429 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 12:37:56,429 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 12:37:56,429 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_123756.log
2025-08-05 12:37:57,133 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_123757.log
2025-08-05 12:37:57,262 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 13:24:05,170 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 13:24:05,170 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 13:24:05,170 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 13:24:05,170 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_132405.log
2025-08-05 13:24:05,703 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_132405.log
2025-08-05 13:24:05,758 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 13:52:57,301 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 13:52:57,301 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 13:52:57,301 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 13:52:57,302 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_135257.log
2025-08-05 13:52:59,223 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_135259.log
2025-08-05 13:52:59,266 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 14:11:54,632 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 14:11:54,633 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 14:11:54,633 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 14:11:54,634 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_141154.log
2025-08-05 14:11:56,751 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_141156.log
2025-08-05 14:11:56,791 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 14:44:27,707 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 14:44:27,708 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: G:\数据分析系统20250725\LOGS
2025-08-05 14:44:27,708 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 14:44:27,709 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\database_table_checker_20250805_144427.log
2025-08-05 14:44:28,989 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\import_data_20250805_144428.log
2025-08-05 14:44:29,223 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-05 14:50:59,593 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-05.log
2025-08-05 14:50:59,594 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-05 14:50:59,594 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-05 14:50:59,595 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250805_145059.log
2025-08-05 14:51:00,187 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250805_145100.log
2025-08-05 14:51:00,255 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
