-- PostgreSQL中文文本搜索配置设置脚本
-- 使用说明：在PostgreSQL中以超级用户身份执行此脚本

-- ==========================================
-- 第一步：检查当前配置状态
-- ==========================================

-- 查看当前可用的文本搜索配置
SELECT '当前可用的文本搜索配置:' as info;
SELECT cfgname as "配置名称", cfgparser as "解析器" FROM pg_ts_config ORDER BY cfgname;

-- 查看当前可用的解析器
SELECT '当前可用的解析器:' as info;
SELECT prsname as "解析器名称" FROM pg_ts_parser ORDER BY prsname;

-- 查看当前已安装的扩展
SELECT '当前已安装的扩展:' as info;
SELECT extname as "扩展名称", extversion as "版本" FROM pg_extension WHERE extname LIKE '%zh%' OR extname LIKE '%jieba%' OR extname LIKE '%chinese%';

-- ==========================================
-- 第二步：尝试安装中文搜索扩展
-- ==========================================

-- 方法1：尝试安装zhparser扩展
DO $$
BEGIN
    BEGIN
        CREATE EXTENSION IF NOT EXISTS zhparser;
        RAISE NOTICE '成功安装zhparser扩展';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '无法安装zhparser扩展: %', SQLERRM;
    END;
END $$;

-- 方法2：尝试安装pg_jieba扩展
DO $$
BEGIN
    BEGIN
        CREATE EXTENSION IF NOT EXISTS pg_jieba;
        RAISE NOTICE '成功安装pg_jieba扩展';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '无法安装pg_jieba扩展: %', SQLERRM;
    END;
END $$;

-- ==========================================
-- 第三步：创建中文文本搜索配置
-- ==========================================

-- 如果zhparser可用，创建chinese_zh配置
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_ts_parser WHERE prsname = 'zhparser') THEN
        -- 创建或替换zhparser配置
        DROP TEXT SEARCH CONFIGURATION IF EXISTS chinese_zh CASCADE;
        CREATE TEXT SEARCH CONFIGURATION chinese_zh (PARSER = zhparser);
        
        -- 添加token映射
        ALTER TEXT SEARCH CONFIGURATION chinese_zh ADD MAPPING FOR 
        n,v,a,i,e,l WITH simple;
        
        RAISE NOTICE '成功创建chinese_zh配置';
    ELSE
        RAISE NOTICE 'zhparser解析器不可用，跳过chinese_zh配置创建';
    END IF;
END $$;

-- 如果pg_jieba可用，创建jieba配置
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_ts_parser WHERE prsname = 'jieba') THEN
        -- 创建或替换jieba配置
        DROP TEXT SEARCH CONFIGURATION IF EXISTS chinese CASCADE;
        CREATE TEXT SEARCH CONFIGURATION chinese (PARSER = jieba);
        
        -- 添加token映射
        ALTER TEXT SEARCH CONFIGURATION chinese ADD MAPPING FOR 
        word WITH simple;
        
        RAISE NOTICE '成功创建chinese配置（基于jieba）';
    ELSE
        RAISE NOTICE 'jieba解析器不可用，跳过chinese配置创建';
    END IF;
END $$;

-- 创建简单的中文配置作为后备
DO $$
BEGIN
    BEGIN
        DROP TEXT SEARCH CONFIGURATION IF EXISTS chinese_simple CASCADE;
        CREATE TEXT SEARCH CONFIGURATION chinese_simple (COPY = simple);
        RAISE NOTICE '成功创建chinese_simple配置';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '无法创建chinese_simple配置: %', SQLERRM;
    END;
END $$;

-- ==========================================
-- 第四步：测试中文分词效果
-- ==========================================

-- 测试文本
SELECT '测试中文分词效果:' as info;

-- 如果chinese_zh配置存在，测试它
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_ts_config WHERE cfgname = 'chinese_zh') THEN
        PERFORM to_tsvector('chinese_zh', '这是一个测试中文分词效果的例子，包含账户交易明细信息');
        RAISE NOTICE 'chinese_zh配置测试通过';
    END IF;
END $$;

-- 如果chinese配置存在，测试它
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_ts_config WHERE cfgname = 'chinese') THEN
        PERFORM to_tsvector('chinese', '这是一个测试中文分词效果的例子，包含账户交易明细信息');
        RAISE NOTICE 'chinese配置测试通过';
    END IF;
END $$;

-- 测试默认配置
DO $$
BEGIN
    PERFORM to_tsvector('这是一个测试中文分词效果的例子，包含账户交易明细信息');
    RAISE NOTICE '默认配置测试通过';
END $$;

-- ==========================================
-- 第五步：显示最终结果
-- ==========================================

SELECT '安装完成后的配置列表:' as info;
SELECT cfgname as "配置名称", 
       CASE 
           WHEN cfgname LIKE '%chinese%' OR cfgname LIKE '%zh%' OR cfgname LIKE '%jieba%' THEN '中文配置'
           ELSE '其他配置'
       END as "类型"
FROM pg_ts_config 
WHERE cfgname LIKE '%chinese%' OR cfgname LIKE '%zh%' OR cfgname LIKE '%jieba%' OR cfgname = 'simple'
ORDER BY cfgname;

-- 显示推荐使用的配置
SELECT '推荐配置（按优先级排序）:' as info;
SELECT cfgname as "配置名称",
       CASE cfgname
           WHEN 'chinese_zh' THEN '1 - 最佳（zhparser）'
           WHEN 'chinese' THEN '2 - 良好（jieba）'
           WHEN 'chinese_simple' THEN '3 - 基础（simple副本）'
           ELSE '4 - 默认'
       END as "优先级说明"
FROM pg_ts_config 
WHERE cfgname IN ('chinese_zh', 'chinese', 'chinese_simple', 'simple')
AND EXISTS (SELECT 1 FROM pg_ts_config WHERE cfgname = pg_ts_config.cfgname)
ORDER BY 
    CASE cfgname
        WHEN 'chinese_zh' THEN 1
        WHEN 'chinese' THEN 2
        WHEN 'chinese_simple' THEN 3
        ELSE 4
    END;

-- ==========================================
-- 使用说明
-- ==========================================

/*
使用方法：

1. 以PostgreSQL超级用户身份连接数据库：
   psql -U postgres -d your_database_name

2. 执行此脚本：
   \i postgresql_chinese_setup.sql

3. 根据输出信息确认配置是否成功创建

4. 如果需要，可以将系统默认配置设置为中文配置：
   ALTER DATABASE your_database_name SET default_text_search_config = 'chinese_zh';

注意事项：
- 需要超级用户权限来安装扩展
- 某些扩展可能需要预先安装系统依赖
- 如果扩展安装失败，系统会自动降级到默认配置
*/ 