#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析字段顺序问题

本文件的功能和实现逻辑：
1. 分析您提供的字段映射关系
2. 检查字段导入顺序是否正确
3. 找出IP地址、MAC地址被错误导入到交易发生地的原因
"""

import json
import logging
from datetime import datetime

# 设置日志
log_file = f'analyze_field_order_issue_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def analyze_user_mapping():
    """分析用户提供的字段映射关系"""
    logging.info("🔍 分析用户提供的字段映射关系...")
    
    # 用户提供的映射关系
    user_mapping = {
        "凭证号": ["凭证号"],
        "现金标志": ["现金标志"],
        "交易是否成功": ["交易是否成功"],
        "交易发生地": ["交易发生地"],
        "商户名称": ["商户名称"],
        "商户号": ["商户号"],
        "IP地址": ["IP地址"],
        "交易方开户银行": ["反馈单位"],
        "交易户名": ["查询对象名称"],
        "交易证件号码": ["证件号码"],
        "MAC地址": ["MAC地址"],
        "备注": ["备注"],
        "交易柜员号": ["交易柜员号"],
        "查询卡号": ["查询卡号"],
        "本方账号": ["本方账号"],
        "本方卡号": ["本方卡号"],
        "交易类型": ["交易类型"],
        "收付标志": ["借贷标志"],
        "交易币种": ["币种"],
        "交易金额": ["交易金额"],
        "交易余额": ["交易余额"],
        "交易日期": ["交易时间"],
        "交易流水号": ["交易流水号"],
        "对手户名": ["交易对方名称"],
        "对手账号": ["交易对方账号"],
        "对手卡号": ["交易对方卡号"],
        "对手身份证号": ["交易对方证件号码"],
        "对手余额": ["交易对手余额"],
        "对手开户银行": ["交易对方账号开户行"],
        "摘要说明": ["交易摘要"],
        "交易网点名称": ["交易网点名称"],
        "日志号": ["日志号"],
        "传票号": ["传票号"],
        "凭证种类": ["凭证种类"]
    }
    
    logging.info("📊 用户映射关系分析:")
    logging.info("=" * 60)
    
    # 重点关注的字段
    focus_fields = ["交易发生地", "IP地址", "MAC地址"]
    
    for field in focus_fields:
        if field in user_mapping:
            source_field = user_mapping[field][0]
            logging.info(f"数据库字段: {field} <- 源字段: {source_field}")
            
            # 检查是否存在问题
            if field == "交易发生地" and source_field in ["IP地址", "MAC地址"]:
                logging.error(f"❌ 发现错误映射: {source_field} -> {field}")
            elif field in ["IP地址", "MAC地址"] and source_field == "交易发生地":
                logging.error(f"❌ 发现错误映射: {source_field} -> {field}")
            else:
                logging.info(f"✅ 映射正常: {source_field} -> {field}")
        else:
            logging.warning(f"⚠️ 字段 {field} 未在映射中找到")
    
    logging.info("=" * 60)
    
    # 分析可能的问题
    problems = []
    
    # 检查映射关系的逻辑
    if user_mapping.get("交易发生地", [None])[0] == "交易发生地":
        logging.info("✅ 交易发生地映射正确")
    else:
        problems.append("交易发生地映射异常")
    
    if user_mapping.get("IP地址", [None])[0] == "IP地址":
        logging.info("✅ IP地址映射正确")
    else:
        problems.append("IP地址映射异常")
    
    if user_mapping.get("MAC地址", [None])[0] == "MAC地址":
        logging.info("✅ MAC地址映射正确")
    else:
        problems.append("MAC地址映射异常")
    
    return problems

def analyze_database_field_order():
    """分析数据库字段顺序"""
    logging.info("\n🔍 分析数据库字段顺序...")
    
    # 从database_setup.py中提取的字段顺序
    db_fields_order = [
        "交易账卡号", "交易账号", "交易户名", "交易证件号码", "交易方开户银行",
        "交易日期", "交易时间", "交易金额", "交易金额正负", "交易余额",
        "收付标志", "借方金额", "贷方金额", "对手账号", "对手卡号",
        "现金标志", "对手户名", "对手身份证号", "对手开户银行", "摘要说明",
        "交易币种", "商户名称", "商户号", "交易网点名称", "交易场所",
        "交易发生地",  # 第213行
        "交易是否成功", "传票号",
        "IP地址",      # 第216行
        "MAC地址",     # 第217行
        "交易流水号", "对手余额", "渠道", "交易类型", "日志号",
        "凭证种类", "凭证号", "交易柜员号", "备注", "案件编号",
        "源文件位置", "导入批次", "查询账号", "查询卡号", "本方账号", "本方卡号"
    ]
    
    logging.info("📊 数据库字段顺序分析:")
    logging.info("=" * 60)
    
    # 找到关键字段的位置
    key_fields = ["交易发生地", "IP地址", "MAC地址"]
    positions = {}
    
    for field in key_fields:
        if field in db_fields_order:
            pos = db_fields_order.index(field)
            positions[field] = pos
            logging.info(f"{field}: 位置 {pos + 1}")
        else:
            logging.error(f"❌ 字段 {field} 未在数据库字段列表中找到")
    
    logging.info("=" * 60)
    
    # 分析字段间的距离
    if "交易发生地" in positions and "IP地址" in positions and "MAC地址" in positions:
        location_pos = positions["交易发生地"]
        ip_pos = positions["IP地址"]
        mac_pos = positions["MAC地址"]
        
        logging.info(f"字段位置关系:")
        logging.info(f"  交易发生地: 位置 {location_pos + 1}")
        logging.info(f"  IP地址: 位置 {ip_pos + 1} (距离交易发生地: {ip_pos - location_pos})")
        logging.info(f"  MAC地址: 位置 {mac_pos + 1} (距离交易发生地: {mac_pos - location_pos})")
        
        # 检查是否存在位置混淆的可能
        if abs(ip_pos - location_pos) <= 3 or abs(mac_pos - location_pos) <= 3:
            logging.warning("⚠️ IP地址、MAC地址与交易发生地位置较近，可能存在索引混淆")
            return True
    
    return False

def analyze_import_logic():
    """分析导入逻辑中可能的问题"""
    logging.info("\n🔍 分析导入逻辑中可能的问题...")
    
    # 可能的问题场景
    potential_issues = [
        {
            "问题": "字段映射顺序错误",
            "描述": "在创建DataFrame时，字段顺序与数据库表结构不匹配",
            "可能原因": "使用了错误的字段索引或列顺序"
        },
        {
            "问题": "列名匹配错误",
            "描述": "Excel文件中的列名与映射规则不匹配",
            "可能原因": "Excel文件中IP地址、MAC地址列的实际内容被错误识别"
        },
        {
            "问题": "数据类型转换问题",
            "描述": "在数据导入过程中，字段值被错误地分配到其他字段",
            "可能原因": "DataFrame列的重新排序或索引错误"
        },
        {
            "问题": "批量插入时的字段顺序",
            "描述": "在执行SQL插入时，字段顺序与值的顺序不匹配",
            "可能原因": "INSERT语句中的字段顺序与数据顺序不一致"
        }
    ]
    
    logging.info("🔍 可能的问题场景:")
    logging.info("=" * 60)
    
    for i, issue in enumerate(potential_issues, 1):
        logging.info(f"{i}. {issue['问题']}")
        logging.info(f"   描述: {issue['描述']}")
        logging.info(f"   可能原因: {issue['可能原因']}")
        logging.info("-" * 40)
    
    return potential_issues

def generate_fix_recommendations():
    """生成修复建议"""
    logging.info("\n💡 生成修复建议...")
    
    recommendations = [
        {
            "优先级": "高",
            "建议": "检查具体的Excel文件内容",
            "操作": [
                "打开出问题的Excel文件",
                "确认IP地址、MAC地址、交易发生地列的实际内容",
                "检查列标题是否正确",
                "验证数据是否在正确的列中"
            ]
        },
        {
            "优先级": "高", 
            "建议": "检查字段映射规则的生成过程",
            "操作": [
                "重新生成该Excel文件的字段映射规则",
                "对比新生成的规则与现有规则",
                "确认字段映射的准确性"
            ]
        },
        {
            "优先级": "中",
            "建议": "检查数据导入过程中的字段顺序",
            "操作": [
                "在temp_import_data.py中添加调试日志",
                "记录DataFrame创建时的列顺序",
                "验证SQL插入时的字段顺序"
            ]
        },
        {
            "优先级": "中",
            "建议": "验证数据库插入逻辑",
            "操作": [
                "检查批量插入时的字段-值对应关系",
                "确认INSERT语句的字段顺序",
                "验证数据预处理过程"
            ]
        }
    ]
    
    logging.info("🎯 修复建议 (按优先级排序):")
    logging.info("=" * 60)
    
    for rec in recommendations:
        logging.info(f"优先级: {rec['优先级']}")
        logging.info(f"建议: {rec['建议']}")
        logging.info("操作步骤:")
        for i, step in enumerate(rec['操作'], 1):
            logging.info(f"  {i}. {step}")
        logging.info("-" * 40)
    
    return recommendations

def main():
    """主函数"""
    print(f"🔍 开始分析字段顺序问题")
    print(f"📄 详细日志保存到: {log_file}")
    
    # 分析用户映射关系
    logging.info("=" * 60)
    logging.info("步骤1: 分析用户提供的字段映射关系")
    logging.info("=" * 60)
    mapping_problems = analyze_user_mapping()
    
    # 分析数据库字段顺序
    logging.info("=" * 60)
    logging.info("步骤2: 分析数据库字段顺序")
    logging.info("=" * 60)
    order_issues = analyze_database_field_order()
    
    # 分析导入逻辑问题
    logging.info("=" * 60)
    logging.info("步骤3: 分析导入逻辑问题")
    logging.info("=" * 60)
    logic_issues = analyze_import_logic()
    
    # 生成修复建议
    logging.info("=" * 60)
    logging.info("步骤4: 生成修复建议")
    logging.info("=" * 60)
    recommendations = generate_fix_recommendations()
    
    # 总结
    print("\\n" + "=" * 60)
    print("🎯 分析结果总结")
    print("=" * 60)
    
    print("📊 关键发现:")
    print(f"1. 用户映射关系问题: {len(mapping_problems)} 个")
    print(f"2. 字段顺序问题: {'是' if order_issues else '否'}")
    print(f"3. 潜在逻辑问题: {len(logic_issues)} 个")
    
    print("\\n🎯 最可能的原因:")
    print("根据分析，IP地址、MAC地址被错误导入到交易发生地字段的原因可能是:")
    print("1. **Excel文件内容问题**: 实际的Excel文件中，IP地址和MAC地址的数据")
    print("   可能出现在了交易发生地列中")
    print("2. **字段映射执行错误**: 在数据导入过程中，字段映射没有正确执行")
    print("3. **数据预处理问题**: DataFrame处理过程中列顺序发生了错误")
    
    print("\\n💡 建议的修复步骤:")
    print("1. 🔍 **立即检查**: 打开具体的Excel文件，查看IP地址、MAC地址、交易发生地列的实际内容")
    print("2. 🔧 **重新映射**: 为该文件重新生成字段映射规则")
    print("3. 🧪 **测试导入**: 使用小样本数据测试导入功能")
    print("4. 📋 **验证结果**: 检查导入后的数据是否正确")
    
    return 0

if __name__ == "__main__":
    exit(main())
