2025-08-04 21:39:17.076 - INFO - [MainThread:26096] - enhanced_logging_patch.py:84 - setup_enhanced_logging() - 🔍 增强日志记录已启动
2025-08-04 21:39:17.093 - INFO - [MainThread:26096] - memory_optimizer.py:38 - __init__() - ✅ 内存监控模块已加载
2025-08-04 21:39:17.093 - INFO - [MainThread:26096] - memory_optimizer.py:42 - __init__() - 📊 系统内存信息: 总计 31.7GB, 可用 17.5GB, 使用率 44.9%
2025-08-04 21:39:17.268 - INFO - [MainThread:26096] - database_table_checker.py:417 - check_database_tables() - 开始数据库表格完整性检查...
2025-08-04 21:39:17.332 - INFO - [MainThread:26096] - database_table_checker.py:115 - load_excel_table_config() - 成功读取Excel文件，共94行数据
2025-08-04 21:39:17.354 - INFO - [MainThread:26096] - database_table_checker.py:169 - load_excel_table_config() - 从Excel文件解析出94个有效表配置（处理了94行数据）
2025-08-04 21:39:17.369 - INFO - [MainThread:26096] - database_table_checker.py:182 - load_excel_table_config() - Excel文件统计：总行数94，重复表名0个，有效表格94个
2025-08-04 21:39:17.491 - INFO - [MainThread:26096] - database_table_checker.py:444 - check_database_tables() - 数据库中现有表格总数：106
2025-08-04 21:39:17.491 - INFO - [MainThread:26096] - database_table_checker.py:456 - check_database_tables() - 核心系统表检查：9个必需，9个已存在
2025-08-04 21:39:24.503 - INFO - [MainThread:26096] - database_table_checker.py:507 - check_database_tables() - Excel配置表检查：94个定义，94个已存在
2025-08-04 21:39:24.503 - INFO - [MainThread:26096] - database_table_checker.py:511 - check_database_tables() - 所有Excel配置表都已存在，无需创建
2025-08-04 21:39:24.503 - INFO - [MainThread:26096] - database_table_checker.py:541 - check_database_tables() - 所有表都已存在，无需创建任何表
2025-08-04 21:39:24.504 - INFO - [MainThread:26096] - database_table_checker.py:344 - update_table_column_types() - 开始检查并更新表字段类型...
2025-08-04 21:39:24.723 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_建设用地宅基地' 的字段类型...
2025-08-04 21:39:24.724 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_房地产权表' 的字段类型...
2025-08-04 21:39:24.724 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_抵押权表' 的字段类型...
2025-08-04 21:39:24.724 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_查封登记表' 的字段类型...
2025-08-04 21:39:24.725 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_预告登记表' 的字段类型...
2025-08-04 21:39:24.725 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同乘三次以上同行人' 的字段类型...
2025-08-04 21:39:24.725 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同订单同行人已成行' 的字段类型...
2025-08-04 21:39:24.726 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同订单同行人未成行' 的字段类型...
2025-08-04 21:39:24.726 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班进出港_航班进出港已成行表' 的字段类型...
2025-08-04 21:39:24.726 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班进出港_航班进出港未成行表' 的字段类型...
2025-08-04 21:39:24.726 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券持有_持有信息' 的字段类型...
2025-08-04 21:39:24.727 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券持有变动_持' 的字段类型...
2025-08-04 21:39:24.727 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券账户_证券账户' 的字段类型...
2025-08-04 21:39:24.728 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_同订单同行人_同行人员信息表' 的字段类型...
2025-08-04 21:39:24.728 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_同订单同行人_同行人员客票' 的字段类型...
2025-08-04 21:39:24.728 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_用户注册_互联网注册信息表' 的字段类型...
2025-08-04 21:39:24.728 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_用户注册_常用联系人信息表' 的字段类型...
2025-08-04 21:39:24.728 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_铁路客票_交易信息表' 的字段类型...
2025-08-04 21:39:24.729 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_铁路客票_票面信息表' 的字段类型...
2025-08-04 21:39:24.729 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '临时账户交易明细表' 的字段类型...
2025-08-04 21:39:24.729 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_产品信息表' 的字段类型...
2025-08-04 21:39:24.729 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_受益人信息' 的字段类型...
2025-08-04 21:39:24.729 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_委托人信息' 的字段类型...
2025-08-04 21:39:24.730 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_登记信息_受益权结构' 的字段类型...
2025-08-04 21:39:24.730 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_登记信息_合同信息' 的字段类型...
2025-08-04 21:39:24.730 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_终止登记' 的字段类型...
2025-08-04 21:39:24.731 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_委托人或受益人变动信息表' 的字段类型...
2025-08-04 21:39:24.731 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_登记信息_受益权结构表' 的字段类型...
2025-08-04 21:39:24.731 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_登记信息_合同信息表' 的字段类型...
2025-08-04 21:39:24.732 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_终止登记表' 的字段类型...
2025-08-04 21:39:24.732 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_交通违法_机动车违章信息表' 的字段类型...
2025-08-04 21:39:24.733 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_出入境记录_出入境记录信息表' 的字段类型...
2025-08-04 21:39:24.733 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_出国_境_证件_出入境证件信息' 的字段类型...
2025-08-04 21:39:24.734 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同住址_同住址表' 的字段类型...
2025-08-04 21:39:24.734 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同户人_同户人表' 的字段类型...
2025-08-04 21:39:24.734 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同车违章_同车违章表' 的字段类型...
2025-08-04 21:39:24.734 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃人员_在逃人员登记信息' 的字段类型...
2025-08-04 21:39:24.735 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃同案撤销人员_在逃同案撤销人员' 的字段类型...
2025-08-04 21:39:24.735 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃撤销_在逃人员撤销信息' 的字段类型...
2025-08-04 21:39:24.735 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_户籍人口_基本人员信息表' 的字段类型...
2025-08-04 21:39:24.735 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_旅馆住宿_旅馆住宿人员信息表' 的字段类型...
2025-08-04 21:39:24.736 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_机动车_机动车信息' 的字段类型...
2025-08-04 21:39:24.736 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_驾驶证_驾驶证信息表' 的字段类型...
2025-08-04 21:39:24.736 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_住院结算数据' 的字段类型...
2025-08-04 21:39:24.736 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_参保信息' 的字段类型...
2025-08-04 21:39:24.736 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_普通门诊' 的字段类型...
2025-08-04 21:39:24.736 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_药店购药' 的字段类型...
2025-08-04 21:39:24.738 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_药店购药明细' 的字段类型...
2025-08-04 21:39:24.738 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '国家税务总局_纳税人登记信息_登记信息表' 的字段类型...
2025-08-04 21:39:24.738 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '国家税务总局_纳税信息_税务缴纳信息表' 的字段类型...
2025-08-04 21:39:24.739 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '增值税发票表' 的字段类型...
2025-08-04 21:39:24.739 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '字段匹配规则' 的字段类型...
2025-08-04 21:39:24.739 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '对手信息' 的字段类型...
2025-08-04 21:39:24.740 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '导入记录表' 的字段类型...
2025-08-04 21:39:24.740 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_主要人员表' 的字段类型...
2025-08-04 21:39:24.740 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_内资补充信息表' 的字段类型...
2025-08-04 21:39:24.740 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_农专补充信息表' 的字段类型...
2025-08-04 21:39:24.740 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_分支机构备案信息表' 的字段类型...
2025-08-04 21:39:24.740 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_变更备案信息表' 的字段类型...
2025-08-04 21:39:24.741 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_吊销信息表' 的字段类型...
2025-08-04 21:39:24.741 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_外资补充信息表' 的字段类型...
2025-08-04 21:39:24.741 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_注销信息表' 的字段类型...
2025-08-04 21:39:24.742 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_清算基本信息表' 的字段类型...
2025-08-04 21:39:24.742 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_清算成员信息表' 的字段类型...
2025-08-04 21:39:24.742 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_联络员信息表' 的字段类型...
2025-08-04 21:39:24.743 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_自然人出资信息表' 的字段类型...
2025-08-04 21:39:24.743 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_许可信息表' 的字段类型...
2025-08-04 21:39:24.743 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_财务负责人信息表' 的字段类型...
2025-08-04 21:39:24.743 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_非自然人出资信息表' 的字段类型...
2025-08-04 21:39:24.744 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业基本信息表' 的字段类型...
2025-08-04 21:39:24.744 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_统一社会信用代码_统一社会信用代码表' 的字段类型...
2025-08-04 21:39:24.744 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '开户信息表' 的字段类型...
2025-08-04 21:39:24.745 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '本地银行_客户信息本地表' 的字段类型...
2025-08-04 21:39:24.745 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '案件信息表' 的字段类型...
2025-08-04 21:39:24.745 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_投资行业信息表' 的字段类型...
2025-08-04 21:39:24.745 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_持有信息表' 的字段类型...
2025-08-04 21:39:24.746 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_理财产品信息表' 的字段类型...
2025-08-04 21:39:24.746 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '用户信息表' 的字段类型...
2025-08-04 21:39:24.746 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '电话_登记信息_运营商登记信息表' 的字段类型...
2025-08-04 21:39:24.746 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '电话_话单信息_运营商话单信息表' 的字段类型...
2025-08-04 21:39:24.747 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_专票货物或应税劳务名称表' 的字段类型...
2025-08-04 21:39:24.747 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_增值税专用发票表' 的字段类型...
2025-08-04 21:39:24.748 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_增值税普通发票表' 的字段类型...
2025-08-04 21:39:24.748 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_普票货物或应税劳务服务名' 的字段类型...
2025-08-04 21:39:24.748 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '系统信息表' 的字段类型...
2025-08-04 21:39:24.749 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '系统配置表' 的字段类型...
2025-08-04 21:39:24.749 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '虚拟运营商_登记信息_虚拟运营商登记信息表' 的字段类型...
2025-08-04 21:39:24.750 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '表类型匹配规则_导出文件名分类表' 的字段类型...
2025-08-04 21:39:24.751 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '表类型匹配规则表' 的字段类型...
2025-08-04 21:39:24.751 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '证券登记结算_证券持有变动_持' 的字段类型...
2025-08-04 21:39:24.751 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '证券登记结算_证券持有变动_证券持有变动' 的字段类型...
2025-08-04 21:39:24.752 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '财付通交易明细表' 的字段类型...
2025-08-04 21:39:24.752 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户交易明细表' 的字段类型...
2025-08-04 21:39:24.752 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_共有权优先权信息表' 的字段类型...
2025-08-04 21:39:24.753 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_关联子账户信息表' 的字段类型...
2025-08-04 21:39:24.753 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_关联子账户信息表本地' 的字段类型...
2025-08-04 21:39:24.753 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_客户基本信息表' 的字段类型...
2025-08-04 21:39:24.754 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_强制措施信息表' 的字段类型...
2025-08-04 21:39:24.754 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息（本地）_优先权信息表' 的字段类型...
2025-08-04 21:39:24.754 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '金融理财_金融理财信息表' 的字段类型...
2025-08-04 21:39:24.755 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '金融理财_金融理财账户信息表' 的字段类型...
2025-08-04 21:39:24.755 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险人员信息表' 的字段类型...
2025-08-04 21:39:24.755 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险保单信息表' 的字段类型...
2025-08-04 21:39:24.755 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险赔案信息表' 的字段类型...
2025-08-04 21:39:24.756 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_家庭财产保险表' 的字段类型...
2025-08-04 21:39:24.756 - INFO - [MainThread:26096] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_航空延误保险表' 的字段类型...
2025-08-04 21:39:24.756 - INFO - [MainThread:26096] - database_table_checker.py:404 - update_table_column_types() - 字段类型更新完成: 已更新 0 个字段, 跳过 2702 个字段, 失败 0 个字段
2025-08-04 21:39:24.841 - INFO - [MainThread:26096] - database_table_checker.py:580 - check_database_tables() - === 数据库表格检查报告 ===
2025-08-04 21:39:24.842 - INFO - [MainThread:26096] - database_table_checker.py:581 - check_database_tables() - 总计检查表格：103个 (核心9个 + Excel94个)
2025-08-04 21:39:24.842 - INFO - [MainThread:26096] - database_table_checker.py:582 - check_database_tables() - 最终存在表格：103个
2025-08-04 21:39:24.842 - INFO - [MainThread:26096] - database_table_checker.py:589 - check_database_tables() - 本次未创建任何新表格（所有表都已存在）
2025-08-04 21:39:44.670 - WARNING - [MainThread:26096] - import_error_handler.py:476 - check_for_previous_crash() - 检测到可能的程序崩溃，上次心跳: {'timestamp': 1754291939.8970847, 'operation': '心跳监控', 'pid': 17916, 'readable_time': '2025-08-04 15:18:59'}
2025-08-04 21:39:44.674 - WARNING - [MainThread:26096] - import_data.py:5457 - __init__() - 检测到之前的程序崩溃，将启用增强监控
2025-08-04 21:39:46.082 - INFO - [MainThread:26096] - import_error_handler.py:523 - start_heartbeat_monitor() - 心跳监控已启动
2025-08-04 21:39:46.105 - INFO - [MainThread:26096] - import_data.py:148 - __init__() - ✅ 自动化管理器已初始化
2025-08-04 21:39:49.024 - INFO - [Dummy-2:30068] - data_cleaning.py:2149 - start_cleaning() - 用户选择了 15 个清洗步骤: ['clean_customer_basic_info', 'clean_transaction_details', 'clean_account_opening_info', 'clean_special_characters', 'complement_transaction_account_fields', 'clean_numeric_account_names', 'enrich_account_opening_info', 'preprocess_data', 'match_transaction_names', 'match_certificate_numbers', 'match_opponent_names', 'check_and_correct_shoufu', 'fill_counterparty_name_with_cash', 'finalize_cleaning', 'deduplicate_all_tables']
2025-08-04 21:39:49.025 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 1/15: clean_customer_basic_info
2025-08-04 21:39:49.103 - INFO - [Dummy-2:30068] - data_cleaning.py:1752 - clean_customer_basic_info() - 🔄 开始清洗账户信息_客户基本信息表...
2025-08-04 21:39:49.115 - INFO - [Dummy-2:30068] - data_cleaning.py:1794 - clean_customer_basic_info() - ✅ 账户信息_客户基本信息表清洗完成，更新 0 条记录
2025-08-04 21:39:49.115 - INFO - [Dummy-2:30068] - data_cleaning.py:1795 - clean_customer_basic_info() - 📝 处理内容：证件类型为'账号/卡号'时，查询对象名称→卡号，证件号码→账号
2025-08-04 21:39:49.116 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 clean_customer_basic_info 完成
2025-08-04 21:39:49.116 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 2/15: clean_transaction_details
2025-08-04 21:39:49.179 - INFO - [Dummy-2:30068] - data_cleaning.py:326 - clean_transaction_details() - 开始清洗账户交易明细表
2025-08-04 21:39:49.188 - INFO - [Dummy-2:30068] - data_cleaning.py:331 - clean_transaction_details() - 🔧 清理交易账卡号和交易账号中的'-'值
2025-08-04 21:39:52.898 - INFO - [Dummy-2:30068] - data_cleaning.py:345 - clean_transaction_details() - ✅ 已清理 0 条记录中的'-'值
2025-08-04 21:39:53.341 - INFO - [Dummy-2:30068] - data_cleaning.py:363 - clean_transaction_details() - 🔍 发现 166903 条记录需要清洗币种数据
2025-08-04 21:39:53.347 - INFO - [Dummy-2:30068] - data_cleaning.py:366 - clean_transaction_details() - 🌐 标准化交易币种格式
2025-08-04 21:39:53.822 - INFO - [Dummy-2:30068] - data_cleaning.py:394 - clean_transaction_details() - ✅ 币种标准化完成，处理 0 条记录
2025-08-04 21:39:54.294 - INFO - [Dummy-2:30068] - data_cleaning.py:408 - clean_transaction_details() - 📊 账户交易明细表清洗完成统计：
2025-08-04 21:39:54.295 - INFO - [Dummy-2:30068] - data_cleaning.py:409 - clean_transaction_details() -   • 清理'-'值记录数：0
2025-08-04 21:39:54.297 - INFO - [Dummy-2:30068] - data_cleaning.py:410 - clean_transaction_details() -   • 币种标准化记录数：0
2025-08-04 21:39:54.298 - INFO - [Dummy-2:30068] - data_cleaning.py:411 - clean_transaction_details() -   • 总处理记录数：0
2025-08-04 21:39:54.299 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 clean_transaction_details 完成
2025-08-04 21:39:54.300 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 3/15: clean_account_opening_info
2025-08-04 21:39:54.364 - INFO - [Dummy-2:30068] - data_cleaning.py:428 - clean_account_opening_info() - 开始清洗开户信息表
2025-08-04 21:39:54.375 - INFO - [Dummy-2:30068] - data_cleaning.py:438 - clean_account_opening_info() - 🗑️ 删除交易账号和交易卡号都为空的记录
2025-08-04 21:39:54.379 - INFO - [Dummy-2:30068] - data_cleaning.py:448 - clean_account_opening_info() - ✅ 已删除 0 条交易账号和交易卡号都为空的记录
2025-08-04 21:39:54.380 - INFO - [Dummy-2:30068] - data_cleaning.py:458 - clean_account_opening_info() - 🔧 标准化剩余数据格式
2025-08-04 21:39:55.003 - INFO - [Dummy-2:30068] - data_cleaning.py:473 - clean_account_opening_info() - 📊 开户信息表清洗完成统计：
2025-08-04 21:39:55.005 - INFO - [Dummy-2:30068] - data_cleaning.py:474 - clean_account_opening_info() -   • 清洗前记录数：1122
2025-08-04 21:39:55.005 - INFO - [Dummy-2:30068] - data_cleaning.py:475 - clean_account_opening_info() -   • 删除无效记录：0
2025-08-04 21:39:55.005 - INFO - [Dummy-2:30068] - data_cleaning.py:476 - clean_account_opening_info() -   • 清洗后记录数：1122
2025-08-04 21:39:55.005 - INFO - [Dummy-2:30068] - data_cleaning.py:477 - clean_account_opening_info() -   • 标准化记录数：1122
2025-08-04 21:39:55.009 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 clean_account_opening_info 完成
2025-08-04 21:39:55.009 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 4/15: clean_special_characters
2025-08-04 21:39:55.077 - INFO - [Dummy-2:30068] - data_cleaning.py:1054 - clean_special_characters() - 开始清理特殊字符和无效值
2025-08-04 21:47:57.756 - INFO - [Dummy-2:30068] - data_cleaning.py:1142 - clean_special_characters() - 账户交易明细表清理完成，处理了 280487 条记录
2025-08-04 21:47:57.778 - INFO - [Dummy-2:30068] - data_cleaning.py:1175 - clean_special_characters() - 开户信息表清理完成，处理了 3 条记录
2025-08-04 21:47:57.780 - INFO - [Dummy-2:30068] - data_cleaning.py:1178 - clean_special_characters() - 开始生成_digits字段...
2025-08-04 21:48:00.939 - INFO - [Dummy-2:30068] - data_cleaning.py:1198 - clean_special_characters() - 账户交易明细表_digits字段生成完成，处理了 41 条记录
2025-08-04 21:48:00.961 - INFO - [Dummy-2:30068] - data_cleaning.py:1214 - clean_special_characters() - 开户信息表_digits字段生成完成，处理了 0 条记录
2025-08-04 21:48:00.964 - INFO - [Dummy-2:30068] - data_cleaning.py:1217 - clean_special_characters() - 🔢 _digits字段生成总计完成，共处理 41 条记录
2025-08-04 21:48:00.965 - INFO - [Dummy-2:30068] - data_cleaning.py:1222 - clean_special_characters() - 🔧 开始创建_digits字段索引...
2025-08-04 21:48:00.969 - INFO - [Dummy-2:30068] - data_cleaning.py:1249 - clean_special_characters() - ✅ _digits字段索引创建完成
2025-08-04 21:48:00.970 - INFO - [Dummy-2:30068] - data_cleaning.py:1256 - clean_special_characters() - 跳过财付通交易明细表清理（按用户要求）
2025-08-04 21:48:00.971 - INFO - [Dummy-2:30068] - data_cleaning.py:1258 - clean_special_characters() - 特殊字符清理总计完成，共处理 280490 条记录
2025-08-04 21:48:00.974 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 clean_special_characters 完成
2025-08-04 21:48:00.975 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 5/15: complement_transaction_account_fields
2025-08-04 21:48:01.202 - INFO - [Dummy-2:30068] - data_cleaning.py:1381 - complement_transaction_account_fields() - 开始执行账户交易明细表的交易账号和交易账卡号字段互补
2025-08-04 21:48:10.576 - INFO - [Dummy-2:30068] - data_cleaning.py:1400 - complement_transaction_account_fields() - 交易账号字段互补完成，更新了 0 条记录
2025-08-04 21:48:13.116 - INFO - [Dummy-2:30068] - data_cleaning.py:1417 - complement_transaction_account_fields() - 交易账卡号字段互补完成，更新了 0 条记录
2025-08-04 21:48:13.119 - INFO - [Dummy-2:30068] - data_cleaning.py:1420 - complement_transaction_account_fields() - 字段互补完成后，重新生成_digits字段...
2025-08-04 21:48:13.128 - INFO - [Dummy-2:30068] - data_cleaning.py:1435 - complement_transaction_account_fields() - 账户交易明细表_digits字段重新生成完成，更新了 1 条记录
2025-08-04 21:48:13.129 - INFO - [Dummy-2:30068] - data_cleaning.py:1437 - complement_transaction_account_fields() - 账户交易明细表字段互补总计完成，共更新 0 条记录
2025-08-04 21:48:13.132 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 complement_transaction_account_fields 完成
2025-08-04 21:48:13.133 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 6/15: clean_numeric_account_names
2025-08-04 21:48:13.395 - INFO - [Dummy-2:30068] - data_cleaning.py:1463 - clean_numeric_account_names() - 🔄 开始清理开户信息表中的数字账户开户名称...
2025-08-04 21:48:13.435 - INFO - [Dummy-2:30068] - data_cleaning.py:1498 - clean_numeric_account_names() - ✅ 未发现纯数字账户开户名称，无需清理
2025-08-04 21:48:13.437 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 clean_numeric_account_names 完成
2025-08-04 21:48:13.439 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 7/15: enrich_account_opening_info
2025-08-04 21:48:13.733 - INFO - [Dummy-2:30068] - data_cleaning.py:1529 - enrich_account_opening_info() - 🔄 开始增强开户信息表数据...
2025-08-04 21:48:13.734 - INFO - [Dummy-2:30068] - data_cleaning.py:1534 - enrich_account_opening_info() - 📝 处理账户开户名称为空的记录...
2025-08-04 21:48:13.779 - INFO - [Dummy-2:30068] - data_cleaning.py:1559 - enrich_account_opening_info() - ✅ 通过交易卡号更新账户开户名称：0 条记录
2025-08-04 21:48:13.785 - INFO - [Dummy-2:30068] - data_cleaning.py:1584 - enrich_account_opening_info() - ✅ 通过交易账号更新账户开户名称：0 条记录
2025-08-04 21:48:13.786 - INFO - [Dummy-2:30068] - data_cleaning.py:1587 - enrich_account_opening_info() - 📝 处理开户人证件号码为空的记录...
2025-08-04 21:48:13.796 - INFO - [Dummy-2:30068] - data_cleaning.py:1612 - enrich_account_opening_info() - ✅ 通过交易卡号更新开户人证件号码：0 条记录
2025-08-04 21:48:13.800 - INFO - [Dummy-2:30068] - data_cleaning.py:1637 - enrich_account_opening_info() - ✅ 通过交易账号更新开户人证件号码：0 条记录
2025-08-04 21:48:13.800 - INFO - [Dummy-2:30068] - data_cleaning.py:1640 - enrich_account_opening_info() - 📝 从账户交易明细表的对手信息中补充账户开户名称...
2025-08-04 21:48:19.397 - INFO - [Dummy-2:30068] - data_cleaning.py:1668 - enrich_account_opening_info() - ✅ 通过对手卡号更新账户开户名称：0 条记录
2025-08-04 21:48:21.196 - INFO - [Dummy-2:30068] - data_cleaning.py:1696 - enrich_account_opening_info() - ✅ 通过对手账号更新账户开户名称：0 条记录
2025-08-04 21:48:21.197 - INFO - [Dummy-2:30068] - data_cleaning.py:1699 - enrich_account_opening_info() - 🗑️ 删除账户开户名称仍为空的开户信息表记录...
2025-08-04 21:48:21.199 - INFO - [Dummy-2:30068] - data_cleaning.py:1709 - enrich_account_opening_info() - 🗑️ 删除账户开户名称为空的记录：0 条
2025-08-04 21:48:21.199 - INFO - [Dummy-2:30068] - data_cleaning.py:1712 - enrich_account_opening_info() - 🎯 开户信息表数据增强完成，总计更新 0 条记录，删除 0 条记录
2025-08-04 21:48:21.199 - INFO - [Dummy-2:30068] - data_cleaning.py:1713 - enrich_account_opening_info() - 📊 详细统计：
2025-08-04 21:48:21.200 - INFO - [Dummy-2:30068] - data_cleaning.py:1714 - enrich_account_opening_info() -    - 通过交易卡号更新客户名称：0 条
2025-08-04 21:48:21.201 - INFO - [Dummy-2:30068] - data_cleaning.py:1715 - enrich_account_opening_info() -    - 通过交易账号更新客户名称：0 条
2025-08-04 21:48:21.201 - INFO - [Dummy-2:30068] - data_cleaning.py:1716 - enrich_account_opening_info() -    - 通过交易卡号更新证件号码：0 条
2025-08-04 21:48:21.202 - INFO - [Dummy-2:30068] - data_cleaning.py:1717 - enrich_account_opening_info() -    - 通过交易账号更新证件号码：0 条
2025-08-04 21:48:21.202 - INFO - [Dummy-2:30068] - data_cleaning.py:1718 - enrich_account_opening_info() -    - 通过对手卡号更新账户开户名称：0 条
2025-08-04 21:48:21.203 - INFO - [Dummy-2:30068] - data_cleaning.py:1719 - enrich_account_opening_info() -    - 通过对手账号更新账户开户名称：0 条
2025-08-04 21:48:21.204 - INFO - [Dummy-2:30068] - data_cleaning.py:1720 - enrich_account_opening_info() -    - 删除账户开户名称为空的记录：0 条
2025-08-04 21:48:21.205 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 enrich_account_opening_info 完成
2025-08-04 21:48:21.207 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 8/15: preprocess_data
2025-08-04 21:48:21.365 - INFO - [Dummy-2:30068] - data_cleaning.py:486 - preprocess_data() - 开始数据预处理
2025-08-04 21:48:21.368 - INFO - [Dummy-2:30068] - data_cleaning.py:505 - preprocess_data() - 所有必要的列已成功添加并填充。
2025-08-04 21:48:21.369 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 preprocess_data 完成
2025-08-04 21:48:21.370 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 9/15: match_transaction_names
2025-08-04 21:48:21.533 - INFO - [Dummy-2:30068] - data_cleaning.py:523 - match_transaction_names() - 开始批量匹配交易户名
2025-08-04 21:48:21.534 - INFO - [Dummy-2:30068] - data_cleaning.py:528 - match_transaction_names() - 步骤1：交易账号_digits → 开户信息表.交易账号_digits
2025-08-04 21:48:21.930 - INFO - [Dummy-2:30068] - data_cleaning.py:546 - match_transaction_names() - 步骤1完成：通过交易账号_digits精确匹配，成功匹配 29 条记录
2025-08-04 21:48:21.931 - INFO - [Dummy-2:30068] - data_cleaning.py:549 - match_transaction_names() - 步骤2：交易账号_digits → 开户信息表.交易卡号_digits
2025-08-04 21:48:22.022 - INFO - [Dummy-2:30068] - data_cleaning.py:567 - match_transaction_names() - 步骤2完成：通过交易账号_digits匹配交易卡号_digits，成功匹配 29 条记录
2025-08-04 21:48:22.022 - INFO - [Dummy-2:30068] - data_cleaning.py:570 - match_transaction_names() - 步骤3：交易账卡号_digits → 开户信息表.交易账号_digits
2025-08-04 21:48:22.112 - INFO - [Dummy-2:30068] - data_cleaning.py:588 - match_transaction_names() - 步骤3完成：通过交易账卡号_digits匹配交易账号_digits，成功匹配 29 条记录
2025-08-04 21:48:22.113 - INFO - [Dummy-2:30068] - data_cleaning.py:591 - match_transaction_names() - 步骤4：交易账卡号_digits → 开户信息表.交易卡号_digits
2025-08-04 21:48:22.204 - INFO - [Dummy-2:30068] - data_cleaning.py:609 - match_transaction_names() - 步骤4完成：通过交易账卡号_digits匹配交易卡号_digits，成功匹配 29 条记录
2025-08-04 21:48:22.204 - INFO - [Dummy-2:30068] - data_cleaning.py:612 - match_transaction_names() - 🎯 交易户名匹配完成！总计匹配 116 条记录
2025-08-04 21:48:22.205 - INFO - [Dummy-2:30068] - data_cleaning.py:613 - match_transaction_names() - 📊 详细统计：
2025-08-04 21:48:22.205 - INFO - [Dummy-2:30068] - data_cleaning.py:614 - match_transaction_names() -    - 步骤1（账号对账号）：29 条
2025-08-04 21:48:22.206 - INFO - [Dummy-2:30068] - data_cleaning.py:615 - match_transaction_names() -    - 步骤2（账号对卡号）：29 条
2025-08-04 21:48:22.206 - INFO - [Dummy-2:30068] - data_cleaning.py:616 - match_transaction_names() -    - 步骤3（卡号对账号）：29 条
2025-08-04 21:48:22.207 - INFO - [Dummy-2:30068] - data_cleaning.py:617 - match_transaction_names() -    - 步骤4（卡号对卡号）：29 条
2025-08-04 21:48:22.210 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 match_transaction_names 完成
2025-08-04 21:48:22.212 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 10/15: match_certificate_numbers
2025-08-04 21:48:22.375 - INFO - [Dummy-2:30068] - data_cleaning.py:636 - match_certificate_numbers() - 开始批量匹配交易证件号码
2025-08-04 21:48:22.376 - INFO - [Dummy-2:30068] - data_cleaning.py:641 - match_certificate_numbers() - 步骤1：交易账号_digits → 开户信息表.交易账号_digits
2025-08-04 21:49:19.633 - INFO - [Dummy-2:30068] - data_cleaning.py:659 - match_certificate_numbers() - 步骤1完成：通过交易账号_digits精确匹配，成功匹配 40025 条记录
2025-08-04 21:49:19.641 - INFO - [Dummy-2:30068] - data_cleaning.py:662 - match_certificate_numbers() - 步骤2：交易账号_digits → 开户信息表.交易卡号_digits
2025-08-04 21:50:18.525 - INFO - [Dummy-2:30068] - data_cleaning.py:680 - match_certificate_numbers() - 步骤2完成：通过交易账号_digits匹配交易卡号_digits，成功匹配 40025 条记录
2025-08-04 21:50:18.526 - INFO - [Dummy-2:30068] - data_cleaning.py:683 - match_certificate_numbers() - 步骤3：交易账卡号_digits → 开户信息表.交易账号_digits
2025-08-04 21:51:17.541 - INFO - [Dummy-2:30068] - data_cleaning.py:701 - match_certificate_numbers() - 步骤3完成：通过交易账卡号_digits匹配交易账号_digits，成功匹配 40025 条记录
2025-08-04 21:51:17.542 - INFO - [Dummy-2:30068] - data_cleaning.py:704 - match_certificate_numbers() - 步骤4：交易账卡号_digits → 开户信息表.交易卡号_digits
2025-08-04 21:52:19.915 - INFO - [Dummy-2:30068] - data_cleaning.py:722 - match_certificate_numbers() - 步骤4完成：通过交易账卡号_digits匹配交易卡号_digits，成功匹配 40025 条记录
2025-08-04 21:52:19.917 - INFO - [Dummy-2:30068] - data_cleaning.py:725 - match_certificate_numbers() - 🎯 交易证件号码匹配完成！总计匹配 160100 条记录
2025-08-04 21:52:19.917 - INFO - [Dummy-2:30068] - data_cleaning.py:726 - match_certificate_numbers() - 📊 详细统计：
2025-08-04 21:52:19.918 - INFO - [Dummy-2:30068] - data_cleaning.py:727 - match_certificate_numbers() -    - 步骤1（账号对账号）：40025 条
2025-08-04 21:52:19.919 - INFO - [Dummy-2:30068] - data_cleaning.py:728 - match_certificate_numbers() -    - 步骤2（账号对卡号）：40025 条
2025-08-04 21:52:19.920 - INFO - [Dummy-2:30068] - data_cleaning.py:729 - match_certificate_numbers() -    - 步骤3（卡号对账号）：40025 条
2025-08-04 21:52:19.920 - INFO - [Dummy-2:30068] - data_cleaning.py:730 - match_certificate_numbers() -    - 步骤4（卡号对卡号）：40025 条
2025-08-04 21:52:20.000 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 match_certificate_numbers 完成
2025-08-04 21:52:20.002 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 11/15: match_opponent_names
2025-08-04 21:52:20.267 - INFO - [Dummy-2:30068] - data_cleaning.py:739 - match_opponent_names() - 开始批量匹配对手户名
2025-08-04 21:52:23.893 - INFO - [Dummy-2:30068] - data_cleaning.py:758 - match_opponent_names() - 对手户名匹配完成，共匹配 604 条记录
2025-08-04 21:52:23.897 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 match_opponent_names 完成
2025-08-04 21:52:23.898 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 12/15: check_and_correct_shoufu
2025-08-04 21:52:24.224 - INFO - [Dummy-2:30068] - data_cleaning.py:766 - check_and_correct_shoufu() - 开始修正收付标志
2025-08-04 21:52:24.226 - INFO - [Dummy-2:30068] - data_cleaning.py:795 - check_and_correct_shoufu() - 开始自动修正收付标志，共有 19 个匹配规则
2025-08-04 21:52:56.585 - INFO - [Dummy-2:30068] - data_cleaning.py:844 - check_and_correct_shoufu() - 收付标志修正完成，共自动修复了 0 条记录
2025-08-04 21:52:56.587 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 check_and_correct_shoufu 完成
2025-08-04 21:52:56.588 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 13/15: fill_counterparty_name_with_cash
2025-08-04 21:52:56.776 - INFO - [Dummy-2:30068] - data_cleaning.py:858 - fill_counterparty_name_with_cash() - 开始根据摘要说明和交易类型填充对手户名（含例外关键词过滤）
2025-08-04 21:52:56.777 - INFO - [Dummy-2:30068] - data_cleaning.py:900 - fill_counterparty_name_with_cash() - 💰 现金关键词数量: 77
2025-08-04 21:52:56.778 - INFO - [Dummy-2:30068] - data_cleaning.py:904 - fill_counterparty_name_with_cash() - 🔍 用户反馈关键词检查:
2025-08-04 21:52:56.778 - INFO - [Dummy-2:30068] - data_cleaning.py:907 - fill_counterparty_name_with_cash() -    ✅ 现金存入
2025-08-04 21:52:56.779 - INFO - [Dummy-2:30068] - data_cleaning.py:907 - fill_counterparty_name_with_cash() -    ✅ 现金支取
2025-08-04 21:52:56.779 - INFO - [Dummy-2:30068] - data_cleaning.py:907 - fill_counterparty_name_with_cash() -    ✅ 网络ATM取款
2025-08-04 21:52:56.780 - INFO - [Dummy-2:30068] - data_cleaning.py:907 - fill_counterparty_name_with_cash() -    ✅ ATM取款
2025-08-04 21:52:56.780 - INFO - [Dummy-2:30068] - data_cleaning.py:907 - fill_counterparty_name_with_cash() -    ✅ ATM存款
2025-08-04 21:52:56.780 - INFO - [Dummy-2:30068] - data_cleaning.py:907 - fill_counterparty_name_with_cash() -    ✅ ATM取现
2025-08-04 21:52:56.781 - WARNING - [Dummy-2:30068] - data_cleaning.py:909 - fill_counterparty_name_with_cash() -    ❌ 存款:ATM - 缺失
2025-08-04 21:53:09.315 - INFO - [Dummy-2:30068] - data_cleaning.py:938 - fill_counterparty_name_with_cash() - 现金交易识别完成，共更新 0 条记录
2025-08-04 21:53:09.317 - INFO - [Dummy-2:30068] - data_cleaning.py:939 - fill_counterparty_name_with_cash() - 📊 现金交易识别统计：
2025-08-04 21:53:09.318 - INFO - [Dummy-2:30068] - data_cleaning.py:940 - fill_counterparty_name_with_cash() -    - 匹配条件：对手户名、对手账号、对手卡号均为空
2025-08-04 21:53:09.318 - INFO - [Dummy-2:30068] - data_cleaning.py:941 - fill_counterparty_name_with_cash() -    - 匹配字段：摘要说明 或 交易类型
2025-08-04 21:53:09.319 - INFO - [Dummy-2:30068] - data_cleaning.py:942 - fill_counterparty_name_with_cash() -    - ✅ 识别为现金：0 条记录
2025-08-04 21:53:09.319 - INFO - [Dummy-2:30068] - data_cleaning.py:943 - fill_counterparty_name_with_cash() -    - 💡 使用精确现金关键词匹配，无例外关键词排除
2025-08-04 21:53:09.321 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 fill_counterparty_name_with_cash 完成
2025-08-04 21:53:09.323 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 14/15: finalize_cleaning
2025-08-04 21:53:09.617 - INFO - [Dummy-2:30068] - data_cleaning.py:957 - finalize_cleaning() - 开始最终清理
2025-08-04 21:53:11.567 - INFO - [Dummy-2:30068] - data_cleaning.py:970 - finalize_cleaning() - 最终清理完成，案件 20250726182751 共有 282480 条有效交易记录
2025-08-04 21:53:11.570 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 finalize_cleaning 完成
2025-08-04 21:53:11.573 - INFO - [Dummy-2:30068] - data_cleaning.py:2167 - start_cleaning() - 开始执行清洗步骤 15/15: deduplicate_all_tables
2025-08-04 21:53:11.850 - INFO - [Dummy-2:30068] - data_cleaning.py:1815 - deduplicate_all_tables() - 🔄 开始对所有数据表进行去重操作...
2025-08-04 21:53:11.852 - INFO - [Dummy-2:30068] - data_cleaning.py:1957 - deduplicate_all_tables() - 📊 准备对 94 个数据表进行去重操作...
2025-08-04 21:53:13.302 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '账户交易明细表'（原有 282509 条记录）...
2025-08-04 21:53:13.327 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: id
2025-08-04 21:53:13.328 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 交易方开户银行
2025-08-04 21:53:13.329 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 现金标志
2025-08-04 21:53:13.329 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 交易场所
2025-08-04 21:53:13.329 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 交易是否成功
2025-08-04 21:53:13.331 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 查询账号
2025-08-04 21:53:13.331 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 查询卡号
2025-08-04 21:53:13.331 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 本方账号
2025-08-04 21:53:13.332 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 本方卡号
2025-08-04 21:53:13.332 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 源文件位置
2025-08-04 21:53:13.333 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 导入批次
2025-08-04 21:53:13.334 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 交易账卡号_digits
2025-08-04 21:53:13.335 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 交易账号_digits
2025-08-04 21:53:13.335 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 对手账号_digits
2025-08-04 21:53:13.336 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户交易明细表' 排除字段: 对手卡号_digits
2025-08-04 21:53:13.336 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '账户交易明细表' 去重字段: ['交易账卡号', '交易账号', '交易户名', '交易证件号码', '交易日期', '交易金额', '交易余额', '收付标志', '对手账号', '对手卡号', '对手户名', '对手身份证号', '对手开户银行', '摘要说明', '交易币种', '商户名称', '商户号', '交易网点名称', '交易发生地', '传票号', 'IP地址', 'MAC地址', '交易流水号', '对手余额', '渠道', '交易类型', '日志号', '凭证种类', '凭证号', '交易柜员号', '备注', '案件编号']
2025-08-04 21:53:21.220 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '账户交易明细表' 去重前记录数: 282509
2025-08-04 21:56:38.042 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '账户交易明细表' 去重后记录数: 282509
2025-08-04 21:56:38.043 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '账户交易明细表' 去重删除了 0 条记录。
2025-08-04 21:56:38.043 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '账户交易明细表' 去重完成，无重复记录
2025-08-04 21:56:38.048 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '开户信息表'（原有 1122 条记录）...
2025-08-04 21:56:38.050 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '开户信息表' 排除字段: id
2025-08-04 21:56:38.050 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '开户信息表' 排除字段: 源文件位置
2025-08-04 21:56:38.050 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '开户信息表' 排除字段: 导入批次
2025-08-04 21:56:38.050 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '开户信息表' 去重字段: ['账户开户名称', '开户人证件号码', '交易卡号', '交易账号', '账号开户时间', '账户余额', '可用余额', '币种', '开户网点代码', '开户网点', '账户状态', '钞汇标志名称', '销户日期', '账户类型', '开户联系方式', '联系电话', '通信地址', '代理人', '代理人电话', '备注', '开户省份', '开户城市', '账号开户银行', '客户代码', '法人代表', '客户工商执照号码', '法人代表证件号码', '住宅地址', '邮政编码', '代办人证件号码', '邮箱地址', '关联资金账户', '地税纳税号', '单位电话', '代办人证件类型', '住宅电话', '法人代表证件类型', '国税纳税号', '单位地址', '工作单位', '销户网点', '最后交易时间', '账户销户银行', '案件编号', '交易卡号_digits', '交易账号_digits', '数据源文件']
2025-08-04 21:56:38.066 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '开户信息表' 去重前记录数: 1122
2025-08-04 21:56:38.318 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '开户信息表' 去重后记录数: 1122
2025-08-04 21:56:38.318 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '开户信息表' 去重删除了 0 条记录。
2025-08-04 21:56:38.318 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '开户信息表' 去重完成，无重复记录
2025-08-04 21:56:38.323 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '临时账户交易明细表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.328 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '财付通交易明细表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.334 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '账户信息_客户基本信息表'（原有 50 条记录）...
2025-08-04 21:56:38.337 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_客户基本信息表' 排除字段: ID
2025-08-04 21:56:38.338 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_客户基本信息表' 排除字段: 源文件位置
2025-08-04 21:56:38.338 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_客户基本信息表' 排除字段: 导入批次
2025-08-04 21:56:38.338 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '账户信息_客户基本信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询对象名称', '证件类型', '证件号码', '是否有财产', '客户名称', '查询反馈结果原因', '证件类型_1', '证件号码_1', '联系电话', '联系手机', '代办人姓名', '代办人证件类型', '代办人证件号码', '住宅地址', '住宅电话', '工作单位', '单位地址', '单位电话', '邮箱地址', '账单地址', '法人代表', '法人代表证件类型', '法人代表证件号码', '客户工商执照号码', '国税纳税号', '地税纳税号', '案件编号', '卡号', '账号']
2025-08-04 21:56:38.361 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '账户信息_客户基本信息表' 去重前记录数: 50
2025-08-04 21:56:38.370 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '账户信息_客户基本信息表' 去重后记录数: 50
2025-08-04 21:56:38.372 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '账户信息_客户基本信息表' 去重删除了 0 条记录。
2025-08-04 21:56:38.372 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '账户信息_客户基本信息表' 去重完成，无重复记录
2025-08-04 21:56:38.377 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '增值税发票表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.381 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '公安部_户籍人口_基本人员信息表'（原有 32 条记录）...
2025-08-04 21:56:38.385 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '公安部_户籍人口_基本人员信息表' 排除字段: ID
2025-08-04 21:56:38.385 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '公安部_户籍人口_基本人员信息表' 排除字段: 源文件位置
2025-08-04 21:56:38.385 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '公安部_户籍人口_基本人员信息表' 排除字段: 导入批次
2025-08-04 21:56:38.385 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '公安部_户籍人口_基本人员信息表' 去重字段: ['数据源文件', '姓名', '性别', '身份证号', '民族', '出生日期', '曾用名', '籍贯', '身高', '职业', '户籍地区划', '籍贯国家', '出生地区划', '出生地国家_地区', '兵役情况', '死亡日期', '文化程度', '婚姻状况', '户籍地', '人员状态', '从业单位', '照片', '案件编号']
2025-08-04 21:56:38.402 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '公安部_户籍人口_基本人员信息表' 去重前记录数: 32
2025-08-04 21:56:38.413 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '公安部_户籍人口_基本人员信息表' 去重后记录数: 32
2025-08-04 21:56:38.413 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '公安部_户籍人口_基本人员信息表' 去重删除了 0 条记录。
2025-08-04 21:56:38.414 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '公安部_户籍人口_基本人员信息表' 去重完成，无重复记录
2025-08-04 21:56:38.416 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_驾驶证_驾驶证信息表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.422 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_交通违法_机动车违章信息表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.454 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_旅馆住宿_旅馆住宿人员信息表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.458 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_同车违章_同车违章表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.463 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_同户人_同户人表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.468 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_同住址_同住址表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.474 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_出国_境_证件_出入境证件信息' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.477 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_机动车_机动车信息' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.481 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_在逃撤销_在逃人员撤销信息' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.484 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_在逃同案撤销人员_在逃同案撤销人员' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.487 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_在逃人员_在逃人员登记信息' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.491 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '公安部_出入境记录_出入境记录信息表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.493 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '国家税务总局_纳税人登记信息_登记信息表'（原有 11 条记录）...
2025-08-04 21:56:38.496 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '国家税务总局_纳税人登记信息_登记信息表' 排除字段: ID
2025-08-04 21:56:38.496 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '国家税务总局_纳税人登记信息_登记信息表' 排除字段: 源文件位置
2025-08-04 21:56:38.496 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '国家税务总局_纳税人登记信息_登记信息表' 排除字段: 导入批次
2025-08-04 21:56:38.496 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '国家税务总局_纳税人登记信息_登记信息表' 去重字段: ['数据源文件', '姓名', '证件类型', '证件号码', '纳税人识别号', '居住地址', '电子邮箱', '电话号码', '序号', '单位名称', '职务名称', '案件编号']
2025-08-04 21:56:38.504 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '国家税务总局_纳税人登记信息_登记信息表' 去重前记录数: 11
2025-08-04 21:56:38.510 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '国家税务总局_纳税人登记信息_登记信息表' 去重后记录数: 11
2025-08-04 21:56:38.510 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '国家税务总局_纳税人登记信息_登记信息表' 去重删除了 0 条记录。
2025-08-04 21:56:38.511 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '国家税务总局_纳税人登记信息_登记信息表' 去重完成，无重复记录
2025-08-04 21:56:38.550 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '国家税务总局_纳税信息_税务缴纳信息表'（原有 1072 条记录）...
2025-08-04 21:56:38.554 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '国家税务总局_纳税信息_税务缴纳信息表' 排除字段: ID
2025-08-04 21:56:38.555 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '国家税务总局_纳税信息_税务缴纳信息表' 排除字段: 源文件位置
2025-08-04 21:56:38.555 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '国家税务总局_纳税信息_税务缴纳信息表' 排除字段: 导入批次
2025-08-04 21:56:38.555 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '国家税务总局_纳税信息_税务缴纳信息表' 去重字段: ['数据源文件', '姓名', '证件号码', '税款所属期始', '税款所属期止', '征收品目代码', '征收品目名称', '税种名称', '应纳税额', '案件编号']
2025-08-04 21:56:38.576 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '国家税务总局_纳税信息_税务缴纳信息表' 去重前记录数: 1072
2025-08-04 21:56:38.597 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '国家税务总局_纳税信息_税务缴纳信息表' 去重后记录数: 1072
2025-08-04 21:56:38.597 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '国家税务总局_纳税信息_税务缴纳信息表' 去重删除了 0 条记录。
2025-08-04 21:56:38.599 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '国家税务总局_纳税信息_税务缴纳信息表' 去重完成，无重复记录
2025-08-04 21:56:38.624 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '金融理财_金融理财账户信息表'（原有 165 条记录）...
2025-08-04 21:56:38.628 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '金融理财_金融理财账户信息表' 排除字段: ID
2025-08-04 21:56:38.628 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '金融理财_金融理财账户信息表' 排除字段: 源文件位置
2025-08-04 21:56:38.628 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '金融理财_金融理财账户信息表' 排除字段: 导入批次
2025-08-04 21:56:38.629 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '金融理财_金融理财账户信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询对象名称', '证件类型', '证件号码', '是否有财产', '查询反馈结果原因', '查询卡号', '理财卡号', '理财账号', '网银账户名称', '最后登录IP', '最后登录时间', '账户类别', '账户状态', '开户网点', '开户网点代码', '开户日期', '销户日期', '销户网点', '币种', '钞汇标志', '账户余额', '可用余额', '最后交易时间', '案件编号']
2025-08-04 21:56:38.648 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '金融理财_金融理财账户信息表' 去重前记录数: 165
2025-08-04 21:56:38.664 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '金融理财_金融理财账户信息表' 去重后记录数: 165
2025-08-04 21:56:38.665 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '金融理财_金融理财账户信息表' 去重删除了 0 条记录。
2025-08-04 21:56:38.666 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '金融理财_金融理财账户信息表' 去重完成，无重复记录
2025-08-04 21:56:38.679 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '金融理财_金融理财信息表'（原有 162 条记录）...
2025-08-04 21:56:38.682 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '金融理财_金融理财信息表' 排除字段: ID
2025-08-04 21:56:38.682 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '金融理财_金融理财信息表' 排除字段: 源文件位置
2025-08-04 21:56:38.682 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '金融理财_金融理财信息表' 排除字段: 导入批次
2025-08-04 21:56:38.682 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '金融理财_金融理财信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询对象名称', '证件类型', '证件号码', '查询卡号', '开户网点', '账户类别', '账户状态', '理财卡号', '理财账号', '金融资产序号', '金融理财名称', '金融理财类型', '产品销售种类', '金融产品编号', '资产管理人', '资产可否通过银行交易', '资产交易限制类型', '资产交易限制消除时间', '产品状态', '质押权人', '托管人', '受益人', '成立日', '赎回日', '托管账号', '计量单位', '币种', '资产单位价格', '数量_份额_金额', '可控数量_份额_金额', '资产总数额', '可控资产总数额', '备注', '反馈人', '反馈日期', '案件编号']
2025-08-04 21:56:38.696 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '金融理财_金融理财信息表' 去重前记录数: 162
2025-08-04 21:56:38.706 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '金融理财_金融理财信息表' 去重后记录数: 162
2025-08-04 21:56:38.708 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '金融理财_金融理财信息表' 去重删除了 0 条记录。
2025-08-04 21:56:38.709 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '金融理财_金融理财信息表' 去重完成，无重复记录
2025-08-04 21:56:38.733 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '账户信息_强制措施信息表'（原有 12 条记录）...
2025-08-04 21:56:38.736 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_强制措施信息表' 排除字段: ID
2025-08-04 21:56:38.737 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_强制措施信息表' 排除字段: 源文件位置
2025-08-04 21:56:38.737 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_强制措施信息表' 排除字段: 导入批次
2025-08-04 21:56:38.737 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '账户信息_强制措施信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询对象名称', '证件类型', '证件号码', '账号', '措施序号', '冻结开始日', '冻结截止日', '冻结机关名称', '冻结金额', '备注', '冻结措施类型', '案件编号']
2025-08-04 21:56:38.752 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '账户信息_强制措施信息表' 去重前记录数: 12
2025-08-04 21:56:38.761 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '账户信息_强制措施信息表' 去重后记录数: 12
2025-08-04 21:56:38.762 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '账户信息_强制措施信息表' 去重删除了 0 条记录。
2025-08-04 21:56:38.763 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '账户信息_强制措施信息表' 去重完成，无重复记录
2025-08-04 21:56:38.764 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '账户信息_共有权优先权信息表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.773 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '账户信息_关联子账户信息表'（原有 119 条记录）...
2025-08-04 21:56:38.777 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_关联子账户信息表' 排除字段: ID
2025-08-04 21:56:38.777 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_关联子账户信息表' 排除字段: 源文件位置
2025-08-04 21:56:38.777 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_关联子账户信息表' 排除字段: 导入批次
2025-08-04 21:56:38.777 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '账户信息_关联子账户信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询对象名称', '证件类型', '证件号码', '账号', '子账户序号', '子账户类别', '子账户账号', '币种', '钞汇标志', '账户余额', '账户状态', '可用余额', '案件编号']
2025-08-04 21:56:38.789 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '账户信息_关联子账户信息表' 去重前记录数: 119
2025-08-04 21:56:38.796 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '账户信息_关联子账户信息表' 去重后记录数: 119
2025-08-04 21:56:38.796 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '账户信息_关联子账户信息表' 去重删除了 0 条记录。
2025-08-04 21:56:38.797 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '账户信息_关联子账户信息表' 去重完成，无重复记录
2025-08-04 21:56:38.803 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '账户信息_关联子账户信息表本地'（原有 35 条记录）...
2025-08-04 21:56:38.805 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_关联子账户信息表本地' 排除字段: ID
2025-08-04 21:56:38.806 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_关联子账户信息表本地' 排除字段: 源文件位置
2025-08-04 21:56:38.806 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '账户信息_关联子账户信息表本地' 排除字段: 导入批次
2025-08-04 21:56:38.806 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '账户信息_关联子账户信息表本地' 去重字段: ['数据源文件', '账卡号', '子账户序号', '子账户类别', '子账户账号', '币种', '钞汇标志', '账户余额', '账户状态', '可用余额', '案件编号']
2025-08-04 21:56:38.813 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '账户信息_关联子账户信息表本地' 去重前记录数: 35
2025-08-04 21:56:38.820 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '账户信息_关联子账户信息表本地' 去重后记录数: 35
2025-08-04 21:56:38.821 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '账户信息_关联子账户信息表本地' 去重删除了 0 条记录。
2025-08-04 21:56:38.821 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '账户信息_关联子账户信息表本地' 去重完成，无重复记录
2025-08-04 21:56:38.823 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '账户信息（本地）_优先权信息表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:38.829 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '理财登记中心_理财产品_理财产品信息表'（原有 32 条记录）...
2025-08-04 21:56:38.831 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '理财登记中心_理财产品_理财产品信息表' 排除字段: ID
2025-08-04 21:56:38.832 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '理财登记中心_理财产品_理财产品信息表' 排除字段: 源文件位置
2025-08-04 21:56:38.832 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '理财登记中心_理财产品_理财产品信息表' 排除字段: 导入批次
2025-08-04 21:56:38.832 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '理财登记中心_理财产品_理财产品信息表' 去重字段: ['数据源文件', '名称', '证件类型', '证件号码', '性别', '手机号码', '固定电话', '电子邮箱', '投资者类别', '联系地址', '紧急联系人', '紧急联系人证件类型', '紧急联系人证件号码', '紧急联系人联系方式', '产品名称', '产品登记编码', '产品运作模式', '产品募集方式', '产品收益类型', '收益率分档情况说明', '目标客户类型', '发行机构', '预计客户最低年收益率___', '预计客户最高年收益率___', '业绩比较基准', '是否结构化_分级_产品', '初始净值', '产品净值', '累计净值', '募集起始日期', '产品起始日期', '案件编号']
2025-08-04 21:56:38.840 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '理财登记中心_理财产品_理财产品信息表' 去重前记录数: 32
2025-08-04 21:56:38.848 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '理财登记中心_理财产品_理财产品信息表' 去重后记录数: 32
2025-08-04 21:56:38.850 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '理财登记中心_理财产品_理财产品信息表' 去重删除了 0 条记录。
2025-08-04 21:56:38.851 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '理财登记中心_理财产品_理财产品信息表' 去重完成，无重复记录
2025-08-04 21:56:40.281 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '理财登记中心_理财产品_投资行业信息表'（原有 30478 条记录）...
2025-08-04 21:56:40.283 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '理财登记中心_理财产品_投资行业信息表' 排除字段: ID
2025-08-04 21:56:40.284 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '理财登记中心_理财产品_投资行业信息表' 排除字段: 源文件位置
2025-08-04 21:56:40.284 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '理财登记中心_理财产品_投资行业信息表' 排除字段: 导入批次
2025-08-04 21:56:40.284 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '理财登记中心_理财产品_投资行业信息表' 去重字段: ['数据源文件', '产品登记编码', '投资资产名称', '投资资产类别', '投资资产状态', '投资行业', '投资资产净值比重___', '案件编号']
2025-08-04 21:56:42.289 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '理财登记中心_理财产品_投资行业信息表' 去重前记录数: 30478
2025-08-04 21:56:45.391 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '理财登记中心_理财产品_投资行业信息表' 去重后记录数: 30478
2025-08-04 21:56:45.391 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '理财登记中心_理财产品_投资行业信息表' 去重删除了 0 条记录。
2025-08-04 21:56:45.392 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '理财登记中心_理财产品_投资行业信息表' 去重完成，无重复记录
2025-08-04 21:56:45.504 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '理财登记中心_理财产品_持有信息表'（原有 2228 条记录）...
2025-08-04 21:56:45.508 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '理财登记中心_理财产品_持有信息表' 排除字段: ID
2025-08-04 21:56:45.509 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '理财登记中心_理财产品_持有信息表' 排除字段: 源文件位置
2025-08-04 21:56:45.509 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '理财登记中心_理财产品_持有信息表' 排除字段: 导入批次
2025-08-04 21:56:45.509 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '理财登记中心_理财产品_持有信息表' 去重字段: ['数据源文件', '产品登记编码', '持有日期', '币种', '持有份额', '持有金额', '折算人民币金额', '理财收益', '理财收益率___', '案件编号']
2025-08-04 21:56:45.703 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '理财登记中心_理财产品_持有信息表' 去重前记录数: 2228
2025-08-04 21:56:45.888 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '理财登记中心_理财产品_持有信息表' 去重后记录数: 2228
2025-08-04 21:56:45.893 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '理财登记中心_理财产品_持有信息表' 去重删除了 0 条记录。
2025-08-04 21:56:45.895 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '理财登记中心_理财产品_持有信息表' 去重完成，无重复记录
2025-08-04 21:56:45.906 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '电话_登记信息_运营商登记信息表'（原有 398 条记录）...
2025-08-04 21:56:45.916 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '电话_登记信息_运营商登记信息表' 排除字段: ID
2025-08-04 21:56:45.917 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '电话_登记信息_运营商登记信息表' 排除字段: 源文件位置
2025-08-04 21:56:45.917 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '电话_登记信息_运营商登记信息表' 排除字段: 导入批次
2025-08-04 21:56:45.917 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '电话_登记信息_运营商登记信息表' 去重字段: ['数据源文件', '序号', '号码类别', '号码', 'IMSI号', 'IMEI号', 'SIM卡类型', 'SIM卡号', 'PUK码', '归属运营商标识', '亲情号码业务', '亲情号码列表', '一号多终端关联号码列表', '群组名称', '群组标识', '语音国际漫游', '群组语音业务', '前转业务', '前转号码', '号码归属省', '号码归属市', '入网渠道', '渠道地址', '入网时间', '销户时间', '首次通话时间', '最后停机时间', '实名状态', '用户类型', '使用人姓名', '使用人证件类型', '使用人证件号码', '使用人证件地址', '通信地址', '单位名称', '单位证件类型', '单位证件号码', '单位证件地址', '单位通信地址', '责任人姓名', '责任人证件类型', '责任人证件号码', '责任人证件地址', '责任人通讯地址', '责任人电话', '代办人姓名', '代办人证件类型', '代办人证件号码', '代办人证件地址', '代办人通讯地址', '代办人电话', '电子邮箱', '电子邮箱接收地址', '其他登记地址', '号码状态', '主副卡标识', '案件编号']
2025-08-04 21:56:45.932 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '电话_登记信息_运营商登记信息表' 去重前记录数: 398
2025-08-04 21:56:45.941 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '电话_登记信息_运营商登记信息表' 去重后记录数: 398
2025-08-04 21:56:45.943 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '电话_登记信息_运营商登记信息表' 去重删除了 0 条记录。
2025-08-04 21:56:45.943 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '电话_登记信息_运营商登记信息表' 去重完成，无重复记录
2025-08-04 21:56:46.233 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '电话_话单信息_运营商话单信息表'（原有 31595 条记录）...
2025-08-04 21:56:46.235 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '电话_话单信息_运营商话单信息表' 排除字段: ID
2025-08-04 21:56:46.236 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '电话_话单信息_运营商话单信息表' 排除字段: 源文件位置
2025-08-04 21:56:46.236 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '电话_话单信息_运营商话单信息表' 排除字段: 导入批次
2025-08-04 21:56:46.236 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '电话_话单信息_运营商话单信息表' 去重字段: ['数据源文件', '序号', '通信记录唯一标识', '通话类型', '话单类型', '本机号码', '本机IMSI号', '本机IMEI号', '本机RAC号', '本机LAC号', '本机基站ID', '本机CELLID', '本机归属运营商', '本机通话所在地', '对方号码', '对方IMSI号', '对方IMEI号', '对方RAC号', '对方LAC号', '对方基站ID', '对方CELLID', '对方归属运营商', '对方通话所在地', '对方号码归属地', '前转主叫号码', '呼叫开始时间', '呼叫时长', '是否群内呼叫', '群组编号', '群组名称', '短信发送接收时间', '案件编号', '用户姓名', '用户身份证号码', '对方姓名', '对方身份证号码']
2025-08-04 21:56:46.707 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '电话_话单信息_运营商话单信息表' 去重前记录数: 31595
2025-08-04 21:56:47.412 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '电话_话单信息_运营商话单信息表' 去重后记录数: 31595
2025-08-04 21:56:47.415 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '电话_话单信息_运营商话单信息表' 去重删除了 0 条记录。
2025-08-04 21:56:47.416 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '电话_话单信息_运营商话单信息表' 去重完成，无重复记录
2025-08-04 21:56:47.419 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '虚拟运营商_登记信息_虚拟运营商登记信息表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:47.608 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '银保信_保险产品_保险保单信息表'（原有 4648 条记录）...
2025-08-04 21:56:47.611 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '银保信_保险产品_保险保单信息表' 排除字段: ID
2025-08-04 21:56:47.611 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '银保信_保险产品_保险保单信息表' 排除字段: 源文件位置
2025-08-04 21:56:47.611 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '银保信_保险产品_保险保单信息表' 排除字段: 导入批次
2025-08-04 21:56:47.611 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '银保信_保险产品_保险保单信息表' 去重字段: ['数据源文件', '自然人对象名称', '自然人证件类型', '自然人证件号码', '机构名称', '营业执照号码', '组织机构代码', '统一社会信用代码', '税务登记证号', '保险产品名称', '保单号', '保险公司名称', '累计缴纳保费', '币种', '平台名称', '险种名称', '保单团个性质', '购买日期', '保单生效日期', '保单终止日期', '保险标的名称', '保险账户价值', '数据提取日期', '标的数量', '保单序号', '案件编号']
2025-08-04 21:56:47.867 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '银保信_保险产品_保险保单信息表' 去重前记录数: 4648
2025-08-04 21:56:48.258 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '银保信_保险产品_保险保单信息表' 去重后记录数: 4648
2025-08-04 21:56:48.258 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '银保信_保险产品_保险保单信息表' 去重删除了 0 条记录。
2025-08-04 21:56:48.259 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '银保信_保险产品_保险保单信息表' 去重完成，无重复记录
2025-08-04 21:56:48.467 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '银保信_保险产品_保险人员信息表'（原有 7747 条记录）...
2025-08-04 21:56:48.473 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '银保信_保险产品_保险人员信息表' 排除字段: ID
2025-08-04 21:56:48.473 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '银保信_保险产品_保险人员信息表' 排除字段: 源文件位置
2025-08-04 21:56:48.473 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '银保信_保险产品_保险人员信息表' 排除字段: 导入批次
2025-08-04 21:56:48.473 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '银保信_保险产品_保险人员信息表' 去重字段: ['数据源文件', '保单序号', '人员类别', '人员序号', '人员证件类型', '人员证件号码', '人员联系电话', '人员联系地址', '缴费账号', '投保人名称', '被保险人名称', '受益人名称', '案件编号']
2025-08-04 21:56:48.730 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '银保信_保险产品_保险人员信息表' 去重前记录数: 7747
2025-08-04 21:56:49.198 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '银保信_保险产品_保险人员信息表' 去重后记录数: 7747
2025-08-04 21:56:49.199 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '银保信_保险产品_保险人员信息表' 去重删除了 0 条记录。
2025-08-04 21:56:49.200 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '银保信_保险产品_保险人员信息表' 去重完成，无重复记录
2025-08-04 21:56:49.203 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '银保信_保险产品_家庭财产保险表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:49.206 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '银保信_保险产品_航空延误保险表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:49.235 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '银保信_保险产品_保险赔案信息表'（原有 1256 条记录）...
2025-08-04 21:56:49.238 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '银保信_保险产品_保险赔案信息表' 排除字段: ID
2025-08-04 21:56:49.238 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '银保信_保险产品_保险赔案信息表' 排除字段: 源文件位置
2025-08-04 21:56:49.239 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '银保信_保险产品_保险赔案信息表' 排除字段: 导入批次
2025-08-04 21:56:49.239 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '银保信_保险产品_保险赔案信息表' 去重字段: ['数据源文件', '保单序号', '赔案序号', '赔案报案人姓名', '赔案报案人联系电话', '赔案号', '出险时间', '报案时间', '出险原因', '赔款支付账号', '赔付金额', '赔付日期', '案件编号']
2025-08-04 21:56:49.261 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '银保信_保险产品_保险赔案信息表' 去重前记录数: 1256
2025-08-04 21:56:49.299 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '银保信_保险产品_保险赔案信息表' 去重后记录数: 1256
2025-08-04 21:56:49.300 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '银保信_保险产品_保险赔案信息表' 去重删除了 0 条记录。
2025-08-04 21:56:49.301 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '银保信_保险产品_保险赔案信息表' 去重完成，无重复记录
2025-08-04 21:56:49.325 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国航空_航班进出港_航班进出港已成行表'（原有 304 条记录）...
2025-08-04 21:56:49.328 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港已成行表' 排除字段: ID
2025-08-04 21:56:49.329 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港已成行表' 排除字段: 源文件位置
2025-08-04 21:56:49.329 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港已成行表' 排除字段: 导入批次
2025-08-04 21:56:49.329 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港已成行表' 去重字段: ['数据源文件', '旅客证件号', '手机号', '航空公司', '航班号', '旅客中文姓名', '旅客英文名字', '起飞日期', '起飞时间', '到达日期', '到达时间', '起飞机场三字码', '到达机场三字码', '起飞机场', '到达机场', '值机日期', '值机时间', '离港舱位', '登机牌序号', '座位行号', '座位号', '票号', '记录编号', '销售舱位', '出票日期', '常客卡所属航空公司', '常客卡号', '行李件数', '行李重量_kg_', '票面总价', '付款方式', '票价货币类型', '出票处office', '出票处电话', '出票处地址', '订票处office', '订票处名字', '订票处电话', '订票处地址', '同行人类别', '关联目标人', '人员类别', '案件编号']
2025-08-04 21:56:49.350 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港已成行表' 去重前记录数: 304
2025-08-04 21:56:49.362 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港已成行表' 去重后记录数: 304
2025-08-04 21:56:49.364 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港已成行表' 去重删除了 0 条记录。
2025-08-04 21:56:49.365 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国航空_航班进出港_航班进出港已成行表' 去重完成，无重复记录
2025-08-04 21:56:49.368 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国航空_航班进出港_航班进出港未成行表'（原有 26 条记录）...
2025-08-04 21:56:49.373 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港未成行表' 排除字段: ID
2025-08-04 21:56:49.373 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港未成行表' 排除字段: 源文件位置
2025-08-04 21:56:49.373 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港未成行表' 排除字段: 导入批次
2025-08-04 21:56:49.373 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港未成行表' 去重字段: ['数据源文件', '旅客证件号', '手机号', '航空公司', '航班号', '旅客中文姓名', '旅客英文名字', '起飞日期', '起飞时间', '到达日期', '到达时间', '起飞机场三字码', '到达机场三字码', '起飞机场', '到达机场', '值机日期', '值机时间', '离港舱位', '登机牌序号', '座位行号', '座位号', '票号', '记录编号', '销售舱位', '出票日期', '常客卡所属航空公司', '常客卡号', '行李件数', '行李重量_kg_', '票面总价', '付款方式', '票价货币类型', '出票处office', '出票处电话', '出票处地址', '订票处office', '订票处名字', '订票处电话', '订票处地址', '同行人类别', '关联目标人', '人员类别', '案件编号']
2025-08-04 21:56:49.383 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港未成行表' 去重前记录数: 26
2025-08-04 21:56:49.391 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港未成行表' 去重后记录数: 26
2025-08-04 21:56:49.391 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国航空_航班进出港_航班进出港未成行表' 去重删除了 0 条记录。
2025-08-04 21:56:49.392 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国航空_航班进出港_航班进出港未成行表' 去重完成，无重复记录
2025-08-04 21:56:49.407 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国航空_航班同行人信息_同订单同行人已成行'（原有 205 条记录）...
2025-08-04 21:56:49.410 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人已成行' 排除字段: ID
2025-08-04 21:56:49.410 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人已成行' 排除字段: 源文件位置
2025-08-04 21:56:49.410 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人已成行' 排除字段: 导入批次
2025-08-04 21:56:49.411 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人已成行' 去重字段: ['数据源文件', '旅客证件号', '手机号', '航空公司', '航班号', '旅客中文姓名', '旅客英文名字', '起飞日期', '起飞时间', '到达日期', '到达时间', '起飞机场三字码', '到达机场三字码', '起飞机场', '到达机场', '值机日期', '值机时间', '离港舱位', '登机牌序号', '座位行号', '座位号', '票号', '记录编号', '销售舱位', '出票日期', '常客卡所属航空公司', '常客卡号', '行李件数', '行李重量_kg_', '票面总价', '付款方式', '票价货币类型', '出票处office', '出票处电话', '出票处地址', '订票处office', '订票处名字', '订票处电话', '订票处地址', '同行人类别', '关联目标人', '人员类别', '案件编号']
2025-08-04 21:56:49.426 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人已成行' 去重前记录数: 205
2025-08-04 21:56:49.437 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人已成行' 去重后记录数: 205
2025-08-04 21:56:49.445 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人已成行' 去重删除了 0 条记录。
2025-08-04 21:56:49.447 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国航空_航班同行人信息_同订单同行人已成行' 去重完成，无重复记录
2025-08-04 21:56:49.451 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国航空_航班同行人信息_同订单同行人未成行'（原有 18 条记录）...
2025-08-04 21:56:49.454 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人未成行' 排除字段: ID
2025-08-04 21:56:49.454 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人未成行' 排除字段: 源文件位置
2025-08-04 21:56:49.454 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人未成行' 排除字段: 导入批次
2025-08-04 21:56:49.454 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人未成行' 去重字段: ['数据源文件', '旅客证件号', '手机号', '航空公司', '航班号', '旅客中文姓名', '旅客英文名字', '起飞日期', '起飞时间', '到达日期', '到达时间', '起飞机场三字码', '到达机场三字码', '起飞机场', '到达机场', '值机日期', '值机时间', '离港舱位', '登机牌序号', '座位行号', '座位号', '票号', '记录编号', '销售舱位', '出票日期', '常客卡所属航空公司', '常客卡号', '行李件数', '行李重量_kg_', '票面总价', '付款方式', '票价货币类型', '出票处office', '出票处电话', '出票处地址', '订票处office', '订票处名字', '订票处电话', '订票处地址', '同行人类别', '关联目标人', '人员类别', '案件编号']
2025-08-04 21:56:49.464 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人未成行' 去重前记录数: 18
2025-08-04 21:56:49.477 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人未成行' 去重后记录数: 18
2025-08-04 21:56:49.479 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国航空_航班同行人信息_同订单同行人未成行' 去重删除了 0 条记录。
2025-08-04 21:56:49.480 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国航空_航班同行人信息_同订单同行人未成行' 去重完成，无重复记录
2025-08-04 21:56:49.516 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国航空_航班同行人信息_同乘三次以上同行人'（原有 242 条记录）...
2025-08-04 21:56:49.520 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班同行人信息_同乘三次以上同行人' 排除字段: ID
2025-08-04 21:56:49.521 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班同行人信息_同乘三次以上同行人' 排除字段: 源文件位置
2025-08-04 21:56:49.521 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国航空_航班同行人信息_同乘三次以上同行人' 排除字段: 导入批次
2025-08-04 21:56:49.521 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国航空_航班同行人信息_同乘三次以上同行人' 去重字段: ['数据源文件', '旅客证件号', '手机号', '航空公司', '航班号', '旅客中文姓名', '旅客英文名字', '起飞日期', '起飞时间', '到达日期', '到达时间', '起飞机场三字码', '到达机场三字码', '起飞机场', '到达机场', '值机日期', '值机时间', '离港舱位', '登机牌序号', '座位行号', '座位号', '票号', '记录编号', '销售舱位', '出票日期', '常客卡所属航空公司', '常客卡号', '行李件数', '行李重量_kg_', '票面总价', '付款方式', '票价货币类型', '出票处office', '出票处电话', '出票处地址', '订票处office', '订票处名字', '订票处电话', '订票处地址', '同行人类别', '关联目标人', '人员类别', '案件编号']
2025-08-04 21:56:49.556 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国航空_航班同行人信息_同乘三次以上同行人' 去重前记录数: 242
2025-08-04 21:56:49.580 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国航空_航班同行人信息_同乘三次以上同行人' 去重后记录数: 242
2025-08-04 21:56:49.582 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国航空_航班同行人信息_同乘三次以上同行人' 去重删除了 0 条记录。
2025-08-04 21:56:49.583 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国航空_航班同行人信息_同乘三次以上同行人' 去重完成，无重复记录
2025-08-04 21:56:49.587 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '信托登记公司_产品信息表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:49.590 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '信托登记公司_登记信息_受益权结构表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:49.592 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '信托登记公司_登记信息_合同信息表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:49.598 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '信托登记公司_委托人或受益人变动信息表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:49.601 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '信托登记公司_终止登记表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:49.605 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '信托登记公司_信托产品_委托人信息' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:49.608 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '信托登记公司_信托产品_受益人信息' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:49.611 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '信托登记公司_信托产品_登记信息_受益权结构' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:49.616 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '信托登记公司_信托产品_登记信息_合同信息' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:49.628 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业基本信息表'（原有 97 条记录）...
2025-08-04 21:56:49.632 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业基本信息表' 排除字段: ID
2025-08-04 21:56:49.632 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业基本信息表' 排除字段: 源文件位置
2025-08-04 21:56:49.632 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业基本信息表' 排除字段: 导入批次
2025-08-04 21:56:49.632 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业基本信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '主体身份代码', '统一社会信用代码', '法定代表人', '注册号', '市场主体类型', '行业门类', '行业代码', '成立日期', '登记机关', '业务范围类型', '经营范围', '经营_驻在_期限自', '经营_驻在_期限至', '登记状态', '住所所在行政区划', '住所', '注册资本_金__万元_', '注册资本_金_币种', '注册资本_金_折万美元', '实收资本', '实收资本折万美元', '国别_地区_', '从业人员_农专成员总数', '是否城镇', '统计企业类型', '核准日期', '案件编号']
2025-08-04 21:56:49.655 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业基本信息表' 去重前记录数: 97
2025-08-04 21:56:49.669 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业基本信息表' 去重后记录数: 97
2025-08-04 21:56:49.670 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业基本信息表' 去重删除了 0 条记录。
2025-08-04 21:56:49.672 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业基本信息表' 去重完成，无重复记录
2025-08-04 21:56:49.676 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_许可信息表'（原有 1 条记录）...
2025-08-04 21:56:49.681 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_许可信息表' 排除字段: ID
2025-08-04 21:56:49.682 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_许可信息表' 排除字段: 源文件位置
2025-08-04 21:56:49.682 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_许可信息表' 排除字段: 导入批次
2025-08-04 21:56:49.682 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_许可信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '许可信息ID', '主体身份代码', '统一社会信用代码', '企业_机构_名称_1', '注册号', '许可文件编号', '有效期自', '有效期至', '许可机关', '许可内容', '状态', '注销日期', '注销原因', '被吊销日期', '被吊销原因', '其它无效日期', '其它无效原因', '公示日期', '案件编号']
2025-08-04 21:56:49.694 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_许可信息表' 去重前记录数: 1
2025-08-04 21:56:49.702 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_许可信息表' 去重后记录数: 1
2025-08-04 21:56:49.704 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_许可信息表' 去重删除了 0 条记录。
2025-08-04 21:56:49.706 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_许可信息表' 去重完成，无重复记录
2025-08-04 21:56:49.728 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_自然人出资信息表'（原有 260 条记录）...
2025-08-04 21:56:49.732 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_自然人出资信息表' 排除字段: ID
2025-08-04 21:56:49.732 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_自然人出资信息表' 排除字段: 源文件位置
2025-08-04 21:56:49.732 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_自然人出资信息表' 排除字段: 导入批次
2025-08-04 21:56:49.732 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_自然人出资信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '投资人身份标识', '主体身份代码', '自然人姓名', '自然人证件类型', '自然人证件号码', '认缴出资额_万元_', '认缴出资额折万美元_万美元_', '认缴出资方式', '认缴出资比例', '认缴出资期限', '实缴出资额_万元_', '实缴出资额折万美元_万美元_', '住址', '币种', '国别_地区_', '执行合伙事务标志', '承担责任方式_责任形式', '出资方式_个独_', '性别', '民族', '出生日期', '文化程度', '政治面貌', '职业状况', '邮政编码', '电话', '案件编号']
2025-08-04 21:56:49.749 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_自然人出资信息表' 去重前记录数: 260
2025-08-04 21:56:49.761 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_自然人出资信息表' 去重后记录数: 260
2025-08-04 21:56:49.762 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_自然人出资信息表' 去重删除了 0 条记录。
2025-08-04 21:56:49.763 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_自然人出资信息表' 去重完成，无重复记录
2025-08-04 21:56:49.770 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_非自然人出资信息表'（原有 194 条记录）...
2025-08-04 21:56:49.772 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_非自然人出资信息表' 排除字段: ID
2025-08-04 21:56:49.773 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_非自然人出资信息表' 排除字段: 源文件位置
2025-08-04 21:56:49.773 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_非自然人出资信息表' 排除字段: 导入批次
2025-08-04 21:56:49.773 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_非自然人出资信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '投资人身份标识', '主体身份代码', '投资人_主管部门名称', '投资人类型_主管部门类型', '证照类型', '证照编号', '认缴出资额', '认缴出资额折万美元_万美元_', '认缴出资方式', '认缴出资比例', '认缴出资时间', '实缴出资额_万元_', '实缴出资额折万美元_万美元_', '住所', '币种', '国别_地区_', '执行合伙事务标志', '承担责任方式_责任形式', '案件编号']
2025-08-04 21:56:49.784 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_非自然人出资信息表' 去重前记录数: 194
2025-08-04 21:56:49.792 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_非自然人出资信息表' 去重后记录数: 194
2025-08-04 21:56:49.792 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_非自然人出资信息表' 去重删除了 0 条记录。
2025-08-04 21:56:49.793 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_非自然人出资信息表' 去重完成，无重复记录
2025-08-04 21:56:49.824 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_主要人员表'（原有 650 条记录）...
2025-08-04 21:56:49.826 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_主要人员表' 排除字段: ID
2025-08-04 21:56:49.827 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_主要人员表' 排除字段: 源文件位置
2025-08-04 21:56:49.827 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_主要人员表' 排除字段: 导入批次
2025-08-04 21:56:49.827 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_主要人员表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '人员ID', '主体身份代码', '主要人员姓名', '性别', '出生日期', '主要人员证件类型', '证件号码_代表证编号', '职务', '职务产生方式', '申请前职业状况', '法定代表人标志_首席代表标志_负责人标识', '任命单位_委派方', '联系电话', '国别_地区_', '固定电话', '移动电话', '电子邮箱', '住址', '入境时间', '代表证期限自_任职起始日期', '代表证期限至_任职截止日期', '邮政编码', '案件编号']
2025-08-04 21:56:49.848 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_主要人员表' 去重前记录数: 650
2025-08-04 21:56:49.862 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_主要人员表' 去重后记录数: 650
2025-08-04 21:56:49.864 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_主要人员表' 去重删除了 0 条记录。
2025-08-04 21:56:49.865 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_主要人员表' 去重完成，无重复记录
2025-08-04 21:56:49.918 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_变更备案信息表'（原有 529 条记录）...
2025-08-04 21:56:49.922 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_变更备案信息表' 排除字段: ID
2025-08-04 21:56:49.922 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_变更备案信息表' 排除字段: 源文件位置
2025-08-04 21:56:49.922 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_变更备案信息表' 排除字段: 导入批次
2025-08-04 21:56:49.922 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_变更备案信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '变更项ID', '主体身份代码', '变更事项', '变更前内容', '变更后内容', '变更日期', '案件编号']
2025-08-04 21:56:49.960 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_变更备案信息表' 去重前记录数: 529
2025-08-04 21:56:49.997 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_变更备案信息表' 去重后记录数: 529
2025-08-04 21:56:49.999 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_变更备案信息表' 去重删除了 0 条记录。
2025-08-04 21:56:49.999 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_变更备案信息表' 去重完成，无重复记录
2025-08-04 21:56:50.008 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_财务负责人信息表'（原有 92 条记录）...
2025-08-04 21:56:50.015 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_财务负责人信息表' 排除字段: ID
2025-08-04 21:56:50.015 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_财务负责人信息表' 排除字段: 源文件位置
2025-08-04 21:56:50.015 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_财务负责人信息表' 排除字段: 导入批次
2025-08-04 21:56:50.015 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_财务负责人信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '财务负责人ID', '主体身份代码', '财务负责人姓名', '财务负责人证件类型', '财务负责人证件号码', '固定电话', '移动电话', '电子邮箱', '案件编号']
2025-08-04 21:56:50.039 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_财务负责人信息表' 去重前记录数: 92
2025-08-04 21:56:50.053 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_财务负责人信息表' 去重后记录数: 92
2025-08-04 21:56:50.055 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_财务负责人信息表' 去重删除了 0 条记录。
2025-08-04 21:56:50.056 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_财务负责人信息表' 去重完成，无重复记录
2025-08-04 21:56:50.073 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_联络员信息表'（原有 140 条记录）...
2025-08-04 21:56:50.077 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_联络员信息表' 排除字段: ID
2025-08-04 21:56:50.077 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_联络员信息表' 排除字段: 源文件位置
2025-08-04 21:56:50.077 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_联络员信息表' 排除字段: 导入批次
2025-08-04 21:56:50.077 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_联络员信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '联络员ID', '主体身份代码', '联络员姓名', '联络员证件类型', '联络员证件号码', '固定电话', '移动电话', '电子邮箱', '案件编号']
2025-08-04 21:56:50.094 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_联络员信息表' 去重前记录数: 140
2025-08-04 21:56:50.108 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_联络员信息表' 去重后记录数: 140
2025-08-04 21:56:50.108 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_联络员信息表' 去重删除了 0 条记录。
2025-08-04 21:56:50.108 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_联络员信息表' 去重完成，无重复记录
2025-08-04 21:56:50.113 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_分支机构备案信息表'（原有 1 条记录）...
2025-08-04 21:56:50.117 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_分支机构备案信息表' 排除字段: ID
2025-08-04 21:56:50.118 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_分支机构备案信息表' 排除字段: 源文件位置
2025-08-04 21:56:50.118 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_分支机构备案信息表' 排除字段: 导入批次
2025-08-04 21:56:50.118 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_分支机构备案信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '分支机构ID', '主体身份代码', '分支机构主体身份代码', '分支机构名称', '注册号', '统一社会信用代码', '登记机关', '登记日期', '案件编号']
2025-08-04 21:56:50.129 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_分支机构备案信息表' 去重前记录数: 1
2025-08-04 21:56:50.140 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_分支机构备案信息表' 去重后记录数: 1
2025-08-04 21:56:50.141 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_分支机构备案信息表' 去重删除了 0 条记录。
2025-08-04 21:56:50.143 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_分支机构备案信息表' 去重完成，无重复记录
2025-08-04 21:56:50.148 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_清算基本信息表'（原有 2 条记录）...
2025-08-04 21:56:50.150 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算基本信息表' 排除字段: ID
2025-08-04 21:56:50.150 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算基本信息表' 排除字段: 源文件位置
2025-08-04 21:56:50.150 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算基本信息表' 排除字段: 导入批次
2025-08-04 21:56:50.151 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算基本信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '清算信息编号', '主体身份代码', '清算完结情况', '清算完结日期', '债务承接人', '债权承接人', '案件编号']
2025-08-04 21:56:50.164 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算基本信息表' 去重前记录数: 2
2025-08-04 21:56:50.169 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算基本信息表' 去重后记录数: 2
2025-08-04 21:56:50.171 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算基本信息表' 去重删除了 0 条记录。
2025-08-04 21:56:50.172 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_清算基本信息表' 去重完成，无重复记录
2025-08-04 21:56:50.179 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_清算成员信息表'（原有 5 条记录）...
2025-08-04 21:56:50.184 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算成员信息表' 排除字段: ID
2025-08-04 21:56:50.185 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算成员信息表' 排除字段: 源文件位置
2025-08-04 21:56:50.185 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算成员信息表' 排除字段: 导入批次
2025-08-04 21:56:50.185 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算成员信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '清算成员编号', '清算信息编号', '主体身份代码', '清算组成员', '清算组成员证件类型', '清算组成员证件号码', '地址', '联系电话', '清算负责人标志', '案件编号']
2025-08-04 21:56:50.199 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算成员信息表' 去重前记录数: 5
2025-08-04 21:56:50.209 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算成员信息表' 去重后记录数: 5
2025-08-04 21:56:50.211 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_清算成员信息表' 去重删除了 0 条记录。
2025-08-04 21:56:50.214 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_清算成员信息表' 去重完成，无重复记录
2025-08-04 21:56:50.219 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_吊销信息表'（原有 1 条记录）...
2025-08-04 21:56:50.224 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_吊销信息表' 排除字段: ID
2025-08-04 21:56:50.225 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_吊销信息表' 排除字段: 源文件位置
2025-08-04 21:56:50.225 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_吊销信息表' 排除字段: 导入批次
2025-08-04 21:56:50.225 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_吊销信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '主体身份代码', '吊销日期', '违法依据', '吊销机关', '吊销依据', '案件编号']
2025-08-04 21:56:50.237 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_吊销信息表' 去重前记录数: 1
2025-08-04 21:56:50.244 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_吊销信息表' 去重后记录数: 1
2025-08-04 21:56:50.245 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_吊销信息表' 去重删除了 0 条记录。
2025-08-04 21:56:50.245 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_吊销信息表' 去重完成，无重复记录
2025-08-04 21:56:50.250 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_注销信息表'（原有 20 条记录）...
2025-08-04 21:56:50.253 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_注销信息表' 排除字段: ID
2025-08-04 21:56:50.253 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_注销信息表' 排除字段: 源文件位置
2025-08-04 21:56:50.253 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_注销信息表' 排除字段: 导入批次
2025-08-04 21:56:50.253 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_注销信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '主体身份代码', '注销日期', '注销原因', '对外投资清理完毕标志', '分公司注销登记情况', '债权债务清理完结情况', '清算组成员备案确认文书编号', '公告报纸名称', '公告日期', '批准机关', '批准文号', '批准日期', '清稅情况', '批准证书缴销情况', '海关手续清缴情况', '清理债权债务单位', '案件编号']
2025-08-04 21:56:50.263 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_注销信息表' 去重前记录数: 20
2025-08-04 21:56:50.276 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_注销信息表' 去重后记录数: 20
2025-08-04 21:56:50.276 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_注销信息表' 去重删除了 0 条记录。
2025-08-04 21:56:50.277 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_注销信息表' 去重完成，无重复记录
2025-08-04 21:56:50.282 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_外资补充信息表'（原有 1 条记录）...
2025-08-04 21:56:50.284 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_外资补充信息表' 排除字段: ID
2025-08-04 21:56:50.284 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_外资补充信息表' 排除字段: 源文件位置
2025-08-04 21:56:50.285 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_外资补充信息表' 排除字段: 导入批次
2025-08-04 21:56:50.285 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_外资补充信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '主体身份代码', '邮政编码', '联系电话', '电子邮箱', '属地监管工商所', '生产经营地所在行政区划', '生产经营地', '核算方式', '外资产业代码', '中西部优势产业代码', '实收资本折人民币_万元_', '注册资本_金_折人民币_万元_', '住所所在经济开发区', '项目类型', '投资总额_万元_', '投资总额币种', '投资总额折万美元_万美元_', '中方注册资本_金__万元_', '中方注册资本_金_币种', '中方注册资本_金_折万美元_万美元_', '中方注册资本_金_出资比例', '中方实收资本_万元_', '中方实收资本币种', '中方实收资本折万美元_万美元_', '中方实收资本出资比例', '外方注册资本_金__万美元_', '外方注册资本_金_币种', '外方注册资本_金_折万美元_万美元_', '外方注册资本_金_出资比例', '外方实收资本_万美元_', '外方实收资本币种', '外方实收资本折万美元_万美元_', '外方实收资本出资比例', '转型日期', '设立方式', '经营活动类型', '承包工程或经营管理项目', '承包工程或经营管理内容', '主管部门', '审批机关', '批准日期', '外国_地区_企业名称', '境外住所', '境外注册资本_万美元_', '境外经营范围', '批准文号', '外国_地区_企业外文名称', '外国_地区_企业经营起始日期', '外国_地区_企业经营截止日期', '外国_地区_企业有权签字人', '外国_地区_企业责任形式', '外国_地区_企业资本_资产_币种', '合伙人数', '有限合伙人数', '合伙方式', '执行人数', '案件编号']
2025-08-04 21:56:50.295 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_外资补充信息表' 去重前记录数: 1
2025-08-04 21:56:50.302 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_外资补充信息表' 去重后记录数: 1
2025-08-04 21:56:50.302 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_外资补充信息表' 去重删除了 0 条记录。
2025-08-04 21:56:50.303 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_外资补充信息表' 去重完成，无重复记录
2025-08-04 21:56:50.311 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_企业登记_企业公示_内资补充信息表'（原有 95 条记录）...
2025-08-04 21:56:50.314 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_内资补充信息表' 排除字段: ID
2025-08-04 21:56:50.314 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_内资补充信息表' 排除字段: 源文件位置
2025-08-04 21:56:50.315 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_企业登记_企业公示_内资补充信息表' 排除字段: 导入批次
2025-08-04 21:56:50.315 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_企业登记_企业公示_内资补充信息表' 去重字段: ['数据源文件', '反馈单位', '审批表', '查询名称', '证供类型', '证件号码', '企业_机构_名称', '主体身份代码', '邮政编码', '联系电话', '电子邮箱', '属地监管工商所', '生产经营地所在行政区划', '生产经营地', '核算方式', '住所产权', '设立方式', '主管部门', '隶属关系', '经营方式', '合伙人数', '有限合伙人数', '合伙方式', '执行人数', '案件编号']
2025-08-04 21:56:50.326 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_企业登记_企业公示_内资补充信息表' 去重前记录数: 95
2025-08-04 21:56:50.332 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_企业登记_企业公示_内资补充信息表' 去重后记录数: 95
2025-08-04 21:56:50.332 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_企业登记_企业公示_内资补充信息表' 去重删除了 0 条记录。
2025-08-04 21:56:50.333 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_企业登记_企业公示_内资补充信息表' 去重完成，无重复记录
2025-08-04 21:56:50.336 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '市监_企业登记_企业公示_农专补充信息表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:50.342 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '市监_统一社会信用代码_统一社会信用代码表'（原有 10 条记录）...
2025-08-04 21:56:50.344 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_统一社会信用代码_统一社会信用代码表' 排除字段: ID
2025-08-04 21:56:50.345 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_统一社会信用代码_统一社会信用代码表' 排除字段: 源文件位置
2025-08-04 21:56:50.345 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '市监_统一社会信用代码_统一社会信用代码表' 排除字段: 导入批次
2025-08-04 21:56:50.345 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '市监_统一社会信用代码_统一社会信用代码表' 去重字段: ['数据源文件', '反馈单位', '审批表', '名称', '证件类型', '证件号码', '统一社会信用代码', '组织机构代码', '机构名称', '注册号', '机构类型', '法定代表人', '国家', '注册资本', '注册资本币种', '实收资本', '业务范围类型', '成立日期', '经营范围', '经营期限起', '经营期限止', '经营状态', '登记机关', '颁发日期_变更日期_', '机构地址', '反馈人', '反馈录入时间', '备注', '法定代表人证件号码', '法定代表人电话号码', '案件编号']
2025-08-04 21:56:50.355 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '市监_统一社会信用代码_统一社会信用代码表' 去重前记录数: 10
2025-08-04 21:56:50.366 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '市监_统一社会信用代码_统一社会信用代码表' 去重后记录数: 10
2025-08-04 21:56:50.366 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '市监_统一社会信用代码_统一社会信用代码表' 去重删除了 0 条记录。
2025-08-04 21:56:50.367 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '市监_统一社会信用代码_统一社会信用代码表' 去重完成，无重复记录
2025-08-04 21:56:50.409 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '不动产查询_不动产全国总库_房地产权表'（原有 226 条记录）...
2025-08-04 21:56:50.411 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_房地产权表' 排除字段: ID
2025-08-04 21:56:50.412 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_房地产权表' 排除字段: 源文件位置
2025-08-04 21:56:50.412 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_房地产权表' 排除字段: 导入批次
2025-08-04 21:56:50.412 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '不动产查询_不动产全国总库_房地产权表' 去重字段: ['数据源文件', '反馈单位', '审批表', '名称', '证件类型', '证件号码', '不动产单元号', '房地坐落', '建筑面积_平方米_', '规划用途', '房屋性质', '竣工时间', '土地使用起始时间', '土地使用结束时间', '不动产权证号', '登记机构', '权属状态', '登记时间', '交易金额_万元_', '共有情况', '共有人名称', '共有人证件类型', '共有人证件号码', '案件编号']
2025-08-04 21:56:50.438 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '不动产查询_不动产全国总库_房地产权表' 去重前记录数: 226
2025-08-04 21:56:50.458 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '不动产查询_不动产全国总库_房地产权表' 去重后记录数: 226
2025-08-04 21:56:50.459 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '不动产查询_不动产全国总库_房地产权表' 去重删除了 0 条记录。
2025-08-04 21:56:50.460 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '不动产查询_不动产全国总库_房地产权表' 去重完成，无重复记录
2025-08-04 21:56:50.502 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '不动产查询_不动产全国总库_抵押权表'（原有 252 条记录）...
2025-08-04 21:56:50.506 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_抵押权表' 排除字段: ID
2025-08-04 21:56:50.506 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_抵押权表' 排除字段: 源文件位置
2025-08-04 21:56:50.506 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_抵押权表' 排除字段: 导入批次
2025-08-04 21:56:50.506 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '不动产查询_不动产全国总库_抵押权表' 去重字段: ['数据源文件', '反馈单位', '审批表', '名称', '证件类型', '证件号码', '不动产单元号', '抵押不动产类型', '坐落', '抵押人', '抵押方式', '被担保主债权数额', '债务履行起始时间', '债务履行结束时间', '不动产登记证明号', '登记机构', '权属状态', '登记时间', '案件编号']
2025-08-04 21:56:50.570 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '不动产查询_不动产全国总库_抵押权表' 去重前记录数: 252
2025-08-04 21:56:50.632 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '不动产查询_不动产全国总库_抵押权表' 去重后记录数: 252
2025-08-04 21:56:50.632 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '不动产查询_不动产全国总库_抵押权表' 去重删除了 0 条记录。
2025-08-04 21:56:50.632 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '不动产查询_不动产全国总库_抵押权表' 去重完成，无重复记录
2025-08-04 21:56:50.639 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '不动产查询_不动产全国总库_预告登记表'（原有 19 条记录）...
2025-08-04 21:56:50.642 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_预告登记表' 排除字段: ID
2025-08-04 21:56:50.642 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_预告登记表' 排除字段: 源文件位置
2025-08-04 21:56:50.642 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_预告登记表' 排除字段: 导入批次
2025-08-04 21:56:50.642 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '不动产查询_不动产全国总库_预告登记表' 去重字段: ['数据源文件', '反馈单位', '审批表', '名称', '证件类型', '证件号码', '不动产单元号', '预告登记种类', '坐落', '规划用途', '建筑面积_平方米_', '不动产登记证明号', '登记机构', '权属状态', '登记时间', '交易金额_万元_', '共有情况', '共有人名称', '共有人证件类型', '共有人证件号码', '案件编号']
2025-08-04 21:56:50.653 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '不动产查询_不动产全国总库_预告登记表' 去重前记录数: 19
2025-08-04 21:56:50.661 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '不动产查询_不动产全国总库_预告登记表' 去重后记录数: 19
2025-08-04 21:56:50.662 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '不动产查询_不动产全国总库_预告登记表' 去重删除了 0 条记录。
2025-08-04 21:56:50.663 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '不动产查询_不动产全国总库_预告登记表' 去重完成，无重复记录
2025-08-04 21:56:50.667 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '不动产查询_不动产全国总库_建设用地宅基地'（原有 15 条记录）...
2025-08-04 21:56:50.671 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_建设用地宅基地' 排除字段: ID
2025-08-04 21:56:50.671 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_建设用地宅基地' 排除字段: 源文件位置
2025-08-04 21:56:50.671 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '不动产查询_不动产全国总库_建设用地宅基地' 排除字段: 导入批次
2025-08-04 21:56:50.672 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '不动产查询_不动产全国总库_建设用地宅基地' 去重字段: ['数据源文件', '反馈单位', '审批表', '名称', '证件类型', '证件号码', '不动产单元号', '坐落', '用途', '使用权面积_平方米_', '权利性质', '使用权起始时间', '使用权结束时间', '不动产权证号', '登记机构', '权属状态', '登记时间', '交易金额_万元_', '共有情况', '共有人名称', '共有人证件类型', '共有人证件号码', '案件编号']
2025-08-04 21:56:50.683 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '不动产查询_不动产全国总库_建设用地宅基地' 去重前记录数: 15
2025-08-04 21:56:50.691 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '不动产查询_不动产全国总库_建设用地宅基地' 去重后记录数: 15
2025-08-04 21:56:50.692 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '不动产查询_不动产全国总库_建设用地宅基地' 去重删除了 0 条记录。
2025-08-04 21:56:50.692 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '不动产查询_不动产全国总库_建设用地宅基地' 去重完成，无重复记录
2025-08-04 21:56:50.700 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '不动产查询_不动产全国总库_查封登记表' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:50.772 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国铁路总公司_铁路客票_票面信息表'（原有 2085 条记录）...
2025-08-04 21:56:50.775 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_票面信息表' 排除字段: ID
2025-08-04 21:56:50.775 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_票面信息表' 排除字段: 源文件位置
2025-08-04 21:56:50.776 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_票面信息表' 排除字段: 导入批次
2025-08-04 21:56:50.776 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_票面信息表' 去重字段: ['数据源文件', '车票序号', '发车日期', '发车时间', '乘车人姓名', '证件类型', '证件号码', '票号', '列车车次', '发站', '到站', '车厢号', '席别', '席位号', '售票车站', '售票时间', '购票人', '购票人证件号码', '购票人联系方式', '反馈人', '反馈时间', '退票车站', '退票日期', '退票时间', '改签车站', '改签时间', '改签新票票号', '案件编号']
2025-08-04 21:56:50.887 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_票面信息表' 去重前记录数: 2085
2025-08-04 21:56:50.995 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_票面信息表' 去重后记录数: 2085
2025-08-04 21:56:50.997 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_票面信息表' 去重删除了 0 条记录。
2025-08-04 21:56:50.998 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国铁路总公司_铁路客票_票面信息表' 去重完成，无重复记录
2025-08-04 21:56:51.041 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国铁路总公司_铁路客票_交易信息表'（原有 1845 条记录）...
2025-08-04 21:56:51.045 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_交易信息表' 排除字段: ID
2025-08-04 21:56:51.045 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_交易信息表' 排除字段: 源文件位置
2025-08-04 21:56:51.045 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_交易信息表' 排除字段: 导入批次
2025-08-04 21:56:51.045 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_交易信息表' 去重字段: ['数据源文件', '车票序号', '列车车次', '发车日期', '交易订单号', '交易时间', '收单行', '案件编号']
2025-08-04 21:56:51.113 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_交易信息表' 去重前记录数: 1845
2025-08-04 21:56:51.180 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_交易信息表' 去重后记录数: 1845
2025-08-04 21:56:51.182 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国铁路总公司_铁路客票_交易信息表' 去重删除了 0 条记录。
2025-08-04 21:56:51.182 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国铁路总公司_铁路客票_交易信息表' 去重完成，无重复记录
2025-08-04 21:56:51.197 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国铁路总公司_同订单同行人_同行人员信息表'（原有 474 条记录）...
2025-08-04 21:56:51.200 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员信息表' 排除字段: ID
2025-08-04 21:56:51.200 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员信息表' 排除字段: 源文件位置
2025-08-04 21:56:51.200 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员信息表' 排除字段: 导入批次
2025-08-04 21:56:51.200 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员信息表' 去重字段: ['数据源文件', '同行人序号', '同行人姓名', '证件类型', '证件号码', '手机号码', '固定电话', '性别', '国籍', '电子邮箱', '旅客类型', '生日', '案件编号']
2025-08-04 21:56:51.213 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员信息表' 去重前记录数: 474
2025-08-04 21:56:51.224 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员信息表' 去重后记录数: 474
2025-08-04 21:56:51.225 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员信息表' 去重删除了 0 条记录。
2025-08-04 21:56:51.226 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国铁路总公司_同订单同行人_同行人员信息表' 去重完成，无重复记录
2025-08-04 21:56:51.336 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国铁路总公司_同订单同行人_同行人员客票'（原有 3972 条记录）...
2025-08-04 21:56:51.339 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员客票' 排除字段: ID
2025-08-04 21:56:51.340 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员客票' 排除字段: 源文件位置
2025-08-04 21:56:51.340 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员客票' 排除字段: 导入批次
2025-08-04 21:56:51.340 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员客票' 去重字段: ['数据源文件', '同行人序号', '同行人姓名', '证件号码', '同行车次', '购票票号', '乘车时间', '乘车起始站', '乘车终止站', '车厢号', '席别', '席位号', '购票人', '购票人证件号码', '购票时间', '案件编号']
2025-08-04 21:56:51.486 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员客票' 去重前记录数: 3972
2025-08-04 21:56:51.649 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员客票' 去重后记录数: 3972
2025-08-04 21:56:51.650 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国铁路总公司_同订单同行人_同行人员客票' 去重删除了 0 条记录。
2025-08-04 21:56:51.650 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国铁路总公司_同订单同行人_同行人员客票' 去重完成，无重复记录
2025-08-04 21:56:51.653 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国铁路总公司_用户注册_互联网注册信息表'（原有 13 条记录）...
2025-08-04 21:56:51.655 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_用户注册_互联网注册信息表' 排除字段: ID
2025-08-04 21:56:51.655 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_用户注册_互联网注册信息表' 排除字段: 源文件位置
2025-08-04 21:56:51.655 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_用户注册_互联网注册信息表' 排除字段: 导入批次
2025-08-04 21:56:51.655 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国铁路总公司_用户注册_互联网注册信息表' 去重字段: ['数据源文件', '注册序号', '注册人姓名', '证件类型', '证件号码', '手机号码', '旅客类型', '生日', '性别', '国籍', '固定电话', '电子邮箱', '反馈人', '反馈时间', '案件编号']
2025-08-04 21:56:51.665 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国铁路总公司_用户注册_互联网注册信息表' 去重前记录数: 13
2025-08-04 21:56:51.669 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国铁路总公司_用户注册_互联网注册信息表' 去重后记录数: 13
2025-08-04 21:56:51.670 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国铁路总公司_用户注册_互联网注册信息表' 去重删除了 0 条记录。
2025-08-04 21:56:51.670 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国铁路总公司_用户注册_互联网注册信息表' 去重完成，无重复记录
2025-08-04 21:56:51.675 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国铁路总公司_用户注册_常用联系人信息表'（原有 84 条记录）...
2025-08-04 21:56:51.677 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_用户注册_常用联系人信息表' 排除字段: ID
2025-08-04 21:56:51.677 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_用户注册_常用联系人信息表' 排除字段: 源文件位置
2025-08-04 21:56:51.678 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国铁路总公司_用户注册_常用联系人信息表' 排除字段: 导入批次
2025-08-04 21:56:51.678 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国铁路总公司_用户注册_常用联系人信息表' 去重字段: ['数据源文件', '注册序号', '乘客姓名', '证件类型', '证件号码', '性别', '旅客类型', '手机号码', '固定电话', '电子邮箱', '邮编', '地址', '案件编号']
2025-08-04 21:56:51.686 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国铁路总公司_用户注册_常用联系人信息表' 去重前记录数: 84
2025-08-04 21:56:51.692 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国铁路总公司_用户注册_常用联系人信息表' 去重后记录数: 84
2025-08-04 21:56:51.693 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国铁路总公司_用户注册_常用联系人信息表' 去重删除了 0 条记录。
2025-08-04 21:56:51.694 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国铁路总公司_用户注册_常用联系人信息表' 去重完成，无重复记录
2025-08-04 21:56:51.698 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '税务_增值税发票_增值税普通发票表'（原有 22 条记录）...
2025-08-04 21:56:51.701 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_增值税普通发票表' 排除字段: ID
2025-08-04 21:56:51.701 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_增值税普通发票表' 排除字段: 源文件位置
2025-08-04 21:56:51.701 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_增值税普通发票表' 排除字段: 导入批次
2025-08-04 21:56:51.702 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '税务_增值税发票_增值税普通发票表' 去重字段: ['数据源文件', '发票序号', '纳税人名称', '纳税人识别号', '纳税人社会信用代码', '发票代码', '发票号码', '价税合计', '作废标志', '开票日期', '校验码', '机器编码', '购货方纳税人识别号', '购货方名称', '购货方社会信用代码', '购货方地址电话', '购货方开户行及账号', '销货方纳税人识别号', '销货方名称', '销货方社会信用代码', '销货方地址电话', '销货方开户行及账号', '销货方省级行政区划', '销货方省级行政区划名称', '销货方省级税务机关', '销货方省级税务机关名称', '销货方地市税务机关', '销货方地市税务机关名称', '销货方区县税务机关', '销货方区县税务机关名称', '发票类别', '发票类别名称', '金额', '税额', '开票方mac地址', '开票人', '收款人', '复核', '备注', '案件编号']
2025-08-04 21:56:51.709 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '税务_增值税发票_增值税普通发票表' 去重前记录数: 22
2025-08-04 21:56:51.714 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '税务_增值税发票_增值税普通发票表' 去重后记录数: 22
2025-08-04 21:56:51.715 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '税务_增值税发票_增值税普通发票表' 去重删除了 0 条记录。
2025-08-04 21:56:51.715 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '税务_增值税发票_增值税普通发票表' 去重完成，无重复记录
2025-08-04 21:56:51.724 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '税务_增值税发票_普票货物或应税劳务服务名'（原有 307 条记录）...
2025-08-04 21:56:51.727 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_普票货物或应税劳务服务名' 排除字段: ID
2025-08-04 21:56:51.728 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_普票货物或应税劳务服务名' 排除字段: 源文件位置
2025-08-04 21:56:51.728 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_普票货物或应税劳务服务名' 排除字段: 导入批次
2025-08-04 21:56:51.728 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '税务_增值税发票_普票货物或应税劳务服务名' 去重字段: ['数据源文件', '发票序号', '纳税人识别号', '行号', '商品编码', '商品名称', '货物劳务名称', '规格型号', '单位', '数量', '单价', '货物金额', '普票税率', '货物税额', '货物或应税劳务名称', '货物或劳务编码', '案件编号']
2025-08-04 21:56:51.740 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '税务_增值税发票_普票货物或应税劳务服务名' 去重前记录数: 307
2025-08-04 21:56:51.754 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '税务_增值税发票_普票货物或应税劳务服务名' 去重后记录数: 307
2025-08-04 21:56:51.755 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '税务_增值税发票_普票货物或应税劳务服务名' 去重删除了 0 条记录。
2025-08-04 21:56:51.756 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '税务_增值税发票_普票货物或应税劳务服务名' 去重完成，无重复记录
2025-08-04 21:56:51.760 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '税务_增值税发票_增值税专用发票表'（原有 8 条记录）...
2025-08-04 21:56:51.764 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_增值税专用发票表' 排除字段: ID
2025-08-04 21:56:51.765 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_增值税专用发票表' 排除字段: 源文件位置
2025-08-04 21:56:51.765 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_增值税专用发票表' 排除字段: 导入批次
2025-08-04 21:56:51.765 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '税务_增值税发票_增值税专用发票表' 去重字段: ['数据源文件', '发票序号', '纳税人名称', '纳税人识别号', '纳税人社会信用代码', '发票代码', '发票号码', '价税合计', '作废标志', '开票日期', '校验码', '机器编码', '购货方纳税人识别号', '购货方名称', '购货方社会信用代码', '购货方地址电话', '购货方开户行及账号', '销货方纳税人识别号', '销货方名称', '销货方社会信用代码', '销货方地址电话', '销货方开户行及账号', '销货方省级行政区划', '销货方省级行政区划名称', '销货方省级税务机关', '销货方省级税务机关名称', '销货方地市税务机关', '销货方地市税务机关名称', '销货方区县税务机关', '销货方区县税务机关名称', '发票类别', '发票类别名称', '金额', '税额', '开票方mac地址', '开票人', '收款人', '复核', '备注', '案件编号']
2025-08-04 21:56:51.772 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '税务_增值税发票_增值税专用发票表' 去重前记录数: 8
2025-08-04 21:56:51.778 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '税务_增值税发票_增值税专用发票表' 去重后记录数: 8
2025-08-04 21:56:51.780 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '税务_增值税发票_增值税专用发票表' 去重删除了 0 条记录。
2025-08-04 21:56:51.780 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '税务_增值税发票_增值税专用发票表' 去重完成，无重复记录
2025-08-04 21:56:51.791 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '税务_增值税发票_专票货物或应税劳务名称表'（原有 194 条记录）...
2025-08-04 21:56:51.794 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_专票货物或应税劳务名称表' 排除字段: ID
2025-08-04 21:56:51.794 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_专票货物或应税劳务名称表' 排除字段: 源文件位置
2025-08-04 21:56:51.794 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '税务_增值税发票_专票货物或应税劳务名称表' 排除字段: 导入批次
2025-08-04 21:56:51.795 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '税务_增值税发票_专票货物或应税劳务名称表' 去重字段: ['数据源文件', '发票序号', '纳税人识别号', '行号', '商品编码', '商品名称', '货物劳务名称', '规格型号', '单位', '数量', '单价', '货物金额', '专票税率', '货物税额', '货物或应税劳务名称', '货物或劳务编码', '案件编号']
2025-08-04 21:56:51.803 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '税务_增值税发票_专票货物或应税劳务名称表' 去重前记录数: 194
2025-08-04 21:56:51.813 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '税务_增值税发票_专票货物或应税劳务名称表' 去重后记录数: 194
2025-08-04 21:56:51.813 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '税务_增值税发票_专票货物或应税劳务名称表' 去重删除了 0 条记录。
2025-08-04 21:56:51.814 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '税务_增值税发票_专票货物或应税劳务名称表' 去重完成，无重复记录
2025-08-04 21:56:51.818 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '本地银行_客户信息本地表'（原有 15 条记录）...
2025-08-04 21:56:51.821 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '本地银行_客户信息本地表' 排除字段: ID
2025-08-04 21:56:51.821 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '本地银行_客户信息本地表' 排除字段: 源文件位置
2025-08-04 21:56:51.821 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '本地银行_客户信息本地表' 排除字段: 导入批次
2025-08-04 21:56:51.821 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '本地银行_客户信息本地表' 去重字段: ['数据源文件', '客户名称', '法人代表', '法人代表证件类型', '法人代表证件号码', '联系手机', '单位地址', '住宅地址', '住宅电话', '联系电话', '证件号码', '工作单位', '客户工商执照号码', '单位电话', '邮箱地址', '地税纳税号', '国税纳税号', '代办人证件类型', '代办人姓名', '代办人证件号码', '任务流水号', '查询反馈结果', '标识', '案件编号']
2025-08-04 21:56:51.829 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '本地银行_客户信息本地表' 去重前记录数: 15
2025-08-04 21:56:51.836 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '本地银行_客户信息本地表' 去重后记录数: 15
2025-08-04 21:56:51.836 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '本地银行_客户信息本地表' 去重删除了 0 条记录。
2025-08-04 21:56:51.837 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '本地银行_客户信息本地表' 去重完成，无重复记录
2025-08-04 21:56:51.839 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '医保_参保信息'（原有 4 条记录）...
2025-08-04 21:56:51.841 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_参保信息' 排除字段: ID
2025-08-04 21:56:51.841 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_参保信息' 排除字段: 源文件位置
2025-08-04 21:56:51.842 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_参保信息' 排除字段: 导入批次
2025-08-04 21:56:51.842 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '医保_参保信息' 去重字段: ['数据源文件', '人员姓名', '性别', '出生日期', '人员证件类型', '证件号码', '手机号码', '户籍地址', '居住地址', '参保单位名称', '参保单位代码', '险种类型', '本次参保日期', '人员参保状态', '人员类别', '有效标志', '案件编号']
2025-08-04 21:56:51.850 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '医保_参保信息' 去重前记录数: 4
2025-08-04 21:56:51.855 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '医保_参保信息' 去重后记录数: 4
2025-08-04 21:56:51.855 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '医保_参保信息' 去重删除了 0 条记录。
2025-08-04 21:56:51.856 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '医保_参保信息' 去重完成，无重复记录
2025-08-04 21:56:51.859 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '医保_药店购药'（原有 46 条记录）...
2025-08-04 21:56:51.861 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_药店购药' 排除字段: ID
2025-08-04 21:56:51.861 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_药店购药' 排除字段: 源文件位置
2025-08-04 21:56:51.861 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_药店购药' 排除字段: 导入批次
2025-08-04 21:56:51.861 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '医保_药店购药' 去重字段: ['数据源文件', '结算ID', '就诊ID', '人员姓名', '人员证件类型', '证件号码', '公务员标志', '定点医药机构名称', '结算时间', '医疗类别', '结算类别', '医疗费总额', '有效标志', '手机号码', '案件编号']
2025-08-04 21:56:51.868 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '医保_药店购药' 去重前记录数: 46
2025-08-04 21:56:51.873 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '医保_药店购药' 去重后记录数: 46
2025-08-04 21:56:51.873 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '医保_药店购药' 去重删除了 0 条记录。
2025-08-04 21:56:51.874 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '医保_药店购药' 去重完成，无重复记录
2025-08-04 21:56:51.878 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '医保_药店购药明细'（原有 76 条记录）...
2025-08-04 21:56:51.881 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_药店购药明细' 排除字段: ID
2025-08-04 21:56:51.881 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_药店购药明细' 排除字段: 源文件位置
2025-08-04 21:56:51.881 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_药店购药明细' 排除字段: 导入批次
2025-08-04 21:56:51.881 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '医保_药店购药明细' 去重字段: ['数据源文件', '结算ID', '就诊ID', '人员姓名', '人员证件类型', '证件号码', '手机号码', '费用发生时间', '定点医药机构名称', '医疗类别', '数量', '单价', '明细项目费用总额', '商品名', '规格', '案件编号']
2025-08-04 21:56:51.888 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '医保_药店购药明细' 去重前记录数: 76
2025-08-04 21:56:51.893 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '医保_药店购药明细' 去重后记录数: 76
2025-08-04 21:56:51.894 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '医保_药店购药明细' 去重删除了 0 条记录。
2025-08-04 21:56:51.895 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '医保_药店购药明细' 去重完成，无重复记录
2025-08-04 21:56:51.898 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '医保_普通门诊'（原有 14 条记录）...
2025-08-04 21:56:51.901 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_普通门诊' 排除字段: ID
2025-08-04 21:56:51.901 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_普通门诊' 排除字段: 源文件位置
2025-08-04 21:56:51.902 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '医保_普通门诊' 排除字段: 导入批次
2025-08-04 21:56:51.902 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '医保_普通门诊' 去重字段: ['数据源文件', '人员姓名', '人员证件类型', '证件号码', '公务员标志', '定点医药机构名称', '结算时间', '病种名称', '医疗类别', '结算类别', '医疗费总额', '案件编号']
2025-08-04 21:56:51.908 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '医保_普通门诊' 去重前记录数: 14
2025-08-04 21:56:51.918 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '医保_普通门诊' 去重后记录数: 14
2025-08-04 21:56:51.919 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '医保_普通门诊' 去重删除了 0 条记录。
2025-08-04 21:56:51.919 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '医保_普通门诊' 去重完成，无重复记录
2025-08-04 21:56:51.922 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '医保_住院结算数据' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:51.925 - INFO - [Dummy-2:30068] - data_cleaning.py:1989 - deduplicate_all_tables() - 🔄 开始去重表 '中国证券登记结算有限公司_证券账户_证券账户'（原有 10 条记录）...
2025-08-04 21:56:51.929 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国证券登记结算有限公司_证券账户_证券账户' 排除字段: ID
2025-08-04 21:56:51.929 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国证券登记结算有限公司_证券账户_证券账户' 排除字段: 源文件位置
2025-08-04 21:56:51.929 - DEBUG - [Dummy-2:30068] - data_cleaning.py:2303 - deduplicate_data() - 表 '中国证券登记结算有限公司_证券账户_证券账户' 排除字段: 导入批次
2025-08-04 21:56:51.929 - INFO - [Dummy-2:30068] - data_cleaning.py:2309 - deduplicate_data() - 表 '中国证券登记结算有限公司_证券账户_证券账户' 去重字段: ['数据源文件', '持有人名称', '证件类型', '证件号码', '市场类型', '证券账户', '证券账户状态', '开户日期', '联系地址', '联系电话', '开户代理机构名称', '案件编号']
2025-08-04 21:56:51.938 - INFO - [Dummy-2:30068] - data_cleaning.py:2334 - deduplicate_data() - 表 '中国证券登记结算有限公司_证券账户_证券账户' 去重前记录数: 10
2025-08-04 21:56:51.947 - INFO - [Dummy-2:30068] - data_cleaning.py:2356 - deduplicate_data() - 表 '中国证券登记结算有限公司_证券账户_证券账户' 去重后记录数: 10
2025-08-04 21:56:51.948 - INFO - [Dummy-2:30068] - data_cleaning.py:2359 - deduplicate_data() - 表 '中国证券登记结算有限公司_证券账户_证券账户' 去重删除了 0 条记录。
2025-08-04 21:56:51.949 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1998 - deduplicate_all_tables() - ✅ 表 '中国证券登记结算有限公司_证券账户_证券账户' 去重完成，无重复记录
2025-08-04 21:56:51.952 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '中国证券登记结算有限公司_证券持有变动_持' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:51.955 - DEBUG - [Dummy-2:30068] - data_cleaning.py:1983 - deduplicate_all_tables() - ℹ️ 表 '中国证券登记结算有限公司_证券持有_持有信息' 中没有案件 20250726182751 的数据，跳过去重
2025-08-04 21:56:51.955 - INFO - [Dummy-2:30068] - data_cleaning.py:2007 - deduplicate_all_tables() - 🎯 所有表去重操作完成
2025-08-04 21:56:51.955 - INFO - [Dummy-2:30068] - data_cleaning.py:2008 - deduplicate_all_tables() - 📊 去重统计总览：
2025-08-04 21:56:51.956 - INFO - [Dummy-2:30068] - data_cleaning.py:2009 - deduplicate_all_tables() -    - 总表数量: 94 个
2025-08-04 21:56:51.957 - INFO - [Dummy-2:30068] - data_cleaning.py:2010 - deduplicate_all_tables() -    - 成功去重: 60 个表
2025-08-04 21:56:51.957 - INFO - [Dummy-2:30068] - data_cleaning.py:2011 - deduplicate_all_tables() -    - 不存在的表: 0 个
2025-08-04 21:56:51.958 - INFO - [Dummy-2:30068] - data_cleaning.py:2012 - deduplicate_all_tables() -    - 无数据的表: 34 个
2025-08-04 21:56:51.958 - INFO - [Dummy-2:30068] - data_cleaning.py:2013 - deduplicate_all_tables() -    - 去重失败: 0 个表
2025-08-04 21:56:51.959 - INFO - [Dummy-2:30068] - data_cleaning.py:2014 - deduplicate_all_tables() -    - 总计删除重复记录: 0 条
2025-08-04 21:56:51.959 - INFO - [Dummy-2:30068] - data_cleaning.py:2017 - deduplicate_all_tables() - 📋 有效去重详情：
2025-08-04 21:56:52.010 - INFO - [Dummy-2:30068] - data_cleaning.py:2182 - start_cleaning() - 清洗步骤 deduplicate_all_tables 完成
2025-08-04 21:56:52.021 - INFO - [Dummy-2:30068] - data_cleaning.py:2199 - start_cleaning() - 选中的 15 个清洗步骤已完成
2025-08-04 21:59:54.025 - INFO - [MainThread:26096] - import_data.py:5519 - closeEvent() - 数据导入窗口正在关闭
2025-08-04 21:59:54.025 - INFO - [MainThread:26096] - import_error_handler.py:324 - stop_monitoring() - 进程监控: 停止监控导入过程
2025-08-04 21:59:54.025 - INFO - [MainThread:26096] - import_error_handler.py:495 - cleanup_heartbeat() - 心跳文件已清理
2025-08-04 21:59:54.025 - INFO - [MainThread:26096] - import_data.py:5548 - closeEvent() - 数据导入窗口关闭完成
2025-08-04 22:00:27.544 - INFO - [MainThread:26096] - import_error_handler.py:523 - start_heartbeat_monitor() - 心跳监控已启动
2025-08-04 22:00:27.564 - INFO - [MainThread:26096] - import_data.py:148 - __init__() - ✅ 自动化管理器已初始化
2025-08-04 22:00:30.360 - INFO - [MainThread:26096] - pivot_export.py:2444 - export_data_with_partitioning() - 发现 100 个包含案件编号的数据表: ['不动产查询_不动产全国总库_建设用地宅基地', '不动产查询_不动产全国总库_房地产权表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_预告登记表', '中国航空_航班同行人信息_同乘三次以上同行人', '中国航空_航班同行人信息_同订单同行人已成行', '中国航空_航班同行人信息_同订单同行人未成行', '中国航空_航班进出港_航班进出港已成行表', '中国航空_航班进出港_航班进出港未成行表', '中国证券登记结算有限公司_证券持有_持有信息', '中国证券登记结算有限公司_证券持有变动_持', '中国证券登记结算有限公司_证券账户_证券账户', '中国铁路总公司_同订单同行人_同行人员信息表', '中国铁路总公司_同订单同行人_同行人员客票', '中国铁路总公司_用户注册_互联网注册信息表', '中国铁路总公司_用户注册_常用联系人信息表', '中国铁路总公司_铁路客票_交易信息表', '中国铁路总公司_铁路客票_票面信息表', '临时账户交易明细表', '信托登记公司_产品信息表', '信托登记公司_信托产品_受益人信息', '信托登记公司_信托产品_委托人信息', '信托登记公司_信托产品_登记信息_受益权结构', '信托登记公司_信托产品_登记信息_合同信息', '信托登记公司_信托产品_终止登记', '信托登记公司_委托人或受益人变动信息表', '信托登记公司_登记信息_受益权结构表', '信托登记公司_登记信息_合同信息表', '信托登记公司_终止登记表', '公安部_交通违法_机动车违章信息表', '公安部_出入境记录_出入境记录信息表', '公安部_出国_境_证件_出入境证件信息', '公安部_同住址_同住址表', '公安部_同户人_同户人表', '公安部_同车违章_同车违章表', '公安部_在逃人员_在逃人员登记信息', '公安部_在逃同案撤销人员_在逃同案撤销人员', '公安部_在逃撤销_在逃人员撤销信息', '公安部_户籍人口_基本人员信息表', '公安部_旅馆住宿_旅馆住宿人员信息表', '公安部_机动车_机动车信息', '公安部_驾驶证_驾驶证信息表', '医保_住院结算数据', '医保_参保信息', '医保_普通门诊', '医保_药店购药', '医保_药店购药明细', '国家税务总局_纳税人登记信息_登记信息表', '国家税务总局_纳税信息_税务缴纳信息表', '增值税发票表', '对手信息', '导入记录表', '市监_企业登记_企业公示_主要人员表', '市监_企业登记_企业公示_内资补充信息表', '市监_企业登记_企业公示_农专补充信息表', '市监_企业登记_企业公示_分支机构备案信息表', '市监_企业登记_企业公示_变更备案信息表', '市监_企业登记_企业公示_吊销信息表', '市监_企业登记_企业公示_外资补充信息表', '市监_企业登记_企业公示_注销信息表', '市监_企业登记_企业公示_清算基本信息表', '市监_企业登记_企业公示_清算成员信息表', '市监_企业登记_企业公示_联络员信息表', '市监_企业登记_企业公示_自然人出资信息表', '市监_企业登记_企业公示_许可信息表', '市监_企业登记_企业公示_财务负责人信息表', '市监_企业登记_企业公示_非自然人出资信息表', '市监_企业登记_企业基本信息表', '市监_统一社会信用代码_统一社会信用代码表', '开户信息表', '本地银行_客户信息本地表', '理财登记中心_理财产品_投资行业信息表', '理财登记中心_理财产品_持有信息表', '理财登记中心_理财产品_理财产品信息表', '电话_登记信息_运营商登记信息表', '电话_话单信息_运营商话单信息表', '税务_增值税发票_专票货物或应税劳务名称表', '税务_增值税发票_增值税专用发票表', '税务_增值税发票_增值税普通发票表', '税务_增值税发票_普票货物或应税劳务服务名', '虚拟运营商_登记信息_虚拟运营商登记信息表', '表类型匹配规则_导出文件名分类表', '证券登记结算_证券持有变动_持', '证券登记结算_证券持有变动_证券持有变动', '财付通交易明细表', '账户交易明细表', '账户信息_共有权优先权信息表', '账户信息_关联子账户信息表', '账户信息_关联子账户信息表本地', '账户信息_客户基本信息表', '账户信息_强制措施信息表', '账户信息（本地）_优先权信息表', '金融理财_金融理财信息表', '金融理财_金融理财账户信息表', '银保信_保险产品_保险人员信息表', '银保信_保险产品_保险保单信息表', '银保信_保险产品_保险赔案信息表', '银保信_保险产品_家庭财产保险表', '银保信_保险产品_航空延误保险表']
2025-08-04 22:00:30.368 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 不动产查询_不动产全国总库_建设用地宅基地 中案件 20250726182751 的数据量: 15
2025-08-04 22:00:30.376 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 不动产查询_不动产全国总库_房地产权表 中案件 20250726182751 的数据量: 226
2025-08-04 22:00:30.394 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 不动产查询_不动产全国总库_抵押权表 中案件 20250726182751 的数据量: 252
2025-08-04 22:00:30.410 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 不动产查询_不动产全国总库_查封登记表 中案件 20250726182751 无数据
2025-08-04 22:00:30.411 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 不动产查询_不动产全国总库_预告登记表 中案件 20250726182751 的数据量: 19
2025-08-04 22:00:30.427 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国航空_航班同行人信息_同乘三次以上同行人 中案件 20250726182751 的数据量: 242
2025-08-04 22:00:30.439 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国航空_航班同行人信息_同订单同行人已成行 中案件 20250726182751 的数据量: 205
2025-08-04 22:00:30.441 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国航空_航班同行人信息_同订单同行人未成行 中案件 20250726182751 的数据量: 18
2025-08-04 22:00:30.441 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国航空_航班进出港_航班进出港已成行表 中案件 20250726182751 的数据量: 304
2025-08-04 22:00:30.441 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国航空_航班进出港_航班进出港未成行表 中案件 20250726182751 的数据量: 26
2025-08-04 22:00:30.441 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 中国证券登记结算有限公司_证券持有_持有信息 中案件 20250726182751 无数据
2025-08-04 22:00:30.441 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 中国证券登记结算有限公司_证券持有变动_持 中案件 20250726182751 无数据
2025-08-04 22:00:30.441 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国证券登记结算有限公司_证券账户_证券账户 中案件 20250726182751 的数据量: 10
2025-08-04 22:00:30.455 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国铁路总公司_同订单同行人_同行人员信息表 中案件 20250726182751 的数据量: 474
2025-08-04 22:00:30.519 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国铁路总公司_同订单同行人_同行人员客票 中案件 20250726182751 的数据量: 3,972
2025-08-04 22:00:30.519 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国铁路总公司_用户注册_互联网注册信息表 中案件 20250726182751 的数据量: 13
2025-08-04 22:00:30.519 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国铁路总公司_用户注册_常用联系人信息表 中案件 20250726182751 的数据量: 84
2025-08-04 22:00:30.552 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国铁路总公司_铁路客票_交易信息表 中案件 20250726182751 的数据量: 1,845
2025-08-04 22:00:30.613 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 中国铁路总公司_铁路客票_票面信息表 中案件 20250726182751 的数据量: 2,085
2025-08-04 22:00:30.614 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 临时账户交易明细表 中案件 20250726182751 无数据
2025-08-04 22:00:30.615 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 信托登记公司_产品信息表 中案件 20250726182751 无数据
2025-08-04 22:00:30.615 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 信托登记公司_信托产品_受益人信息 中案件 20250726182751 无数据
2025-08-04 22:00:30.615 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 信托登记公司_信托产品_委托人信息 中案件 20250726182751 无数据
2025-08-04 22:00:30.615 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 信托登记公司_信托产品_登记信息_受益权结构 中案件 20250726182751 无数据
2025-08-04 22:00:30.615 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 信托登记公司_信托产品_登记信息_合同信息 中案件 20250726182751 无数据
2025-08-04 22:00:30.622 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 信托登记公司_信托产品_终止登记 中案件 20250726182751 无数据
2025-08-04 22:00:30.624 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 信托登记公司_委托人或受益人变动信息表 中案件 20250726182751 无数据
2025-08-04 22:00:30.624 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 信托登记公司_登记信息_受益权结构表 中案件 20250726182751 无数据
2025-08-04 22:00:30.624 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 信托登记公司_登记信息_合同信息表 中案件 20250726182751 无数据
2025-08-04 22:00:30.624 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 信托登记公司_终止登记表 中案件 20250726182751 无数据
2025-08-04 22:00:30.628 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_交通违法_机动车违章信息表 中案件 20250726182751 无数据
2025-08-04 22:00:30.630 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_出入境记录_出入境记录信息表 中案件 20250726182751 无数据
2025-08-04 22:00:30.630 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_出国_境_证件_出入境证件信息 中案件 20250726182751 无数据
2025-08-04 22:00:30.634 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_同住址_同住址表 中案件 20250726182751 无数据
2025-08-04 22:00:30.634 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_同户人_同户人表 中案件 20250726182751 无数据
2025-08-04 22:00:30.634 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_同车违章_同车违章表 中案件 20250726182751 无数据
2025-08-04 22:00:30.634 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_在逃人员_在逃人员登记信息 中案件 20250726182751 无数据
2025-08-04 22:00:30.634 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_在逃同案撤销人员_在逃同案撤销人员 中案件 20250726182751 无数据
2025-08-04 22:00:30.634 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_在逃撤销_在逃人员撤销信息 中案件 20250726182751 无数据
2025-08-04 22:00:30.634 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 公安部_户籍人口_基本人员信息表 中案件 20250726182751 的数据量: 32
2025-08-04 22:00:30.677 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_旅馆住宿_旅馆住宿人员信息表 中案件 20250726182751 无数据
2025-08-04 22:00:30.677 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_机动车_机动车信息 中案件 20250726182751 无数据
2025-08-04 22:00:30.677 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 公安部_驾驶证_驾驶证信息表 中案件 20250726182751 无数据
2025-08-04 22:00:30.677 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 医保_住院结算数据 中案件 20250726182751 无数据
2025-08-04 22:00:30.677 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 医保_参保信息 中案件 20250726182751 的数据量: 4
2025-08-04 22:00:30.677 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 医保_普通门诊 中案件 20250726182751 的数据量: 14
2025-08-04 22:00:30.677 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 医保_药店购药 中案件 20250726182751 的数据量: 46
2025-08-04 22:00:30.696 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 医保_药店购药明细 中案件 20250726182751 的数据量: 76
2025-08-04 22:00:30.696 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 国家税务总局_纳税人登记信息_登记信息表 中案件 20250726182751 的数据量: 11
2025-08-04 22:00:30.771 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 国家税务总局_纳税信息_税务缴纳信息表 中案件 20250726182751 的数据量: 1,072
2025-08-04 22:00:30.771 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 增值税发票表 中案件 20250726182751 无数据
2025-08-04 22:00:30.804 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 对手信息 中案件 20250726182751 的数据量: 5,526
2025-08-04 22:00:30.804 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 导入记录表 中案件 20250726182751 无数据
2025-08-04 22:00:30.819 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_主要人员表 中案件 20250726182751 的数据量: 650
2025-08-04 22:00:30.851 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_内资补充信息表 中案件 20250726182751 的数据量: 95
2025-08-04 22:00:30.865 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 市监_企业登记_企业公示_农专补充信息表 中案件 20250726182751 无数据
2025-08-04 22:00:30.867 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_分支机构备案信息表 中案件 20250726182751 的数据量: 1
2025-08-04 22:00:30.898 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_变更备案信息表 中案件 20250726182751 的数据量: 529
2025-08-04 22:00:30.898 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_吊销信息表 中案件 20250726182751 的数据量: 1
2025-08-04 22:00:30.898 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_外资补充信息表 中案件 20250726182751 的数据量: 1
2025-08-04 22:00:30.898 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_注销信息表 中案件 20250726182751 的数据量: 20
2025-08-04 22:00:30.898 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_清算基本信息表 中案件 20250726182751 的数据量: 2
2025-08-04 22:00:30.898 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_清算成员信息表 中案件 20250726182751 的数据量: 5
2025-08-04 22:00:30.915 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_联络员信息表 中案件 20250726182751 的数据量: 140
2025-08-04 22:00:30.931 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_自然人出资信息表 中案件 20250726182751 的数据量: 260
2025-08-04 22:00:30.931 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_许可信息表 中案件 20250726182751 的数据量: 1
2025-08-04 22:00:30.931 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_财务负责人信息表 中案件 20250726182751 的数据量: 92
2025-08-04 22:00:30.947 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业公示_非自然人出资信息表 中案件 20250726182751 的数据量: 194
2025-08-04 22:00:30.947 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_企业登记_企业基本信息表 中案件 20250726182751 的数据量: 97
2025-08-04 22:00:30.947 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 市监_统一社会信用代码_统一社会信用代码表 中案件 20250726182751 的数据量: 10
2025-08-04 22:00:30.969 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 开户信息表 中案件 20250726182751 的数据量: 1,122
2025-08-04 22:00:30.979 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 本地银行_客户信息本地表 中案件 20250726182751 的数据量: 15
2025-08-04 22:00:31.807 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 理财登记中心_理财产品_投资行业信息表 中案件 20250726182751 的数据量: 30,478
2025-08-04 22:00:31.890 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 理财登记中心_理财产品_持有信息表 中案件 20250726182751 的数据量: 2,228
2025-08-04 22:00:31.890 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 理财登记中心_理财产品_理财产品信息表 中案件 20250726182751 的数据量: 32
2025-08-04 22:00:31.890 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 电话_登记信息_运营商登记信息表 中案件 20250726182751 的数据量: 398
2025-08-04 22:00:32.032 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 电话_话单信息_运营商话单信息表 中案件 20250726182751 的数据量: 31,595
2025-08-04 22:00:32.043 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 税务_增值税发票_专票货物或应税劳务名称表 中案件 20250726182751 的数据量: 194
2025-08-04 22:00:32.046 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 税务_增值税发票_增值税专用发票表 中案件 20250726182751 的数据量: 8
2025-08-04 22:00:32.046 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 税务_增值税发票_增值税普通发票表 中案件 20250726182751 的数据量: 22
2025-08-04 22:00:32.046 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 税务_增值税发票_普票货物或应税劳务服务名 中案件 20250726182751 的数据量: 307
2025-08-04 22:00:32.061 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 虚拟运营商_登记信息_虚拟运营商登记信息表 中案件 20250726182751 无数据
2025-08-04 22:00:32.067 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 表类型匹配规则_导出文件名分类表 中案件 20250726182751 无数据
2025-08-04 22:00:32.067 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 证券登记结算_证券持有变动_持 中案件 20250726182751 无数据
2025-08-04 22:00:32.071 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 证券登记结算_证券持有变动_证券持有变动 中案件 20250726182751 无数据
2025-08-04 22:00:32.077 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 财付通交易明细表 中案件 20250726182751 无数据
2025-08-04 22:00:32.917 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 账户交易明细表 中案件 20250726182751 的数据量: 282,509
2025-08-04 22:00:32.917 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 账户信息_共有权优先权信息表 中案件 20250726182751 无数据
2025-08-04 22:00:32.917 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 账户信息_关联子账户信息表 中案件 20250726182751 的数据量: 119
2025-08-04 22:00:32.933 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 账户信息_关联子账户信息表本地 中案件 20250726182751 的数据量: 35
2025-08-04 22:00:32.933 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 账户信息_客户基本信息表 中案件 20250726182751 的数据量: 50
2025-08-04 22:00:32.949 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 账户信息_强制措施信息表 中案件 20250726182751 的数据量: 12
2025-08-04 22:00:32.949 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 账户信息（本地）_优先权信息表 中案件 20250726182751 无数据
2025-08-04 22:00:32.949 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 金融理财_金融理财信息表 中案件 20250726182751 的数据量: 162
2025-08-04 22:00:32.971 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 金融理财_金融理财账户信息表 中案件 20250726182751 的数据量: 165
2025-08-04 22:00:33.076 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 银保信_保险产品_保险人员信息表 中案件 20250726182751 的数据量: 7,747
2025-08-04 22:00:33.217 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 银保信_保险产品_保险保单信息表 中案件 20250726182751 的数据量: 4,648
2025-08-04 22:00:33.239 - INFO - [MainThread:26096] - pivot_export.py:2459 - export_data_with_partitioning() - 📊 表 银保信_保险产品_保险赔案信息表 中案件 20250726182751 的数据量: 1,256
2025-08-04 22:00:33.242 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 银保信_保险产品_家庭财产保险表 中案件 20250726182751 无数据
2025-08-04 22:00:33.242 - DEBUG - [MainThread:26096] - pivot_export.py:2461 - export_data_with_partitioning() - ⚪ 表 银保信_保险产品_航空延误保险表 中案件 20250726182751 无数据
2025-08-04 22:00:33.242 - INFO - [MainThread:26096] - pivot_export.py:2474 - export_data_with_partitioning() - ✅ 找到 100 个包含案件编号字段的表（包含无数据的表）
2025-08-04 22:00:33.242 - INFO - [MainThread:26096] - pivot_export.py:2479 - export_data_with_partitioning() - 📊 其中有数据的表: 61 个，无数据的表: 39 个
2025-08-04 22:00:33.242 - INFO - [MainThread:26096] - pivot_export.py:2516 - export_data_with_partitioning() - 🚀 导入数据界面：显示表选择对话框，选择后按分类导出
2025-08-04 22:00:35.891 - INFO - [MainThread:26096] - pivot_export.py:2542 - export_data_with_partitioning() - 用户选择了以下表进行导出: ['账户交易明细表', '电话_话单信息_运营商话单信息表', '理财登记中心_理财产品_投资行业信息表', '银保信_保险产品_保险人员信息表', '对手信息', '银保信_保险产品_保险保单信息表', '中国铁路总公司_同订单同行人_同行人员客票', '理财登记中心_理财产品_持有信息表', '中国铁路总公司_铁路客票_票面信息表', '中国铁路总公司_铁路客票_交易信息表', '银保信_保险产品_保险赔案信息表', '开户信息表', '国家税务总局_纳税信息_税务缴纳信息表', '市监_企业登记_企业公示_主要人员表', '市监_企业登记_企业公示_变更备案信息表', '中国铁路总公司_同订单同行人_同行人员信息表', '电话_登记信息_运营商登记信息表', '税务_增值税发票_普票货物或应税劳务服务名', '中国航空_航班进出港_航班进出港已成行表', '市监_企业登记_企业公示_自然人出资信息表', '不动产查询_不动产全国总库_抵押权表', '中国航空_航班同行人信息_同乘三次以上同行人', '不动产查询_不动产全国总库_房地产权表', '中国航空_航班同行人信息_同订单同行人已成行', '市监_企业登记_企业公示_非自然人出资信息表', '税务_增值税发票_专票货物或应税劳务名称表', '金融理财_金融理财账户信息表', '金融理财_金融理财信息表', '市监_企业登记_企业公示_联络员信息表', '账户信息_关联子账户信息表', '市监_企业登记_企业基本信息表', '市监_企业登记_企业公示_内资补充信息表', '市监_企业登记_企业公示_财务负责人信息表', '中国铁路总公司_用户注册_常用联系人信息表', '医保_药店购药明细', '账户信息_客户基本信息表', '医保_药店购药', '账户信息_关联子账户信息表本地', '公安部_户籍人口_基本人员信息表', '理财登记中心_理财产品_理财产品信息表', '中国航空_航班进出港_航班进出港未成行表', '税务_增值税发票_增值税普通发票表', '市监_企业登记_企业公示_注销信息表', '不动产查询_不动产全国总库_预告登记表', '中国航空_航班同行人信息_同订单同行人未成行', '不动产查询_不动产全国总库_建设用地宅基地', '本地银行_客户信息本地表', '医保_普通门诊', '中国铁路总公司_用户注册_互联网注册信息表', '账户信息_强制措施信息表', '国家税务总局_纳税人登记信息_登记信息表', '中国证券登记结算有限公司_证券账户_证券账户', '市监_统一社会信用代码_统一社会信用代码表', '税务_增值税发票_增值税专用发票表', '市监_企业登记_企业公示_清算成员信息表', '医保_参保信息', '市监_企业登记_企业公示_清算基本信息表', '市监_企业登记_企业公示_分支机构备案信息表', '市监_企业登记_企业公示_吊销信息表', '市监_企业登记_企业公示_外资补充信息表', '市监_企业登记_企业公示_许可信息表']
2025-08-04 22:00:55.426 - INFO - [MainThread:26096] - pivot_export.py:2552 - export_data_with_partitioning() - 📁 导出目录: G:/市JW/20250804版本
2025-08-04 22:00:55.426 - INFO - [MainThread:26096] - pivot_export.py:2553 - export_data_with_partitioning() - 🎯 开始按分类导出案件 20250726182751 的数据，案件名称: 20250726SYH
2025-08-04 22:00:55.548 - INFO - [Dummy-4:23576] - pivot_export.py:3658 - start_export() - 开始按分类导出案件 20250726182751 的数据，案件名称: 20250726SYH
2025-08-04 22:00:55.548 - INFO - [Dummy-4:23576] - pivot_export.py:3665 - start_export() - 📖 读取规则文件: 表类型匹配规则_导出文件名分类.xlsx
2025-08-04 22:00:55.569 - INFO - [Dummy-4:23576] - pivot_export.py:3589 - _build_export_map_from_rules() - 📊 规则文件包含 91 行，列名: ['序号', '数据库表名', '工作表名', '导出文件名']
2025-08-04 22:00:55.569 - INFO - [Dummy-4:23576] - pivot_export.py:3596 - _build_export_map_from_rules() - 找到数据库表列: 数据库表名
2025-08-04 22:00:55.569 - INFO - [Dummy-4:23576] - pivot_export.py:3599 - _build_export_map_from_rules() - 找到工作表列: 工作表名
2025-08-04 22:00:55.569 - INFO - [Dummy-4:23576] - pivot_export.py:3602 - _build_export_map_from_rules() - 找到导出文件列: 导出文件名
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 医保信息 -> 参保信息 -> 医保_参保信息
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 医保信息 -> 普通门诊 -> 医保_普通门诊
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 医保信息 -> 药店购药 -> 医保_药店购药
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 医保信息 -> 药店购药明细 -> 医保_药店购药明细
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 医保信息 -> 住院结算数据 -> 医保_住院结算数据
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 通讯信息 -> 运营商登记信息 -> 电话_登记信息_运营商登记信息表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 通讯信息 -> 运营商话单信息 -> 电话_话单信息_运营商话单信息表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 通讯信息 -> 虚拟运营商登记信息 -> 虚拟运营商_登记信息_虚拟运营商登记信息表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 出入境证件信息 -> 公安部_出国_境_证件_出入境证件信息
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 出入境记录信息 -> 公安部_出入境记录_出入境记录信息表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 基本人员信息 -> 公安部_户籍人口_基本人员信息表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 机动车信息 -> 公安部_机动车_机动车信息
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 驾驶证信息 -> 公安部_驾驶证_驾驶证信息表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 机动车违章信息 -> 公安部_交通违法_机动车违章信息表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 旅馆住宿人员信息 -> 公安部_旅馆住宿_旅馆住宿人员信息表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 同车违章 -> 公安部_同车违章_同车违章表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 同户人 -> 公安部_同户人_同户人表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 同住址 -> 公安部_同住址_同住址表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 在逃人员撤销信息 -> 公安部_在逃撤销_在逃人员撤销信息
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 在逃人员登记信息 -> 公安部_在逃人员_在逃人员登记信息
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 公安信息 -> 在逃同案撤销人员 -> 公安部_在逃同案撤销人员_在逃同案撤销人员
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 账户信息 -> 关联子账户信息 -> 账户信息_关联子账户信息表本地
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 账户信息 -> 账号信息 -> 开户信息表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 账户信息 -> 账户信息（本地） -> 本地银行_客户信息本地表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 账户信息 -> 优先权信息 -> 账户信息（本地）_优先权信息表
2025-08-04 22:00:55.569 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 账户信息 -> 共有权、优先权信息 -> 账户信息_共有权优先权信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 账户信息 -> 关联子账户信息 -> 账户信息_关联子账户信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 账户信息 -> 客户基本信息 -> 账户信息_客户基本信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 账户信息 -> 强制措施信息 -> 账户信息_强制措施信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 账户信息 -> 账号基本信息 -> 开户信息表_基本
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 税务纳税信息 -> 登记信息 -> 国家税务总局_纳税人登记信息_登记信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 税务纳税信息 -> 税务缴纳信息 -> 国家税务总局_纳税信息_税务缴纳信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 增值税发票信息 -> 普票货物或应税劳务、服务名称 -> 税务_增值税发票_普票货物或应税劳务服务名
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 增值税发票信息 -> 增值税普通发票 -> 税务_增值税发票_增值税普通发票表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 增值税发票信息 -> 增值税专用发票 -> 税务_增值税发票_增值税专用发票表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 增值税发票信息 -> 专票货物或应税劳务名称 -> 税务_增值税发票_专票货物或应税劳务名称表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 理财信息 -> 金融理财信息 -> 金融理财_金融理财信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 理财信息 -> 金融理财账户信息 -> 金融理财_金融理财账户信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 理财信息 -> 持有信息 -> 理财登记中心_理财产品_持有信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 理财信息 -> 理财产品信息 -> 理财登记中心_理财产品_理财产品信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 理财信息 -> 投资行业信息 -> 理财登记中心_理财产品_投资行业信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 变更备案信息 -> 市监_企业登记_企业公示_变更备案信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 财务负责人信息 -> 市监_企业登记_企业公示_财务负责人信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 吊销信息 -> 市监_企业登记_企业公示_吊销信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 非自然人出资信息 -> 市监_企业登记_企业公示_非自然人出资信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 分支机构备案信息 -> 市监_企业登记_企业公示_分支机构备案信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 联络员信息 -> 市监_企业登记_企业公示_联络员信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 内资补充信息 -> 市监_企业登记_企业公示_内资补充信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 农专补充信息 -> 市监_企业登记_企业公示_农专补充信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 企业公示_许可信息 -> 市监_企业登记_企业公示_许可信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 企业基本信息 -> 市监_企业登记_企业基本信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 清算成员信息 -> 市监_企业登记_企业公示_清算成员信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 清算基本信息 -> 市监_企业登记_企业公示_清算基本信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 外资补充信息 -> 市监_企业登记_企业公示_外资补充信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 主要人员 -> 市监_企业登记_企业公示_主要人员表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 注销信息 -> 市监_企业登记_企业公示_注销信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 自然人出资信息 -> 市监_企业登记_企业公示_自然人出资信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 工商信息 -> 统一社会信用代码 -> 市监_统一社会信用代码_统一社会信用代码表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 信托信息 -> 产品信息 -> 信托登记公司_产品信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 信托信息 -> 合同信息 -> 信托登记公司_登记信息_合同信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 信托信息 -> 受益权结构 -> 信托登记公司_登记信息_受益权结构表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 信托信息 -> 委托人或受益人变动信息 -> 信托登记公司_委托人或受益人变动信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 信托信息 -> 登记信息_受益权结构 -> 信托登记公司_信托产品_登记信息_受益权结构
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 信托信息 -> 登记信息_合同信息 -> 信托登记公司_信托产品_登记信息_合同信息
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 信托信息 -> 终止登记 -> 信托登记公司_信托产品_终止登记
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 信托信息 -> 委托人信息 -> 信托登记公司_信托产品_委托人信息
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 信托信息 -> 终止登记 -> 信托登记公司_终止登记表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 保险信息 -> 保险保单信息 -> 银保信_保险产品_保险保单信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 保险信息 -> 保险赔案信息 -> 银保信_保险产品_保险赔案信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 保险信息 -> 保险人员信息 -> 银保信_保险产品_保险人员信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 保险信息 -> 航空延误保险 -> 银保信_保险产品_航空延误保险表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 保险信息 -> 家庭财产保险 -> 银保信_保险产品_家庭财产保险表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 航空信息 -> 航班进出港(未成行) -> 中国航空_航班进出港_航班进出港未成行表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 航空信息 -> 航班进出港(已成行) -> 中国航空_航班进出港_航班进出港已成行表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 航空信息 -> 同乘三次以上同行人 -> 中国航空_航班同行人信息_同乘三次以上同行人
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 航空信息 -> 同订单同行人(未成行) -> 中国航空_航班同行人信息_同订单同行人未成行
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 航空信息 -> 同订单同行人(已成行) -> 中国航空_航班同行人信息_同订单同行人已成行
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 铁路信息 -> 交易信息 -> 中国铁路总公司_铁路客票_交易信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 铁路信息 -> 票面信息 -> 中国铁路总公司_铁路客票_票面信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 铁路信息 -> 同行人员客票信息 -> 中国铁路总公司_同订单同行人_同行人员客票
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 铁路信息 -> 同行人员信息 -> 中国铁路总公司_同订单同行人_同行人员信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 铁路信息 -> 常用联系人信息 -> 中国铁路总公司_用户注册_常用联系人信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 铁路信息 -> 互联网注册信息 -> 中国铁路总公司_用户注册_互联网注册信息表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 证券信息 -> 持有信息 -> 中国证券登记结算有限公司_证券持有变动_持
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 证券信息 -> 证券账户 -> 中国证券登记结算有限公司_证券账户_证券账户
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 不动产 -> 查封登记 -> 不动产查询_不动产全国总库_查封登记表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 不动产 -> 抵押权 -> 不动产查询_不动产全国总库_抵押权表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 不动产 -> 房地产权 -> 不动产查询_不动产全国总库_房地产权表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 不动产 -> 建设用地、宅基地使用权 -> 不动产查询_不动产全国总库_建设用地宅基地
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 不动产 -> 预告登记 -> 不动产查询_不动产全国总库_预告登记表
2025-08-04 22:00:55.585 - DEBUG - [Dummy-4:23576] - pivot_export.py:3625 - _build_export_map_from_rules() - 添加映射: 证券信息 -> 证券持有变动 -> 证券登记结算_证券持有变动_证券持有变动
2025-08-04 22:00:55.589 - INFO - [Dummy-4:23576] - pivot_export.py:3669 - start_export() - ✅ 成功从规则文件构建导出映射，共14个分类
2025-08-04 22:00:55.589 - INFO - [Dummy-4:23576] - pivot_export.py:3689 - start_export() - 📋 成功构建导出映射，共14个导出文件：['医保信息', '通讯信息', '公安信息', '账户信息', '税务纳税信息', '增值税发票信息', '理财信息', '工商信息', '信托信息', '保险信息', '航空信息', '铁路信息', '证券信息', '不动产']
2025-08-04 22:00:55.589 - INFO - [Dummy-4:23576] - pivot_export.py:3698 - start_export() - 🚀 开始导出 14 个文件...
2025-08-04 22:00:55.589 - INFO - [Dummy-4:23576] - pivot_export.py:3715 - start_export() - 🔧 开始处理特殊表: 账户交易明细表
2025-08-04 22:00:55.589 - INFO - [Dummy-4:23576] - pivot_export.py:3937 - _export_special_table() - 🔧 开始处理特殊表: 账户交易明细表
2025-08-04 22:00:55.824 - INFO - [Dummy-4:23576] - pivot_export.py:3963 - _export_special_table() - 特殊表 账户交易明细表 有 282,509 条记录，使用传统导出模式
2025-08-04 22:00:55.827 - INFO - [Dummy-4:23576] - pivot_export.py:58 - __init__() - 系统资源配置: CPU核心=24, 内存=31.7GB, 设置并行工作线程=6, chunk大小=1000000
2025-08-04 22:00:55.827 - INFO - [Dummy-4:23576] - pivot_export.py:86 - __init__() - 成功创建PostgreSQL数据库连接
2025-08-04 22:00:55.827 - INFO - [Dummy-4:23576] - pivot_export.py:3976 - _export_special_table() - 表 账户交易明细表 数据量较小，使用单文件导出
2025-08-04 22:00:55.889 - INFO - [Dummy-4:23576] - pivot_export.py:309 - _export_single_file() - 表 账户交易明细表 的列名: ['id', '交易账卡号', '交易账号', '交易户名', '交易证件号码', '交易方开户银行', '交易日期', '交易金额', '交易余额', '收付标志', '对手账号', '对手卡号', '现金标志', '对手户名', '对手身份证号', '对手开户银行', '摘要说明', '交易币种', '商户名称', '商户号', '交易网点名称', '交易场所', '交易发生地', '交易是否成功', '传票号', 'IP地址', 'MAC地址', '交易流水号', '对手余额', '渠道', '交易类型', '日志号', '凭证种类', '凭证号', '交易柜员号', '备注', '查询账号', '查询卡号', '本方账号', '本方卡号', '案件编号', '源文件位置', '导入批次', '交易账卡号_digits', '交易账号_digits', '对手账号_digits', '对手卡号_digits']
2025-08-04 22:00:55.889 - INFO - [Dummy-4:23576] - pivot_export.py:322 - _export_single_file() - 导出表 '账户交易明细表' 时排除字段: ['id', 'ID', '交易账卡号_digits', '交易账号_digits', '对手账号_digits', '对手卡号_digits']
2025-08-04 22:00:56.041 - INFO - [Dummy-4:23576] - pivot_export.py:340 - _export_single_file() - 开始导出表 账户交易明细表，总记录数: 282509
2025-08-04 22:00:56.047 - INFO - [Dummy-4:23576] - pivot_export.py:352 - _export_single_file() - 账户交易明细表将按 ORDER BY "交易日期" ASC排序
2025-08-04 22:00:56.047 - INFO - [Dummy-4:23576] - pivot_export.py:355 - _export_single_file() - 表 账户交易明细表 数据量较小，一次性获取全部数据
2025-08-04 22:01:55.976 - INFO - [Dummy-4:23576] - pivot_export.py:1236 - _format_excel_columns() - 从DataFrame获取到 42 个列名
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易账卡号 设置宽度: 18 (标题长度: 5, 数据长度: 16)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易账号 设置宽度: 18 (标题长度: 4, 数据长度: 16)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易户名 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易证件号码 设置宽度: 20 (标题长度: 6, 数据长度: 18)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易方开户银行 设置宽度: 9 (标题长度: 7, 数据长度: 7)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易日期 设置宽度: 21 (标题长度: 4, 数据长度: 19)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易金额 设置宽度: 9 (标题长度: 4, 数据长度: 7)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易余额 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 收付标志 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 对手账号 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 对手卡号 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 现金标志 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 对手户名 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 对手身份证号 设置宽度: 8 (标题长度: 6, 数据长度: 6)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 对手开户银行 设置宽度: 8 (标题长度: 6, 数据长度: 6)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 摘要说明 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易币种 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 商户名称 设置宽度: 20 (标题长度: 4, 数据长度: 18)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 商户号 设置宽度: 17 (标题长度: 3, 数据长度: 15)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易网点名称 设置宽度: 8 (标题长度: 6, 数据长度: 6)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易场所 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易发生地 设置宽度: 8 (标题长度: 5, 数据长度: 5)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易是否成功 设置宽度: 8 (标题长度: 6, 数据长度: 6)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 传票号 设置宽度: 8 (标题长度: 3, 数据长度: 3)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 IP地址 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 MAC地址 设置宽度: 8 (标题长度: 5, 数据长度: 5)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易流水号 设置宽度: 8 (标题长度: 5, 数据长度: 5)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 对手余额 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 渠道 设置宽度: 8 (标题长度: 2, 数据长度: 2)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易类型 设置宽度: 15 (标题长度: 4, 数据长度: 13)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 日志号 设置宽度: 8 (标题长度: 3, 数据长度: 3)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 凭证种类 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 凭证号 设置宽度: 8 (标题长度: 3, 数据长度: 3)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 交易柜员号 设置宽度: 8 (标题长度: 5, 数据长度: 5)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 备注 设置宽度: 8 (标题长度: 2, 数据长度: 2)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 查询账号 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 查询卡号 设置宽度: 18 (标题长度: 4, 数据长度: 16)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 本方账号 设置宽度: 18 (标题长度: 4, 数据长度: 16)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 本方卡号 设置宽度: 18 (标题长度: 4, 数据长度: 16)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 案件编号 设置宽度: 16 (标题长度: 4, 数据长度: 14)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 源文件位置 设置宽度: 60 (标题长度: 5, 数据长度: 144)
2025-08-04 22:01:55.976 - DEBUG - [Dummy-4:23576] - pivot_export.py:1302 - _format_excel_columns() - 列 导入批次 设置宽度: 8 (标题长度: 4, 数据长度: 4)
2025-08-04 22:01:55.976 - INFO - [Dummy-4:23576] - pivot_export.py:1309 - _format_excel_columns() - 完成Excel格式化，表名: 账户交易明细表，应用了 42 列的格式设置
2025-08-04 22:02:11.377 - INFO - [Dummy-4:23576] - pivot_export.py:388 - _export_single_file() - 成功导出 282509 行数据到文件: 20250726SYH_账户交易明细表_20250804_220055.xlsx
2025-08-04 22:02:11.566 - INFO - [Dummy-4:23576] - pivot_export.py:3985 - _export_special_table() - ✅ 成功导出特殊表: 账户交易明细表
2025-08-04 22:02:11.584 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 1/14: 医保信息
2025-08-04 22:02:11.584 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_医保信息_20250804_220211.xlsx
2025-08-04 22:02:11.598 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 参保信息 合并1个表, 共4行, 字段数18
2025-08-04 22:02:11.598 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 普通门诊 合并1个表, 共14行, 字段数13
2025-08-04 22:02:11.598 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 药店购药 合并1个表, 共46行, 字段数16
2025-08-04 22:02:11.617 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 药店购药明细 合并1个表, 共76行, 字段数17
2025-08-04 22:02:11.622 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 医保_住院结算数据 未被用户选择，跳过
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 参保信息 添加筛选，范围: A1:R5
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 50
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(人员姓名) 设置宽度: 8
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(性别) 设置宽度: 8
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(出生日期) 设置宽度: 12
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(人员证件类型) 设置宽度: 8
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(手机号码) 设置宽度: 8
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(户籍地址) 设置宽度: 8
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(居住地址) 设置宽度: 11
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(参保单位名称) 设置宽度: 11
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(参保单位代码) 设置宽度: 28
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(险种类型) 设置宽度: 8
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(本次参保日期) 设置宽度: 12
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(人员参保状态) 设置宽度: 8
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(人员类别) 设置宽度: 8
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(有效标志) 设置宽度: 8
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(案件编号) 设置宽度: 16
2025-08-04 22:02:11.627 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 参保信息 冻结首行
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 普通门诊 添加筛选，范围: A1:M15
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 50
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(人员姓名) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(人员证件类型) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(证件号码) 设置宽度: 20
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(公务员标志) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(定点医药机构名称) 设置宽度: 27
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(结算时间) 设置宽度: 21
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(病种名称) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(医疗类别) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(结算类别) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(医疗费总额) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(案件编号) 设置宽度: 16
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 普通门诊 冻结首行
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 药店购药 添加筛选，范围: A1:P47
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 50
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(结算ID) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(就诊ID) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(人员姓名) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(人员证件类型) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(公务员标志) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(定点医药机构名称) 设置宽度: 24
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(结算时间) 设置宽度: 21
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(医疗类别) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(结算类别) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(医疗费总额) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(有效标志) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(手机号码) 设置宽度: 8
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(案件编号) 设置宽度: 16
2025-08-04 22:02:11.631 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 药店购药 冻结首行
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 药店购药明细 添加筛选，范围: A1:Q77
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 50
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(结算ID) 设置宽度: 8
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(就诊ID) 设置宽度: 8
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(人员姓名) 设置宽度: 8
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(人员证件类型) 设置宽度: 8
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(手机号码) 设置宽度: 8
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(费用发生时间) 设置宽度: 21
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(定点医药机构名称) 设置宽度: 24
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(医疗类别) 设置宽度: 8
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(数量) 设置宽度: 10
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(单价) 设置宽度: 12
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(明细项目费用总额) 设置宽度: 10
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(商品名) 设置宽度: 8
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(规格) 设置宽度: 8
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(案件编号) 设置宽度: 16
2025-08-04 22:02:11.646 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 药店购药明细 冻结首行
2025-08-04 22:02:11.678 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 医保信息 导出完成: G:/市JW/20250804版本\20250726SYH_医保信息_20250804_220211.xlsx
2025-08-04 22:02:11.678 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 2/14: 通讯信息
2025-08-04 22:02:11.693 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_通讯信息_20250804_220211.xlsx
2025-08-04 22:02:11.709 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 运营商登记信息 合并1个表, 共398行, 字段数58
2025-08-04 22:02:12.028 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 运营商话单信息 合并1个表, 共31595行, 字段数37
2025-08-04 22:02:12.035 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 虚拟运营商_登记信息_虚拟运营商登记信息表 未被用户选择，跳过
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 运营商登记信息 添加筛选，范围: A1:BF399
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(序号) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(号码类别) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(号码) 设置宽度: 22
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(IMSI号) 设置宽度: 17
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(IMEI号) 设置宽度: 18
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(SIM卡类型) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(SIM卡号) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(PUK码) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(归属运营商标识) 设置宽度: 9
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(亲情号码业务) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(亲情号码列表) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(一号多终端关联号码列表) 设置宽度: 13
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(群组名称) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(群组标识) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(语音国际漫游) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(群组语音业务) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(前转业务) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(前转号码) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(号码归属省) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(号码归属市) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(入网渠道) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(渠道地址) 设置宽度: 29
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(入网时间) 设置宽度: 22
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(销户时间) 设置宽度: 21
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(首次通话时间) 设置宽度: 21
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(最后停机时间) 设置宽度: 21
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(实名状态) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(用户类型) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(使用人姓名) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(使用人证件类型) 设置宽度: 12
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(使用人证件号码) 设置宽度: 20
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(使用人证件地址) 设置宽度: 37
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(通信地址) 设置宽度: 18
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(单位名称) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(单位证件类型) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AL(单位证件号码) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AM(单位证件地址) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AN(单位通信地址) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AO(责任人姓名) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AP(责任人证件类型) 设置宽度: 9
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AQ(责任人证件号码) 设置宽度: 9
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AR(责任人证件地址) 设置宽度: 9
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AS(责任人通讯地址) 设置宽度: 9
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AT(责任人电话) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AU(代办人姓名) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AV(代办人证件类型) 设置宽度: 12
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AW(代办人证件号码) 设置宽度: 20
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AX(代办人证件地址) 设置宽度: 17
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AY(代办人通讯地址) 设置宽度: 9
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AZ(代办人电话) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BA(电子邮箱) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BB(电子邮箱接收地址) 设置宽度: 10
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BC(其他登记地址) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BD(号码状态) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BE(主副卡标识) 设置宽度: 8
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BF(案件编号) 设置宽度: 16
2025-08-04 22:02:12.140 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 运营商登记信息 冻结首行
2025-08-04 22:02:17.552 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 运营商话单信息 添加筛选，范围: A1:AK31596
2025-08-04 22:02:17.600 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 9
2025-08-04 22:02:17.616 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:17.632 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(序号) 设置宽度: 8
2025-08-04 22:02:17.632 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(通信记录唯一标识) 设置宽度: 18
2025-08-04 22:02:17.648 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(通话类型) 设置宽度: 11
2025-08-04 22:02:17.664 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(话单类型) 设置宽度: 8
2025-08-04 22:02:17.680 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(本机号码) 设置宽度: 15
2025-08-04 22:02:17.690 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(本机IMSI号) 设置宽度: 17
2025-08-04 22:02:17.695 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(本机IMEI号) 设置宽度: 9
2025-08-04 22:02:17.695 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(本机RAC号) 设置宽度: 8
2025-08-04 22:02:17.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(本机LAC号) 设置宽度: 9
2025-08-04 22:02:17.727 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(本机基站ID) 设置宽度: 8
2025-08-04 22:02:17.727 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(本机CELLID) 设置宽度: 21
2025-08-04 22:02:17.743 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(本机归属运营商) 设置宽度: 9
2025-08-04 22:02:17.759 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(本机通话所在地) 设置宽度: 9
2025-08-04 22:02:17.775 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(对方号码) 设置宽度: 15
2025-08-04 22:02:17.778 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(对方IMSI号) 设置宽度: 9
2025-08-04 22:02:17.791 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(对方IMEI号) 设置宽度: 9
2025-08-04 22:02:17.806 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(对方RAC号) 设置宽度: 8
2025-08-04 22:02:17.806 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(对方LAC号) 设置宽度: 8
2025-08-04 22:02:17.822 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(对方基站ID) 设置宽度: 8
2025-08-04 22:02:17.838 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(对方CELLID) 设置宽度: 10
2025-08-04 22:02:17.838 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(对方归属运营商) 设置宽度: 9
2025-08-04 22:02:17.854 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(对方通话所在地) 设置宽度: 9
2025-08-04 22:02:17.870 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(对方号码归属地) 设置宽度: 9
2025-08-04 22:02:17.886 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(前转主叫号码) 设置宽度: 13
2025-08-04 22:02:17.886 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(呼叫开始时间) 设置宽度: 21
2025-08-04 22:02:17.903 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(呼叫时长) 设置宽度: 8
2025-08-04 22:02:17.918 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(是否群内呼叫) 设置宽度: 8
2025-08-04 22:02:17.934 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(群组编号) 设置宽度: 8
2025-08-04 22:02:17.934 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(群组名称) 设置宽度: 8
2025-08-04 22:02:17.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(短信发送接收时间) 设置宽度: 21
2025-08-04 22:02:17.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(案件编号) 设置宽度: 16
2025-08-04 22:02:17.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(用户姓名) 设置宽度: 8
2025-08-04 22:02:17.982 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(用户身份证号码) 设置宽度: 9
2025-08-04 22:02:17.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(对方姓名) 设置宽度: 8
2025-08-04 22:02:17.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(对方身份证号码) 设置宽度: 9
2025-08-04 22:02:17.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 运营商话单信息 冻结首行
2025-08-04 22:02:22.839 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 通讯信息 导出完成: G:/市JW/20250804版本\20250726SYH_通讯信息_20250804_220211.xlsx
2025-08-04 22:02:22.855 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 3/14: 公安信息
2025-08-04 22:02:22.855 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_公安信息_20250804_220222.xlsx
2025-08-04 22:02:22.871 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_出国_境_证件_出入境证件信息 未被用户选择，跳过
2025-08-04 22:02:22.871 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_出入境记录_出入境记录信息表 未被用户选择，跳过
2025-08-04 22:02:22.890 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 基本人员信息 合并1个表, 共32行, 字段数24
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_机动车_机动车信息 未被用户选择，跳过
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_驾驶证_驾驶证信息表 未被用户选择，跳过
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_交通违法_机动车违章信息表 未被用户选择，跳过
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_旅馆住宿_旅馆住宿人员信息表 未被用户选择，跳过
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_同车违章_同车违章表 未被用户选择，跳过
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_同户人_同户人表 未被用户选择，跳过
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_同住址_同住址表 未被用户选择，跳过
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_在逃撤销_在逃人员撤销信息 未被用户选择，跳过
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_在逃人员_在逃人员登记信息 未被用户选择，跳过
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 公安部_在逃同案撤销人员_在逃同案撤销人员 未被用户选择，跳过
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 基本人员信息 添加筛选，范围: A1:X33
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 25
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(姓名) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(性别) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(身份证号) 设置宽度: 20
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(民族) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(出生日期) 设置宽度: 10
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(曾用名) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(籍贯) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(身高) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(职业) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(户籍地区划) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(籍贯国家) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(出生地区划) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(出生地国家_地区) 设置宽度: 10
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(兵役情况) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(死亡日期) 设置宽度: 10
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(文化程度) 设置宽度: 11
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(婚姻状况) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(户籍地) 设置宽度: 27
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(人员状态) 设置宽度: 8
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(从业单位) 设置宽度: 17
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(照片) 设置宽度: 50
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(案件编号) 设置宽度: 16
2025-08-04 22:02:22.890 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 基本人员信息 冻结首行
2025-08-04 22:02:22.948 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 公安信息 导出完成: G:/市JW/20250804版本\20250726SYH_公安信息_20250804_220222.xlsx
2025-08-04 22:02:22.949 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 4/14: 账户信息
2025-08-04 22:02:22.951 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_账户信息_20250804_220222.xlsx
2025-08-04 22:02:22.981 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 关联子账户信息 合并2个表, 共154行, 字段数18
2025-08-04 22:02:22.996 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 账号信息 合并1个表, 共1122行, 字段数47
2025-08-04 22:02:23.013 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 账户信息（本地） 合并1个表, 共15行, 字段数25
2025-08-04 22:02:23.013 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 账户信息（本地）_优先权信息表 未被用户选择，跳过
2025-08-04 22:02:23.013 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 账户信息_共有权优先权信息表 未被用户选择，跳过
2025-08-04 22:02:23.028 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 客户基本信息 合并1个表, 共50行, 字段数33
2025-08-04 22:02:23.044 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 强制措施信息 合并1个表, 共12行, 字段数16
2025-08-04 22:02:23.060 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 开户信息表_基本 未被用户选择，跳过
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 关联子账户信息 添加筛选，范围: A1:R155
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 50
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(账卡号) 设置宽度: 24
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(子账户序号) 设置宽度: 19
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(子账户类别) 设置宽度: 8
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(子账户账号) 设置宽度: 28
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(币种) 设置宽度: 8
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(钞汇标志) 设置宽度: 8
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(账户余额) 设置宽度: 11
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(账户状态) 设置宽度: 8
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(可用余额) 设置宽度: 11
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(案件编号) 设置宽度: 16
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(反馈单位) 设置宽度: 13
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(审批表) 设置宽度: 22
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(查询对象名称) 设置宽度: 14
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(证件类型) 设置宽度: 8
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(证件号码) 设置宽度: 20
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(账号) 设置宽度: 21
2025-08-04 22:02:23.092 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 关联子账户信息 冻结首行
2025-08-04 22:02:23.299 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 账号信息 添加筛选，范围: A1:AU1123
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(账户开户名称) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(开户人证件号码) 设置宽度: 20
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(交易卡号) 设置宽度: 21
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(交易账号) 设置宽度: 24
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(账号开户时间) 设置宽度: 12
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(账户余额) 设置宽度: 9
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(可用余额) 设置宽度: 9
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(币种) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(开户网点代码) 设置宽度: 16
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(开户网点) 设置宽度: 23
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(账户状态) 设置宽度: 9
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(钞汇标志名称) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(销户日期) 设置宽度: 12
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(账户类型) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(开户联系方式) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(联系电话) 设置宽度: 13
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(通信地址) 设置宽度: 25
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(代理人) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(代理人电话) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(备注) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(开户省份) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(开户城市) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(账号开户银行) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(客户代码) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(法人代表) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(客户工商执照号码) 设置宽度: 10
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(法人代表证件号码) 设置宽度: 10
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(住宅地址) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(邮政编码) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(代办人证件号码) 设置宽度: 9
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(邮箱地址) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(关联资金账户) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(地税纳税号) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(单位电话) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(代办人证件类型) 设置宽度: 9
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(住宅电话) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(法人代表证件类型) 设置宽度: 10
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AL(国税纳税号) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AM(单位地址) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AN(工作单位) 设置宽度: 16
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AO(销户网点) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AP(最后交易时间) 设置宽度: 16
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AQ(账户销户银行) 设置宽度: 8
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AR(案件编号) 设置宽度: 16
2025-08-04 22:02:23.315 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AS(交易卡号_digits) 设置宽度: 21
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AT(交易账号_digits) 设置宽度: 24
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AU(数据源文件) 设置宽度: 37
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 账号信息 冻结首行
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 账户信息（本地） 添加筛选，范围: A1:Y16
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 50
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(客户名称) 设置宽度: 18
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(法人代表) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(法人代表证件类型) 设置宽度: 10
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(法人代表证件号码) 设置宽度: 10
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(联系手机) 设置宽度: 13
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(单位地址) 设置宽度: 43
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(住宅地址) 设置宽度: 32
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(住宅电话) 设置宽度: 13
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(联系电话) 设置宽度: 13
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(证件号码) 设置宽度: 20
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(工作单位) 设置宽度: 24
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(客户工商执照号码) 设置宽度: 10
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(单位电话) 设置宽度: 14
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(邮箱地址) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(地税纳税号) 设置宽度: 20
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(国税纳税号) 设置宽度: 20
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(代办人证件类型) 设置宽度: 9
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(代办人姓名) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(代办人证件号码) 设置宽度: 9
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(任务流水号) 设置宽度: 29
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(查询反馈结果) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(标识) 设置宽度: 34
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(案件编号) 设置宽度: 16
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 账户信息（本地） 冻结首行
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 客户基本信息 添加筛选，范围: A1:AG51
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询对象名称) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件类型) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(是否有财产) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(客户名称) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(查询反馈结果原因) 设置宽度: 10
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(证件类型_1) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(证件号码_1) 设置宽度: 20
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(联系电话) 设置宽度: 15
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(联系手机) 设置宽度: 16
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(代办人姓名) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(代办人证件类型) 设置宽度: 9
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(代办人证件号码) 设置宽度: 9
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(住宅地址) 设置宽度: 37
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(住宅电话) 设置宽度: 15
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(工作单位) 设置宽度: 25
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(单位地址) 设置宽度: 37
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(单位电话) 设置宽度: 15
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(邮箱地址) 设置宽度: 21
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(账单地址) 设置宽度: 28
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(法人代表) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(法人代表证件类型) 设置宽度: 10
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(法人代表证件号码) 设置宽度: 10
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(客户工商执照号码) 设置宽度: 10
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(国税纳税号) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(地税纳税号) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(案件编号) 设置宽度: 16
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(卡号) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(账号) 设置宽度: 8
2025-08-04 22:02:23.331 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 客户基本信息 冻结首行
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 强制措施信息 添加筛选，范围: A1:P13
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 33
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询对象名称) 设置宽度: 14
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件类型) 设置宽度: 8
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(账号) 设置宽度: 22
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(措施序号) 设置宽度: 8
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(冻结开始日) 设置宽度: 10
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(冻结截止日) 设置宽度: 10
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(冻结机关名称) 设置宽度: 18
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(冻结金额) 设置宽度: 13
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(备注) 设置宽度: 27
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(冻结措施类型) 设置宽度: 8
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(案件编号) 设置宽度: 16
2025-08-04 22:02:23.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 强制措施信息 冻结首行
2025-08-04 22:02:23.554 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 账户信息 导出完成: G:/市JW/20250804版本\20250726SYH_账户信息_20250804_220222.xlsx
2025-08-04 22:02:23.554 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 5/14: 税务纳税信息
2025-08-04 22:02:23.554 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_税务纳税信息_20250804_220223.xlsx
2025-08-04 22:02:23.569 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 登记信息 合并1个表, 共11行, 字段数13
2025-08-04 22:02:23.633 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 税务缴纳信息 合并1个表, 共1072行, 字段数11
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 登记信息 添加筛选，范围: A1:M12
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(姓名) 设置宽度: 8
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(证件类型) 设置宽度: 8
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(证件号码) 设置宽度: 20
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(纳税人识别号) 设置宽度: 20
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(居住地址) 设置宽度: 8
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(电子邮箱) 设置宽度: 8
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(电话号码) 设置宽度: 13
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(序号) 设置宽度: 8
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(单位名称) 设置宽度: 18
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(职务名称) 设置宽度: 8
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(案件编号) 设置宽度: 16
2025-08-04 22:02:23.650 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 登记信息 冻结首行
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 税务缴纳信息 添加筛选，范围: A1:K1073
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(姓名) 设置宽度: 8
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(证件号码) 设置宽度: 20
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(税款所属期始) 设置宽度: 12
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(税款所属期止) 设置宽度: 12
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(征收品目代码) 设置宽度: 11
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(征收品目名称) 设置宽度: 8
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(税种名称) 设置宽度: 8
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(应纳税额) 设置宽度: 14
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(案件编号) 设置宽度: 16
2025-08-04 22:02:23.712 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 税务缴纳信息 冻结首行
2025-08-04 22:02:23.792 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 税务纳税信息 导出完成: G:/市JW/20250804版本\20250726SYH_税务纳税信息_20250804_220223.xlsx
2025-08-04 22:02:23.808 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 6/14: 增值税发票信息
2025-08-04 22:02:23.808 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_增值税发票信息_20250804_220223.xlsx
2025-08-04 22:02:23.840 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 普票货物或应税劳务、服务名称 合并1个表, 共307行, 字段数18
2025-08-04 22:02:23.858 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 增值税普通发票 合并1个表, 共22行, 字段数41
2025-08-04 22:02:23.872 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 增值税专用发票 合并1个表, 共8行, 字段数41
2025-08-04 22:02:23.890 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 专票货物或应税劳务名称 合并1个表, 共194行, 字段数18
2025-08-04 22:02:23.934 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 普票货物或应税劳务、服务名称 添加筛选，范围: A1:R308
2025-08-04 22:02:23.934 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:23.934 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 34
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(发票序号) 设置宽度: 8
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(纳税人识别号) 设置宽度: 20
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(行号) 设置宽度: 8
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(商品编码) 设置宽度: 21
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(商品名称) 设置宽度: 8
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(货物劳务名称) 设置宽度: 16
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(规格型号) 设置宽度: 8
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(单位) 设置宽度: 8
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(数量) 设置宽度: 14
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(单价) 设置宽度: 10
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(货物金额) 设置宽度: 12
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(普票税率) 设置宽度: 8
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(货物税额) 设置宽度: 10
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(货物或应税劳务名称) 设置宽度: 16
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(货物或劳务编码) 设置宽度: 21
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(案件编号) 设置宽度: 16
2025-08-04 22:02:23.950 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 普票货物或应税劳务、服务名称 冻结首行
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 增值税普通发票 添加筛选，范围: A1:AO23
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 50
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(发票序号) 设置宽度: 8
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(纳税人名称) 设置宽度: 18
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(纳税人识别号) 设置宽度: 20
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(纳税人社会信用代码) 设置宽度: 20
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(发票代码) 设置宽度: 14
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(发票号码) 设置宽度: 10
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(价税合计) 设置宽度: 12
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(作废标志) 设置宽度: 8
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(开票日期) 设置宽度: 21
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(校验码) 设置宽度: 8
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(机器编码) 设置宽度: 8
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(购货方纳税人识别号) 设置宽度: 20
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(购货方名称) 设置宽度: 19
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(购货方社会信用代码) 设置宽度: 20
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(购货方地址电话) 设置宽度: 48
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(购货方开户行及账号) 设置宽度: 40
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(销货方纳税人识别号) 设置宽度: 20
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(销货方名称) 设置宽度: 18
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(销货方社会信用代码) 设置宽度: 11
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(销货方地址电话) 设置宽度: 50
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(销货方开户行及账号) 设置宽度: 48
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(销货方省级行政区划) 设置宽度: 11
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(销货方省级行政区划名称) 设置宽度: 13
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(销货方省级税务机关) 设置宽度: 13
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(销货方省级税务机关名称) 设置宽度: 13
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(销货方地市税务机关) 设置宽度: 13
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(销货方地市税务机关名称) 设置宽度: 13
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(销货方区县税务机关) 设置宽度: 13
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(销货方区县税务机关名称) 设置宽度: 18
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(发票类别) 设置宽度: 8
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(发票类别名称) 设置宽度: 8
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(金额) 设置宽度: 12
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(税额) 设置宽度: 10
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(开票方mac地址) 设置宽度: 19
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(开票人) 设置宽度: 8
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AL(收款人) 设置宽度: 8
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AM(复核) 设置宽度: 8
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AN(备注) 设置宽度: 50
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AO(案件编号) 设置宽度: 16
2025-08-04 22:02:23.966 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 增值税普通发票 冻结首行
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 增值税专用发票 添加筛选，范围: A1:AO9
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 35
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(发票序号) 设置宽度: 8
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(纳税人名称) 设置宽度: 16
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(纳税人识别号) 设置宽度: 20
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(纳税人社会信用代码) 设置宽度: 20
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(发票代码) 设置宽度: 14
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(发票号码) 设置宽度: 10
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(价税合计) 设置宽度: 12
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(作废标志) 设置宽度: 8
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(开票日期) 设置宽度: 21
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(校验码) 设置宽度: 8
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(机器编码) 设置宽度: 8
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(购货方纳税人识别号) 设置宽度: 20
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(购货方名称) 设置宽度: 20
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(购货方社会信用代码) 设置宽度: 20
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(购货方地址电话) 设置宽度: 34
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(购货方开户行及账号) 设置宽度: 44
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(销货方纳税人识别号) 设置宽度: 20
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(销货方名称) 设置宽度: 16
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(销货方社会信用代码) 设置宽度: 11
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(销货方地址电话) 设置宽度: 50
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(销货方开户行及账号) 设置宽度: 41
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(销货方省级行政区划) 设置宽度: 11
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(销货方省级行政区划名称) 设置宽度: 13
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(销货方省级税务机关) 设置宽度: 13
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(销货方省级税务机关名称) 设置宽度: 13
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(销货方地市税务机关) 设置宽度: 13
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(销货方地市税务机关名称) 设置宽度: 13
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(销货方区县税务机关) 设置宽度: 13
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(销货方区县税务机关名称) 设置宽度: 18
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(发票类别) 设置宽度: 8
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(发票类别名称) 设置宽度: 8
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(金额) 设置宽度: 12
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(税额) 设置宽度: 11
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(开票方mac地址) 设置宽度: 19
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(开票人) 设置宽度: 8
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AL(收款人) 设置宽度: 8
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AM(复核) 设置宽度: 8
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AN(备注) 设置宽度: 50
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AO(案件编号) 设置宽度: 16
2025-08-04 22:02:23.981 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 增值税专用发票 冻结首行
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 专票货物或应税劳务名称 添加筛选，范围: A1:R195
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 35
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(发票序号) 设置宽度: 8
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(纳税人识别号) 设置宽度: 20
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(行号) 设置宽度: 8
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(商品编码) 设置宽度: 21
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(商品名称) 设置宽度: 8
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(货物劳务名称) 设置宽度: 16
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(规格型号) 设置宽度: 16
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(单位) 设置宽度: 8
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(数量) 设置宽度: 10
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(单价) 设置宽度: 10
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(货物金额) 设置宽度: 12
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(专票税率) 设置宽度: 8
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(货物税额) 设置宽度: 11
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(货物或应税劳务名称) 设置宽度: 16
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(货物或劳务编码) 设置宽度: 21
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(案件编号) 设置宽度: 16
2025-08-04 22:02:23.998 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 专票货物或应税劳务名称 冻结首行
2025-08-04 22:02:24.076 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 增值税发票信息 导出完成: G:/市JW/20250804版本\20250726SYH_增值税发票信息_20250804_220223.xlsx
2025-08-04 22:02:24.076 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 7/14: 理财信息
2025-08-04 22:02:24.076 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_理财信息_20250804_220224.xlsx
2025-08-04 22:02:24.102 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 金融理财信息 合并1个表, 共162行, 字段数40
2025-08-04 22:02:24.124 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 金融理财账户信息 合并1个表, 共165行, 字段数28
2025-08-04 22:02:24.251 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 持有信息 合并1个表, 共2228行, 字段数11
2025-08-04 22:02:24.268 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 理财产品信息 合并1个表, 共32行, 字段数33
2025-08-04 22:02:25.158 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 投资行业信息 合并1个表, 共30478行, 字段数9
2025-08-04 22:02:25.188 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 金融理财信息 添加筛选，范围: A1:AN163
2025-08-04 22:02:25.188 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:25.188 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 35
2025-08-04 22:02:25.188 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:25.188 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:25.190 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询对象名称) 设置宽度: 16
2025-08-04 22:02:25.190 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件类型) 设置宽度: 8
2025-08-04 22:02:25.190 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:25.190 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(查询卡号) 设置宽度: 8
2025-08-04 22:02:25.190 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(开户网点) 设置宽度: 21
2025-08-04 22:02:25.190 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(账户类别) 设置宽度: 12
2025-08-04 22:02:25.190 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(账户状态) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(理财卡号) 设置宽度: 21
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(理财账号) 设置宽度: 21
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(金融资产序号) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(金融理财名称) 设置宽度: 27
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(金融理财类型) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(产品销售种类) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(金融产品编号) 设置宽度: 10
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(资产管理人) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(资产可否通过银行交易) 设置宽度: 12
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(资产交易限制类型) 设置宽度: 10
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(资产交易限制消除时间) 设置宽度: 19
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(产品状态) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(质押权人) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(托管人) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(受益人) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(成立日) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(赎回日) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(托管账号) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(计量单位) 设置宽度: 8
2025-08-04 22:02:25.191 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(币种) 设置宽度: 8
2025-08-04 22:02:25.192 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(资产单位价格) 设置宽度: 8
2025-08-04 22:02:25.192 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(数量_份额_金额) 设置宽度: 10
2025-08-04 22:02:25.192 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(可控数量_份额_金额) 设置宽度: 12
2025-08-04 22:02:25.192 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(资产总数额) 设置宽度: 10
2025-08-04 22:02:25.192 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(可控资产总数额) 设置宽度: 10
2025-08-04 22:02:25.192 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(备注) 设置宽度: 50
2025-08-04 22:02:25.192 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AL(反馈人) 设置宽度: 8
2025-08-04 22:02:25.192 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AM(反馈日期) 设置宽度: 21
2025-08-04 22:02:25.192 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AN(案件编号) 设置宽度: 16
2025-08-04 22:02:25.192 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 金融理财信息 冻结首行
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 金融理财账户信息 添加筛选，范围: A1:AB166
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 33
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询对象名称) 设置宽度: 14
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件类型) 设置宽度: 8
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(是否有财产) 设置宽度: 8
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(查询反馈结果原因) 设置宽度: 10
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(查询卡号) 设置宽度: 21
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(理财卡号) 设置宽度: 27
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(理财账号) 设置宽度: 27
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(网银账户名称) 设置宽度: 8
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(最后登录IP) 设置宽度: 8
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(最后登录时间) 设置宽度: 8
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(账户类别) 设置宽度: 12
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(账户状态) 设置宽度: 8
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(开户网点) 设置宽度: 23
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(开户网点代码) 设置宽度: 14
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(开户日期) 设置宽度: 10
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(销户日期) 设置宽度: 10
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(销户网点) 设置宽度: 13
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(币种) 设置宽度: 8
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(钞汇标志) 设置宽度: 8
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(账户余额) 设置宽度: 13
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(可用余额) 设置宽度: 13
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(最后交易时间) 设置宽度: 16
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(案件编号) 设置宽度: 16
2025-08-04 22:02:25.206 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 金融理财账户信息 冻结首行
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 持有信息 添加筛选，范围: A1:K2229
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(产品登记编码) 设置宽度: 16
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(持有日期) 设置宽度: 12
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(币种) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(持有份额) 设置宽度: 11
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(持有金额) 设置宽度: 11
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(折算人民币金额) 设置宽度: 11
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(理财收益) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(理财收益率___) 设置宽度: 10
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(案件编号) 设置宽度: 16
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 持有信息 冻结首行
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 理财产品信息 添加筛选，范围: A1:AG33
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(名称) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(证件类型) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(证件号码) 设置宽度: 20
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(性别) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(手机号码) 设置宽度: 13
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(固定电话) 设置宽度: 15
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(电子邮箱) 设置宽度: 18
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(投资者类别) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(联系地址) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(紧急联系人) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(紧急联系人证件类型) 设置宽度: 11
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(紧急联系人证件号码) 设置宽度: 11
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(紧急联系人联系方式) 设置宽度: 11
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(产品名称) 设置宽度: 32
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(产品登记编码) 设置宽度: 16
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(产品运作模式) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(产品募集方式) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(产品收益类型) 设置宽度: 9
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(收益率分档情况说明) 设置宽度: 17
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(目标客户类型) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(发行机构) 设置宽度: 12
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(预计客户最低年收益率___) 设置宽度: 15
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(预计客户最高年收益率___) 设置宽度: 15
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(业绩比较基准) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(是否结构化_分级_产品) 设置宽度: 13
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(初始净值) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(产品净值) 设置宽度: 8
2025-08-04 22:02:25.301 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(累计净值) 设置宽度: 8
2025-08-04 22:02:25.317 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(募集起始日期) 设置宽度: 12
2025-08-04 22:02:25.317 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(产品起始日期) 设置宽度: 12
2025-08-04 22:02:25.317 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(案件编号) 设置宽度: 16
2025-08-04 22:02:25.317 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 理财产品信息 冻结首行
2025-08-04 22:02:26.640 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 投资行业信息 添加筛选，范围: A1:I30479
2025-08-04 22:02:26.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 10
2025-08-04 22:02:26.671 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:26.671 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(产品登记编码) 设置宽度: 16
2025-08-04 22:02:26.687 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(投资资产名称) 设置宽度: 16
2025-08-04 22:02:26.695 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(投资资产类别) 设置宽度: 11
2025-08-04 22:02:26.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(投资资产状态) 设置宽度: 8
2025-08-04 22:02:26.719 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(投资行业) 设置宽度: 12
2025-08-04 22:02:26.735 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(投资资产净值比重___) 设置宽度: 13
2025-08-04 22:02:26.735 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(案件编号) 设置宽度: 16
2025-08-04 22:02:26.735 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 投资行业信息 冻结首行
2025-08-04 22:02:28.379 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 理财信息 导出完成: G:/市JW/20250804版本\20250726SYH_理财信息_20250804_220224.xlsx
2025-08-04 22:02:28.398 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 8/14: 工商信息
2025-08-04 22:02:28.398 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_工商信息_20250804_220228.xlsx
2025-08-04 22:02:28.421 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 变更备案信息 合并1个表, 共529行, 字段数15
2025-08-04 22:02:28.453 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 财务负责人信息 合并1个表, 共92行, 字段数17
2025-08-04 22:02:28.469 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 吊销信息 合并1个表, 共1行, 字段数14
2025-08-04 22:02:28.504 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 非自然人出资信息 合并1个表, 共194行, 字段数27
2025-08-04 22:02:28.528 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 分支机构备案信息 合并1个表, 共1行, 字段数17
2025-08-04 22:02:28.539 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 联络员信息 合并1个表, 共140行, 字段数17
2025-08-04 22:02:28.564 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 内资补充信息 合并1个表, 共95行, 字段数26
2025-08-04 22:02:28.564 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 市监_企业登记_企业公示_农专补充信息表 未被用户选择，跳过
2025-08-04 22:02:28.564 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 企业公示_许可信息 合并1个表, 共1行, 字段数27
2025-08-04 22:02:28.599 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 企业基本信息 合并1个表, 共97行, 字段数35
2025-08-04 22:02:28.613 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 清算成员信息 合并1个表, 共5行, 字段数18
2025-08-04 22:02:28.631 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 清算基本信息 合并1个表, 共2行, 字段数15
2025-08-04 22:02:28.645 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 外资补充信息 合并1个表, 共1行, 字段数65
2025-08-04 22:02:28.709 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 主要人员 合并1个表, 共650行, 字段数31
2025-08-04 22:02:28.725 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 注销信息 合并1个表, 共20行, 字段数25
2025-08-04 22:02:28.741 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 自然人出资信息 合并1个表, 共260行, 字段数35
2025-08-04 22:02:28.741 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 统一社会信用代码 合并1个表, 共10行, 字段数32
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 变更备案信息 添加筛选，范围: A1:O530
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 8
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 8
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 16
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(变更项ID) 设置宽度: 20
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(主体身份代码) 设置宽度: 20
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(变更事项) 设置宽度: 34
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(变更前内容) 设置宽度: 50
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(变更后内容) 设置宽度: 50
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(变更日期) 设置宽度: 12
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(案件编号) 设置宽度: 16
2025-08-04 22:02:28.773 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 变更备案信息 冻结首行
2025-08-04 22:02:28.792 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 财务负责人信息 添加筛选，范围: A1:Q93
2025-08-04 22:02:28.792 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.792 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 50
2025-08-04 22:02:28.793 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.793 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.793 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 20
2025-08-04 22:02:28.793 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 11
2025-08-04 22:02:28.793 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.793 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 22
2025-08-04 22:02:28.793 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(财务负责人ID) 设置宽度: 38
2025-08-04 22:02:28.793 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(主体身份代码) 设置宽度: 38
2025-08-04 22:02:28.793 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(财务负责人姓名) 设置宽度: 9
2025-08-04 22:02:28.793 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(财务负责人证件类型) 设置宽度: 14
2025-08-04 22:02:28.794 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(财务负责人证件号码) 设置宽度: 20
2025-08-04 22:02:28.794 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(固定电话) 设置宽度: 15
2025-08-04 22:02:28.794 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(移动电话) 设置宽度: 13
2025-08-04 22:02:28.794 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(电子邮箱) 设置宽度: 21
2025-08-04 22:02:28.794 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(案件编号) 设置宽度: 16
2025-08-04 22:02:28.794 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 财务负责人信息 冻结首行
2025-08-04 22:02:28.795 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 吊销信息 添加筛选，范围: A1:N2
2025-08-04 22:02:28.795 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.795 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:28.795 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 8
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 8
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 17
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(主体身份代码) 设置宽度: 20
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(吊销日期) 设置宽度: 15
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(违法依据) 设置宽度: 8
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(吊销机关) 设置宽度: 8
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(吊销依据) 设置宽度: 9
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(案件编号) 设置宽度: 16
2025-08-04 22:02:28.796 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 吊销信息 冻结首行
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 非自然人出资信息 添加筛选，范围: A1:AA195
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 33
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 14
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 11
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 19
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(投资人身份标识) 设置宽度: 34
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(主体身份代码) 设置宽度: 26
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(投资人_主管部门名称) 设置宽度: 31
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(投资人类型_主管部门类型) 设置宽度: 14
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(证照类型) 设置宽度: 14
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(证照编号) 设置宽度: 20
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(认缴出资额) 设置宽度: 13
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(认缴出资额折万美元_万美元_) 设置宽度: 16
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(认缴出资方式) 设置宽度: 8
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(认缴出资比例) 设置宽度: 10
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(认缴出资时间) 设置宽度: 12
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(实缴出资额_万元_) 设置宽度: 11
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(实缴出资额折万美元_万美元_) 设置宽度: 16
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(住所) 设置宽度: 50
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(币种) 设置宽度: 8
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(国别_地区_) 设置宽度: 8
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(执行合伙事务标志) 设置宽度: 10
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(承担责任方式_责任形式) 设置宽度: 13
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(案件编号) 设置宽度: 16
2025-08-04 22:02:28.810 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 非自然人出资信息 冻结首行
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 分支机构备案信息 添加筛选，范围: A1:Q2
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 8
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 8
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 22
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(分支机构ID) 设置宽度: 20
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(主体身份代码) 设置宽度: 20
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(分支机构主体身份代码) 设置宽度: 20
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(分支机构名称) 设置宽度: 22
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(注册号) 设置宽度: 17
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(统一社会信用代码) 设置宽度: 20
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(登记机关) 设置宽度: 18
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(登记日期) 设置宽度: 21
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(案件编号) 设置宽度: 16
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 分支机构备案信息 冻结首行
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 联络员信息 添加筛选，范围: A1:Q141
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 35
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 16
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 11
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 20
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(联络员ID) 设置宽度: 38
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(主体身份代码) 设置宽度: 38
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(联络员姓名) 设置宽度: 8
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(联络员证件类型) 设置宽度: 14
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(联络员证件号码) 设置宽度: 20
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(固定电话) 设置宽度: 15
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(移动电话) 设置宽度: 13
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(电子邮箱) 设置宽度: 21
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(案件编号) 设置宽度: 16
2025-08-04 22:02:28.820 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 联络员信息 冻结首行
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 内资补充信息 添加筛选，范围: A1:Z96
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 50
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 20
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 11
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 22
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(主体身份代码) 设置宽度: 38
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(邮政编码) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(联系电话) 设置宽度: 15
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(电子邮箱) 设置宽度: 20
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(属地监管工商所) 设置宽度: 24
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(生产经营地所在行政区划) 设置宽度: 13
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(生产经营地) 设置宽度: 50
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(核算方式) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(住所产权) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(设立方式) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(主管部门) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(隶属关系) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(经营方式) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(合伙人数) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(有限合伙人数) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(合伙方式) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(执行人数) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(案件编号) 设置宽度: 16
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 内资补充信息 冻结首行
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 企业公示_许可信息 添加筛选，范围: A1:AA2
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 15
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(许可信息ID) 设置宽度: 20
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(主体身份代码) 设置宽度: 20
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(统一社会信用代码) 设置宽度: 20
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(企业_机构_名称_1) 设置宽度: 15
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(注册号) 设置宽度: 17
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(许可文件编号) 设置宽度: 9
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(有效期自) 设置宽度: 15
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(有效期至) 设置宽度: 15
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(许可机关) 设置宽度: 14
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(许可内容) 设置宽度: 39
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(状态) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(注销日期) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(注销原因) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(被吊销日期) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(被吊销原因) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(其它无效日期) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(其它无效原因) 设置宽度: 8
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(公示日期) 设置宽度: 15
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(案件编号) 设置宽度: 16
2025-08-04 22:02:28.836 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 企业公示_许可信息 冻结首行
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 企业基本信息 添加筛选，范围: A1:AI98
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 50
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 20
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 11
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 22
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(主体身份代码) 设置宽度: 38
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(统一社会信用代码) 设置宽度: 20
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(法定代表人) 设置宽度: 14
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(注册号) 设置宽度: 17
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(市场主体类型) 设置宽度: 24
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(行业门类) 设置宽度: 17
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(行业代码) 设置宽度: 18
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(成立日期) 设置宽度: 12
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(登记机关) 设置宽度: 20
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(业务范围类型) 设置宽度: 8
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(经营范围) 设置宽度: 50
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(经营_驻在_期限自) 设置宽度: 12
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(经营_驻在_期限至) 设置宽度: 12
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(登记状态) 设置宽度: 14
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(住所所在行政区划) 设置宽度: 10
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(住所) 设置宽度: 50
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(注册资本_金__万元_) 设置宽度: 14
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(注册资本_金_币种) 设置宽度: 11
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(注册资本_金_折万美元) 设置宽度: 13
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(实收资本) 设置宽度: 12
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(实收资本折万美元) 设置宽度: 10
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(国别_地区_) 设置宽度: 8
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(从业人员_农专成员总数) 设置宽度: 13
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(是否城镇) 设置宽度: 8
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(统计企业类型) 设置宽度: 13
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(核准日期) 设置宽度: 21
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(案件编号) 设置宽度: 16
2025-08-04 22:02:28.852 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 企业基本信息 冻结首行
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 清算成员信息 添加筛选，范围: A1:R6
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 14
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(清算成员编号) 设置宽度: 34
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(清算信息编号) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(主体身份代码) 设置宽度: 26
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(清算组成员) 设置宽度: 13
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(清算组成员证件类型) 设置宽度: 14
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(清算组成员证件号码) 设置宽度: 20
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(地址) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(联系电话) 设置宽度: 13
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(清算负责人标志) 设置宽度: 9
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(案件编号) 设置宽度: 16
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 清算成员信息 冻结首行
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 清算基本信息 添加筛选，范围: A1:O3
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 14
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(清算信息编号) 设置宽度: 34
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(主体身份代码) 设置宽度: 26
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(清算完结情况) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(清算完结日期) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(债务承接人) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(债权承接人) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(案件编号) 设置宽度: 16
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 清算基本信息 冻结首行
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 外资补充信息 添加筛选，范围: A1:BM2
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 14
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(主体身份代码) 设置宽度: 26
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(邮政编码) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(联系电话) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(电子邮箱) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(属地监管工商所) 设置宽度: 9
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(生产经营地所在行政区划) 设置宽度: 13
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(生产经营地) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(核算方式) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(外资产业代码) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(中西部优势产业代码) 设置宽度: 11
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(实收资本折人民币_万元_) 设置宽度: 14
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(注册资本_金_折人民币_万元_) 设置宽度: 17
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(住所所在经济开发区) 设置宽度: 11
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(项目类型) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(投资总额_万元_) 设置宽度: 13
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(投资总额币种) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(投资总额折万美元_万美元_) 设置宽度: 15
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(中方注册资本_金__万元_) 设置宽度: 15
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(中方注册资本_金_币种) 设置宽度: 13
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(中方注册资本_金_折万美元_万美元_) 设置宽度: 20
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(中方注册资本_金_出资比例) 设置宽度: 15
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(中方实收资本_万元_) 设置宽度: 12
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(中方实收资本币种) 设置宽度: 10
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(中方实收资本折万美元_万美元_) 设置宽度: 17
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(中方实收资本出资比例) 设置宽度: 12
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(外方注册资本_金__万美元_) 设置宽度: 16
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(外方注册资本_金_币种) 设置宽度: 13
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(外方注册资本_金_折万美元_万美元_) 设置宽度: 20
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(外方注册资本_金_出资比例) 设置宽度: 15
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AL(外方实收资本_万美元_) 设置宽度: 13
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AM(外方实收资本币种) 设置宽度: 10
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AN(外方实收资本折万美元_万美元_) 设置宽度: 17
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AO(外方实收资本出资比例) 设置宽度: 12
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AP(转型日期) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AQ(设立方式) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AR(经营活动类型) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AS(承包工程或经营管理项目) 设置宽度: 13
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AT(承包工程或经营管理内容) 设置宽度: 13
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AU(主管部门) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AV(审批机关) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AW(批准日期) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AX(外国_地区_企业名称) 设置宽度: 12
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AY(境外住所) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AZ(境外注册资本_万美元_) 设置宽度: 13
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BA(境外经营范围) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BB(批准文号) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BC(外国_地区_企业外文名称) 设置宽度: 14
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BD(外国_地区_企业经营起始日期) 设置宽度: 16
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BE(外国_地区_企业经营截止日期) 设置宽度: 16
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BF(外国_地区_企业有权签字人) 设置宽度: 15
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BG(外国_地区_企业责任形式) 设置宽度: 14
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BH(外国_地区_企业资本_资产_币种) 设置宽度: 18
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BI(合伙人数) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BJ(有限合伙人数) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BK(合伙方式) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BL(执行人数) 设置宽度: 8
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 BM(案件编号) 设置宽度: 16
2025-08-04 22:02:28.868 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 外资补充信息 冻结首行
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 主要人员 添加筛选，范围: A1:AE651
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 8
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 8
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 17
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(人员ID) 设置宽度: 21
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(主体身份代码) 设置宽度: 20
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(主要人员姓名) 设置宽度: 8
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(性别) 设置宽度: 8
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(出生日期) 设置宽度: 12
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(主要人员证件类型) 设置宽度: 14
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(证件号码_代表证编号) 设置宽度: 20
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(职务) 设置宽度: 10
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(职务产生方式) 设置宽度: 8
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(申请前职业状况) 设置宽度: 9
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(法定代表人标志_首席代表标志_负责人标识) 设置宽度: 22
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(任命单位_委派方) 设置宽度: 10
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(联系电话) 设置宽度: 15
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(国别_地区_) 设置宽度: 8
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(固定电话) 设置宽度: 15
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(移动电话) 设置宽度: 13
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(电子邮箱) 设置宽度: 21
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(住址) 设置宽度: 31
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(入境时间) 设置宽度: 8
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(代表证期限自_任职起始日期) 设置宽度: 15
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(代表证期限至_任职截止日期) 设置宽度: 15
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(邮政编码) 设置宽度: 8
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(案件编号) 设置宽度: 16
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 主要人员 冻结首行
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 注销信息 添加筛选，范围: A1:Y21
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 35
2025-08-04 22:02:28.948 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 16
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 11
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 22
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(主体身份代码) 设置宽度: 26
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(注销日期) 设置宽度: 12
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(注销原因) 设置宽度: 8
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(对外投资清理完毕标志) 设置宽度: 12
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(分公司注销登记情况) 设置宽度: 11
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(债权债务清理完结情况) 设置宽度: 12
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(清算组成员备案确认文书编号) 设置宽度: 25
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(公告报纸名称) 设置宽度: 8
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(公告日期) 设置宽度: 12
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(批准机关) 设置宽度: 8
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(批准文号) 设置宽度: 26
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(批准日期) 设置宽度: 12
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(清稅情况) 设置宽度: 8
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(批准证书缴销情况) 设置宽度: 10
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(海关手续清缴情况) 设置宽度: 10
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(清理债权债务单位) 设置宽度: 10
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(案件编号) 设置宽度: 16
2025-08-04 22:02:28.963 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 注销信息 冻结首行
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 自然人出资信息 添加筛选，范围: A1:AI261
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 35
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(查询名称) 设置宽度: 16
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证供类型) 设置宽度: 11
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(企业_机构_名称) 设置宽度: 20
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(投资人身份标识) 设置宽度: 34
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(主体身份代码) 设置宽度: 34
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(自然人姓名) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(自然人证件类型) 设置宽度: 14
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(自然人证件号码) 设置宽度: 20
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(认缴出资额_万元_) 设置宽度: 14
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(认缴出资额折万美元_万美元_) 设置宽度: 16
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(认缴出资方式) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(认缴出资比例) 设置宽度: 10
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(认缴出资期限) 设置宽度: 12
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(实缴出资额_万元_) 设置宽度: 11
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(实缴出资额折万美元_万美元_) 设置宽度: 16
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(住址) 设置宽度: 28
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(币种) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(国别_地区_) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(执行合伙事务标志) 设置宽度: 10
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(承担责任方式_责任形式) 设置宽度: 13
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(出资方式_个独_) 设置宽度: 10
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(性别) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(民族) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(出生日期) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(文化程度) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(政治面貌) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(职业状况) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(邮政编码) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(电话) 设置宽度: 13
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(案件编号) 设置宽度: 16
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 自然人出资信息 冻结首行
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 统一社会信用代码 添加筛选，范围: A1:AF11
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 50
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(名称) 设置宽度: 20
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件类型) 设置宽度: 11
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(统一社会信用代码) 设置宽度: 20
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(组织机构代码) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(机构名称) 设置宽度: 20
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(注册号) 设置宽度: 17
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(机构类型) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(法定代表人) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(国家) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(注册资本) 设置宽度: 14
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(注册资本币种) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(实收资本) 设置宽度: 11
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(业务范围类型) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(成立日期) 设置宽度: 12
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(经营范围) 设置宽度: 50
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(经营期限起) 设置宽度: 12
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(经营期限止) 设置宽度: 12
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(经营状态) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(登记机关) 设置宽度: 20
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(颁发日期_变更日期_) 设置宽度: 12
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(机构地址) 设置宽度: 45
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(反馈人) 设置宽度: 10
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(反馈录入时间) 设置宽度: 21
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(备注) 设置宽度: 8
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(法定代表人证件号码) 设置宽度: 11
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(法定代表人电话号码) 设置宽度: 11
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(案件编号) 设置宽度: 16
2025-08-04 22:02:29.000 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 统一社会信用代码 冻结首行
2025-08-04 22:02:29.330 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 工商信息 导出完成: G:/市JW/20250804版本\20250726SYH_工商信息_20250804_220228.xlsx
2025-08-04 22:02:29.346 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 9/14: 信托信息
2025-08-04 22:02:29.346 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_信托信息_20250804_220229.xlsx
2025-08-04 22:02:29.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 信托登记公司_产品信息表 未被用户选择，跳过
2025-08-04 22:02:29.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 信托登记公司_登记信息_合同信息表 未被用户选择，跳过
2025-08-04 22:02:29.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 信托登记公司_登记信息_受益权结构表 未被用户选择，跳过
2025-08-04 22:02:29.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 信托登记公司_委托人或受益人变动信息表 未被用户选择，跳过
2025-08-04 22:02:29.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 信托登记公司_信托产品_登记信息_受益权结构 未被用户选择，跳过
2025-08-04 22:02:29.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 信托登记公司_信托产品_登记信息_合同信息 未被用户选择，跳过
2025-08-04 22:02:29.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 信托登记公司_信托产品_终止登记 未被用户选择，跳过
2025-08-04 22:02:29.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 信托登记公司_终止登记表 未被用户选择，跳过
2025-08-04 22:02:29.346 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 信托登记公司_信托产品_委托人信息 未被用户选择，跳过
2025-08-04 22:02:29.346 - INFO - [Dummy-4:23576] - pivot_export.py:3821 - start_export() - 文件 信托信息 无数据，跳过导出
2025-08-04 22:02:29.362 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 10/14: 保险信息
2025-08-04 22:02:29.362 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_保险信息_20250804_220229.xlsx
2025-08-04 22:02:29.552 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 保险保单信息 合并1个表, 共4648行, 字段数27
2025-08-04 22:02:29.577 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 保险赔案信息 合并1个表, 共1256行, 字段数14
2025-08-04 22:02:29.870 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 保险人员信息 合并1个表, 共7747行, 字段数14
2025-08-04 22:02:29.870 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 银保信_保险产品_航空延误保险表 未被用户选择，跳过
2025-08-04 22:02:29.870 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 银保信_保险产品_家庭财产保险表 未被用户选择，跳过
2025-08-04 22:02:30.376 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 保险保单信息 添加筛选，范围: A1:AA4649
2025-08-04 22:02:30.392 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:30.392 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:30.392 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(自然人对象名称) 设置宽度: 9
2025-08-04 22:02:30.392 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(自然人证件类型) 设置宽度: 9
2025-08-04 22:02:30.403 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(自然人证件号码) 设置宽度: 20
2025-08-04 22:02:30.403 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(机构名称) 设置宽度: 8
2025-08-04 22:02:30.403 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(营业执照号码) 设置宽度: 8
2025-08-04 22:02:30.403 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(组织机构代码) 设置宽度: 8
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(统一社会信用代码) 设置宽度: 10
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(税务登记证号) 设置宽度: 8
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(保险产品名称) 设置宽度: 23
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(保单号) 设置宽度: 24
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(保险公司名称) 设置宽度: 17
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(累计缴纳保费) 设置宽度: 11
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(币种) 设置宽度: 8
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(平台名称) 设置宽度: 8
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(险种名称) 设置宽度: 11
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(保单团个性质) 设置宽度: 8
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(购买日期) 设置宽度: 12
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(保单生效日期) 设置宽度: 12
2025-08-04 22:02:30.408 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(保单终止日期) 设置宽度: 12
2025-08-04 22:02:30.424 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(保险标的名称) 设置宽度: 13
2025-08-04 22:02:30.424 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(保险账户价值) 设置宽度: 8
2025-08-04 22:02:30.424 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(数据提取日期) 设置宽度: 12
2025-08-04 22:02:30.424 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(标的数量) 设置宽度: 8
2025-08-04 22:02:30.424 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(保单序号) 设置宽度: 8
2025-08-04 22:02:30.424 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(案件编号) 设置宽度: 16
2025-08-04 22:02:30.424 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 保险保单信息 冻结首行
2025-08-04 22:02:30.546 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 保险赔案信息 添加筛选，范围: A1:N1257
2025-08-04 22:02:30.546 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:30.546 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 35
2025-08-04 22:02:30.546 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(保单序号) 设置宽度: 8
2025-08-04 22:02:30.546 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(赔案序号) 设置宽度: 8
2025-08-04 22:02:30.546 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(赔案报案人姓名) 设置宽度: 9
2025-08-04 22:02:30.546 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(赔案报案人联系电话) 设置宽度: 15
2025-08-04 22:02:30.552 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(赔案号) 设置宽度: 24
2025-08-04 22:02:30.552 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(出险时间) 设置宽度: 21
2025-08-04 22:02:30.553 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(报案时间) 设置宽度: 21
2025-08-04 22:02:30.553 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(出险原因) 设置宽度: 50
2025-08-04 22:02:30.553 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(赔款支付账号) 设置宽度: 23
2025-08-04 22:02:30.553 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(赔付金额) 设置宽度: 11
2025-08-04 22:02:30.553 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(赔付日期) 设置宽度: 12
2025-08-04 22:02:30.553 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(案件编号) 设置宽度: 16
2025-08-04 22:02:30.553 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 保险赔案信息 冻结首行
2025-08-04 22:02:31.013 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 保险人员信息 添加筛选，范围: A1:N7748
2025-08-04 22:02:31.013 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:31.013 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:31.023 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(保单序号) 设置宽度: 8
2025-08-04 22:02:31.023 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(人员类别) 设置宽度: 8
2025-08-04 22:02:31.023 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(人员序号) 设置宽度: 8
2025-08-04 22:02:31.029 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(人员证件类型) 设置宽度: 8
2025-08-04 22:02:31.029 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(人员证件号码) 设置宽度: 20
2025-08-04 22:02:31.032 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(人员联系电话) 设置宽度: 27
2025-08-04 22:02:31.032 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(人员联系地址) 设置宽度: 29
2025-08-04 22:02:31.032 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(缴费账号) 设置宽度: 32
2025-08-04 22:02:31.032 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(投保人名称) 设置宽度: 8
2025-08-04 22:02:31.042 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(被保险人名称) 设置宽度: 8
2025-08-04 22:02:31.042 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(受益人名称) 设置宽度: 8
2025-08-04 22:02:31.045 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(案件编号) 设置宽度: 16
2025-08-04 22:02:31.045 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 保险人员信息 冻结首行
2025-08-04 22:02:32.223 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 保险信息 导出完成: G:/市JW/20250804版本\20250726SYH_保险信息_20250804_220229.xlsx
2025-08-04 22:02:32.240 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 11/14: 航空信息
2025-08-04 22:02:32.240 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_航空信息_20250804_220232.xlsx
2025-08-04 22:02:32.258 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 航班进出港(未成行) 合并1个表, 共26行, 字段数44
2025-08-04 22:02:32.274 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 航班进出港(已成行) 合并1个表, 共304行, 字段数44
2025-08-04 22:02:32.287 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 同乘三次以上同行人 合并1个表, 共242行, 字段数44
2025-08-04 22:02:32.319 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 同订单同行人(未成行) 合并1个表, 共18行, 字段数44
2025-08-04 22:02:32.336 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 同订单同行人(已成行) 合并1个表, 共205行, 字段数44
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 航班进出港(未成行) 添加筛选，范围: A1:AR27
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 25
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(旅客证件号) 设置宽度: 17
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(手机号) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(航空公司) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(航班号) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(旅客中文姓名) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(旅客英文名字) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(起飞日期) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(起飞时间) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(到达日期) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(到达时间) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(起飞机场三字码) 设置宽度: 9
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(到达机场三字码) 设置宽度: 9
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(起飞机场) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(到达机场) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(值机日期) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(值机时间) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(离港舱位) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(登机牌序号) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(座位行号) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(座位号) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(票号) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(记录编号) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(销售舱位) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(出票日期) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(常客卡所属航空公司) 设置宽度: 11
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(常客卡号) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(行李件数) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(行李重量_kg_) 设置宽度: 10
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(票面总价) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(付款方式) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(票价货币类型) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(出票处office) 设置宽度: 11
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(出票处电话) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(出票处地址) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(订票处office) 设置宽度: 11
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AL(订票处名字) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AM(订票处电话) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AN(订票处地址) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AO(同行人类别) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AP(关联目标人) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AQ(人员类别) 设置宽度: 8
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AR(案件编号) 设置宽度: 16
2025-08-04 22:02:32.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 航班进出港(未成行) 冻结首行
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 航班进出港(已成行) 添加筛选，范围: A1:AR305
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 25
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(旅客证件号) 设置宽度: 20
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(手机号) 设置宽度: 13
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(航空公司) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(航班号) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(旅客中文姓名) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(旅客英文名字) 设置宽度: 14
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(起飞日期) 设置宽度: 10
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(起飞时间) 设置宽度: 10
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(到达日期) 设置宽度: 10
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(到达时间) 设置宽度: 10
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(起飞机场三字码) 设置宽度: 9
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(到达机场三字码) 设置宽度: 9
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(起飞机场) 设置宽度: 10
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(到达机场) 设置宽度: 10
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(值机日期) 设置宽度: 10
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(值机时间) 设置宽度: 10
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(离港舱位) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(登机牌序号) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(座位行号) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(座位号) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(票号) 设置宽度: 15
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(记录编号) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(销售舱位) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(出票日期) 设置宽度: 10
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(常客卡所属航空公司) 设置宽度: 11
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(常客卡号) 设置宽度: 14
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(行李件数) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(行李重量_kg_) 设置宽度: 10
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(票面总价) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(付款方式) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(票价货币类型) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(出票处office) 设置宽度: 11
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(出票处电话) 设置宽度: 18
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(出票处地址) 设置宽度: 37
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(订票处office) 设置宽度: 11
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AL(订票处名字) 设置宽度: 18
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AM(订票处电话) 设置宽度: 18
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AN(订票处地址) 设置宽度: 37
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AO(同行人类别) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AP(关联目标人) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AQ(人员类别) 设置宽度: 8
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AR(案件编号) 设置宽度: 16
2025-08-04 22:02:32.479 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 航班进出港(已成行) 冻结首行
2025-08-04 22:02:32.527 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 同乘三次以上同行人 添加筛选，范围: A1:AR243
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 25
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(旅客证件号) 设置宽度: 20
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(手机号) 设置宽度: 13
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(航空公司) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(航班号) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(旅客中文姓名) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(旅客英文名字) 设置宽度: 16
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(起飞日期) 设置宽度: 10
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(起飞时间) 设置宽度: 10
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(到达日期) 设置宽度: 10
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(到达时间) 设置宽度: 10
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(起飞机场三字码) 设置宽度: 9
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(到达机场三字码) 设置宽度: 9
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(起飞机场) 设置宽度: 10
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(到达机场) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(值机日期) 设置宽度: 10
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(值机时间) 设置宽度: 10
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(离港舱位) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(登机牌序号) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(座位行号) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(座位号) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(票号) 设置宽度: 15
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(记录编号) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(销售舱位) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(出票日期) 设置宽度: 10
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(常客卡所属航空公司) 设置宽度: 11
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(常客卡号) 设置宽度: 14
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(行李件数) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(行李重量_kg_) 设置宽度: 10
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(票面总价) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(付款方式) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(票价货币类型) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(出票处office) 设置宽度: 11
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(出票处电话) 设置宽度: 15
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(出票处地址) 设置宽度: 32
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(订票处office) 设置宽度: 11
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AL(订票处名字) 设置宽度: 18
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AM(订票处电话) 设置宽度: 15
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AN(订票处地址) 设置宽度: 32
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AO(同行人类别) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AP(关联目标人) 设置宽度: 20
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AQ(人员类别) 设置宽度: 8
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AR(案件编号) 设置宽度: 16
2025-08-04 22:02:32.528 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 同乘三次以上同行人 冻结首行
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 同订单同行人(未成行) 添加筛选，范围: A1:AR19
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 25
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(旅客证件号) 设置宽度: 18
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(手机号) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(航空公司) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(航班号) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(旅客中文姓名) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(旅客英文名字) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(起飞日期) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(起飞时间) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(到达日期) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(到达时间) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(起飞机场三字码) 设置宽度: 9
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(到达机场三字码) 设置宽度: 9
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(起飞机场) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(到达机场) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(值机日期) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(值机时间) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(离港舱位) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(登机牌序号) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(座位行号) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(座位号) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(票号) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(记录编号) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(销售舱位) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(出票日期) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(常客卡所属航空公司) 设置宽度: 11
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(常客卡号) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(行李件数) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(行李重量_kg_) 设置宽度: 10
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(票面总价) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(付款方式) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(票价货币类型) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(出票处office) 设置宽度: 11
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(出票处电话) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(出票处地址) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(订票处office) 设置宽度: 11
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AL(订票处名字) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AM(订票处电话) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AN(订票处地址) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AO(同行人类别) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AP(关联目标人) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AQ(人员类别) 设置宽度: 8
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AR(案件编号) 设置宽度: 16
2025-08-04 22:02:32.538 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 同订单同行人(未成行) 冻结首行
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 同订单同行人(已成行) 添加筛选，范围: A1:AR206
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 25
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(旅客证件号) 设置宽度: 20
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(手机号) 设置宽度: 13
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(航空公司) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(航班号) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(旅客中文姓名) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(旅客英文名字) 设置宽度: 16
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(起飞日期) 设置宽度: 10
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(起飞时间) 设置宽度: 10
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(到达日期) 设置宽度: 10
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(到达时间) 设置宽度: 10
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(起飞机场三字码) 设置宽度: 9
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(到达机场三字码) 设置宽度: 9
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(起飞机场) 设置宽度: 10
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(到达机场) 设置宽度: 10
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(值机日期) 设置宽度: 10
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(值机时间) 设置宽度: 10
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(离港舱位) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(登机牌序号) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(座位行号) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(座位号) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(票号) 设置宽度: 15
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(记录编号) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(销售舱位) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(出票日期) 设置宽度: 10
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(常客卡所属航空公司) 设置宽度: 11
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(常客卡号) 设置宽度: 14
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(行李件数) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AD(行李重量_kg_) 设置宽度: 10
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AE(票面总价) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AF(付款方式) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AG(票价货币类型) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AH(出票处office) 设置宽度: 11
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AI(出票处电话) 设置宽度: 15
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AJ(出票处地址) 设置宽度: 37
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AK(订票处office) 设置宽度: 11
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AL(订票处名字) 设置宽度: 18
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AM(订票处电话) 设置宽度: 15
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AN(订票处地址) 设置宽度: 37
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AO(同行人类别) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AP(关联目标人) 设置宽度: 20
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AQ(人员类别) 设置宽度: 8
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AR(案件编号) 设置宽度: 16
2025-08-04 22:02:32.574 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 同订单同行人(已成行) 冻结首行
2025-08-04 22:02:32.763 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 航空信息 导出完成: G:/市JW/20250804版本\20250726SYH_航空信息_20250804_220232.xlsx
2025-08-04 22:02:32.763 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 12/14: 铁路信息
2025-08-04 22:02:32.763 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_铁路信息_20250804_220232.xlsx
2025-08-04 22:02:32.811 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 交易信息 合并1个表, 共1845行, 字段数9
2025-08-04 22:02:32.891 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 票面信息 合并1个表, 共2085行, 字段数29
2025-08-04 22:02:32.955 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 同行人员客票信息 合并1个表, 共3972行, 字段数17
2025-08-04 22:02:32.971 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 同行人员信息 合并1个表, 共474行, 字段数14
2025-08-04 22:02:32.987 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 常用联系人信息 合并1个表, 共84行, 字段数14
2025-08-04 22:02:32.987 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 互联网注册信息 合并1个表, 共13行, 字段数16
2025-08-04 22:02:33.066 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 交易信息 添加筛选，范围: A1:I1846
2025-08-04 22:02:33.067 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:33.068 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:33.068 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(车票序号) 设置宽度: 8
2025-08-04 22:02:33.069 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(列车车次) 设置宽度: 8
2025-08-04 22:02:33.069 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(发车日期) 设置宽度: 12
2025-08-04 22:02:33.070 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(交易订单号) 设置宽度: 30
2025-08-04 22:02:33.070 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(交易时间) 设置宽度: 21
2025-08-04 22:02:33.071 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(收单行) 设置宽度: 8
2025-08-04 22:02:33.071 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(案件编号) 设置宽度: 16
2025-08-04 22:02:33.071 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 交易信息 冻结首行
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 票面信息 添加筛选，范围: A1:AC2086
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(车票序号) 设置宽度: 8
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(发车日期) 设置宽度: 12
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(发车时间) 设置宽度: 8
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(乘车人姓名) 设置宽度: 8
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件类型) 设置宽度: 8
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(证件号码) 设置宽度: 20
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(票号) 设置宽度: 9
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(列车车次) 设置宽度: 8
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(发站) 设置宽度: 8
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(到站) 设置宽度: 8
2025-08-04 22:02:33.336 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(车厢号) 设置宽度: 8
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(席别) 设置宽度: 8
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(席位号) 设置宽度: 8
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(售票车站) 设置宽度: 8
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(售票时间) 设置宽度: 21
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(购票人) 设置宽度: 8
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(购票人证件号码) 设置宽度: 20
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(购票人联系方式) 设置宽度: 13
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(反馈人) 设置宽度: 8
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(反馈时间) 设置宽度: 8
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(退票车站) 设置宽度: 8
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(退票日期) 设置宽度: 12
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(退票时间) 设置宽度: 21
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Z(改签车站) 设置宽度: 8
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AA(改签时间) 设置宽度: 21
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AB(改签新票票号) 设置宽度: 9
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 AC(案件编号) 设置宽度: 16
2025-08-04 22:02:33.352 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 票面信息 冻结首行
2025-08-04 22:02:33.639 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 同行人员客票信息 添加筛选，范围: A1:Q3973
2025-08-04 22:02:33.639 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:33.639 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(同行人序号) 设置宽度: 8
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(同行人姓名) 设置宽度: 8
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(证件号码) 设置宽度: 20
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(同行车次) 设置宽度: 8
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(购票票号) 设置宽度: 8
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(乘车时间) 设置宽度: 21
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(乘车起始站) 设置宽度: 8
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(乘车终止站) 设置宽度: 8
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(车厢号) 设置宽度: 8
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(席别) 设置宽度: 8
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(席位号) 设置宽度: 8
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(购票人) 设置宽度: 8
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(购票人证件号码) 设置宽度: 20
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(购票时间) 设置宽度: 21
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(案件编号) 设置宽度: 16
2025-08-04 22:02:33.655 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 同行人员客票信息 冻结首行
2025-08-04 22:02:33.687 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 同行人员信息 添加筛选，范围: A1:N475
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(同行人序号) 设置宽度: 8
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(同行人姓名) 设置宽度: 8
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(证件类型) 设置宽度: 8
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件号码) 设置宽度: 20
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(手机号码) 设置宽度: 13
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(固定电话) 设置宽度: 8
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(性别) 设置宽度: 8
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(国籍) 设置宽度: 8
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(电子邮箱) 设置宽度: 30
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(旅客类型) 设置宽度: 8
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(生日) 设置宽度: 12
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(案件编号) 设置宽度: 16
2025-08-04 22:02:33.703 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 同行人员信息 冻结首行
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 常用联系人信息 添加筛选，范围: A1:N85
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(注册序号) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(乘客姓名) 设置宽度: 13
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(证件类型) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件号码) 设置宽度: 20
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(性别) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(旅客类型) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(手机号码) 设置宽度: 13
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(固定电话) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(电子邮箱) 设置宽度: 21
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(邮编) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(地址) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(案件编号) 设置宽度: 16
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 常用联系人信息 冻结首行
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 互联网注册信息 添加筛选，范围: A1:P14
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(注册序号) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(注册人姓名) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(证件类型) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件号码) 设置宽度: 20
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(手机号码) 设置宽度: 13
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(旅客类型) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(生日) 设置宽度: 12
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(性别) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(国籍) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(固定电话) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(电子邮箱) 设置宽度: 21
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(反馈人) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(反馈时间) 设置宽度: 8
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(案件编号) 设置宽度: 16
2025-08-04 22:02:33.710 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 互联网注册信息 冻结首行
2025-08-04 22:02:34.467 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 铁路信息 导出完成: G:/市JW/20250804版本\20250726SYH_铁路信息_20250804_220232.xlsx
2025-08-04 22:02:34.482 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 13/14: 证券信息
2025-08-04 22:02:34.482 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_证券信息_20250804_220234.xlsx
2025-08-04 22:02:34.482 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 中国证券登记结算有限公司_证券持有变动_持 未被用户选择，跳过
2025-08-04 22:02:34.498 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 证券账户 合并1个表, 共10行, 字段数13
2025-08-04 22:02:34.498 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 证券登记结算_证券持有变动_证券持有变动 未被用户选择，跳过
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 证券账户 添加筛选，范围: A1:M11
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(持有人名称) 设置宽度: 8
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(证件类型) 设置宽度: 8
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(证件号码) 设置宽度: 20
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(市场类型) 设置宽度: 8
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证券账户) 设置宽度: 12
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(证券账户状态) 设置宽度: 8
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(开户日期) 设置宽度: 10
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(联系地址) 设置宽度: 37
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(联系电话) 设置宽度: 13
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(开户代理机构名称) 设置宽度: 14
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(案件编号) 设置宽度: 16
2025-08-04 22:02:34.511 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 证券账户 冻结首行
2025-08-04 22:02:34.530 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 证券信息 导出完成: G:/市JW/20250804版本\20250726SYH_证券信息_20250804_220234.xlsx
2025-08-04 22:02:34.534 - INFO - [Dummy-4:23576] - pivot_export.py:3724 - start_export() - 📄 开始处理文件 14/14: 不动产
2025-08-04 22:02:34.534 - INFO - [Dummy-4:23576] - pivot_export.py:3732 - start_export() - 📁 导出路径: G:/市JW/20250804版本\20250726SYH_不动产_20250804_220234.xlsx
2025-08-04 22:02:34.534 - DEBUG - [Dummy-4:23576] - pivot_export.py:3745 - start_export() - 表 不动产查询_不动产全国总库_查封登记表 未被用户选择，跳过
2025-08-04 22:02:34.566 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 抵押权 合并1个表, 共252行, 字段数20
2025-08-04 22:02:34.594 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 房地产权 合并1个表, 共226行, 字段数25
2025-08-04 22:02:34.611 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 建设用地、宅基地使用权 合并1个表, 共15行, 字段数24
2025-08-04 22:02:34.611 - INFO - [Dummy-4:23576] - pivot_export.py:3815 - start_export() - 工作表 预告登记 合并1个表, 共19行, 字段数22
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 抵押权 添加筛选，范围: A1:T253
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 33
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(名称) 设置宽度: 14
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件类型) 设置宽度: 10
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(不动产单元号) 设置宽度: 30
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(抵押不动产类型) 设置宽度: 9
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(坐落) 设置宽度: 39
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(抵押人) 设置宽度: 18
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(抵押方式) 设置宽度: 8
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(被担保主债权数额) 设置宽度: 10
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(债务履行起始时间) 设置宽度: 12
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(债务履行结束时间) 设置宽度: 12
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(不动产登记证明号) 设置宽度: 28
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(登记机构) 设置宽度: 15
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(权属状态) 设置宽度: 8
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(登记时间) 设置宽度: 21
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(案件编号) 设置宽度: 16
2025-08-04 22:02:34.642 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 抵押权 冻结首行
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 房地产权 添加筛选，范围: A1:Y227
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 31
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(名称) 设置宽度: 12
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件类型) 设置宽度: 10
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(不动产单元号) 设置宽度: 30
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(房地坐落) 设置宽度: 39
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(建筑面积_平方米_) 设置宽度: 11
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(规划用途) 设置宽度: 8
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(房屋性质) 设置宽度: 8
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(竣工时间) 设置宽度: 21
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(土地使用起始时间) 设置宽度: 12
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(土地使用结束时间) 设置宽度: 12
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(不动产权证号) 设置宽度: 25
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(登记机构) 设置宽度: 15
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(权属状态) 设置宽度: 8
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(登记时间) 设置宽度: 21
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(交易金额_万元_) 设置宽度: 10
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(共有情况) 设置宽度: 8
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(共有人名称) 设置宽度: 23
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(共有人证件类型) 设置宽度: 9
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(共有人证件号码) 设置宽度: 40
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Y(案件编号) 设置宽度: 16
2025-08-04 22:02:34.658 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 房地产权 冻结首行
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 建设用地、宅基地使用权 添加筛选，范围: A1:X16
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 33
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(名称) 设置宽度: 14
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件类型) 设置宽度: 10
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(不动产单元号) 设置宽度: 30
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(坐落) 设置宽度: 13
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(用途) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(使用权面积_平方米_) 设置宽度: 12
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(权利性质) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(使用权起始时间) 设置宽度: 12
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(使用权结束时间) 设置宽度: 12
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(不动产权证号) 设置宽度: 25
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(登记机构) 设置宽度: 12
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(权属状态) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(登记时间) 设置宽度: 21
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(交易金额_万元_) 设置宽度: 10
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(共有情况) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(共有人名称) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(共有人证件类型) 设置宽度: 9
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 W(共有人证件号码) 设置宽度: 9
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 X(案件编号) 设置宽度: 16
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 建设用地、宅基地使用权 冻结首行
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3858 - start_export() - 为工作表 预告登记 添加筛选，范围: A1:V20
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 A(ID) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 B(数据源文件) 设置宽度: 24
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 C(反馈单位) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 D(审批表) 设置宽度: 22
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 E(名称) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 F(证件类型) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 G(证件号码) 设置宽度: 20
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 H(不动产单元号) 设置宽度: 30
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 I(预告登记种类) 设置宽度: 13
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 J(坐落) 设置宽度: 50
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 K(规划用途) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 L(建筑面积_平方米_) 设置宽度: 11
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 M(不动产登记证明号) 设置宽度: 27
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 N(登记机构) 设置宽度: 25
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 O(权属状态) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 P(登记时间) 设置宽度: 21
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 Q(交易金额_万元_) 设置宽度: 10
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 R(共有情况) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 S(共有人名称) 设置宽度: 8
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 T(共有人证件类型) 设置宽度: 9
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 U(共有人证件号码) 设置宽度: 20
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3889 - start_export() - 列 V(案件编号) 设置宽度: 16
2025-08-04 22:02:34.674 - DEBUG - [Dummy-4:23576] - pivot_export.py:3896 - start_export() - 为工作表 预告登记 冻结首行
2025-08-04 22:02:34.769 - INFO - [Dummy-4:23576] - pivot_export.py:3914 - start_export() - ✅ 文件 不动产 导出完成: G:/市JW/20250804版本\20250726SYH_不动产_20250804_220234.xlsx
2025-08-04 22:02:34.785 - INFO - [Dummy-4:23576] - pivot_export.py:2574 - on_export_finished() - ✅ 导出完成，共导出 14 个文件
2025-08-04 22:02:34.785 - INFO - [Dummy-4:23576] - pivot_export.py:3258 - set_finished() - 导出完成，共导出 14 个文件
2025-08-04 22:02:34.832 - INFO - [Dummy-4:23576] - pivot_export.py:3281 - set_finished() - 等待用户点击确认按钮...
2025-08-04 22:02:36.617 - INFO - [MainThread:26096] - pivot_export.py:2683 - export_data_with_partitioning() - ✅ 导入数据界面按分类导出完成
2025-08-04 22:07:22.835 - INFO - [MainThread:26096] - import_data.py:5519 - closeEvent() - 数据导入窗口正在关闭
2025-08-04 22:07:22.836 - INFO - [MainThread:26096] - import_error_handler.py:324 - stop_monitoring() - 进程监控: 停止监控导入过程
2025-08-04 22:07:22.838 - INFO - [MainThread:26096] - import_error_handler.py:495 - cleanup_heartbeat() - 心跳文件已清理
2025-08-04 22:07:22.839 - INFO - [MainThread:26096] - import_data.py:5548 - closeEvent() - 数据导入窗口关闭完成
2025-08-04 22:07:23.583 - CRITICAL - [MainThread:26096] - enhanced_logging_patch.py:322 - monitored_exit() - 🚨 程序即将退出! 退出码: 0
2025-08-04 22:07:23.583 - CRITICAL - [MainThread:26096] - enhanced_logging_patch.py:323 - monitored_exit() - 退出时的堆栈跟踪:
2025-08-04 22:07:23.583 - CRITICAL - [MainThread:26096] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\main.py", line 327, in <module>
    main()
2025-08-04 22:07:23.583 - CRITICAL - [MainThread:26096] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\main.py", line 324, in main
    sys.exit(app.exec())
2025-08-04 22:07:23.583 - CRITICAL - [MainThread:26096] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\enhanced_logging_patch.py", line 326, in monitored_exit
    stack = traceback.format_stack()
2025-08-04 22:07:23.583 - INFO - [MainThread:26096] - enhanced_logging_patch.py:155 - log_memory_usage() - 💾 内存使用 程序退出时: 进程=666.7MB, 系统可用=18173.8MB, 系统使用率=44.1%
2025-08-04 22:07:23.583 - DEBUG - [MainThread:26096] - enhanced_logging_patch.py:210 - log_thread_status() - 🧵 当前线程: MainThread (ID: 26096)
2025-08-04 22:07:23.583 - DEBUG - [MainThread:26096] - enhanced_logging_patch.py:211 - log_thread_status() - 🧵 活跃线程数: 5
2025-08-04 22:07:23.583 - DEBUG - [MainThread:26096] - enhanced_logging_patch.py:215 - log_thread_status() -    - MainThread: 运行中
2025-08-04 22:07:23.583 - DEBUG - [MainThread:26096] - enhanced_logging_patch.py:215 - log_thread_status() -    - Thread-1 (heartbeat_worker): 运行中
2025-08-04 22:07:23.583 - DEBUG - [MainThread:26096] - enhanced_logging_patch.py:215 - log_thread_status() -    - Dummy-2: 运行中
2025-08-04 22:07:23.583 - DEBUG - [MainThread:26096] - enhanced_logging_patch.py:215 - log_thread_status() -    - Thread-3 (heartbeat_worker): 运行中
2025-08-04 22:07:23.583 - DEBUG - [MainThread:26096] - enhanced_logging_patch.py:215 - log_thread_status() -    - Dummy-4: 运行中
