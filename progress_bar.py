#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
谷歌风格进度条组件

本文件实现了谷歌Material Design风格的进度条对话框，
包含平滑动画效果、现代化UI设计和详细状态显示功能。

功能特点：
1. Material Design风格设计
2. 平滑的进度动画效果
3. 支持确定和不确定进度模式
4. 详细的状态信息显示
5. 现代化的视觉效果

实现逻辑：
- GoogleStyleProgressBar: 核心进度条组件，支持动画效果
- GoogleStyleProgressDialog: 进度条对话框，集成状态显示
- 使用QPropertyAnimation实现平滑动画
- 采用蓝色渐变进度条，符合谷歌设计规范
"""

import sys
import time
from PySide6.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                               QLabel, QPushButton, QWidget, QProgressBar)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, Property, Signal
from PySide6.QtGui import QPainter, QColor, QLinearGradient, QPen, QFont, QIcon
import logging

# BUTTON_STYLE 定义 - 保持向后兼容性
BUTTON_STYLE = u"QPushButton {\n" \
               "    background-color: rgb(67, 133, 200);\n" \
               "    border: 2px solid #c3ccdf;\n" \
               "    color: rgb(255, 255, 255);\n" \
               "    border-radius: 15px;\n" \
               "    padding: 10px 20px;\n" \
               "    width: 200px;\n" \
               "}\n" \
               "QPushButton:hover {\n" \
               "    background-color: rgb(85, 170, 255);\n" \
               "}\n" \
               "QPushButton:pressed {\n" \
               "    background-color: rgb(255, 0, 127);\n" \
               "}"

class ProgressBarDialog(QDialog):
    """原始进度条对话框 - 保持向后兼容性"""
    cancel_signal = Signal()  # 用于取消进度的信号

    def __init__(self, title="进度条", min_value=0, max_value=100):
        super().__init__()
        self.setWindowTitle(title)
        self.setGeometry(0, 0, 600, 150)
        try:
            self.setWindowIcon(QIcon('gui/images/icons/icon.ico'))
        except:
            pass  # 忽略图标加载错误
        
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                color: #000000;
                border-radius: 10px;
                font-family: Arial, sans-serif;
            }
            QLabel {
                font-size: 14pt;
                padding: 10px;
            }
            QProgressBar {
                text-align: center;
                color: #000000;
                background-color: #ffffff;
                border-radius: 10px;
                border: 2px solid #555555;
            }
            QProgressBar::chunk {
                background-color: #00cc66;
                border-radius: 10px;
            }
        """)

        self.layout = QVBoxLayout()

        self.label = QLabel("正在进行操作，请稍候...")
        self.label.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.label)

        self.progress_bar = QProgressBar(self)
        self.progress_bar.setMinimum(min_value)
        self.progress_bar.setMaximum(max_value)
        self.progress_bar.setValue(min_value)
        self.layout.addWidget(self.progress_bar)

        button_layout = QHBoxLayout()
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setStyleSheet(BUTTON_STYLE)
        self.cancel_button.clicked.connect(self.cancel)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addStretch()

        self.layout.addLayout(button_layout)

        self.setLayout(self.layout)

        # 定时器确保窗口在显示后移动到屏幕中央
        QTimer.singleShot(0, self.center)

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def cancel(self):
        self.cancel_signal.emit()
        self.reject()

    def center(self):
        frame_geometry = self.frameGeometry()
        screen_center = QApplication.primaryScreen().availableGeometry().center()
        frame_geometry.moveCenter(screen_center)
        self.move(frame_geometry.topLeft())

class GoogleStyleProgressBar(QProgressBar):
    """谷歌风格进度条组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(8)  # 更细的进度条
        self.setTextVisible(False)  # 隐藏默认文本
        self.setStyleSheet(self.get_google_style())
        
        # 动画属性
        self._animated_value = 0
        self.animation = QPropertyAnimation(self, b"animated_value")
        self.animation.setDuration(500)  # 动画持续时间
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        
    def get_google_style(self):
        """获取谷歌风格样式表"""
        return """
        QProgressBar {
            border: none;
            border-radius: 4px;
            background-color: #E3F2FD;
            text-align: center;
        }
        
        QProgressBar::chunk {
            border-radius: 4px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #2196F3, stop:0.5 #1976D2, stop:1 #0D47A1);
        }
        """
    
    @Property(int)
    def animated_value(self):
        return self._animated_value
    
    @animated_value.setter
    def animated_value(self, value):
        self._animated_value = value
        self.setValue(value)
    
    def setValueAnimated(self, value):
        """设置带动画的进度值"""
        if value == self.value():
            return
            
        self.animation.setStartValue(self.value())
        self.animation.setEndValue(value)
        self.animation.start()
    
    def setIndeterminate(self, indeterminate=True):
        """设置不确定进度模式"""
        if indeterminate:
            self.setRange(0, 0)  # 不确定进度
            self.setStyleSheet(self.get_google_style() + """
                QProgressBar::chunk {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #E3F2FD, stop:0.3 #2196F3, stop:0.7 #1976D2, stop:1 #E3F2FD);
                }
            """)
        else:
            self.setRange(0, 100)  # 确定进度
            self.setStyleSheet(self.get_google_style())


class GoogleStyleProgressDialog(QDialog):
    """谷歌风格进度条对话框"""
    
    # 信号定义
    cancelled = Signal()

    def __init__(self, title="处理中...", parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(600, 280)  # 增大尺寸以显示更多信息
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.CustomizeWindowHint)
        
        # 初始化UI
        self.init_ui()
        self.center_on_screen()
        
        # 状态变量
        self._is_cancelled = False
        self._is_finished = False
        
        # 倒计时相关属性
        self._countdown_seconds = 0
        self._countdown_timer = None
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题标签
        self.title_label = QLabel("正在处理文件...")
        self.title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        self.title_label.setStyleSheet("color: #1976D2; margin-bottom: 10px;")
        layout.addWidget(self.title_label)
        
        # 状态标签
        self.status_label = QLabel("准备开始...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #666; font-size: 13px;")
        self.status_label.setWordWrap(True)  # 允许文本换行
        self.status_label.setMinimumHeight(40)  # 设置最小高度
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = GoogleStyleProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # 进度信息标签
        progress_info_layout = QHBoxLayout()
        
        self.progress_text = QLabel("0%")
        self.progress_text.setAlignment(Qt.AlignLeft)
        self.progress_text.setStyleSheet("color: #666; font-size: 11px;")
        progress_info_layout.addWidget(self.progress_text)
        
        progress_info_layout.addStretch()
        
        self.detail_text = QLabel("")
        self.detail_text.setAlignment(Qt.AlignRight)
        self.detail_text.setStyleSheet("color: #666; font-size: 12px;")
        self.detail_text.setWordWrap(True)  # 允许文本换行
        self.detail_text.setMinimumWidth(200)  # 设置最小宽度
        progress_info_layout.addWidget(self.detail_text)
        
        layout.addLayout(progress_info_layout)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setFixedSize(80, 32)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                color: #666;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
                border-color: #ccc;
            }
            QPushButton:pressed {
                background-color: #d5d5d5;
            }
        """)
        self.cancel_button.clicked.connect(self.cancel_operation)
        button_layout.addWidget(self.cancel_button)
        
        # 完成按钮（初始隐藏）
        self.done_button = QPushButton("完成")
        self.done_button.setFixedSize(80, 32)
        self.done_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                border: none;
                border-radius: 4px;
                color: white;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        self.done_button.clicked.connect(self.accept)
        self.done_button.hide()
        button_layout.addWidget(self.done_button)
        
        layout.addLayout(button_layout)
        
    def center_on_screen(self):
        """将对话框居中显示"""
        if self.parent():
            parent_geometry = self.parent().geometry()
            x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2
            self.move(x, y)
        else:
            screen = QApplication.primaryScreen().geometry()
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2
            self.move(x, y)
    
    def update_progress(self, value, status_text="", detail_text=""):
        """更新进度条"""
        if self._is_cancelled or self._is_finished:
            return
            
        # 更新进度条（带动画）
        self.progress_bar.setValueAnimated(value)
        
        # 更新进度文本
        self.progress_text.setText(f"{value}%")
        
        # 更新状态文本
        if status_text:
            self.status_label.setText(status_text)
        
        # 更新详细信息
        if detail_text:
            self.detail_text.setText(detail_text)
        
        # 强制刷新UI
        QApplication.processEvents()
        
    def set_indeterminate(self, indeterminate=True):
        """设置不确定进度模式"""
        self.progress_bar.setIndeterminate(indeterminate)
        if indeterminate:
            self.progress_text.setText("处理中...")

    def set_title(self, title):
        """设置标题"""
        self.title_label.setText(title)
        
    def set_status(self, status):
        """设置状态文本"""
        self.status_label.setText(status)
        
    def cancel_operation(self):
        """取消操作"""
        self._is_cancelled = True
        self.cancelled.emit()
        self.reject()
        
    def is_cancelled(self):
        """检查是否已取消"""
        return self._is_cancelled
        
    def finish_with_success(self, message="操作完成", auto_close_seconds=3):
        """以成功状态完成"""
        self._is_finished = True
        self.progress_bar.setValue(100)
        self.progress_text.setText("100%")
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: #4CAF50; font-size: 12px; font-weight: bold;")
        
        # 隐藏取消按钮，显示完成按钮
        self.cancel_button.hide()
        self.done_button.show()
        
        # 🚀 新增：自动关闭功能
        if auto_close_seconds > 0:
            # 创建倒计时定时器
            self._countdown_seconds = auto_close_seconds
            self._countdown_timer = QTimer()
            self._countdown_timer.timeout.connect(self._update_countdown)
            
            # 更新按钮文本显示倒计时
            self.done_button.setText(f"完成 ({self._countdown_seconds}秒)")
            
            # 启动倒计时
            self._countdown_timer.start(1000)  # 每秒更新一次
        
    def finish_with_error(self, message="操作失败"):
        """以错误状态完成"""
        self._is_finished = True
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: #F44336; font-size: 12px; font-weight: bold;")
        
        # 更改取消按钮为关闭按钮
        self.cancel_button.setText("关闭")
        self.cancel_button.clicked.disconnect()
        self.cancel_button.clicked.connect(self.reject)
    
    def _update_countdown(self):
        """更新倒计时"""
        self._countdown_seconds -= 1
        
        if self._countdown_seconds > 0:
            # 更新按钮文本
            self.done_button.setText(f"完成 ({self._countdown_seconds}秒)")
        else:
            # 倒计时结束，自动关闭
            self._countdown_timer.stop()
            self.done_button.setText("完成")
            print("🚀 进度对话框自动关闭")
            self.accept()  # 自动点击完成按钮


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 创建测试对话框
    dialog = GoogleStyleProgressDialog("测试进度条")
    dialog.show()
    
    # 模拟进度更新
    from PySide6.QtCore import QThread
    
    class TestThread(QThread):
        def run(self):
            for i in range(101):
                time.sleep(0.05)
                dialog.update_progress(i, f"正在处理第 {i} 项...", f"剩余 {100-i} 项")
            
            dialog.finish_with_success("所有文件处理完成！")
    
    test_thread = TestThread()
    test_thread.start()

    sys.exit(app.exec())
