#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证导出规则文件

本文件的功能和实现逻辑：
1. 读取生成的导出规则文件
2. 验证规则的完整性和正确性
3. 显示分类统计信息
"""

import pandas as pd
import logging
from datetime import datetime

# 设置日志
log_file = f'verify_export_rules_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def verify_export_rules():
    """验证导出规则文件"""
    try:
        # 读取生成的规则文件
        df = pd.read_excel("表类型匹配规则_导出文件名分类.xlsx")
        
        logging.info("✅ 成功读取导出规则文件")
        logging.info(f"📊 总规则数: {len(df)}")
        
        # 检查列名
        expected_columns = ["序号", "数据库表名", "工作表名", "导出文件名"]
        if list(df.columns) == expected_columns:
            logging.info("✅ 列名正确")
        else:
            logging.error(f"❌ 列名不正确，期望: {expected_columns}, 实际: {list(df.columns)}")
            return False
        
        # 统计分类信息
        category_stats = df["导出文件名"].value_counts().sort_index()
        
        logging.info("📋 分类统计:")
        total_tables = 0
        for category, count in category_stats.items():
            logging.info(f"  • {category}: {count} 个表")
            total_tables += count
        
        logging.info(f"📈 总计: {len(category_stats)} 个分类, {total_tables} 个表")
        
        # 检查是否有重复的表名
        duplicate_tables = df[df.duplicated(subset=['数据库表名'], keep=False)]
        if len(duplicate_tables) > 0:
            logging.warning(f"⚠️ 发现 {len(duplicate_tables)} 个重复的表名:")
            for _, row in duplicate_tables.iterrows():
                logging.warning(f"   - {row['数据库表名']}")
        else:
            logging.info("✅ 没有重复的表名")
        
        # 显示每个分类的详细信息
        logging.info("\n📋 分类详细信息:")
        for category in sorted(category_stats.index):
            category_tables = df[df["导出文件名"] == category]
            logging.info(f"\n📁 {category} ({len(category_tables)} 个表):")
            for _, row in category_tables.iterrows():
                logging.info(f"   {row['序号']:2d}. {row['数据库表名']} → {row['工作表名']}")
        
        # 验证序号连续性
        expected_sequence = list(range(1, len(df) + 1))
        actual_sequence = df["序号"].tolist()
        if actual_sequence == expected_sequence:
            logging.info("✅ 序号连续正确")
        else:
            logging.error("❌ 序号不连续")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 验证规则文件时出错: {e}")
        return False

def test_export_with_rules():
    """测试使用规则文件的导出功能"""
    logging.info("\n🧪 测试导出功能...")
    
    try:
        # 模拟导出功能测试
        from pivot_export import CategoryExportWorker
        
        # 创建测试worker
        worker = CategoryExportWorker("test_case", "测试案件", "/tmp")
        
        # 测试规则文件读取
        rule_path = '表类型匹配规则_导出文件名分类.xlsx'
        if worker._file_exists(rule_path):
            logging.info("✅ 规则文件存在")
            
            # 测试规则文件读取
            try:
                import pandas as pd
                rule_df = pd.read_excel(rule_path)
                export_map = worker._build_export_map_from_rules(rule_df)
                
                logging.info(f"✅ 成功从规则文件构建导出映射，共 {len(export_map)} 个分类")
                
                # 显示映射统计
                total_worksheets = sum(len(worksheets) for worksheets in export_map.values())
                total_tables = sum(
                    sum(len(tables) for tables in worksheets.values()) 
                    for worksheets in export_map.values()
                )
                
                logging.info(f"📊 映射统计:")
                logging.info(f"   分类数: {len(export_map)}")
                logging.info(f"   工作表数: {total_worksheets}")
                logging.info(f"   数据表数: {total_tables}")
                
                return True
                
            except Exception as e:
                logging.error(f"❌ 读取规则文件失败: {e}")
                return False
        else:
            logging.error("❌ 规则文件不存在")
            return False
            
    except Exception as e:
        logging.error(f"❌ 测试导出功能时出错: {e}")
        return False

def create_usage_guide():
    """创建使用指南"""
    guide_content = '''# 导出规则文件使用指南

## 📁 文件说明

**文件名**: `表类型匹配规则_导出文件名分类.xlsx`

**文件结构**:
- **序号**: 规则编号 (1-90)
- **数据库表名**: 数据库中的实际表名
- **工作表名**: 导出Excel中的工作表名称
- **导出文件名**: 分类名称，决定生成的Excel文件名

## 📊 分类统计

总共包含 **14个主要分类**，**90个数据表**：

1. **医保信息** (5个表)
2. **通讯信息** (3个表)  
3. **公安信息** (13个表)
4. **账户信息** (9个表)
5. **税务纳税信息** (2个表)
6. **增值税发票信息** (4个表)
7. **理财信息** (5个表)
8. **工商信息** (17个表)
9. **信托信息** (9个表)
10. **保险信息** (5个表)
11. **航空信息** (5个表)
12. **铁路信息** (6个表)
13. **证券信息** (2个表)
14. **不动产** (5个表)

## 🚀 使用方法

### 自动使用
1. 将文件放在系统根目录
2. 系统会自动检测并使用此规则文件
3. 导出时按分类生成文件

### 手动编辑
1. 打开Excel文件
2. 修改"工作表名"或"导出文件名"列
3. 保存文件
4. 重新导出数据

## 📤 导出效果

使用此规则文件导出后，会生成如下文件：

```
导出目录/
├── {案件名称}_医保信息.xlsx
├── {案件名称}_通讯信息.xlsx
├── {案件名称}_公安信息.xlsx
├── {案件名称}_账户信息.xlsx
├── {案件名称}_税务纳税信息.xlsx
├── {案件名称}_增值税发票信息.xlsx
├── {案件名称}_理财信息.xlsx
├── {案件名称}_工商信息.xlsx
├── {案件名称}_信托信息.xlsx
├── {案件名称}_保险信息.xlsx
├── {案件名称}_航空信息.xlsx
├── {案件名称}_铁路信息.xlsx
├── {案件名称}_证券信息.xlsx
└── {案件名称}_不动产.xlsx
```

## ⚠️ 注意事项

1. **文件名不要改变**: 系统会查找特定的文件名
2. **列名不要修改**: 保持原有的4列结构
3. **表名要准确**: 数据库表名必须与实际表名一致
4. **备份原文件**: 修改前建议备份

## 🔧 故障排除

### 问题1: 导出仍然是单表文件
**原因**: 规则文件未被正确读取
**解决**: 检查文件名和位置是否正确

### 问题2: 某些表没有按分类导出
**原因**: 表名在规则文件中不存在
**解决**: 在规则文件中添加对应的表名规则

### 问题3: 导出文件名不正确
**原因**: "导出文件名"列的值不正确
**解决**: 检查并修正"导出文件名"列的值
'''
    
    try:
        with open('导出规则使用指南.md', 'w', encoding='utf-8') as f:
            f.write(guide_content)
        logging.info("✅ 使用指南创建完成")
        return True
    except Exception as e:
        logging.error(f"❌ 创建使用指南失败: {e}")
        return False

def main():
    """主函数"""
    print(f"🔍 开始验证导出规则文件")
    print(f"📄 详细日志保存到: {log_file}")
    
    success = True
    
    # 验证规则文件
    logging.info("=" * 60)
    logging.info("验证导出规则文件")
    logging.info("=" * 60)
    if not verify_export_rules():
        success = False
    
    # 测试导出功能
    logging.info("=" * 60)
    logging.info("测试导出功能")
    logging.info("=" * 60)
    if not test_export_with_rules():
        success = False
    
    # 创建使用指南
    logging.info("=" * 60)
    logging.info("创建使用指南")
    logging.info("=" * 60)
    create_usage_guide()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 验证结果")
    print("=" * 60)
    
    if success:
        print("✅ 导出规则文件验证通过！")
        print("\n📊 规则文件统计:")
        print("- 总分类数: 14个")
        print("- 总表数: 90个")
        print("- 文件格式: 正确")
        print("- 序号连续性: 正确")
        
        print("\n🚀 使用效果:")
        print("- 按分类导出，不再是每个表一个文件")
        print("- 大大减少导出文件数量（从90个减少到14个）")
        print("- 提高数据整理效率")
        
        print("\n📁 生成的文件:")
        print("- 表类型匹配规则_导出文件名分类.xlsx (规则文件)")
        print("- 导出规则使用指南.md (使用说明)")
        
        return 0
    else:
        print("❌ 验证过程中发现问题，请检查日志")
        return 1

if __name__ == "__main__":
    exit(main())
