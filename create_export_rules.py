import pandas as pd

# 创建导出规则数据
rules_data = [
    # 医保信息
    ["医保_参保信息", "参保信息", "医保信息"],
    ["医保_普通门诊", "普通门诊", "医保信息"],
    ["医保_药店购药", "药店购药", "医保信息"],
    ["医保_药店购药明细", "药店购药明细", "医保信息"],
    ["医保_住院结算数据", "住院结算数据", "医保信息"],
    
    # 通讯信息
    ["电话_登记信息_运营商登记信息表", "运营商登记信息", "通讯信息"],
    ["电话_话单信息_运营商话单信息表", "运营商话单信息", "通讯信息"],
    ["虚拟运营商_登记信息_虚拟运营商登记信息表", "虚拟运营商登记信息", "通讯信息"],
    
    # 公安信息
    ["公安部_出国_境_证件_出入境证件信息", "出入境证件信息", "公安信息"],
    ["公安部_出入境记录_出入境记录信息表", "出入境记录信息", "公安信息"],
    ["公安部_户籍人口_基本人员信息表", "基本人员信息", "公安信息"],
    ["公安部_机动车_机动车信息", "机动车信息", "公安信息"],
    ["公安部_驾驶证_驾驶证信息表", "驾驶证信息", "公安信息"],
    ["公安部_交通违法_机动车违章信息表", "机动车违章信息", "公安信息"],
    ["公安部_旅馆住宿_旅馆住宿人员信息表", "旅馆住宿人员信息", "公安信息"],
    ["公安部_同车违章_同车违章表", "同车违章", "公安信息"],
    ["公安部_同户人_同户人表", "同户人", "公安信息"],
    ["公安部_同住址_同住址表", "同住址", "公安信息"],
    ["公安部_在逃撤销_在逃人员撤销信息", "在逃人员撤销信息", "公安信息"],
    ["公安部_在逃人员_在逃人员登记信息", "在逃人员登记信息", "公安信息"],
    ["公安部_在逃同案撤销人员_在逃同案撤销人员", "在逃同案撤销人员", "公安信息"],
    
    # 账户信息
    ["账户信息_关联子账户信息表本地", "关联子账户信息", "账户信息"],
    ["开户信息表", "账号信息", "账户信息"],
    ["本地银行_客户信息本地表", "账户信息（本地）", "账户信息"],
    ["账户信息（本地）_优先权信息表", "优先权信息", "账户信息"],
    ["账户信息_共有权优先权信息表", "共有权、优先权信息", "账户信息"],
    ["账户信息_关联子账户信息表", "关联子账户信息", "账户信息"],
    ["账户信息_客户基本信息表", "客户基本信息", "账户信息"],
    ["账户信息_强制措施信息表", "强制措施信息", "账户信息"],
    ["开户信息表_基本", "账号基本信息", "账户信息"],  # 修复重复表名
    
    # 税务纳税信息
    ["国家税务总局_纳税人登记信息_登记信息表", "登记信息", "税务纳税信息"],
    ["国家税务总局_纳税信息_税务缴纳信息表", "税务缴纳信息", "税务纳税信息"],
    
    # 增值税发票信息
    ["税务_增值税发票_普票货物或应税劳务服务名", "普票货物或应税劳务、服务名称", "增值税发票信息"],
    ["税务_增值税发票_增值税普通发票表", "增值税普通发票", "增值税发票信息"],
    ["税务_增值税发票_增值税专用发票表", "增值税专用发票", "增值税发票信息"],
    ["税务_增值税发票_专票货物或应税劳务名称表", "专票货物或应税劳务名称", "增值税发票信息"],
    
    # 理财信息
    ["金融理财_金融理财信息表", "金融理财信息", "理财信息"],
    ["金融理财_金融理财账户信息表", "金融理财账户信息", "理财信息"],
    ["理财登记中心_理财产品_持有信息表", "持有信息", "理财信息"],
    ["理财登记中心_理财产品_理财产品信息表", "理财产品信息", "理财信息"],
    ["理财登记中心_理财产品_投资行业信息表", "投资行业信息", "理财信息"],
    
    # 工商信息
    ["市监_企业登记_企业公示_变更备案信息表", "变更备案信息", "工商信息"],
    ["市监_企业登记_企业公示_财务负责人信息表", "财务负责人信息", "工商信息"],
    ["市监_企业登记_企业公示_吊销信息表", "吊销信息", "工商信息"],
    ["市监_企业登记_企业公示_非自然人出资信息表", "非自然人出资信息", "工商信息"],
    ["市监_企业登记_企业公示_分支机构备案信息表", "分支机构备案信息", "工商信息"],
    ["市监_企业登记_企业公示_联络员信息表", "联络员信息", "工商信息"],
    ["市监_企业登记_企业公示_内资补充信息表", "内资补充信息", "工商信息"],
    ["市监_企业登记_企业公示_农专补充信息表", "农专补充信息", "工商信息"],
    ["市监_企业登记_企业公示_许可信息表", "企业公示_许可信息", "工商信息"],
    ["市监_企业登记_企业基本信息表", "企业基本信息", "工商信息"],
    ["市监_企业登记_企业公示_清算成员信息表", "清算成员信息", "工商信息"],
    ["市监_企业登记_企业公示_清算基本信息表", "清算基本信息", "工商信息"],
    ["市监_企业登记_企业公示_外资补充信息表", "外资补充信息", "工商信息"],
    ["市监_企业登记_企业公示_主要人员表", "主要人员", "工商信息"],
    ["市监_企业登记_企业公示_注销信息表", "注销信息", "工商信息"],
    ["市监_企业登记_企业公示_自然人出资信息表", "自然人出资信息", "工商信息"],
    ["市监_统一社会信用代码_统一社会信用代码表", "统一社会信用代码", "工商信息"],
    
    # 信托信息
    ["信托登记公司_产品信息表", "产品信息", "信托信息"],
    ["信托登记公司_登记信息_合同信息表", "合同信息", "信托信息"],
    ["信托登记公司_登记信息_受益权结构表", "受益权结构", "信托信息"],
    ["信托登记公司_委托人或受益人变动信息表", "委托人或受益人变动信息", "信托信息"],
    ["信托登记公司_信托产品_登记信息_受益权结构", "登记信息_受益权结构", "信托信息"],
    ["信托登记公司_信托产品_登记信息_合同信息", "登记信息_合同信息", "信托信息"],
    ["信托登记公司_信托产品_终止登记", "终止登记", "信托信息"],
    ["信托登记公司_信托产品_委托人信息", "委托人信息", "信托信息"],
    ["信托登记公司_终止登记表", "终止登记", "信托信息"],
    
    # 保险信息
    ["银保信_保险产品_保险保单信息表", "保险保单信息", "保险信息"],
    ["银保信_保险产品_保险赔案信息表", "保险赔案信息", "保险信息"],
    ["银保信_保险产品_保险人员信息表", "保险人员信息", "保险信息"],
    ["银保信_保险产品_航空延误保险表", "航空延误保险", "保险信息"],
    ["银保信_保险产品_家庭财产保险表", "家庭财产保险", "保险信息"],
    
    # 航空信息
    ["中国航空_航班进出港_航班进出港未成行表", "航班进出港(未成行)", "航空信息"],
    ["中国航空_航班进出港_航班进出港已成行表", "航班进出港(已成行)", "航空信息"],
    ["中国航空_航班同行人信息_同乘三次以上同行人", "同乘三次以上同行人", "航空信息"],
    ["中国航空_航班同行人信息_同订单同行人未成行", "同订单同行人(未成行)", "航空信息"],
    ["中国航空_航班同行人信息_同订单同行人已成行", "同订单同行人(已成行)", "航空信息"],
    
    # 铁路信息
    ["中国铁路总公司_铁路客票_交易信息表", "交易信息", "铁路信息"],
    ["中国铁路总公司_铁路客票_票面信息表", "票面信息", "铁路信息"],
    ["中国铁路总公司_同订单同行人_同行人员客票", "同行人员客票信息", "铁路信息"],
    ["中国铁路总公司_同订单同行人_同行人员信息表", "同行人员信息", "铁路信息"],
    ["中国铁路总公司_用户注册_常用联系人信息表", "常用联系人信息", "铁路信息"],
    ["中国铁路总公司_用户注册_互联网注册信息表", "互联网注册信息", "铁路信息"],
    
    # 证券信息
    ["中国证券登记结算有限公司_证券持有变动_持", "持有信息", "证券信息"],
    ["中国证券登记结算有限公司_证券账户_证券账户", "证券账户", "证券信息"],
    
    # 不动产
    ["不动产查询_不动产全国总库_查封登记表", "查封登记", "不动产"],
    ["不动产查询_不动产全国总库_抵押权表", "抵押权", "不动产"],
    ["不动产查询_不动产全国总库_房地产权表", "房地产权", "不动产"],
    ["不动产查询_不动产全国总库_建设用地宅基地", "建设用地、宅基地使用权", "不动产"],
    ["不动产查询_不动产全国总库_预告登记表", "预告登记", "不动产"]
]

# 创建DataFrame
df = pd.DataFrame(rules_data, columns=["数据库表名", "工作表名", "导出文件名"])

# 添加序号列
df.insert(0, "序号", range(1, len(df) + 1))

# 保存为Excel文件
df.to_excel("表类型匹配规则_导出文件名分类.xlsx", index=False)

print("✅ 成功创建导出规则文件：表类型匹配规则_导出文件名分类.xlsx")
print(f"📊 包含 {len(df)} 条规则")
print("📋 导出文件分类：")
for category in df["导出文件名"].unique():
    count = len(df[df["导出文件名"] == category])
    print(f"  • {category}: {count} 个表") 