#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据迁移启动脚本 - 避开导入错误
"""

import os
import sys
import time
import logging

# 🔧 修复：使用统一的日志配置
from logger_config import setup_script_logger
logger = setup_script_logger('run_migration')

# 设置日志
s - %(levelname)s - %(message)s',
    # 🔧 修复：使用统一的日志配置
)

# 修复database_setup.py的导入问题
def fix_database_setup():
    try:
        with open('database_setup.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换有问题的函数定义
        error_func_start = 'def migrate_from_sqlite():'
        if error_func_start in content:
            fixed_func = '''def migrate_from_sqlite():
    """从SQLite迁移数据到PostgreSQL"""
    import sqlite3
    import pandas as pd
    
    if not OLD_SQLITE_PATH.exists():
        print("未找到SQLite数据库文件，跳过迁移步骤。")
        return'''
            
            # 找到函数定义开始位置
            start_pos = content.find(error_func_start)
            if start_pos >= 0:
                # 找到函数体结束位置(下一个def开始前)
                next_def_pos = content.find('def ', start_pos + len(error_func_start))
                if next_def_pos > 0:
                    # 替换整个函数
                    content = content[:start_pos] + fixed_func + content[next_def_pos:]
                    
                    # 写回文件
                    with open('database_setup_fixed.py', 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print("已创建修复版本的database_setup_fixed.py文件")
                    
                    # 替换原始文件
                    os.rename('database_setup.py', 'database_setup.py.bak')
                    os.rename('database_setup_fixed.py', 'database_setup.py')
                    print("已备份原始文件并应用修复")
                    return True
    except Exception as e:
        print(f"修复文件时出错: {e}")
    
    return False

# 执行迁移
def run_migration():
    print("开始SQLite到PostgreSQL的多线程数据迁移")
    start_time = time.time()
    
    try:
        # 先尝试修复database_setup.py
        fix_database_setup()
        
        # 导入migrate_data模块的函数
        from migrate_data import migrate_sqlite_to_postgres, list_tables, view_table_data
        
        # 执行迁移
        success = migrate_sqlite_to_postgres()
        
        if success:
            elapsed_time = time.time() - start_time
            print(f"迁移成功完成! 耗时 {elapsed_time:.2f} 秒")
            
            print("数据库中的表:")
            tables = list_tables()
            for table in tables:
                print(f"- {table}")
            
            table_name = input("\n请输入要查看的表名: ")
            limit = int(input("要显示多少行数据: "))
            
            data = view_table_data(table_name, limit)
            print(f"\n{table_name} 的前 {limit} 行数据:")
            print(data)
        else:
            print("迁移失败!")
            sys.exit(1)
    
    except Exception as e:
        print(f"迁移过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_migration() 