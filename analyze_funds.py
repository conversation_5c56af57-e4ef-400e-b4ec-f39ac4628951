import sys
from PySide6.QtCore import Qt, QModelIndex, QAbstractTableModel, QEvent, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QIcon, QColor, QBrush
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QPushButton, QLabel, QTableView, \
    QHBoxLayout, QWidget, QToolBar, QLineEdit, QMessageBox, QTabWidget, QProgressDialog, QProgressBar
import psycopg2
from psycopg2 import extras
import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill
import threading
import time
import datetime
# 导入数据库连接函数
from database_setup import get_db_connection

BUTTON_STYLE = u"QPushButton {\n" \
               "    background-color: rgb(67, 133, 200);\n" \
               "    border: 2px solid #c3ccdf;\n" \
               "    color: rgb(255, 255, 255);\n" \
               "    border-radius: 10px;\n" \
               "    padding: 5px 10px;\n" \
               "    width: 150px;\n" \
               "}\n" \
               "QPushButton:hover {\n" \
               "    background-color: rgb(85, 170, 255);\n" \
               "}\n" \
               "QPushButton:pressed {\n" \
               "    background-color: rgb(255, 0, 127);\n" \
               "}"

# 进度条样式
PROGRESS_STYLE = """
QProgressBar {
    border: 2px solid #c3ccdf;
    border-radius: 5px;
    text-align: center;
    background-color: #f0f0f0;
    color: black;
    font-weight: bold;
}

QProgressBar::chunk {
    background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, 
                      stop:0 #4385c8, stop:0.5 #5aa0ff, stop:1 #4385c8);
    border-radius: 5px;
}
"""

# 自定义事件类型
SEARCH_COMPLETED_EVENT = QEvent.Type(QEvent.registerEventType())
SEARCH_ERROR_EVENT = QEvent.Type(QEvent.registerEventType())

class SearchCompletedEvent(QEvent):
    def __init__(self, tabs_to_add):
        super().__init__(SEARCH_COMPLETED_EVENT)
        self.tabs_to_add = tabs_to_add


class SearchErrorEvent(QEvent):
    def __init__(self, error_message):
        super().__init__(SEARCH_ERROR_EVENT)
        self.error_message = error_message


class AnimatedProgressDialog(QWidget):
    """自定义美化的进度对话框"""
    def __init__(self, parent=None):
        super().__init__(parent, Qt.Dialog | Qt.FramelessWindowHint)
        self.setWindowModality(Qt.WindowModal)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 设置阴影效果
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 220);
                border-radius: 10px;
                border: 1px solid #c3ccdf;
            }
        """)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        self.title_label = QLabel("正在搜索")
        self.title_label.setStyleSheet("font-size: 16px; font-weight: bold; border: none;")
        self.title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.title_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setFixedHeight(10)
        self.progress_bar.setStyleSheet(PROGRESS_STYLE)
        layout.addWidget(self.progress_bar)
        
        # 状态文本
        self.status_label = QLabel("正在搜索数据...")
        self.status_label.setStyleSheet("font-size: 12px; color: #666666; border: none;")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 设置固定大小
        self.setFixedSize(300, 120)
        
        # 创建进度条动画
        self.animation = QPropertyAnimation(self.progress_bar, b"value")
        self.animation.setDuration(1000)  # 1秒
        self.animation.setStartValue(0)
        self.animation.setEndValue(100)
        self.animation.setEasingCurve(QEasingCurve.InOutQuad)
        
        # 设置循环动画
        self.is_animating = False
        self.current_progress = 0
        
        # 居中显示
        if parent:
            self.center_on_parent()
    
    def center_on_parent(self):
        """居中显示在父窗口上"""
        if self.parentWidget():
            parent_geo = self.parentWidget().geometry()
            self.move(
                parent_geo.x() + (parent_geo.width() - self.width()) // 2,
                parent_geo.y() + (parent_geo.height() - self.height()) // 2
            )
    
    def start_animation(self):
        """开始进度条动画"""
        self.is_animating = True
        self.update_animation()
    
    def update_animation(self):
        """更新进度条动画"""
        if not self.is_animating:
            return
            
        # 更新进度值
        self.current_progress = (self.current_progress + 5) % 100
        self.progress_bar.setValue(self.current_progress)
        
        # 更新状态文本
        dots = "." * ((int(time.time() * 2) % 3) + 1)
        self.status_label.setText(f"正在搜索数据{dots}")
        
        # 100毫秒后再次更新
        QTimer.singleShot(100, self.update_animation)
    
    def stop_animation(self):
        """停止动画"""
        self.is_animating = False


class AnalyzeFundsWindow(QMainWindow):
    def __init__(self, case_id, case_name):
        super().__init__()
        self.case_id = case_id
        self.case_name = case_name
        self.setWindowTitle(f"资金分析 - {case_name} ({case_id})")
        self.setWindowIcon(QIcon('gui/images/icons/icon.ico'))
        self.setStyleSheet("background-color: white; color: black;")
        self.showMaximized()
        self.current_page = 1
        self.page_size = 1000  # 每页显示1000条记录
        self.total_records = 0  # 当前表的总记录数
        self.total_pages = 1  # 总页数
        self.search_results = {}  # 按表名存储搜索结果
        self.current_tab_index = 0  # 当前选中的标签页索引
        self.search_keywords = []  # 存储搜索关键词列表，用于高亮显示
        self.progress_dialog = None  # 进度对话框
        self.search_thread = None  # 搜索线程
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout()
        
        # 创建搜索区域
        search_layout = QHBoxLayout()
        search_label = QLabel("请输入关键词:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键词搜索所有案件数据（多个关键词请用空格分隔）...")
        self.search_input.returnPressed.connect(self.on_search_clicked)
        
        search_button = QPushButton("搜索")
        search_button.setStyleSheet(BUTTON_STYLE)
        search_button.clicked.connect(self.on_search_clicked)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input, 1)  # 设置搜索框占据更多空间
        search_layout.addWidget(search_button)
        
        main_layout.addLayout(search_layout)
        
        # 状态信息
        self.status_label = QLabel("请输入关键词搜索当前案件相关数据")
        main_layout.addWidget(self.status_label)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        main_layout.addWidget(self.tab_widget)

        # 分页控件
        pagination_layout = QHBoxLayout()
        self.prev_button = QPushButton("上一页")
        self.prev_button.setStyleSheet(BUTTON_STYLE)
        self.prev_button.clicked.connect(self.prev_page)
        self.prev_button.setEnabled(False)

        self.next_button = QPushButton("下一页")
        self.next_button.setStyleSheet(BUTTON_STYLE)
        self.next_button.clicked.connect(self.next_page)
        self.next_button.setEnabled(False)

        self.page_info_label = QLabel("第 1 页，共 1 页")
        self.page_info_label.setAlignment(Qt.AlignCenter)

        self.export_button = QPushButton("导出Excel")
        self.export_button.setStyleSheet(BUTTON_STYLE)
        self.export_button.clicked.connect(self.export_data)
        self.export_button.setEnabled(False)

        pagination_layout.addWidget(self.prev_button)
        pagination_layout.addWidget(self.page_info_label)
        pagination_layout.addWidget(self.next_button)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.export_button)

        main_layout.addLayout(pagination_layout)

        container = QWidget()
        container.setLayout(main_layout)
        self.setCentralWidget(container)

    def on_tab_changed(self, index):
        """标签页切换事件"""
        self.current_tab_index = index
        self.current_page = 1  # 切换标签页时重置到第一页
        self.display_current_page()

    def on_search_clicked(self):
        search_text = self.search_input.text().strip()
        if not search_text:
            QMessageBox.warning(self, "警告", "请输入搜索关键词")
            return

        keywords = search_text.split()
        self.search_keywords = keywords

        # 显示进度对话框
        self.progress_dialog = AnimatedProgressDialog(self)
        self.progress_dialog.start_animation()
        self.progress_dialog.show()

        # 启动搜索线程
        self.search_thread = threading.Thread(target=self.search_data, args=(keywords,))
        self.search_thread.daemon = True
        self.search_thread.start()

    def search_data(self, keywords):
        """在后台线程中搜索数据 - 优化PostgreSQL查询性能"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=extras.DictCursor)

            # 构建优化的搜索查询
            search_conditions = []
            search_params = []
            
            for keyword in keywords:
                keyword_param = f'%{keyword}%'
                search_params.extend([keyword_param] * 6)  # 每个关键词用于6个字段
                
            # 定义要搜索的表和字段 - 优化查询条件
            table_searches = {
                "账户交易明细表": {
                    "fields": ['"交易户名"', '"对手户名"', '"摘要说明"', '"交易账卡号"', '"对手账号"', '"备注"'],
                    "display_name": "账户交易明细"
                },
                "开户信息表": {
                    "fields": ['"账户开户名称"', '"开户人证件号码"', '"交易卡号"', '"交易账号"', '"联系电话"', '"通信地址"'],
                    "display_name": "开户信息"
                },
                "财付通交易明细表": {
                    "fields": ['"用户侧账号名称"', '"对手侧账户名称"', '"用户银行卡号"', '"对手方银行卡号"', '"备注1"', '"备注2"'],
                    "display_name": "财付通交易明细"
                },
                "增值税发票表": {
                    "fields": ['"销方纳税人名称"', '"购方纳税人名称"', '"货物劳务名称"', '"发票号码"', '"销方纳税人识别号"', '"购方纳税人识别号"'],
                    "display_name": "增值税发票"
                }
            }

            results = {}
            
            for table_name, config in table_searches.items():
                try:
                    # 构建优化的WHERE子句 - 使用ILIKE进行不区分大小写搜索
                    where_conditions = []
                    params_for_table = []
                    
                    for keyword in keywords:
                        keyword_conditions = []
                        for field in config["fields"]:
                            keyword_conditions.append(f"{field} ILIKE %s")
                            params_for_table.append(f'%{keyword}%')
                        where_conditions.append(f"({' OR '.join(keyword_conditions)})")
                    
                    # 使用AND连接多个关键词，使用OR连接同一关键词的不同字段
                    final_where = ' AND '.join(where_conditions)
                    
                    # 优化的查询语句 - 添加LIMIT提高性能
                    query = f'''
                        SELECT * FROM "{table_name}" 
                        WHERE "案件编号" = %s AND ({final_where})
                        ORDER BY "id" DESC 
                        LIMIT 10000
                    '''
                    
                    params = [self.case_id] + params_for_table
                    cursor.execute(query, params)
                    rows = cursor.fetchall()

                    if rows:
                        # 转换为DataFrame进行进一步处理
                        df = pd.DataFrame([dict(row) for row in rows])
                        results[config["display_name"]] = df
                        print(f"在{table_name}中找到 {len(df)} 条记录")

                except Exception as e:
                    print(f"搜索表 {table_name} 时出错: {e}")
                    continue

            conn.close()

            # 发送搜索完成事件
            event = SearchCompletedEvent(results)
            QApplication.postEvent(self, event)

        except Exception as e:
            print(f"搜索过程中发生错误: {e}")
            error_event = SearchErrorEvent(str(e))
            QApplication.postEvent(self, error_event)

    def event(self, event):
        """处理自定义事件"""
        if event.type() == SEARCH_COMPLETED_EVENT:
            self.progress_dialog.stop_animation()
            self.progress_dialog.close()
            
            if event.tabs_to_add:
                self.update_tabs_with_results(event.tabs_to_add)
                total_results = sum(len(df) for df in event.tabs_to_add.values())
                self.status_label.setText(f"搜索完成，共找到 {total_results} 条相关记录")
            else:
                self.status_label.setText("未找到相关记录")
                QMessageBox.information(self, "搜索结果", "未找到包含指定关键词的记录")
            return True
            
        elif event.type() == SEARCH_ERROR_EVENT:
            self.progress_dialog.stop_animation()
            self.progress_dialog.close()
            self.status_label.setText("搜索时发生错误")
            QMessageBox.critical(self, "搜索错误", f"搜索过程中发生错误: {event.error_message}")
            return True
            
        return super().event(event)

    def update_tabs_with_results(self, tabs_to_add):
        """更新标签页显示搜索结果"""
        # 清空现有标签页
        self.tab_widget.clear()
        self.search_results = tabs_to_add
        
        for table_name, df in tabs_to_add.items():
            # 为每个有结果的表创建标签页
            table_view = QTableView()
            
            # 创建数据模型
            model = DataFrameModel(df, self.search_keywords)
            table_view.setModel(model)
            
            # 设置表格属性
            table_view.setAlternatingRowColors(True)
            table_view.setSelectionBehavior(QTableView.SelectRows)
            table_view.setSortingEnabled(True)
            
            # 自动调整列宽
            table_view.resizeColumnsToContents()
            
            # 添加标签页
            tab_index = self.tab_widget.addTab(table_view, f"{table_name} ({len(df)})")
        
        # 启用导出按钮
        self.export_button.setEnabled(True)
        
        # 更新分页信息
        self.current_page = 1
        self.display_current_page()

    def display_current_page(self):
        """显示当前页的数据 - 优化分页性能"""
        if not self.search_results or self.current_tab_index >= self.tab_widget.count():
            return
        
        # 获取当前标签页对应的表名
        current_tab_text = self.tab_widget.tabText(self.current_tab_index)
        table_name = current_tab_text.split(' (')[0]  # 移除记录数部分
        
        if table_name not in self.search_results:
            return
            
        df = self.search_results[table_name]
        self.total_records = len(df)
        self.total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)
        
        # 计算当前页的数据范围
        start_idx = (self.current_page - 1) * self.page_size
        end_idx = min(start_idx + self.page_size, self.total_records)
        
        # 获取当前页数据
        if start_idx < self.total_records:
            page_df = df.iloc[start_idx:end_idx]
        else:
            page_df = pd.DataFrame()  # 空DataFrame
        
        # 更新表格模型
        current_table_view = self.tab_widget.currentWidget()
        if isinstance(current_table_view, QTableView):
            model = DataFrameModel(page_df, self.search_keywords)
            current_table_view.setModel(model)
            current_table_view.resizeColumnsToContents()
        
        # 更新分页控件状态
        self.prev_button.setEnabled(self.current_page > 1)
        self.next_button.setEnabled(self.current_page < self.total_pages)
        
        # 更新页面信息
        if self.total_records > 0:
            self.page_info_label.setText(f"第 {self.current_page} 页，共 {self.total_pages} 页 (显示 {start_idx+1}-{end_idx} 条，共 {self.total_records} 条)")
        else:
            self.page_info_label.setText("第 1 页，共 1 页")

    def export_data(self):
        """导出当前搜索结果到Excel - 优化导出性能"""
        if not self.search_results:
            QMessageBox.warning(self, "警告", "没有数据可导出")
            return

        try:
            # 选择保存文件
            file_name = f"资金分析结果_{self.case_name}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path, _ = QMessageBox.getSaveFileName(
                self, "保存Excel文件", file_name, "Excel Files (*.xlsx)"
            )
            
            if not file_path:
                return

            # 使用pandas的ExcelWriter进行批量写入，提高性能
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                for table_name, df in self.search_results.items():
                    # 限制工作表名长度
                    sheet_name = table_name[:31] if len(table_name) > 31 else table_name
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

            # 应用高亮格式 - 在单独的函数中处理
            self.export_to_excel_with_highlighting(file_path, self.search_results)
            
            QMessageBox.information(self, "导出成功", f"数据已成功导出到:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "导出错误", f"导出过程中发生错误: {str(e)}")

    def export_to_excel_with_highlighting(self, file_path, data_dict):
        """为Excel文件添加关键词高亮 - 优化性能"""
        try:
            # 加载工作簿
            workbook = openpyxl.load_workbook(file_path)
            
            # 定义高亮填充样式
            highlight_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
            
            for table_name, df in data_dict.items():
                sheet_name = table_name[:31] if len(table_name) > 31 else table_name
                if sheet_name not in workbook.sheetnames:
                    continue
                    
                worksheet = workbook[sheet_name]
                
                # 优化：批量处理高亮
                cells_to_highlight = []
                
                # 遍历数据查找包含关键词的单元格
                for row_idx, (_, row) in enumerate(df.iterrows(), start=2):  # 从第2行开始（第1行是标题）
                    for col_idx, value in enumerate(row, start=1):
                        if pd.isna(value):
                            continue
                            
                        cell_value = str(value).lower()
                        # 检查是否包含任何搜索关键词
                        for keyword in self.search_keywords:
                            if keyword.lower() in cell_value:
                                cells_to_highlight.append((row_idx, col_idx))
                                break  # 找到一个关键词就足够了
                
                # 批量应用高亮样式
                for row_idx, col_idx in cells_to_highlight:
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    cell.fill = highlight_fill
            
            # 保存工作簿
            workbook.save(file_path)
            
        except Exception as e:
            print(f"添加高亮时出错: {e}")
            # 即使高亮失败，文件仍然可用

    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.display_current_page()

    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.display_current_page()


class DataFrameModel(QAbstractTableModel):
    """用于在QTableView中显示pandas DataFrame的模型 - 优化性能"""
    def __init__(self, df=pd.DataFrame(), keywords=None, parent=None):
        super().__init__(parent)
        self._df = df.copy()
        self.keywords = keywords or []
        # 预处理关键词，转换为小写以提高搜索性能
        self.keywords_lower = [kw.lower() for kw in self.keywords]

    def rowCount(self, parent=QModelIndex()):
        return len(self._df)

    def columnCount(self, parent=QModelIndex()):
        return len(self._df.columns)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None

        value = self._df.iloc[index.row(), index.column()]
        
        if role == Qt.DisplayRole:
            if pd.isna(value):
                return ""
            return str(value)
        
        elif role == Qt.BackgroundRole:
            # 高亮包含关键词的单元格 - 优化性能
            if self.keywords_lower and not pd.isna(value):
                cell_value = str(value).lower()
                for keyword in self.keywords_lower:
                    if keyword in cell_value:
                        return QBrush(QColor(255, 255, 0, 100))  # 半透明黄色高亮
        
        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:
                return str(self._df.columns[section])
            else:
                return str(section + 1)
        return None


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = AnalyzeFundsWindow("TEST001", "测试案件")
    window.show()
    sys.exit(app.exec())
