from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QPushButton, QLineEdit


class PaginationWidget(QWidget):
    def __init__(self, total_records, records_per_page=200, parent=None):
        super().__init__(parent)
        self.total_records = total_records
        self.records_per_page = records_per_page
        self.total_pages = (self.total_records + self.records_per_page - 1) // self.records_per_page

        self.init_ui()

    def init_ui(self):
        layout = QHBoxLayout()
        self.setLayout(layout)

        self.page_info_label = QLabel(
            f"显示第 1 - {min(self.records_per_page, self.total_records)} 条, 总共 {self.total_records} 条")
        layout.addWidget(self.page_info_label)

        layout.addStretch()

        self.prev_button = QPushButton("上一页")
        self.prev_button.setEnabled(False)
        layout.addWidget(self.prev_button)

        self.page_input = QLineEdit("1")
        self.page_input.setFixedWidth(40)
        self.page_input.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.page_input)

        self.next_button = QPushButton("下一页")
        self.next_button.setEnabled(self.total_pages > 1)
        layout.addWidget(self.next_button)

        self.page_info_total_label = QLabel(f"共 {self.total_pages} 页")
        layout.addWidget(self.page_info_total_label)

    def update_info(self, current_page):
        start_record = (current_page - 1) * self.records_per_page + 1
        end_record = min(current_page * self.records_per_page, self.total_records)
        self.page_info_label.setText(f"显示第 {start_record} - {end_record} 条, 总共 {self.total_records} 条")
        self.page_input.setText(str(current_page))
        self.prev_button.setEnabled(current_page > 1)
        self.next_button.setEnabled(current_page < self.total_pages)
