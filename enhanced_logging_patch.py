#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强日志记录补丁

本文件的功能和实现逻辑：
1. 为数据导入过程添加详细的日志记录
2. 监控程序执行的每个关键步骤
3. 捕获可能导致程序退出的异常
4. 提供详细的调试信息

日志增强内容：
- 函数进入和退出日志
- 内存使用监控
- 数据库操作详细记录
- 线程状态跟踪
- 异常堆栈跟踪
"""

import logging
import traceback
import sys
import os
import threading
import time
import functools
from datetime import datetime

# 创建详细的日志配置
def setup_enhanced_logging():
    """设置增强的日志记录"""
    
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 创建详细的日志格式
    detailed_formatter = logging.Formatter(
        '%(asctime)s.%(msecs)03d - %(levelname)s - [%(threadName)s:%(thread)d] - '
        '%(filename)s:%(lineno)d - %(funcName)s() - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 🔧 修复：使用统一的日志配置，简化处理器设置
    handlers = []

    # 1. 主日志文件 - 所有日志
    from logger_config import get_log_file_path
    main_log_file = get_log_file_path('import_detailed')
    main_handler = logging.FileHandler(main_log_file, encoding='utf-8')
    main_handler.setLevel(logging.DEBUG)
    main_handler.setFormatter(detailed_formatter)
    handlers.append(main_handler)

    # 2. 错误日志文件 - 只记录错误
    error_log_file = get_log_file_path('import_errors')
    error_handler = logging.FileHandler(error_log_file, encoding='utf-8')
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    handlers.append(error_handler)
    
    # 🔧 修复：禁用控制台输出，避免PowerShell命令解析错误
    # 控制台输出会被PowerShell误认为是命令，导致解析错误
    # 所有日志都写入文件，不输出到控制台
    # console_handler = logging.StreamHandler(sys.stdout)
    # console_handler.setLevel(logging.INFO)
    # console_formatter = logging.Formatter(
    #     '%(asctime)s - %(levelname)s - %(message)s',
    #     datefmt='%H:%M:%S'
    # )
    # console_handler.setFormatter(console_formatter)
    # handlers.append(console_handler)
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 添加新处理器
    for handler in handlers:
        root_logger.addHandler(handler)
    
    logging.info("🔍 增强日志记录已启动")
    return handlers

def log_function_calls(func):
    """函数调用日志装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        func_name = func.__name__
        class_name = ""
        
        # 获取类名（如果是方法）
        if args and hasattr(args[0], '__class__'):
            class_name = f"{args[0].__class__.__name__}."
        
        full_name = f"{class_name}{func_name}"
        thread_name = threading.current_thread().name
        
        # 记录函数进入
        logging.debug(f"🔵 进入函数: {full_name} [线程: {thread_name}]")
        
        try:
            # 记录参数（但不记录敏感数据）
            safe_args = []
            for i, arg in enumerate(args[1:], 1):  # 跳过self参数
                if isinstance(arg, (str, int, float, bool)):
                    if len(str(arg)) > 100:
                        safe_args.append(f"arg{i}=<长字符串:{len(str(arg))}字符>")
                    else:
                        safe_args.append(f"arg{i}={arg}")
                else:
                    safe_args.append(f"arg{i}=<{type(arg).__name__}>")
            
            if safe_args:
                logging.debug(f"   参数: {', '.join(safe_args[:5])}")  # 只记录前5个参数
            
            # 执行函数
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            # 记录函数退出
            duration = end_time - start_time
            logging.debug(f"🟢 退出函数: {full_name} [耗时: {duration:.3f}s]")
            
            return result
            
        except Exception as e:
            # 记录异常详情
            logging.error(f"🔴 函数异常: {full_name}")
            logging.error(f"   异常类型: {type(e).__name__}")
            logging.error(f"   异常信息: {str(e)}")
            logging.error(f"   异常堆栈:\n{traceback.format_exc()}")
            
            # 重新抛出异常
            raise
    
    return wrapper

def log_memory_usage(operation_name=""):
    """记录内存使用情况"""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        
        # 获取系统内存信息
        system_memory = psutil.virtual_memory()
        system_available_mb = system_memory.available / 1024 / 1024
        system_percent = system_memory.percent
        
        logging.info(f"💾 内存使用 {operation_name}: "
                    f"进程={memory_mb:.1f}MB, "
                    f"系统可用={system_available_mb:.1f}MB, "
                    f"系统使用率={system_percent:.1f}%")
        
        # 如果内存使用过高，记录警告
        if memory_mb > 1024:  # 超过1GB
            logging.warning(f"⚠️ 进程内存使用过高: {memory_mb:.1f}MB")
        
        if system_percent > 85:  # 系统内存使用超过85%
            logging.warning(f"⚠️ 系统内存使用率过高: {system_percent:.1f}%")
            
    except ImportError:
        logging.debug("psutil未安装，无法监控内存使用")
    except Exception as e:
        logging.error(f"内存监控失败: {e}")

def log_database_operation(operation_name, sql="", params=None):
    """记录数据库操作"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logging.debug(f"🗄️ 数据库操作开始: {operation_name}")
            if sql:
                # 只记录SQL的前200个字符
                safe_sql = sql[:200] + "..." if len(sql) > 200 else sql
                logging.debug(f"   SQL: {safe_sql}")
            if params:
                logging.debug(f"   参数: {params}")
            
            try:
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()
                
                duration = end_time - start_time
                logging.debug(f"✅ 数据库操作完成: {operation_name} [耗时: {duration:.3f}s]")
                
                return result
                
            except Exception as e:
                logging.error(f"❌ 数据库操作失败: {operation_name}")
                logging.error(f"   错误: {str(e)}")
                logging.error(f"   堆栈:\n{traceback.format_exc()}")
                raise
        
        return wrapper
    return decorator

def log_thread_status():
    """记录线程状态"""
    try:
        current_thread = threading.current_thread()
        all_threads = threading.enumerate()
        
        logging.debug(f"🧵 当前线程: {current_thread.name} (ID: {current_thread.ident})")
        logging.debug(f"🧵 活跃线程数: {len(all_threads)}")
        
        for thread in all_threads:
            status = "运行中" if thread.is_alive() else "已停止"
            logging.debug(f"   - {thread.name}: {status}")
            
    except Exception as e:
        logging.error(f"线程状态记录失败: {e}")

def log_file_operation(operation, file_path, details=""):
    """记录文件操作"""
    try:
        file_size = 0
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / 1024 / 1024  # MB
        
        logging.info(f"📁 文件操作: {operation}")
        logging.info(f"   路径: {file_path}")
        logging.info(f"   大小: {file_size:.2f}MB")
        if details:
            logging.info(f"   详情: {details}")
            
    except Exception as e:
        logging.error(f"文件操作记录失败: {e}")

def log_exception_with_context(e, context=""):
    """记录异常及其上下文"""
    logging.error(f"🚨 异常发生 {context}")
    logging.error(f"   异常类型: {type(e).__name__}")
    logging.error(f"   异常信息: {str(e)}")
    logging.error(f"   发生位置: {traceback.format_exc().split('File')[-1].split(',')[0] if traceback.format_exc() else '未知'}")
    
    # 记录完整堆栈
    logging.error("   完整堆栈:")
    for line in traceback.format_exc().split('\n'):
        if line.strip():
            logging.error(f"     {line}")
    
    # 记录系统状态
    log_memory_usage("异常发生时")
    log_thread_status()

def create_import_monitor():
    """创建导入监控器"""
    class ImportMonitor:
        def __init__(self):
            self.start_time = time.time()
            self.last_heartbeat = time.time()
            self.processed_files = 0
            self.total_files = 0
            self.current_operation = "初始化"
            self.is_active = True
            
        def heartbeat(self, operation=""):
            """心跳检测"""
            current_time = time.time()
            if operation:
                self.current_operation = operation
            
            # 如果超过30秒没有心跳，记录警告
            if current_time - self.last_heartbeat > 30:
                logging.warning(f"⚠️ 长时间无响应: {current_time - self.last_heartbeat:.1f}秒")
                logging.warning(f"   当前操作: {self.current_operation}")
                log_memory_usage("长时间无响应时")
                log_thread_status()
            
            self.last_heartbeat = current_time
            logging.debug(f"💓 心跳: {operation}")
        
        def update_progress(self, processed, total):
            """更新进度"""
            self.processed_files = processed
            self.total_files = total
            
            if total > 0:
                progress = (processed / total) * 100
                elapsed = time.time() - self.start_time
                
                logging.info(f"📊 进度更新: {processed}/{total} ({progress:.1f}%) "
                           f"[已用时: {elapsed:.1f}s]")
        
        def stop(self):
            """停止监控"""
            self.is_active = False
            total_time = time.time() - self.start_time
            logging.info(f"⏹️ 导入监控停止 [总耗时: {total_time:.1f}s]")
    
    return ImportMonitor()

# 全局监控器
import_monitor = None

def start_import_monitoring():
    """启动导入监控"""
    global import_monitor
    import_monitor = create_import_monitor()
    logging.info("🔍 导入监控已启动")
    return import_monitor

def stop_import_monitoring():
    """停止导入监控"""
    global import_monitor
    if import_monitor:
        import_monitor.stop()
        import_monitor = None

# 系统退出监控
original_exit = sys.exit

def monitored_exit(code=0):
    """监控系统退出"""
    logging.critical(f"🚨 程序即将退出! 退出码: {code}")
    logging.critical("退出时的堆栈跟踪:")
    
    # 记录调用堆栈
    stack = traceback.format_stack()
    for line in stack:
        logging.critical(f"  {line.strip()}")
    
    # 记录系统状态
    log_memory_usage("程序退出时")
    log_thread_status()
    
    # 调用原始exit
    original_exit(code)

# 替换系统exit函数
sys.exit = monitored_exit

if __name__ == "__main__":
    # 测试增强日志
    setup_enhanced_logging()
    
    @log_function_calls
    def test_function(x, y):
        logging.info(f"测试函数执行: {x} + {y} = {x + y}")
        return x + y
    
    # 测试日志功能
    logging.info("开始测试增强日志功能")
    log_memory_usage("测试开始")
    
    result = test_function(1, 2)
    logging.info(f"测试结果: {result}")
    
    log_memory_usage("测试结束")
    logging.info("增强日志测试完成")
