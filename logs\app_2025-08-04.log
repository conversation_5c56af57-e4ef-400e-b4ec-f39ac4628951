2025-08-04 11:16:16,649 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-04.log
2025-08-04 11:16:16,649 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-04 11:16:16,649 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-04 11:16:16,649 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250804_111616.log
2025-08-04 11:16:17,347 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250804_111617.log
2025-08-04 11:16:17,477 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-04 14:45:09,104 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-04.log
2025-08-04 14:45:09,104 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-04 14:45:09,104 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-04 14:45:09,104 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250804_144509.log
2025-08-04 14:45:09,766 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250804_144509.log
2025-08-04 14:45:09,862 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-04 16:14:04,562 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-04.log
2025-08-04 16:14:04,562 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-04 16:14:04,562 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-04 16:14:04,562 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250804_161404.log
2025-08-04 16:14:05,132 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250804_161405.log
2025-08-04 16:14:05,202 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-04 21:10:05,327 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\app_2025-08-04.log
2025-08-04 21:10:05,327 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: G:\数据分析系统20250725\LOGS
2025-08-04 21:10:05,327 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-04 21:10:05,328 - INFO - [test_cash_flag_fill.py:21] - test_method_import - ✅ fill_counterparty_name_by_cash_flag方法导入成功
2025-08-04 21:10:05,329 - INFO - [test_cash_flag_fill.py:148] - main - ✅ 方法导入测试 通过
2025-08-04 21:10:05,329 - INFO - [test_cash_flag_fill.py:144] - main - 
📋 执行测试: 方法签名测试
2025-08-04 21:10:05,331 - INFO - [test_cash_flag_fill.py:42] - test_method_signature - ✅ 方法签名正确: ['cursor', 'case_id']
2025-08-04 21:10:05,331 - INFO - [test_cash_flag_fill.py:148] - main - ✅ 方法签名测试 通过
2025-08-04 21:10:05,331 - INFO - [test_cash_flag_fill.py:144] - main - 
📋 执行测试: 现金标志值测试
2025-08-04 21:10:05,332 - INFO - [test_cash_flag_fill.py:64] - test_cash_flag_values - ✅ 找到现金标志值: '现金'
2025-08-04 21:10:05,332 - INFO - [test_cash_flag_fill.py:64] - test_cash_flag_values - ✅ 找到现金标志值: '1'
2025-08-04 21:10:05,332 - INFO - [test_cash_flag_fill.py:64] - test_cash_flag_values - ✅ 找到现金标志值: 'Y'
2025-08-04 21:10:05,333 - INFO - [test_cash_flag_fill.py:64] - test_cash_flag_values - ✅ 找到现金标志值: 'y'
2025-08-04 21:10:05,333 - INFO - [test_cash_flag_fill.py:64] - test_cash_flag_values - ✅ 找到现金标志值: '是'
2025-08-04 21:10:05,333 - INFO - [test_cash_flag_fill.py:64] - test_cash_flag_values - ✅ 找到现金标志值: 'YES'
2025-08-04 21:10:05,333 - INFO - [test_cash_flag_fill.py:64] - test_cash_flag_values - ✅ 找到现金标志值: 'yes'
2025-08-04 21:10:05,333 - INFO - [test_cash_flag_fill.py:64] - test_cash_flag_values - ✅ 找到现金标志值: 'CASH'
2025-08-04 21:10:05,333 - INFO - [test_cash_flag_fill.py:64] - test_cash_flag_values - ✅ 找到现金标志值: 'cash'
2025-08-04 21:10:05,333 - INFO - [test_cash_flag_fill.py:148] - main - ✅ 现金标志值测试 通过
2025-08-04 21:10:05,333 - INFO - [test_cash_flag_fill.py:144] - main - 
📋 执行测试: SQL逻辑测试
2025-08-04 21:10:05,334 - INFO - [test_cash_flag_fill.py:92] - test_sql_logic - ✅ 找到SQL部分: SET "对手户名" = '现金'
2025-08-04 21:10:05,334 - INFO - [test_cash_flag_fill.py:92] - test_sql_logic - ✅ 找到SQL部分: ("对手户名" IS NULL OR "对手户名" = '')
2025-08-04 21:10:05,334 - INFO - [test_cash_flag_fill.py:92] - test_sql_logic - ✅ 找到SQL部分: "现金标志" = %s
2025-08-04 21:10:05,334 - INFO - [test_cash_flag_fill.py:92] - test_sql_logic - ✅ 找到SQL部分: TRIM("现金标志") = %s
2025-08-04 21:10:05,334 - INFO - [test_cash_flag_fill.py:148] - main - ✅ SQL逻辑测试 通过
2025-08-04 21:10:05,334 - INFO - [test_cash_flag_fill.py:144] - main - 
📋 执行测试: 清洗步骤集成测试
2025-08-04 21:10:05,335 - INFO - [test_cash_flag_fill.py:111] - test_cleaning_steps_integration - ✅ 新方法已添加到清洗步骤信息中
2025-08-04 21:10:05,335 - INFO - [test_cash_flag_fill.py:115] - test_cleaning_steps_integration - ✅ 执行逻辑已添加
2025-08-04 21:10:05,335 - INFO - [test_cash_flag_fill.py:148] - main - ✅ 清洗步骤集成测试 通过
2025-08-04 21:10:05,335 - INFO - [test_cash_flag_fill.py:154] - main - 
📊 测试结果: 5/5 通过
2025-08-04 21:10:05,335 - INFO - [test_cash_flag_fill.py:157] - main - 🎉 所有测试通过！现金标志填充对手户名功能已正确添加
2025-08-04 21:39:16,441 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-04.log
2025-08-04 21:39:16,441 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-04 21:39:16,441 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-04 21:39:16,441 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250804_213916.log
2025-08-04 21:39:16,949 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250804_213916.log
2025-08-04 21:39:17,076 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-04 22:16:20,913 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-04.log
2025-08-04 22:16:20,914 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-04 22:16:20,914 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-04 22:16:20,914 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250804_221620.log
2025-08-04 22:16:21,332 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250804_221621.log
2025-08-04 22:16:23,086 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-04 22:21:17,132 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-04.log
2025-08-04 22:21:17,132 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-04 22:21:17,132 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-04 22:21:17,136 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250804_222117.log
2025-08-04 22:21:17,526 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250804_222117.log
2025-08-04 22:21:17,562 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
2025-08-04 22:25:48,769 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\app_2025-08-04.log
2025-08-04 22:25:48,770 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: G:\数据分析系统20250725\LOGS
2025-08-04 22:25:48,770 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-04 22:25:48,775 - INFO - [test_cleaning_cancel.py:28] - test_datacleaner_cancel_flag - ✅ 取消标志初始状态正确（False）
2025-08-04 22:25:48,776 - INFO - [test_cleaning_cancel.py:173] - main - ✅ DataCleaner取消标志测试 通过
2025-08-04 22:25:48,776 - INFO - [test_cleaning_cancel.py:169] - main - 
📋 执行测试: stop_cleaning方法测试
2025-08-04 22:25:48,777 - INFO - [test_cleaning_cancel.py:52] - test_stop_cleaning_method - ✅ stop_cleaning方法存在
2025-08-04 22:25:48,777 - INFO - [data_cleaning.py:2144] - stop_cleaning - ⚠️ 用户取消了数据清洗操作
2025-08-04 22:25:48,777 - INFO - [test_cleaning_cancel.py:59] - test_stop_cleaning_method - ✅ stop_cleaning方法正确设置了取消标志
2025-08-04 22:25:48,777 - INFO - [test_cleaning_cancel.py:173] - main - ✅ stop_cleaning方法测试 通过
2025-08-04 22:25:48,777 - INFO - [test_cleaning_cancel.py:169] - main - 
📋 执行测试: 清洗循环取消检查测试
2025-08-04 22:25:48,778 - INFO - [test_cleaning_cancel.py:88] - test_cancel_check_in_loop - ✅ 找到取消检查: if self._is_cancelled:
2025-08-04 22:25:48,778 - INFO - [test_cleaning_cancel.py:88] - test_cancel_check_in_loop - ✅ 找到取消检查: 数据清洗已被用户取消
2025-08-04 22:25:48,778 - INFO - [test_cleaning_cancel.py:88] - test_cancel_check_in_loop - ✅ 找到取消检查: return
2025-08-04 22:25:48,779 - INFO - [test_cleaning_cancel.py:173] - main - ✅ 清洗循环取消检查测试 通过
2025-08-04 22:25:48,779 - INFO - [test_cleaning_cancel.py:169] - main - 
📋 执行测试: 进度对话框cancelled信号测试
