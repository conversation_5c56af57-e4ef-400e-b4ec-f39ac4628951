# import_table_type_rules.py
"""
表类型匹配规则导入脚本
功能：将表类型匹配.xlsx文件的数据导入到数据库中
实现逻辑：
1. 读取Excel文件
2. 解析文件关键词和工作表名
3. 导入到数据库表类型匹配规则表中
"""

import pandas as pd
import os
import logging
from database_setup import get_db_connection

def parse_filename_keyword(filename):
    """
    解析文件名称，提取关键词和工作表名
    例如：'公安部_户籍人口_基本人员信息' -> 关键词='公安部_户籍人口', 工作表名='基本人员信息'
    """
    # 按下划线分割
    parts = filename.split('_')
    
    if len(parts) >= 3:
        # 前两部分作为关键词，最后一部分作为工作表名
        keyword = '_'.join(parts[:-1])
        worksheet_name = parts[-1]
    elif len(parts) == 2:
        # 只有两部分的情况
        keyword = parts[0]
        worksheet_name = parts[1]
    else:
        # 只有一部分的情况
        keyword = filename
        worksheet_name = None
    
    return keyword, worksheet_name

def import_table_type_rules():
    """导入表类型匹配规则到数据库"""
    file_path = "表类型匹配.xlsx"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件 {file_path} 不存在")
        return False
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path, sheet_name='Sheet1')
        print(f"📊 读取到 {len(df)} 条表类型匹配规则")
        
        # 连接数据库
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 清空现有规则
        cursor.execute("DELETE FROM 表类型匹配规则表")
        print("🗑️  已清空原有的表类型匹配规则")
        
        success_count = 0
        error_count = 0
        
        for index, row in df.iterrows():
            try:
                filename = str(row['文件名称']).strip()
                table_name = str(row['数据库表名']).strip()
                
                # 解析文件关键词和工作表名
                keyword, worksheet_name = parse_filename_keyword(filename)
                
                # 插入数据库
                cursor.execute('''
                    INSERT INTO 表类型匹配规则表 (文件关键词, 工作表名, 数据库表名, 备注)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (文件关键词, 工作表名) DO UPDATE SET
                        数据库表名 = EXCLUDED.数据库表名,
                        更新时间 = TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
                        备注 = EXCLUDED.备注
                ''', (keyword, worksheet_name, table_name, f"从Excel文件导入：{filename}"))
                
                success_count += 1
                
                # 打印导入的规则
                if worksheet_name:
                    print(f"✅ 规则 {success_count}: 关键词='{keyword}' + 工作表='{worksheet_name}' → '{table_name}'")
                else:
                    print(f"✅ 规则 {success_count}: 关键词='{keyword}' → '{table_name}'")
                
            except Exception as e:
                error_count += 1
                print(f"❌ 处理第 {index+1} 行时出错: {e}")
                logging.error(f"导入表类型规则第 {index+1} 行时出错: {e}")
        
        # 提交事务
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"\n📈 导入完成！")
        print(f"   ✅ 成功导入: {success_count} 条规则")
        print(f"   ❌ 失败: {error_count} 条规则")
        print(f"   📊 总计: {len(df)} 条规则")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入表类型匹配规则时出错: {e}")
        logging.error(f"导入表类型匹配规则时出错: {e}")
        return False

def test_table_type_matching():
    """测试表类型匹配功能"""
    test_cases = [
        ("公安部_户籍人口_045_周福军_522724196509110051", "基本人员信息"),
        ("国家税务总局_纳税人登记信息_某企业", "登记信息"),
        ("金融理财_某银行理财产品", None),
    ]
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("\n🧪 测试表类型匹配功能:")
        
        for filename, worksheet_name in test_cases:
            # 提取文件关键词
            parts = filename.split('_')
            if len(parts) >= 2:
                # 取前两个部分作为关键词
                keyword = '_'.join(parts[:2])
            else:
                keyword = parts[0] if parts else filename
            
            # 查询匹配规则
            if worksheet_name:
                cursor.execute('''
                    SELECT 数据库表名, 备注 FROM 表类型匹配规则表 
                    WHERE 文件关键词 = %s AND 工作表名 = %s
                ''', (keyword, worksheet_name))
            else:
                cursor.execute('''
                    SELECT 数据库表名, 备注 FROM 表类型匹配规则表 
                    WHERE 文件关键词 = %s AND 工作表名 IS NULL
                ''', (keyword,))
            
            result = cursor.fetchone()
            
            if result:
                table_name, note = result
                print(f"  📁 '{filename}' + 工作表'{worksheet_name}' → ✅ '{table_name}'")
            else:
                print(f"  📁 '{filename}' + 工作表'{worksheet_name}' → ❌ 未找到匹配规则")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 测试表类型匹配时出错: {e}")

if __name__ == "__main__":
    print("🚀 开始导入表类型匹配规则...")
    if import_table_type_rules():
        test_table_type_matching()
    else:
        print("❌ 导入失败，跳过测试") 