import psycopg2  # 替换 sqlite3 为 psycopg2
from psycopg2 import extras  # 用于字典游标
import logging
import os
from database_setup import get_db_connection  # 导入数据库连接函数

class PostgresParser:
    """
    PostgreSQL数据库解析器，用于数据库搜索和操作
    优化了搜索性能和连接管理
    """
    def __init__(self, db_config=None):
        """
        初始化PostgreSQL解析器
        
        参数:
            db_config: 数据库配置（现在使用统一的连接函数）
        """
        self.db_config = db_config
        self.conn = None
        self.logger = logging.getLogger(__name__)
        # 添加缓存以提高性能
        self._table_cache = None
        self._column_cache = {}
    
    def connect(self, db_config=None):
        """
        连接到PostgreSQL数据库
        
        参数:
            db_config: 数据库配置（可选，使用统一配置）
        
        返回:
            成功返回True，失败返回False
        """
        try:
            if db_config:
                self.db_config = db_config
                
            # 使用统一的数据库连接函数
            self.conn = get_db_connection()
            if not self.conn:
                self.logger.error("无法获取数据库连接")
                return False
                
            return True
        except Exception as e:
            self.logger.error(f"连接PostgreSQL数据库时出错: {str(e)}")
            return False
    
    def close(self):
        """关闭数据库连接并清理缓存"""
        if self.conn:
            self.conn.close()
            self.conn = None
        # 清理缓存
        self._table_cache = None
        self._column_cache = {}
    
    def get_tables(self, use_cache=True):
        """
        获取数据库中的所有表名（优化版本，支持缓存）
        
        参数:
            use_cache: 是否使用缓存
            
        返回:
            表名列表
        """
        try:
            # 使用缓存提高性能
            if use_cache and self._table_cache is not None:
                return self._table_cache
                
            if not self.conn:
                if not self.connect():
                    return []
                    
            cursor = self.conn.cursor()
            # PostgreSQL查询系统表获取用户表
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            """)
            
            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()
            
            # 缓存结果
            if use_cache:
                self._table_cache = tables
                
            return tables
        except Exception as e:
            self.logger.error(f"获取表名时出错: {str(e)}")
            return []
    
    def get_table_columns(self, table_name, use_cache=True):
        """
        获取表的列信息（新增方法，支持缓存）
        
        参数:
            table_name: 表名
            use_cache: 是否使用缓存
            
        返回:
            列名列表
        """
        try:
            # 使用缓存提高性能
            if use_cache and table_name in self._column_cache:
                return self._column_cache[table_name]
                
            if not self.conn:
                if not self.connect():
                    return []
                    
            cursor = self.conn.cursor()
            # PostgreSQL查询列信息
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = %s
                ORDER BY ordinal_position
            """, (table_name,))
            
            columns = [row[0] for row in cursor.fetchall()]
            cursor.close()
            
            # 缓存结果
            if use_cache:
                self._column_cache[table_name] = columns
                
            return columns
        except Exception as e:
            self.logger.error(f"获取表 {table_name} 的列信息时出错: {str(e)}")
            return []
    
    def search_in_table(self, table_name, keyword, limit=1000):
        """
        在表中搜索关键词（优化版本，支持分页和性能提升）
        
        参数:
            table_name: 表名
            keyword: 搜索关键词
            limit: 最大返回记录数
            
        返回:
            符合条件的记录列表
        """
        try:
            if not self.conn:
                if not self.connect():
                    return []
                    
            # 使用字典游标以获得更好的性能
            cursor = self.conn.cursor(cursor_factory=extras.DictCursor)
            
            # 获取表的列名（使用缓存）
            columns = self.get_table_columns(table_name)
            if not columns:
                return []
            
            # 构建优化的搜索SQL（使用ILIKE进行大小写不敏感搜索）
            search_conditions = []
            params = []
            
            for col in columns:
                # 只搜索文本类型的列以提高性能
                search_conditions.append(f'"{col}"::text ILIKE %s')
                params.append(f"%{keyword}%")
                
            # 添加LIMIT子句以提高性能
            sql = f'''
                SELECT * FROM "{table_name}" 
                WHERE {' OR '.join(search_conditions)}
                LIMIT %s
            '''
            params.append(limit)
            
            # 执行搜索
            cursor.execute(sql, params)
            results = []
            
            for row in cursor.fetchall():
                # DictCursor返回字典类型的结果
                results.append(dict(row))
                
            cursor.close()
            self.logger.info(f"在表 {table_name} 中找到 {len(results)} 条匹配记录")
            return results
            
        except Exception as e:
            self.logger.error(f"搜索表 {table_name} 时出错: {str(e)}")
            return []
    
    def execute_query(self, query, params=None, limit=None):
        """
        执行自定义SQL查询（优化版本，支持参数化查询和限制）
        
        参数:
            query: SQL查询语句
            params: 查询参数
            limit: 可选的结果限制
            
        返回:
            查询结果列表
        """
        try:
            if not self.conn:
                if not self.connect():
                    return []
                    
            # 使用字典游标
            cursor = self.conn.cursor(cursor_factory=extras.DictCursor)
            
            # 添加LIMIT子句如果指定了限制
            if limit and 'LIMIT' not in query.upper():
                query += f' LIMIT {limit}'
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
                
            results = []
            for row in cursor.fetchall():
                results.append(dict(row))
                
            cursor.close()
            return results
            
        except Exception as e:
            self.logger.error(f"执行查询时出错: {str(e)}")
            return []
    
    def count_table_records(self, table_name):
        """
        获取表的记录数（新增方法，用于性能监控）
        
        参数:
            table_name: 表名
            
        返回:
            记录数量
        """
        try:
            if not self.conn:
                if not self.connect():
                    return 0
                    
            cursor = self.conn.cursor()
            cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
            count = cursor.fetchone()[0]
            cursor.close()
            
            return count
        except Exception as e:
            self.logger.error(f"获取表 {table_name} 记录数时出错: {str(e)}")
            return 0 