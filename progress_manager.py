# progress_manager.py
# 进度保存管理器 - 支持断点续传功能

import json
import os
import logging
import time
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Tuple

class ProgressManager:
    """
    进度保存管理器
    
    功能说明：
    - 保存数据导入进度信息
    - 支持断点续传
    - 记录已完成的文件列表
    - 提供进度恢复功能
    """
    
    def __init__(self, case_id: str, progress_dir: str = "progress"):
        """
        初始化进度管理器
        
        Args:
            case_id: 案件编号
            progress_dir: 进度文件保存目录
        """
        self.case_id = case_id
        self.progress_dir = progress_dir
        
        # 确保进度目录存在
        os.makedirs(progress_dir, exist_ok=True)
        
        # 进度文件路径
        self.progress_file = os.path.join(progress_dir, f"progress_{case_id}.json")
        
        # 初始化进度数据
        self.progress_data = {
            "case_id": case_id,
            "created_time": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "status": "pending",  # pending, in_progress, completed, failed
            "total_files": 0,
            "completed_files": 0,
            "total_records": 0,
            "imported_records": 0,
            "completed_file_list": [],  # 已完成的文件信息
            "failed_file_list": [],     # 失败的文件信息
            "current_batch": 1,
            "memory_stats": {},
            "error_message": None
        }
        
        # 尝试加载现有进度
        self.load_progress()
        
        logging.info(f"📋 进度管理器初始化完成: 案件 {case_id}")

    def generate_file_hash(self, file_path: str) -> str:
        """
        生成文件哈希值，用于唯一标识文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件的MD5哈希值
        """
        try:
            # 使用文件路径和修改时间生成哈希
            file_stat = os.stat(file_path)
            file_info = f"{file_path}_{file_stat.st_size}_{file_stat.st_mtime}"
            return hashlib.md5(file_info.encode('utf-8')).hexdigest()
        except Exception as e:
            # 如果无法获取文件信息，使用文件路径生成哈希
            logging.warning(f"无法生成文件哈希 {file_path}: {e}")
            return hashlib.md5(file_path.encode('utf-8')).hexdigest()

    def is_file_completed(self, file_path: str) -> bool:
        """
        检查文件是否已经完成导入
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件是否已完成
        """
        file_hash = self.generate_file_hash(file_path)
        
        for completed_file in self.progress_data["completed_file_list"]:
            if completed_file.get("file_hash") == file_hash:
                return True
        
        return False

    def mark_file_completed(self, file_path: str, records_count: int, worksheet_name: str = ""):
        """
        标记文件为已完成
        
        Args:
            file_path: 文件路径
            records_count: 导入的记录数
            worksheet_name: 工作表名称
        """
        file_hash = self.generate_file_hash(file_path)
        
        # 检查是否已经存在
        for completed_file in self.progress_data["completed_file_list"]:
            if completed_file.get("file_hash") == file_hash:
                logging.debug(f"文件已标记为完成: {file_path}")
                return
        
        # 添加到已完成列表
        completed_info = {
            "file_path": file_path,
            "file_hash": file_hash,
            "records_count": records_count,
            "worksheet_name": worksheet_name,
            "completed_time": datetime.now().isoformat()
        }
        
        self.progress_data["completed_file_list"].append(completed_info)
        self.progress_data["completed_files"] += 1
        self.progress_data["imported_records"] += records_count
        self.progress_data["last_updated"] = datetime.now().isoformat()
        
        # 保存进度
        self.save_progress()
        
        logging.info(f"✅ 文件完成标记: {os.path.basename(file_path)} ({records_count} 条记录)")

    def mark_file_failed(self, file_path: str, error_message: str):
        """
        标记文件为失败
        
        Args:
            file_path: 文件路径
            error_message: 错误信息
        """
        file_hash = self.generate_file_hash(file_path)
        
        # 检查是否已经存在
        for failed_file in self.progress_data["failed_file_list"]:
            if failed_file.get("file_hash") == file_hash:
                # 更新错误信息
                failed_file["error_message"] = error_message
                failed_file["failed_time"] = datetime.now().isoformat()
                self.save_progress()
                return
        
        # 添加到失败列表
        failed_info = {
            "file_path": file_path,
            "file_hash": file_hash,
            "error_message": error_message,
            "failed_time": datetime.now().isoformat()
        }
        
        self.progress_data["failed_file_list"].append(failed_info)
        self.progress_data["last_updated"] = datetime.now().isoformat()
        
        # 保存进度
        self.save_progress()
        
        logging.error(f"❌ 文件失败标记: {os.path.basename(file_path)} - {error_message}")

    def filter_pending_files(self, file_list: List[Tuple]) -> List[Tuple]:
        """
        过滤出待处理的文件列表（排除已完成的文件）
        
        Args:
            file_list: 原始文件列表 [(file_path, df, file_type, import_mapping, worksheet_name), ...]
            
        Returns:
            List[Tuple]: 待处理的文件列表
        """
        pending_files = []
        skipped_count = 0
        
        for file_info in file_list:
            file_path = file_info[0]
            
            if not self.is_file_completed(file_path):
                pending_files.append(file_info)
            else:
                skipped_count += 1
                logging.info(f"⏭️ 跳过已完成文件: {os.path.basename(file_path)}")
        
        if skipped_count > 0:
            logging.info(f"📊 断点续传: 跳过 {skipped_count} 个已完成文件，待处理 {len(pending_files)} 个文件")
        
        return pending_files

    def start_import_session(self, total_files: int, total_records: int = 0):
        """
        开始导入会话
        
        Args:
            total_files: 总文件数
            total_records: 总记录数（可选）
        """
        self.progress_data["status"] = "in_progress"
        self.progress_data["total_files"] = total_files
        if total_records > 0:
            self.progress_data["total_records"] = total_records
        self.progress_data["last_updated"] = datetime.now().isoformat()
        
        # 保存进度
        self.save_progress()
        
        logging.info(f"🚀 开始导入会话: 总文件数 {total_files}")

    def complete_import_session(self):
        """完成导入会话"""
        self.progress_data["status"] = "completed"
        self.progress_data["last_updated"] = datetime.now().isoformat()
        
        # 保存进度
        self.save_progress()
        
        logging.info(f"🎉 导入会话完成: 共处理 {self.progress_data['completed_files']} 个文件，"
                    f"导入 {self.progress_data['imported_records']} 条记录")

    def fail_import_session(self, error_message: str):
        """标记导入会话失败"""
        self.progress_data["status"] = "failed"
        self.progress_data["error_message"] = error_message
        self.progress_data["last_updated"] = datetime.now().isoformat()
        
        # 保存进度
        self.save_progress()
        
        logging.error(f"💥 导入会话失败: {error_message}")

    def update_memory_stats(self, memory_info: Dict):
        """
        更新内存统计信息
        
        Args:
            memory_info: 内存信息字典
        """
        self.progress_data["memory_stats"] = {
            "memory_percent": memory_info.get("memory_percent", 0),
            "used_gb": memory_info.get("used_gb", 0),
            "available_gb": memory_info.get("available_gb", 0),
            "total_gb": memory_info.get("total_gb", 0),
            "last_check": datetime.now().isoformat()
        }

    def save_progress(self):
        """保存进度到文件"""
        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_data, f, ensure_ascii=False, indent=2)
            
            logging.debug(f"💾 进度已保存: {self.progress_file}")
            
        except Exception as e:
            logging.error(f"保存进度失败: {e}")

    def load_progress(self) -> bool:
        """
        从文件加载进度
        
        Returns:
            bool: 是否成功加载进度
        """
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                
                # 合并加载的数据
                self.progress_data.update(loaded_data)
                
                logging.info(f"📂 进度已加载: 已完成 {self.progress_data['completed_files']} 个文件")
                return True
            else:
                logging.info(f"📝 创建新的进度文件: {self.progress_file}")
                return False
                
        except Exception as e:
            logging.warning(f"加载进度失败: {e}")
            return False

    def get_progress_summary(self) -> Dict:
        """
        获取进度摘要
        
        Returns:
            Dict: 进度摘要信息
        """
        total_files = self.progress_data["total_files"]
        completed_files = self.progress_data["completed_files"]
        failed_files = len(self.progress_data["failed_file_list"])
        
        progress_percent = 0
        if total_files > 0:
            progress_percent = (completed_files / total_files) * 100
        
        return {
            "case_id": self.case_id,
            "status": self.progress_data["status"],
            "total_files": total_files,
            "completed_files": completed_files,
            "failed_files": failed_files,
            "pending_files": total_files - completed_files - failed_files,
            "progress_percent": progress_percent,
            "imported_records": self.progress_data["imported_records"],
            "last_updated": self.progress_data["last_updated"],
            "memory_stats": self.progress_data.get("memory_stats", {})
        }

    def cleanup_progress(self):
        """清理进度文件"""
        try:
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
                logging.info(f"🗑️ 进度文件已清理: {self.progress_file}")
        except Exception as e:
            logging.warning(f"清理进度文件失败: {e}")

    @staticmethod
    def list_all_progress(progress_dir: str = "progress") -> List[Dict]:
        """
        列出所有进度文件
        
        Args:
            progress_dir: 进度文件目录
            
        Returns:
            List[Dict]: 所有进度摘要列表
        """
        progress_list = []
        
        if not os.path.exists(progress_dir):
            return progress_list
        
        try:
            for filename in os.listdir(progress_dir):
                if filename.startswith("progress_") and filename.endswith(".json"):
                    file_path = os.path.join(progress_dir, filename)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            progress_data = json.load(f)
                        
                        # 提取案件ID
                        case_id = progress_data.get("case_id", "未知")
                        
                        # 创建临时进度管理器获取摘要
                        temp_manager = ProgressManager(case_id)
                        temp_manager.progress_data = progress_data
                        summary = temp_manager.get_progress_summary()
                        summary["progress_file"] = file_path
                        
                        progress_list.append(summary)
                        
                    except Exception as e:
                        logging.warning(f"读取进度文件失败 {filename}: {e}")
            
        except Exception as e:
            logging.error(f"列出进度文件时出错: {e}")
        
        return progress_list

# 创建全局进度管理器工厂函数
def create_progress_manager(case_id: str) -> ProgressManager:
    """
    创建进度管理器实例
    
    Args:
        case_id: 案件编号
        
    Returns:
        ProgressManager: 进度管理器实例
    """
    return ProgressManager(case_id) 