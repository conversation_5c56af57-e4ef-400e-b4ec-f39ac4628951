"""
功能：对账户交易明细表的对手信息进行清洗、同步到对手信息表，并回填账户交易明细表
实现逻辑：
- 复制当前案件的账户交易明细表中对手账号、对手卡号、对手户名、对手身份证号、对手开户银行五列到中间DataFrame
- 去重，删除五列全空、对手户名为现金、对手户名为空的行
- 只保留对手账号唯一的数据
- 将结果插入对手信息表，案件编号、创建时间、创建人自动填充
- 用对手信息表的对手账号与账户交易明细表的对手账号匹配，若账户交易明细表的对手户名为空，则用对手信息表的对手户名填充
- 所有异常详细日志，所有SQL操作用psycopg2，支持外部传入case_id和login_user
"""
import pandas as pd
import logging
from database_setup import get_db_connection
from datetime import datetime

def clean_and_sync_opponent_info(case_id, login_user):
    """
    对账户交易明细表的对手信息进行清洗、同步到对手信息表，并回填账户交易明细表
    :param case_id: 当前案件编号
    :param login_user: 当前登录用户名
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        logging.info(f"[对手信息清洗] 开始处理案件编号: {case_id}")
        # 1. 读取账户交易明细表的七列数据
        cursor.execute('''
            SELECT "对手账号", "对手卡号", "对手户名", "对手身份证号", "对手开户银行", "摘要说明", "备注"
            FROM 账户交易明细表
            WHERE "案件编号" = %s
        ''', (case_id,))
        rows = cursor.fetchall()
        df = pd.DataFrame(rows, columns=["对手账号", "对手卡号", "对手户名", "对手身份证号", "对手开户银行", "摘要说明", "备注"])
        logging.info(f"[对手信息清洗] 原始数据行数: {len(df)}")
        # 2. 去重、清洗
        df = df.drop_duplicates()
        logging.info(f"[对手信息清洗] 去重后行数: {len(df)}")
        # 删除五列全空的行
        before = len(df)
        df = df.dropna(how='all', subset=["对手账号", "对手卡号", "对手户名", "对手身份证号", "对手开户银行"])
        logging.info(f"[对手信息清洗] 删除五列全空的行: {before - len(df)} 条")
        # 删除对手户名为现金的行
        before = len(df)
        df = df[df["对手户名"].astype(str).str.strip() != "现金"]
        logging.info(f"[对手信息清洗] 删除对手户名为'现金'的行: {before - len(df)} 条")
        # 删除对手户名为空的行（严格过滤，不能插入对手信息表）
        before = len(df)
        df = df[df["对手户名"].astype(str).str.strip() != ""]
        df = df[~df["对手户名"].isnull()]
        logging.info(f"[对手信息清洗] 删除对手户名为空的行: {before - len(df)} 条")
        # 删除对手账号为空的行
        before = len(df)
        df = df[~df["对手账号"].isnull() & (df["对手账号"].astype(str).str.strip() != "")]
        logging.info(f"[对手信息清洗] 删除对手账号为空的行: {before - len(df)} 条")
        # 删除对手账号全部为0的行
        before = len(df)
        df = df[~df["对手账号"].astype(str).str.fullmatch(r"0+")]
        logging.info(f"[对手信息清洗] 删除对手账号全为0的行: {before - len(df)} 条")
        # 删除对手账号长度少于15位的行
        before = len(df)
        df = df[df["对手账号"].astype(str).str.len() >= 15]
        logging.info(f"[对手信息清洗] 删除对手账号长度少于15位的行: {before - len(df)} 条")
        # 过滤掉包含*的对手户名或对手账号
        before = len(df)
        df = df[
            ~df["对手户名"].astype(str).str.contains(r"\*") &
            ~df["对手账号"].astype(str).str.contains(r"\*")
        ]
        logging.info(f"[对手信息清洗] 排除包含*的对手户名/账号后剩余: {len(df)} 条，已过滤: {before - len(df)} 条")
        # 过滤关键词列表
        exclude_keywords = [
            "财付通", "微信", "支付宝", "美团", "抖音", "通联支付", "拉卡拉", "众联享付科技", "商户", "中国银联", "三快科技", "三快在线",
            "消费", "代收", "代发", "代扣", "基金", "扫码","二维码","信用卡","清算", "ETC", "连连银通",
            "网易", "特约", "天弘基金", "唯品会", "待清算"
        ]
        def contains_exclude_keyword(*fields):
            for val in fields:
                if pd.isnull(val):
                    continue
                for kw in exclude_keywords:
                    if kw in str(val):
                        return True
            return False
        # 过滤掉包含关键词的数据
        before = len(df)
        df = df[~df.apply(lambda row: contains_exclude_keyword(row["对手户名"], row["摘要说明"], row["备注"]), axis=1)]
        logging.info(f"[对手信息清洗] 过滤敏感关键词后剩余: {len(df)} 条，已过滤: {before - len(df)} 条")
        # 3. 只保留对手账号唯一的数据
        before = len(df)
        df = df.drop_duplicates(subset=["对手账号"], keep='first')
        logging.info(f"[对手信息清洗] 只保留对手账号唯一的数据: 去除 {before - len(df)} 条重复")
        # 4. 插入对手信息表
        now_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        insert_count = 0
        skip_count = 0
        for _, row in df.iterrows():
            # 再次严格校验对手户名为空不插入
            if not row["对手户名"] or str(row["对手户名"]).strip() == "":
                continue
            # 检查同一案件编号下对手账号是否已存在，若存在则跳过
            cursor.execute('''
                SELECT 1 FROM 对手信息 WHERE "案件编号" = %s AND "对手账号" = %s
            ''', (case_id, row["对手账号"]))
            if cursor.fetchone():
                skip_count += 1
                continue
            cursor.execute('''
                INSERT INTO 对手信息 (对手账号, 对手卡号, 对手户名, 对手身份证号, 对手开户银行, 摘要说明, 备注, 案件编号, 创建时间, 创建人)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ''', (
                row["对手账号"], row["对手卡号"], row["对手户名"], row["对手身份证号"],
                row["对手开户银行"], row["摘要说明"], row["备注"], case_id, now_str, login_user
            ))
            insert_count += 1
        logging.info(f"[对手信息清洗] 对手信息表已插入 {insert_count} 条记录，跳过已存在 {skip_count} 条")
        # 5. 用临时表+JOIN批量回填对手户名，极致性能优化
        try:
            logging.info(f"[对手信息清洗] 开始临时表批量回填对手户名，案件编号: {case_id}")
            # 1. 创建临时表，存储本案对手账号与户名
            cursor.execute('''
                CREATE TEMP TABLE tmp_opponent AS
                SELECT "对手账号", "对手户名"
                FROM 对手信息
                WHERE "案件编号" = %s
            ''', (case_id,))
            # 2. 批量UPDATE，回填对手户名
            cursor.execute('''
                UPDATE 账户交易明细表 t
                SET "对手户名" = tmp."对手户名"
                FROM tmp_opponent tmp
                WHERE t."案件编号" = %s
                  AND (t."对手户名" IS NULL OR t."对手户名" = '')
                  AND t."对手账号" = tmp."对手账号"
            ''', (case_id,))
            updated_rows = cursor.rowcount
            # 3. 删除临时表
            cursor.execute('DROP TABLE tmp_opponent')
            conn.commit()
            logging.info(f"[对手信息清洗] 账户交易明细表对手户名已批量回填 {updated_rows} 条（临时表+JOIN）")
        except Exception as e:
            logging.error(f"[对手信息清洗] 临时表批量回填对手户名时发生错误: {e}")
            conn.rollback()
        # 统计对手信息表中对手账号数量
        cursor.execute('''
            SELECT COUNT(DISTINCT "对手账号") FROM 对手信息 WHERE "案件编号" = %s
        ''', (case_id,))
        opponent_count = cursor.fetchone()[0]
        logging.info(f"[对手信息清洗] 对手信息表中对手账号数量: {opponent_count}")
        # 统计可回填的账户交易明细表记录数
        cursor.execute('''
            SELECT COUNT(*) FROM 账户交易明细表 t
            WHERE t."案件编号" = %s
              AND (t."对手户名" IS NULL OR t."对手户名" = '')
              AND t."对手账号" IN (
                  SELECT i."对手账号" FROM 对手信息 i WHERE i."案件编号" = %s
              )
        ''', (case_id, case_id))
        can_update = cursor.fetchone()[0]
        logging.info(f"[对手信息清洗] 可回填的账户交易明细表记录数: {can_update}")
        cursor.close()
        conn.close()
        logging.info(f"[对手信息清洗] 完成案件编号: {case_id}")
    except Exception as e:
        logging.error(f"[对手信息清洗] 对手信息清洗与同步时发生错误: {e}")
        # 不 raise，保证主流程继续 