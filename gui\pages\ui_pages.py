# ui_pages.py 文件

from qt_core import *

class Ui_application_pages(object):
    def setupUi(self, application_pages):
        if not application_pages.objectName():
            application_pages.setObjectName(u"application_pages")
        application_pages.resize(1200, 1000)

        # 主页
        self.page_1 = QWidget()
        self.page_1.setObjectName(u"page_1")
        self.verticalLayout = QVBoxLayout(self.page_1)
        self.verticalLayout.setObjectName(u"verticalLayout")

        # 增加右侧滑动栏
        self.scroll_area = QScrollArea(self.page_1)
        self.scroll_area.setWidgetResizable(True)

        # 创建一个容器部件用于包含案件图标和滚动区域
        self.container = QWidget()
        self.folder_layout = QGridLayout(self.container)
        self.container.setLayout(self.folder_layout)

        self.scroll_area.setWidget(self.container)
        self.verticalLayout.addWidget(self.scroll_area)  # 让scroll_area填满剩余空间

        # 主页底部按钮
        self.bottom_button_frame = QFrame()
        self.bottom_button_layout = QHBoxLayout(self.bottom_button_frame)
        self.bottom_button_layout.setSpacing(20)
        self.bottom_button_layout.setContentsMargins(10, 10, 10, 10)
        self.btn_add_case = QPushButton("新增案件")
        self.btn_query_case = QPushButton("查询案件")
        self.btn_delete_case = QPushButton("删除案件")

        # 设置按钮样式
        button_style = u"QPushButton {\n" \
                       "	background-color: rgb(67, 133, 200);\n" \
                       "	border: 2px solid #c3ccdf;\n" \
                       "	color: rgb(255, 255, 255);\n" \
                       "	border-radius: 10px;\n" \
                       "}\n" \
                       "QPushButton:hover {\n" \
                       "	background-color: rgb(85, 170, 255);\n" \
                       "}\n" \
                       "QPushButton:pressed {\n" \
                       "	background-color: rgb(255, 0, 127);\n" \
                       "}"

        self.btn_add_case.setFixedSize(150, 70)
        self.btn_query_case.setFixedSize(150, 70)
        self.btn_delete_case.setFixedSize(150, 70)
        self.btn_add_case.setStyleSheet(button_style)
        self.btn_query_case.setStyleSheet(button_style)
        self.btn_delete_case.setStyleSheet(button_style)

        self.bottom_button_layout.addWidget(self.btn_add_case)
        self.bottom_button_layout.addWidget(self.btn_query_case)
        self.bottom_button_layout.addWidget(self.btn_delete_case)
        self.verticalLayout.addWidget(self.bottom_button_frame, 0, Qt.AlignBottom)

        application_pages.addWidget(self.page_1)

        # 工具页
        self.page_2 = QWidget()
        self.page_2.setObjectName(u"page_2")
        self.verticalLayout_2 = QVBoxLayout(self.page_2)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")

        # 增加按钮
        self.button_frame = QFrame()
        self.button_layout = QGridLayout(self.button_frame)
        self.button_layout.setSpacing(20)
        self.button_layout.setContentsMargins(20, 20, 20, 20)

        self.btn_merge_files = QPushButton("合并文件")
        self.btn_merge_files.setFixedSize(150, 150)
        self.btn_merge_files.setStyleSheet(button_style)

        self.btn_split_table = QPushButton("拆分表格")
        self.btn_split_table.setFixedSize(150, 150)
        self.btn_split_table.setStyleSheet(button_style)

        self.btn_pdf_paginate = QPushButton("PDF分页")
        self.btn_pdf_paginate.setFixedSize(150, 150)
        self.btn_pdf_paginate.setStyleSheet(button_style)

        self.btn_pdf_to_word_excel = QPushButton("PDF转Word/Excel")
        self.btn_pdf_to_word_excel.setFixedSize(150, 150)
        self.btn_pdf_to_word_excel.setStyleSheet(button_style)
        
        # 添加数据库搜索按钮
        self.btn_db_search = QPushButton("数据库搜索")
        self.btn_db_search.setFixedSize(150, 150)
        self.btn_db_search.setStyleSheet(button_style)
        
        # 🔧 修复：重新启用导出数据按钮
        self.btn_export_data = QPushButton("导出数据")
        self.btn_export_data.setFixedSize(150, 150)
        self.btn_export_data.setStyleSheet(button_style)

        self.button_layout.addWidget(self.btn_merge_files, 0, 0)
        self.button_layout.addWidget(self.btn_split_table, 0, 1)
        self.button_layout.addWidget(self.btn_pdf_paginate, 1, 0)
        self.button_layout.addWidget(self.btn_pdf_to_word_excel, 1, 1)
        self.button_layout.addWidget(self.btn_db_search, 2, 0)  # 添加到第三行第一列
        # 🔧 修复：重新启用导出数据按钮
        self.button_layout.addWidget(self.btn_export_data, 2, 1)  # 重新启用导出数据按钮

        self.verticalLayout_2.addWidget(self.button_frame, 0, Qt.AlignCenter)
        application_pages.addWidget(self.page_2)

        # 设置页
        self.page_3 = QWidget()
        self.page_3.setObjectName(u"page_3")
        self.verticalLayout_3 = QVBoxLayout(self.page_3)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")

        # 增加选择主题按钮
        self.btn_select_theme = QPushButton("开灯")
        self.btn_select_theme.setFixedSize(90, 90)
        self.btn_select_theme.setStyleSheet(button_style)

        self.verticalLayout_3.addWidget(self.btn_select_theme, 0, Qt.AlignCenter)

        # 增加退出按钮
        self.btn_exit = QPushButton("退出")
        self.btn_exit.setFixedSize(90, 90)
        self.btn_exit.setStyleSheet(button_style)

        self.verticalLayout_3.addWidget(self.btn_exit, 0, Qt.AlignCenter)
        application_pages.addWidget(self.page_3)

        self.retranslateUi(application_pages)
        QMetaObject.connectSlotsByName(application_pages)

    def retranslateUi(self, application_pages):
        application_pages.setWindowTitle(QCoreApplication.translate("application_pages", u"StackedWidget", None))
