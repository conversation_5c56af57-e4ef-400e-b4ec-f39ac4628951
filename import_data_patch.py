# import_data_patch.py
# 数据导入内存优化补丁
# 修复大数据量导入时的内存溢出问题

import logging
from memory_optimizer import memory_optimizer

def patch_import_to_db_method(original_method):
    """
    装饰器：为import_to_db方法添加内存优化功能
    """
    def wrapper(self, mapped_df, file_type):
        try:
            # 🔧 导入前内存检查
            memory_optimizer.optimize_memory_if_needed()
            
            # 🔧 使用内存优化器计算批次大小
            total_rows = len(mapped_df)
            if hasattr(self, '_calculate_optimal_batch_size'):
                # 如果原方法存在，先进行内存优化检查
                original_batch_size = self._calculate_optimal_batch_size(total_rows)
                optimized_batch_size = memory_optimizer.calculate_optimal_batch_size(
                    total_rows, base_batch_size=original_batch_size
                )
                logging.debug(f"🔧 批次大小优化: 原始={original_batch_size}, 优化后={optimized_batch_size}")
            
            # 执行原始导入方法
            result = original_method(self, mapped_df, file_type)
            
            # 🔧 导入后内存清理
            memory_optimizer.cleanup_dataframe(mapped_df)
            
            return result
            
        except Exception as e:
            logging.error(f"内存优化补丁执行时出错: {e}")
            # 如果补丁失败，仍然执行原始方法
            return original_method(self, mapped_df, file_type)
    
    return wrapper

def patch_process_file_completion():
    """
    文件处理完成后的内存管理补丁
    """
    def file_completion_callback():
        # 文件处理完成计数
        memory_optimizer.on_file_processed()
        
        # 每10个文件进行一次内存状态报告
        if memory_optimizer.processed_files % 10 == 0:
            memory_info = memory_optimizer.monitor_memory_usage()
            if memory_info["memory_percent"] > 0:
                logging.info(f"📊 内存状态监控: {memory_info['memory_percent']:.1f}% "
                           f"({memory_info['used_gb']:.2f}GB/{memory_info['total_gb']:.2f}GB)")
    
    return file_completion_callback

def apply_memory_optimization_patches():
    """
    应用所有内存优化补丁
    """
    try:
        # 记录补丁应用
        logging.info("🔧 应用内存优化补丁...")
        
        # 初始化内存监控
        initial_memory = memory_optimizer.monitor_memory_usage()
        if initial_memory["memory_percent"] > 0:
            logging.info(f"📊 当前内存状态: {initial_memory['memory_percent']:.1f}% "
                        f"({initial_memory['used_gb']:.2f}GB/{initial_memory['total_gb']:.2f}GB)")
        
        # 预先执行垃圾回收
        memory_optimizer.force_garbage_collection(detailed_log=False)
        
        # 返回文件完成回调
        return patch_process_file_completion()
        
    except Exception as e:
        logging.error(f"应用内存优化补丁时出错: {e}")
        return None

def get_memory_optimized_batch_size(total_rows, default_method=None):
    """
    获取内存优化的批处理大小
    
    Args:
        total_rows: 总行数
        default_method: 默认的批次计算方法
        
    Returns:
        int: 优化后的批次大小
    """
    try:
        # 使用内存优化器计算批次大小
        if default_method:
            try:
                original_batch_size = default_method(total_rows)
                optimized_batch_size = memory_optimizer.calculate_optimal_batch_size(
                    total_rows, base_batch_size=original_batch_size
                )
                logging.debug(f"🔧 批次大小优化: 原始={original_batch_size}, 优化后={optimized_batch_size}")
                return optimized_batch_size
            except:
                pass
        
        # 直接使用内存优化器
        return memory_optimizer.calculate_optimal_batch_size(total_rows)
        
    except Exception as e:
        logging.warning(f"获取内存优化批次大小时出错: {e}，使用保守默认值")
        # 保守的默认值
        if total_rows > 100000:
            return 2000
        elif total_rows > 10000:
            return 1000
        else:
            return 500

def monitor_and_optimize_memory_during_import():
    """
    在导入过程中监控和优化内存
    """
    try:
        # 检查内存状态并优化
        optimized = memory_optimizer.optimize_memory_if_needed()
        
        if optimized:
            logging.info("🔧 已执行内存优化")
        
        # 返回当前内存状态
        return memory_optimizer.monitor_memory_usage()
        
    except Exception as e:
        logging.warning(f"内存监控和优化时出错: {e}")
        return {"memory_percent": 0, "should_optimize": False}

def emergency_memory_cleanup():
    """
    紧急内存清理函数
    """
    try:
        logging.warning("⚠️ 执行紧急内存清理...")
        
        # 强制垃圾回收
        collected = memory_optimizer.force_garbage_collection(detailed_log=True)
        
        # 检查清理效果
        memory_info = memory_optimizer.monitor_memory_usage()
        
        if memory_info["memory_percent"] > 0:
            logging.info(f"🧹 紧急清理完成: 回收对象 {collected} 个, "
                        f"当前内存 {memory_info['memory_percent']:.1f}%")
        
        return memory_info
        
    except Exception as e:
        logging.error(f"紧急内存清理时出错: {e}")
        return {"memory_percent": 100, "should_optimize": True}  # 假设需要优化 
 