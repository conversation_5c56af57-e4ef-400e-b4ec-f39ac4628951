#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
针对性调试脚本

本文件的功能和实现逻辑：
1. 专门针对现金识别和导出分类问题进行深度调试
2. 增加大量详细日志，追踪每个步骤的执行情况
3. 直接测试SQL查询和函数调用
4. 提供实时的数据状态和执行结果

重点调试内容：
- 现金识别函数是否被正确调用
- SQL查询是否正确执行
- 导出分类规则文件是否正确读取
- CategoryExportWorker是否正常工作
"""

import os
import sys
import pandas as pd
import logging
import configparser
import traceback
from datetime import datetime
import psycopg2

# 设置详细日志
log_file = f'targeted_debug_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class TargetedDebugger:
    """针对性调试器"""
    
    def __init__(self, case_id):
        self.case_id = case_id
        self.conn = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            config = configparser.ConfigParser()
            config.read('db_config.ini')
            
            self.conn = psycopg2.connect(
                host=config['PostgreSQL']['host'],
                port=config['PostgreSQL']['port'],
                database=config['PostgreSQL']['database'],
                user=config['PostgreSQL']['user'],
                password=config['PostgreSQL']['password']
            )
            self.cursor = self.conn.cursor()
            logging.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logging.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def test_cash_recognition_directly(self):
        """直接测试现金识别"""
        logging.info("🧪 直接测试现金识别功能...")
        
        try:
            # 检查执行前状态
            self.cursor.execute('''
                SELECT COUNT(*) FROM "账户交易明细表" 
                WHERE "案件编号" = %s AND "对手户名" = '现金'
            ''', (self.case_id,))
            before_count = self.cursor.fetchone()[0]
            logging.info(f"📊 执行前现金记录数: {before_count}")
            
            # 检查用户反馈的具体关键词
            user_keywords = ['现金存入', '现金支取', '网络ATM取款', 'ATM取款', 'ATM存款', 'ATM取现']
            
            total_potential = 0
            for keyword in user_keywords:
                self.cursor.execute('''
                    SELECT COUNT(*) FROM "账户交易明细表" 
                    WHERE "案件编号" = %s 
                    AND ("对手户名" IS NULL OR "对手户名" = '' OR "对手户名" != '现金')
                    AND ("对手账号" IS NULL OR "对手账号" = '')
                    AND ("对手卡号" IS NULL OR "对手卡号" = '')
                    AND (LOWER("摘要说明") LIKE %s OR LOWER("交易类型") LIKE %s)
                    AND NOT (LOWER("摘要说明") LIKE '%%转账%%' OR LOWER("交易类型") LIKE '%%转账%%')
                ''', (self.case_id, f'%{keyword.lower()}%', f'%{keyword.lower()}%'))
                
                count = self.cursor.fetchone()[0]
                total_potential += count
                
                if count > 0:
                    logging.info(f"🔍 关键词 '{keyword}': {count} 条符合条件的记录")
                    
                    # 显示样本
                    self.cursor.execute('''
                        SELECT "摘要说明", "交易类型", "对手户名"
                        FROM "账户交易明细表" 
                        WHERE "案件编号" = %s 
                        AND ("对手户名" IS NULL OR "对手户名" = '' OR "对手户名" != '现金')
                        AND ("对手账号" IS NULL OR "对手账号" = '')
                        AND ("对手卡号" IS NULL OR "对手卡号" = '')
                        AND (LOWER("摘要说明") LIKE %s OR LOWER("交易类型") LIKE %s)
                        AND NOT (LOWER("摘要说明") LIKE '%%转账%%' OR LOWER("交易类型") LIKE '%%转账%%')
                        LIMIT 2
                    ''', (self.case_id, f'%{keyword.lower()}%', f'%{keyword.lower()}%'))
                    
                    samples = self.cursor.fetchall()
                    for i, sample in enumerate(samples, 1):
                        logging.info(f"   样本{i}: 摘要='{sample[0]}', 类型='{sample[1]}', 对手户名='{sample[2]}'")
            
            logging.info(f"📊 总计符合条件但未标记的记录: {total_potential}")
            
            if total_potential == 0:
                logging.warning("⚠️ 没有找到符合条件但未标记的记录，可能：")
                logging.warning("   1. 所有符合条件的记录已经被标记为现金")
                logging.warning("   2. 关键词匹配条件有问题")
                logging.warning("   3. 数据中确实没有这些关键词")
                return False
            
            # 直接执行现金识别SQL
            logging.info("🚀 直接执行现金识别SQL...")
            
            # 构建现金关键词条件
            cash_keywords = [
                '现金存入', '现金支取', '网络ATM取款', 'ATM取款', 'ATM存款', 'ATM取现',
                '现金', 'ATM', 'atm', '取现', '存现', '现金业务', '现金交易'
            ]
            
            like_conditions = []
            for keyword in cash_keywords:
                like_conditions.append(f"LOWER(\"摘要说明\") LIKE '%{keyword.lower()}%'")
                like_conditions.append(f"LOWER(\"交易类型\") LIKE '%{keyword.lower()}%'")
            
            cash_like_sql = ' OR '.join(like_conditions)
            
            # 例外关键词
            exception_keywords = ['转账', '汇款', '代付', '代收', '转存', '转入', '转出']
            exception_conditions = []
            for ex_kw in exception_keywords:
                exception_conditions.append(f"LOWER(\"摘要说明\") LIKE '%{ex_kw.lower()}%'")
                exception_conditions.append(f"LOWER(\"交易类型\") LIKE '%{ex_kw.lower()}%'")
            
            exception_sql = ' OR '.join(exception_conditions)
            
            # 执行更新
            update_sql = f'''
                UPDATE "账户交易明细表"
                SET "对手户名" = '现金'
                WHERE "案件编号" = %s
                AND ("对手户名" IS NULL OR "对手户名" = '')
                AND ("对手账号" IS NULL OR "对手账号" = '')
                AND ("对手卡号" IS NULL OR "对手卡号" = '')
                AND ({cash_like_sql})
                AND NOT ({exception_sql})
            '''
            
            logging.info("📝 执行的SQL查询:")
            logging.info(f"   更新条件: 案件编号={self.case_id}")
            logging.info(f"   现金关键词数量: {len(cash_keywords)}")
            logging.info(f"   例外关键词数量: {len(exception_keywords)}")
            
            self.cursor.execute(update_sql, (self.case_id,))
            updated_count = self.cursor.rowcount
            
            logging.info(f"📊 SQL执行结果: 更新了 {updated_count} 条记录")
            
            # 提交更改
            self.conn.commit()
            logging.info("✅ 数据库更改已提交")
            
            # 检查执行后状态
            self.cursor.execute('''
                SELECT COUNT(*) FROM "账户交易明细表" 
                WHERE "案件编号" = %s AND "对手户名" = '现金'
            ''', (self.case_id,))
            after_count = self.cursor.fetchone()[0]
            logging.info(f"📊 执行后现金记录数: {after_count}")
            
            actual_increase = after_count - before_count
            logging.info(f"📈 实际增加的现金记录: {actual_increase}")
            
            if actual_increase > 0:
                logging.info("✅ 现金识别成功执行！")
                return True
            else:
                logging.error("❌ 现金识别没有产生预期效果")
                return False
                
        except Exception as e:
            logging.error(f"❌ 测试现金识别时出错: {e}")
            logging.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def test_export_classification_directly(self):
        """直接测试导出分类"""
        logging.info("🧪 直接测试导出分类功能...")
        
        try:
            # 检查规则文件
            rule_file = "表类型匹配规则_导出文件名分类.xlsx"
            
            if not os.path.exists(rule_file):
                logging.error(f"❌ 规则文件不存在: {rule_file}")
                
                # 创建一个基本的规则文件
                logging.info("🔧 创建基本的规则文件...")
                
                # 获取有数据的表
                self.cursor.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_type = 'BASE TABLE'
                """)
                
                all_tables = [row[0] for row in self.cursor.fetchall()]
                tables_with_data = []
                
                for table_name in all_tables:
                    try:
                        self.cursor.execute(f'SELECT COUNT(*) FROM "{table_name}" WHERE "案件编号" = %s', (self.case_id,))
                        count = self.cursor.fetchone()[0]
                        if count > 0:
                            tables_with_data.append({
                                '数据库表名': table_name,
                                '工作表名': self.generate_worksheet_name(table_name),
                                '导出文件名': self.classify_table(table_name),
                                '记录数量': count
                            })
                    except:
                        continue
                
                if tables_with_data:
                    rule_df = pd.DataFrame(tables_with_data)
                    rule_df.to_excel(rule_file, index=False)
                    logging.info(f"✅ 创建了包含 {len(tables_with_data)} 个表的规则文件")
                else:
                    logging.error("❌ 没有找到有数据的表，无法创建规则文件")
                    return False
            
            # 读取规则文件
            try:
                rule_df = pd.read_excel(rule_file)
                logging.info(f"✅ 规则文件读取成功: {len(rule_df)} 行")
                logging.info(f"   列名: {rule_df.columns.tolist()}")
            except Exception as e:
                logging.error(f"❌ 读取规则文件失败: {e}")
                return False
            
            # 检查必要的列
            db_col, ws_col, file_col = None, None, None
            for col in rule_df.columns:
                if '数据库表' in str(col):
                    db_col = col
                if '工作表' in str(col):
                    ws_col = col
                if '导出文件' in str(col):
                    file_col = col
            
            if not (db_col and ws_col and file_col):
                logging.error(f"❌ 规则文件缺少必要列: 数据库表={db_col}, 工作表={ws_col}, 导出文件={file_col}")
                return False
            
            logging.info(f"✅ 找到必要列: 数据库表={db_col}, 工作表={ws_col}, 导出文件={file_col}")
            
            # 构建导出映射
            export_map = {}
            valid_rules = 0
            
            for _, row in rule_df.iterrows():
                try:
                    db = str(row[db_col]).strip() if pd.notna(row[db_col]) else ""
                    ws = str(row[ws_col]).strip() if pd.notna(row[ws_col]) else ""
                    fn = str(row[file_col]).strip() if pd.notna(row[file_col]) else ""
                    
                    if db and ws and fn and db != 'nan':
                        export_map.setdefault(fn, {}).setdefault(ws, []).append(db)
                        valid_rules += 1
                except Exception as e:
                    logging.warning(f"处理规则行时出错: {e}")
                    continue
            
            logging.info(f"📊 导出映射构建结果:")
            logging.info(f"   有效规则数: {valid_rules}")
            logging.info(f"   导出文件数: {len(export_map)}")
            
            if len(export_map) == 0:
                logging.error("❌ 没有构建出任何导出映射")
                return False
            
            # 显示导出映射详情
            for file_name, ws_dict in export_map.items():
                ws_count = len(ws_dict)
                table_count = sum(len(tables) for tables in ws_dict.values())
                logging.info(f"   📄 {file_name}: {ws_count} 个工作表, {table_count} 个表")
            
            logging.info("✅ 导出分类规则验证成功！")
            return True
            
        except Exception as e:
            logging.error(f"❌ 测试导出分类时出错: {e}")
            logging.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def classify_table(self, table_name):
        """分类表类型"""
        table_name_lower = table_name.lower()
        
        if "医保" in table_name_lower:
            return "医保信息汇总"
        elif "电话" in table_name_lower or "虚拟运营商" in table_name_lower:
            return "通讯信息汇总"
        elif "公安" in table_name_lower:
            return "公安信息汇总"
        elif "账户" in table_name_lower or "开户" in table_name_lower or "银行" in table_name_lower:
            if "交易明细" in table_name_lower:
                return "银行交易明细汇总"
            else:
                return "银行账户信息汇总"
        elif "税务" in table_name_lower or "纳税" in table_name_lower:
            return "税务纳税信息汇总"
        elif "发票" in table_name_lower:
            return "增值税发票信息汇总"
        elif "理财" in table_name_lower:
            return "理财信息汇总"
        elif "工商" in table_name_lower:
            return "工商信息汇总"
        elif "保险" in table_name_lower:
            return "保险信息汇总"
        else:
            return "其他信息汇总"
    
    def generate_worksheet_name(self, table_name):
        """生成工作表名"""
        name = table_name
        
        # 移除常见前缀
        prefixes = ["公安部_", "国家税务总局_", "税务_", "医保_", "电话_", "虚拟运营商_", 
                   "理财登记中心_", "金融理财_", "银保信_", "工商_"]
        
        for prefix in prefixes:
            if name.startswith(prefix):
                name = name[len(prefix):]
                break
        
        # 移除常见后缀
        suffixes = ["表", "信息表", "数据表", "明细表", "登记表"]
        
        for suffix in suffixes:
            if name.endswith(suffix):
                name = name[:-len(suffix)]
                break
        
        # 限制长度
        if len(name) > 31:
            name = name[:28] + "..."
        
        return name or table_name[:31]
    
    def run_targeted_debug(self):
        """运行针对性调试"""
        logging.info("🚀 开始针对性调试...")
        
        if not self.connect_database():
            return False
        
        try:
            # 测试现金识别
            logging.info("\n" + "="*50)
            logging.info("🔍 现金识别功能测试")
            logging.info("="*50)
            
            cash_success = self.test_cash_recognition_directly()
            
            # 测试导出分类
            logging.info("\n" + "="*50)
            logging.info("🔍 导出分类功能测试")
            logging.info("="*50)
            
            export_success = self.test_export_classification_directly()
            
            # 总结
            logging.info("\n" + "="*50)
            logging.info("📊 调试结果总结")
            logging.info("="*50)
            
            logging.info(f"现金识别: {'✅ 成功' if cash_success else '❌ 失败'}")
            logging.info(f"导出分类: {'✅ 成功' if export_success else '❌ 失败'}")
            
            if cash_success and export_success:
                logging.info("🎉 两个问题都已解决！")
            elif cash_success:
                logging.info("✅ 现金识别问题已解决，导出分类仍需修复")
            elif export_success:
                logging.info("✅ 导出分类问题已解决，现金识别仍需修复")
            else:
                logging.info("❌ 两个问题都需要进一步修复")
            
            return cash_success and export_success
            
        finally:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()

def main():
    """主函数"""
    print("🎯 针对性调试工具")
    print("=" * 50)
    
    case_id = input("请输入案件编号: ").strip()
    if not case_id:
        print("❌ 案件编号不能为空")
        return 1
    
    debugger = TargetedDebugger(case_id)
    
    print(f"\n🚀 开始针对性调试案件: {case_id}")
    print(f"📄 详细日志保存到: {log_file}")
    
    success = debugger.run_targeted_debug()
    
    if success:
        print("\n✅ 调试完成，问题已解决！")
        return 0
    else:
        print("\n❌ 调试发现问题，请查看日志文件")
        return 1

if __name__ == "__main__":
    exit(main())
