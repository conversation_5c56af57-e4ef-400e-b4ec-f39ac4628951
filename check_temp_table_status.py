#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查临时账户交易明细表的转存状态
功能：
1. 检查临时表中是否还有数据
2. 检查主表中的数据情况
3. 如果临时表有数据，执行转存操作
4. 验证转存结果
"""

import psycopg2
import configparser
import logging
import io
import csv

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_db_connection():
    """获取数据库连接"""
    config = configparser.ConfigParser()
    config.read('db_config.ini', encoding='utf-8')
    
    return psycopg2.connect(
        host=config['PostgreSQL']['host'],
        database=config['PostgreSQL']['database'],
        user=config['PostgreSQL']['user'],
        password=config['PostgreSQL']['password'],
        port=config['PostgreSQL']['port']
    )

def check_table_data():
    """检查临时表和主表的数据情况"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查临时表数据
        cursor.execute("SELECT COUNT(*) FROM 临时账户交易明细表")
        temp_count = cursor.fetchone()[0]
        
        # 检查主表数据
        cursor.execute("SELECT COUNT(*) FROM 账户交易明细表")
        main_count = cursor.fetchone()[0]
        
        logging.info(f"📊 数据统计:")
        logging.info(f"   临时账户交易明细表: {temp_count:,} 条记录")
        logging.info(f"   账户交易明细表: {main_count:,} 条记录")
        
        if temp_count > 0:
            # 检查临时表中的案件分布
            cursor.execute("""
                SELECT 案件编号, COUNT(*) as 记录数
                FROM 临时账户交易明细表 
                GROUP BY 案件编号 
                ORDER BY 记录数 DESC
                LIMIT 10
            """)
            case_distribution = cursor.fetchall()
            
            logging.info("📋 临时表中的案件分布（前10个）:")
            for case_id, count in case_distribution:
                logging.info(f"   案件 {case_id}: {count:,} 条记录")
            
            # 检查临时表中的数据样本
            cursor.execute("""
                SELECT 案件编号, 交易户名, 交易账号, 交易日期, 交易金额, 导入批次
                FROM 临时账户交易明细表 
                ORDER BY id DESC
                LIMIT 5
            """)
            samples = cursor.fetchall()
            
            logging.info("📄 临时表数据样本:")
            for i, (case_id, name, account, date, amount, batch) in enumerate(samples, 1):
                logging.info(f"   样本{i}: 案件{case_id} - {name} - {account} - {date} - {amount} - 批次{batch}")
        
        cursor.close()
        conn.close()
        
        return temp_count, main_count
        
    except Exception as e:
        logging.error(f"检查表数据时出错: {e}")
        return 0, 0

def get_column_types(cursor, table_name):
    """获取表的字段类型信息"""
    cursor.execute("""
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = %s
        ORDER BY ordinal_position
    """, (table_name,))
    
    column_info = {}
    for col_name, data_type, is_nullable in cursor.fetchall():
        column_info[col_name] = {
            'data_type': data_type,
            'is_nullable': is_nullable == 'YES'
        }
    
    return column_info

def clean_value_for_postgres(value, column_name, column_info):
    """为PostgreSQL清理数据值"""
    if value is None:
        return None
    
    # 获取字段类型信息
    if column_name not in column_info:
        return str(value) if value is not None else None
    
    data_type = column_info[column_name]['data_type']
    
    # 处理numeric类型字段
    if data_type in ['numeric', 'decimal', 'integer', 'bigint', 'smallint', 'real', 'double precision']:
        if isinstance(value, str):
            # 清理字符串值
            cleaned_value = value.strip()
            if cleaned_value == '' or cleaned_value == '-' or cleaned_value.lower() == 'null':
                return None
            try:
                # 尝试转换为数字以验证有效性
                float(cleaned_value)
                return cleaned_value
            except ValueError:
                logging.warning(f"字段 {column_name} 的值 '{value}' 无法转换为数字，设为NULL")
                return None
        elif isinstance(value, (int, float)):
            return str(value)
        else:
            return None
    
    # 处理日期时间类型
    elif data_type in ['timestamp', 'date', 'time']:
        if isinstance(value, str):
            cleaned_value = value.strip()
            if cleaned_value == '' or cleaned_value.lower() == 'null':
                return None
            return cleaned_value
        else:
            return str(value) if value is not None else None
    
    # 处理文本类型
    else:
        if isinstance(value, str):
            # 清理特殊字符
            cleaned_value = value.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
            return cleaned_value
        else:
            return str(value) if value is not None else None

def transfer_temp_data():
    """执行临时表到主表的数据转存"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        logging.info("🔄 开始执行临时表到主表的数据转存...")
        
        # 获取临时表结构
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = '临时账户交易明细表'
            ORDER BY ordinal_position
        """)
        temp_columns = [col[0] for col in cursor.fetchall()]
        
        # 获取主表结构
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = '账户交易明细表'
            ORDER BY ordinal_position
        """)
        main_columns = [col[0] for col in cursor.fetchall()]
        
        # 找到两张表的公共列
        common_columns = list(set(temp_columns) & set(main_columns))
        logging.info(f"找到 {len(common_columns)} 个公共字段: {common_columns[:10]}...")
        
        # 获取主表的字段类型信息
        main_column_info = get_column_types(cursor, '账户交易明细表')
        
        if common_columns:
            # 查询需要转存的数据
            columns_str = ', '.join([f'"{col}"' for col in common_columns])
            select_query = f"""
                SELECT {columns_str}
                FROM 临时账户交易明细表
                ORDER BY id
            """
            
            cursor.execute(select_query)
            temp_data = cursor.fetchall()
            
            if temp_data:
                logging.info(f"准备转存 {len(temp_data):,} 条记录")
                
                # 使用改进的COPY命令进行数据转存
                csv_buffer = io.StringIO()
                csv_writer = csv.writer(csv_buffer, delimiter='\t', quoting=csv.QUOTE_MINIMAL)
                
                # 清理数据并写入CSV缓冲区
                processed_count = 0
                for row_idx, row in enumerate(temp_data):
                    cleaned_row = []
                    for col_idx, value in enumerate(row):
                        column_name = common_columns[col_idx]
                        cleaned_value = clean_value_for_postgres(value, column_name, main_column_info)
                        
                        # 对于CSV，None值用空字符串表示
                        if cleaned_value is None:
                            cleaned_row.append('')
                        else:
                            cleaned_row.append(str(cleaned_value))
                    
                    csv_writer.writerow(cleaned_row)
                    processed_count += 1
                    
                    # 每处理10000条记录显示进度
                    if processed_count % 10000 == 0:
                        logging.info(f"已处理 {processed_count:,} 条记录...")
                
                logging.info(f"数据清理完成，共处理 {processed_count:,} 条记录")
                
                # 重置缓冲区位置
                csv_buffer.seek(0)
                
                # 使用COPY命令批量插入数据
                try:
                    logging.info("使用COPY命令批量插入数据...")
                    cursor.copy_from(
                        csv_buffer,
                        '账户交易明细表',
                        columns=common_columns,
                        sep='\t',
                        null=''  # 指定NULL值的表示方式
                    )
                    rows_affected = len(temp_data)
                    logging.info(f"✅ 使用COPY命令成功转存 {rows_affected:,} 条数据")
                    
                except Exception as copy_error:
                    logging.warning(f"COPY命令失败: {copy_error}")
                    logging.info("回退到INSERT方式...")
                    
                    # 回退到INSERT方式，但使用清理后的数据
                    csv_buffer.seek(0)
                    csv_reader = csv.reader(csv_buffer, delimiter='\t')
                    
                    insert_count = 0
                    batch_size = 1000
                    batch_data = []
                    
                    for row in csv_reader:
                        # 将空字符串转换为None
                        cleaned_values = [None if val == '' else val for val in row]
                        batch_data.append(cleaned_values)
                        
                        if len(batch_data) >= batch_size:
                            # 批量插入
                            try:
                                placeholders = ', '.join(['%s'] * len(common_columns))
                                insert_query = f"""
                                    INSERT INTO 账户交易明细表 ({columns_str})
                                    VALUES ({placeholders})
                                """
                                cursor.executemany(insert_query, batch_data)
                                insert_count += len(batch_data)
                                logging.info(f"已插入 {insert_count:,} 条记录...")
                                batch_data = []
                            except Exception as batch_error:
                                logging.error(f"批量插入失败: {batch_error}")
                                # 逐条插入
                                for values in batch_data:
                                    try:
                                        cursor.execute(insert_query, values)
                                        insert_count += 1
                                    except Exception as single_error:
                                        logging.error(f"插入单条记录失败: {single_error}")
                                batch_data = []
                    
                    # 处理剩余的数据
                    if batch_data:
                        try:
                            cursor.executemany(insert_query, batch_data)
                            insert_count += len(batch_data)
                        except Exception as final_error:
                            logging.error(f"最终批量插入失败: {final_error}")
                    
                    logging.info(f"✅ 使用INSERT方式成功转存 {insert_count:,} 条数据")
                
                csv_buffer.close()
                
                # 提交事务
                conn.commit()
                logging.info("✅ 事务提交成功")
                
                # 验证转存结果
                cursor.execute("SELECT COUNT(*) FROM 账户交易明细表")
                final_count = cursor.fetchone()[0]
                logging.info(f"转存后主表总记录数: {final_count:,}")
                
                cursor.close()
                conn.close()
                return True
            else:
                logging.warning("临时表中没有数据需要转存")
                cursor.close()
                conn.close()
                return False
        else:
            logging.error("没有找到公共字段，无法转存")
            cursor.close()
            conn.close()
            return False
            
    except Exception as e:
        logging.error(f"转存过程中发生错误: {e}")
        try:
            conn.rollback()
            cursor.close()
            conn.close()
        except:
            pass
        return False

def main():
    """主函数"""
    logging.info("=" * 60)
    logging.info("检查临时账户交易明细表转存状态")
    logging.info("=" * 60)
    
    # 1. 检查表数据情况
    temp_count, main_count = check_table_data()
    
    if temp_count > 0:
        logging.info(f"🔍 发现临时表中有 {temp_count:,} 条记录需要转存")
        
        # 2. 执行转存操作
        success = transfer_temp_data()
        
        if success:
            # 3. 再次检查转存结果
            logging.info("🔄 验证转存结果...")
            final_temp_count, final_main_count = check_table_data()
            
            logging.info("=" * 60)
            logging.info("转存结果:")
            logging.info(f"转存前 - 临时表: {temp_count:,}, 主表: {main_count:,}")
            logging.info(f"转存后 - 临时表: {final_temp_count:,}, 主表: {final_main_count:,}")
            
            if final_main_count > main_count:
                logging.info(f"✅ 转存成功！新增 {final_main_count - main_count:,} 条记录")
            else:
                logging.error("❌ 转存可能失败，主表记录数未增加")
        else:
            logging.error("❌ 转存失败")
    else:
        logging.info("✅ 临时表中没有数据，无需转存")
    
    logging.info("=" * 60)

if __name__ == "__main__":
    main()
