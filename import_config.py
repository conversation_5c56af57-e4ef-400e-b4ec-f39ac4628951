#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入数据配置管理

本文件功能和实现逻辑：
- 管理导入数据的各种配置参数
- 提供动态调整超时时间的功能
- 根据数据量和系统资源自动优化参数
- 支持用户自定义配置
"""

import os
import json
import logging
from typing import Dict, Any

class ImportConfig:
    """导入配置管理类"""
    
    def __init__(self):
        self.config_file = "import_settings.json"
        self.default_config = {
            # 数据库超时设置（小时）
            "database_timeout": {
                "base_timeout_hours": 2,  # 基础超时时间：2小时
                "max_timeout_hours": 12,  # 最大超时时间：12小时
                "timeout_per_10_files_hours": 1,  # 每10个文件增加1小时
                "lock_timeout_minutes": 5  # 锁等待超时：5分钟
            },
            
            # 批处理设置
            "batch_processing": {
                "small_dataset_batch_size": 200,  # 小数据集批次大小
                "medium_dataset_batch_size": 500,  # 中等数据集批次大小
                "large_dataset_batch_size": 1000,  # 大数据集批次大小
                "small_dataset_threshold": 1000,  # 小数据集阈值
                "large_dataset_threshold": 10000,  # 大数据集阈值
                "transaction_batch_count_small": 1,  # 小数据集事务批次数
                "transaction_batch_count_medium": 5,  # 中等数据集事务批次数
                "transaction_batch_count_large": 3,  # 大数据集事务批次数
                "max_batches_per_transaction": 20  # 每个事务最大批次数
            },
            
            # 内存管理设置
            "memory_management": {
                "memory_check_interval": 10,  # 每10批检查一次内存
                "memory_warning_threshold": 85,  # 内存警告阈值85%
                "memory_critical_threshold": 90,  # 内存严重阈值90%
                "gc_interval": 10,  # 垃圾回收间隔（批次）
                "memory_pause_seconds": 2  # 内存过高时暂停秒数
            },
            
            # 性能优化设置
            "performance": {
                "checkpoint_completion_target": 0.9,  # 检查点完成目标
                "enable_parallel_processing": False,  # 是否启用并行处理
                "max_worker_threads": 2,  # 最大工作线程数
                "connection_pool_size": 5  # 连接池大小
            },
            
            # 错误处理设置
            "error_handling": {
                "max_retry_attempts": 3,  # 最大重试次数
                "retry_delay_seconds": 2,  # 重试延迟秒数
                "enable_single_row_fallback": True,  # 启用逐行插入回退
                "log_batch_details": True  # 记录批次详细信息
            }
        }
        
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                
                # 合并用户配置和默认配置
                config = self.default_config.copy()
                self._deep_update(config, user_config)
                
                logging.info(f"✅ 已加载用户配置: {self.config_file}")
                return config
            else:
                logging.info("使用默认配置")
                return self.default_config.copy()
                
        except Exception as e:
            logging.warning(f"加载配置文件失败，使用默认配置: {e}")
            return self.default_config.copy()
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            logging.info(f"✅ 配置已保存到: {self.config_file}")
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
    
    def _deep_update(self, base_dict: dict, update_dict: dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def get_database_timeout(self, file_count: int = 1) -> int:
        """
        获取数据库超时时间（毫秒）
        
        Args:
            file_count: 文件数量
            
        Returns:
            超时时间（毫秒）
        """
        db_config = self.config["database_timeout"]
        
        # 基础超时时间
        base_timeout_hours = db_config["base_timeout_hours"]
        
        # 根据文件数量动态调整
        if file_count > 10:
            additional_hours = (file_count // 10) * db_config["timeout_per_10_files_hours"]
            total_hours = min(
                base_timeout_hours + additional_hours,
                db_config["max_timeout_hours"]
            )
        else:
            total_hours = base_timeout_hours
        
        # 转换为毫秒
        timeout_ms = int(total_hours * 60 * 60 * 1000)
        
        logging.info(f"数据库超时设置: {total_hours}小时 ({file_count}个文件)")
        return timeout_ms
    
    def get_batch_config(self, total_rows: int) -> Dict[str, int]:
        """
        获取批处理配置
        
        Args:
            total_rows: 总行数
            
        Returns:
            批处理配置字典
        """
        batch_config = self.config["batch_processing"]
        
        if total_rows < batch_config["small_dataset_threshold"]:
            return {
                "batch_size": batch_config["small_dataset_batch_size"],
                "transaction_batch_count": batch_config["transaction_batch_count_small"]
            }
        elif total_rows < batch_config["large_dataset_threshold"]:
            return {
                "batch_size": batch_config["medium_dataset_batch_size"],
                "transaction_batch_count": batch_config["transaction_batch_count_medium"]
            }
        else:
            return {
                "batch_size": batch_config["large_dataset_batch_size"],
                "transaction_batch_count": batch_config["transaction_batch_count_large"]
            }
    
    def get_memory_config(self) -> Dict[str, Any]:
        """获取内存管理配置"""
        return self.config["memory_management"].copy()
    
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        return self.config["performance"].copy()
    
    def get_error_handling_config(self) -> Dict[str, Any]:
        """获取错误处理配置"""
        return self.config["error_handling"].copy()
    
    def update_timeout_for_large_import(self, estimated_hours: float):
        """
        为大型导入任务更新超时时间
        
        Args:
            estimated_hours: 预估的导入时间（小时）
        """
        if estimated_hours > self.config["database_timeout"]["base_timeout_hours"]:
            # 设置为预估时间的1.5倍，但不超过最大值
            new_timeout = min(
                estimated_hours * 1.5,
                self.config["database_timeout"]["max_timeout_hours"]
            )
            
            self.config["database_timeout"]["base_timeout_hours"] = new_timeout
            logging.info(f"已调整数据库超时时间为 {new_timeout} 小时（预估导入时间: {estimated_hours} 小时）")
    
    def create_sample_config_file(self):
        """创建示例配置文件"""
        sample_file = "import_settings_sample.json"
        try:
            with open(sample_file, 'w', encoding='utf-8') as f:
                json.dump(self.default_config, f, indent=2, ensure_ascii=False)
            
            logging.info(f"✅ 已创建示例配置文件: {sample_file}")
            logging.info("您可以复制此文件为 import_settings.json 并根据需要修改配置")
            
        except Exception as e:
            logging.error(f"创建示例配置文件失败: {e}")

# 全局配置实例
import_config = ImportConfig()

def get_import_config() -> ImportConfig:
    """获取导入配置实例"""
    return import_config

def create_config_file_if_not_exists():
    """如果配置文件不存在，创建示例配置文件"""
    if not os.path.exists("import_settings.json"):
        import_config.create_sample_config_file()
        logging.info("💡 提示：您可以通过修改 import_settings.json 来自定义导入配置")

# 模块初始化时检查配置文件
if __name__ != "__main__":
    create_config_file_if_not_exists()
