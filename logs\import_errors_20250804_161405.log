2025-08-04 21:37:16.371 - CRITICAL - [MainThread:32068] - enhanced_logging_patch.py:322 - monitored_exit() - 🚨 程序即将退出! 退出码: 0
2025-08-04 21:37:16.385 - CRITICAL - [MainThread:32068] - enhanced_logging_patch.py:323 - monitored_exit() - 退出时的堆栈跟踪:
2025-08-04 21:37:16.399 - CRITICAL - [MainThread:32068] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\main.py", line 327, in <module>
    main()
2025-08-04 21:37:16.400 - CRITICAL - [MainThread:32068] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\main.py", line 324, in main
    sys.exit(app.exec())
2025-08-04 21:37:16.400 - CRITICAL - [MainThread:32068] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\enhanced_logging_patch.py", line 326, in monitored_exit
    stack = traceback.format_stack()
