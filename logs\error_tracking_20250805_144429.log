2025-08-05 14:44:29,241 - error_tracking - ERROR - test_enhanced_logging.py:66 - test_different_log_levels - 这是错误跟踪日志
2025-08-05 14:44:29,242 - error_tracking - CRITICAL - test_enhanced_logging.py:67 - test_different_log_levels - 这是错误跟踪严重日志
2025-08-05 14:44:29,250 - error_tracking - ERROR - test_enhanced_logging.py:93 - test_exception_handling - 异常详情: Traceback (most recent call last):
  File "G:\数据分析系统20250725\test_enhanced_logging.py", line 86, in test_exception_handling
    cause_exception()
    ~~~~~~~~~~~~~~~^^
  File "G:\数据分析系统20250725\test_enhanced_logging.py", line 83, in cause_exception
    raise ValueError("这是一个测试异常")
ValueError: 这是一个测试异常

2025-08-05 14:44:29,603 - error_tracking - CRITICAL - test_enhanced_logging.py:215 - simulate_crash_scenario - 🚨 模拟程序崩溃: {'exception_type': 'MemoryError', 'exception_message': '内存不足', 'timestamp': '2025-08-05T14:44:29.603490', 'thread_id': 12345, 'process_id': 26856}
