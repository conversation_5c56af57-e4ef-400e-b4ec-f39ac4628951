#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查字段映射问题

本文件的功能和实现逻辑：
1. 检查字段匹配规则数据库中的IP地址、MAC地址、交易发生地映射
2. 分析字段映射逻辑中的潜在问题
3. 提供修复建议
"""

import logging
from datetime import datetime
from database_setup import get_db_connection

# 设置日志
log_file = f'check_field_mapping_issue_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def check_field_mapping_rules():
    """检查字段匹配规则数据库中的映射"""
    logging.info("🔍 检查字段匹配规则数据库中的映射...")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询与IP地址、MAC地址、交易发生地相关的映射规则
        cursor.execute("""
            SELECT 规则ID, 表名, 配置名称, 创建日期, 数据库表字段, 待导入表字段
            FROM 字段匹配规则
            WHERE 数据库表字段 IN ('IP地址', 'MAC地址', '交易发生地')
               OR 待导入表字段 IN ('IP地址', 'MAC地址', '交易发生地')
            ORDER BY 表名, 配置名称, 数据库表字段
        """)
        
        rules = cursor.fetchall()
        
        if not rules:
            logging.warning("❌ 未找到相关的字段映射规则")
            return False
        
        logging.info(f"📊 找到 {len(rules)} 条相关映射规则:")
        logging.info("=" * 80)
        
        problem_rules = []
        
        for rule in rules:
            rule_id, table_name, config_name, create_date, db_field, import_field = rule
            logging.info(f"规则ID: {rule_id}")
            logging.info(f"表名: {table_name}")
            logging.info(f"配置名称: {config_name}")
            logging.info(f"创建日期: {create_date}")
            logging.info(f"数据库字段: {db_field}")
            logging.info(f"待导入字段: {import_field}")
            
            # 检查是否存在错误映射
            if db_field == '交易发生地' and import_field in ['IP地址', 'MAC地址']:
                logging.error(f"❌ 发现错误映射: {import_field} -> {db_field}")
                problem_rules.append(rule)
            elif db_field in ['IP地址', 'MAC地址'] and import_field == '交易发生地':
                logging.error(f"❌ 发现错误映射: {import_field} -> {db_field}")
                problem_rules.append(rule)
            else:
                logging.info(f"✅ 映射正常: {import_field} -> {db_field}")
            
            logging.info("-" * 40)
        
        cursor.close()
        conn.close()
        
        if problem_rules:
            logging.error(f"❌ 发现 {len(problem_rules)} 条错误映射规则")
            return problem_rules
        else:
            logging.info("✅ 所有映射规则正常")
            return []
        
    except Exception as e:
        logging.error(f"❌ 检查字段映射规则时出错: {e}")
        return False

def check_recent_import_rules():
    """检查最近的导入规则"""
    logging.info("\n🔍 检查最近的导入规则...")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询最近创建的规则
        cursor.execute("""
            SELECT 规则ID, 表名, 配置名称, 创建日期, 数据库表字段, 待导入表字段
            FROM 字段匹配规则
            WHERE 表名 LIKE '%交易明细表%'
            ORDER BY 创建日期 DESC, 规则ID DESC
            LIMIT 50
        """)
        
        recent_rules = cursor.fetchall()
        
        if not recent_rules:
            logging.warning("❌ 未找到最近的交易明细表映射规则")
            return False
        
        logging.info(f"📊 最近的 {len(recent_rules)} 条交易明细表映射规则:")
        logging.info("=" * 80)
        
        # 按配置名称分组
        rules_by_config = {}
        for rule in recent_rules:
            rule_id, table_name, config_name, create_date, db_field, import_field = rule
            if config_name not in rules_by_config:
                rules_by_config[config_name] = []
            rules_by_config[config_name].append(rule)
        
        problem_configs = []
        
        for config_name, rules in rules_by_config.items():
            logging.info(f"配置名称: {config_name}")
            
            # 检查这个配置中的IP地址、MAC地址、交易发生地映射
            ip_mapping = None
            mac_mapping = None
            location_mapping = None
            
            for rule in rules:
                rule_id, table_name, config_name, create_date, db_field, import_field = rule
                
                if db_field == 'IP地址':
                    ip_mapping = (db_field, import_field)
                elif db_field == 'MAC地址':
                    mac_mapping = (db_field, import_field)
                elif db_field == '交易发生地':
                    location_mapping = (db_field, import_field)
            
            logging.info(f"  IP地址映射: {ip_mapping}")
            logging.info(f"  MAC地址映射: {mac_mapping}")
            logging.info(f"  交易发生地映射: {location_mapping}")
            
            # 检查是否存在问题
            has_problem = False
            if location_mapping and location_mapping[1] in ['IP地址', 'MAC地址']:
                logging.error(f"  ❌ 交易发生地字段映射错误: {location_mapping}")
                has_problem = True
            
            if ip_mapping and ip_mapping[1] == '交易发生地':
                logging.error(f"  ❌ IP地址字段映射错误: {ip_mapping}")
                has_problem = True
                
            if mac_mapping and mac_mapping[1] == '交易发生地':
                logging.error(f"  ❌ MAC地址字段映射错误: {mac_mapping}")
                has_problem = True
            
            if has_problem:
                problem_configs.append(config_name)
            else:
                logging.info(f"  ✅ 配置 {config_name} 映射正常")
            
            logging.info("-" * 40)
        
        cursor.close()
        conn.close()
        
        if problem_configs:
            logging.error(f"❌ 发现 {len(problem_configs)} 个有问题的配置: {problem_configs}")
            return problem_configs
        else:
            logging.info("✅ 所有配置映射正常")
            return []
        
    except Exception as e:
        logging.error(f"❌ 检查最近导入规则时出错: {e}")
        return False

def analyze_field_mapping_logic():
    """分析字段映射逻辑"""
    logging.info("\n🔍 分析字段映射逻辑...")
    
    try:
        # 检查temp_import_data.py中的字段映射逻辑
        with open('temp_import_data.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找字段映射相关的代码段
        mapping_sections = []
        
        # 查找字段映射函数
        if 'def map_fields_to_database' in content:
            logging.info("✅ 找到字段映射函数 map_fields_to_database")
            mapping_sections.append("map_fields_to_database")
        
        if 'def create_field_mapping' in content:
            logging.info("✅ 找到字段映射函数 create_field_mapping")
            mapping_sections.append("create_field_mapping")
        
        # 查找可能的问题模式
        potential_issues = []
        
        # 检查是否有硬编码的字段映射
        if '交易发生地' in content and ('IP地址' in content or 'MAC地址' in content):
            logging.warning("⚠️ 发现交易发生地与IP/MAC地址在同一文件中，可能存在映射混淆")
            potential_issues.append("字段名称可能存在混淆")
        
        # 检查字段顺序相关的代码
        if 'columns.index' in content or 'enumerate' in content:
            logging.warning("⚠️ 发现基于索引的字段处理，可能存在顺序错误")
            potential_issues.append("可能存在字段索引错误")
        
        if potential_issues:
            logging.error(f"❌ 发现潜在问题: {potential_issues}")
            return potential_issues
        else:
            logging.info("✅ 字段映射逻辑分析未发现明显问题")
            return []
        
    except Exception as e:
        logging.error(f"❌ 分析字段映射逻辑时出错: {e}")
        return False

def generate_fix_suggestions(problem_rules, problem_configs, logic_issues):
    """生成修复建议"""
    logging.info("\n💡 生成修复建议...")
    
    suggestions = []
    
    if problem_rules:
        suggestions.append("1. 修复错误的字段映射规则:")
        for rule in problem_rules:
            rule_id, table_name, config_name, create_date, db_field, import_field = rule
            suggestions.append(f"   - 规则ID {rule_id}: 将 '{import_field}' -> '{db_field}' 修正为正确映射")
    
    if problem_configs:
        suggestions.append("2. 重新生成有问题的配置:")
        for config in problem_configs:
            suggestions.append(f"   - 重新生成配置 '{config}' 的字段映射规则")
    
    if logic_issues:
        suggestions.append("3. 修复字段映射逻辑问题:")
        for issue in logic_issues:
            suggestions.append(f"   - {issue}")
    
    # 通用修复建议
    suggestions.extend([
        "4. 通用修复步骤:",
        "   - 检查字段匹配规则表中的错误映射",
        "   - 重新运行字段映射规则生成",
        "   - 验证数据库表结构与映射规则的一致性",
        "   - 测试导入功能确保字段正确映射"
    ])
    
    logging.info("修复建议:")
    for suggestion in suggestions:
        logging.info(suggestion)
    
    return suggestions

def main():
    """主函数"""
    print(f"🔍 开始检查字段映射问题")
    print(f"📄 详细日志保存到: {log_file}")
    
    # 检查字段映射规则
    logging.info("=" * 60)
    logging.info("步骤1: 检查字段映射规则数据库")
    logging.info("=" * 60)
    problem_rules = check_field_mapping_rules()
    
    # 检查最近的导入规则
    logging.info("=" * 60)
    logging.info("步骤2: 检查最近的导入规则")
    logging.info("=" * 60)
    problem_configs = check_recent_import_rules()
    
    # 分析字段映射逻辑
    logging.info("=" * 60)
    logging.info("步骤3: 分析字段映射逻辑")
    logging.info("=" * 60)
    logic_issues = analyze_field_mapping_logic()
    
    # 生成修复建议
    logging.info("=" * 60)
    logging.info("步骤4: 生成修复建议")
    logging.info("=" * 60)
    suggestions = generate_fix_suggestions(problem_rules or [], problem_configs or [], logic_issues or [])
    
    # 总结
    print("\\n" + "=" * 60)
    print("🎯 检查结果总结")
    print("=" * 60)
    
    has_issues = bool(problem_rules or problem_configs or logic_issues)
    
    if has_issues:
        print("❌ 发现字段映射问题！")
        if problem_rules:
            print(f"   - 错误映射规则: {len(problem_rules)} 条")
        if problem_configs:
            print(f"   - 有问题的配置: {len(problem_configs)} 个")
        if logic_issues:
            print(f"   - 逻辑问题: {len(logic_issues)} 个")
        
        print("\\n💡 修复建议已生成，请查看日志文件获取详细信息")
        return 1
    else:
        print("✅ 未发现明显的字段映射问题")
        print("\\n💡 建议:")
        print("1. 检查具体的导入日志文件")
        print("2. 验证Excel文件中的字段名称")
        print("3. 确认字段映射规则的生成过程")
        return 0

if __name__ == "__main__":
    exit(main())
