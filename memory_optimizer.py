# memory_optimizer.py
# 内存优化模块 - 解决大数据量导入时的内存溢出问题

import logging
import gc
import os
import time

class MemoryOptimizer:
    """
    内存优化器类
    
    功能说明：
    - 实时监控内存使用情况
    - 自动垃圾回收和内存清理
    - 动态调整批处理大小
    - 提供内存不足时的降级策略
    """
    
    def __init__(self, memory_threshold=80, gc_interval=50):
        """
        初始化内存优化器
        
        Args:
            memory_threshold: 内存使用率阈值（%），超过此值时触发优化
            gc_interval: 垃圾回收间隔（处理文件数）
        """
        self.memory_threshold = memory_threshold
        self.gc_interval = gc_interval
        self.processed_files = 0
        self.memory_constrained_mode = False
        self.last_gc_time = time.time()
        
        # 导入必要的内存监控模块
        try:
            import psutil
            self.psutil_available = True
            logging.info("✅ 内存监控模块已加载")
            
            # 记录初始内存状态
            initial_memory = psutil.virtual_memory()
            logging.info(f"📊 系统内存信息: 总计 {initial_memory.total/(1024**3):.1f}GB, "
                        f"可用 {initial_memory.available/(1024**3):.1f}GB, "
                        f"使用率 {initial_memory.percent:.1f}%")
            
        except ImportError:
            self.psutil_available = False
            logging.warning("⚠️ psutil模块未安装，无法进行详细内存监控")
            logging.warning("💡 建议安装: pip install psutil")

    def monitor_memory_usage(self):
        """
        实时监控内存使用情况
        
        Returns:
            dict: 包含内存使用信息的字典
        """
        if not self.psutil_available:
            return {
                "memory_percent": 0, 
                "available_gb": 0, 
                "used_gb": 0, 
                "should_optimize": False,
                "total_gb": 0
            }
        
        try:
            import psutil
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            available_gb = memory.available / (1024**3)
            used_gb = memory.used / (1024**3)
            total_gb = memory.total / (1024**3)
            
            should_optimize = memory_percent > self.memory_threshold
            
            if should_optimize:
                logging.warning(f"⚠️ 内存使用率过高: {memory_percent:.1f}% "
                              f"(已用: {used_gb:.2f}GB, 可用: {available_gb:.2f}GB)")
            
            return {
                "memory_percent": memory_percent,
                "available_gb": available_gb,
                "used_gb": used_gb,
                "total_gb": total_gb,
                "should_optimize": should_optimize
            }
            
        except Exception as e:
            logging.error(f"监控内存使用时出错: {e}")
            return {
                "memory_percent": 0, 
                "available_gb": 0, 
                "used_gb": 0, 
                "total_gb": 0,
                "should_optimize": False
            }

    def force_garbage_collection(self, detailed_log=True):
        """
        强制垃圾回收，释放内存
        
        Args:
            detailed_log: 是否输出详细日志
            
        Returns:
            int: 回收的对象数量
        """
        try:
            before_memory = self.monitor_memory_usage()
            start_time = time.time()
            
            # 执行三代垃圾回收
            gen0_collected = gc.collect(0)  # 第0代
            gen1_collected = gc.collect(1)  # 第1代
            gen2_collected = gc.collect(2)  # 第2代
            
            total_collected = gen0_collected + gen1_collected + gen2_collected
            
            after_memory = self.monitor_memory_usage()
            gc_time = time.time() - start_time
            self.last_gc_time = time.time()
            
            if detailed_log:
                if before_memory["memory_percent"] > 0 and after_memory["memory_percent"] > 0:
                    memory_freed = before_memory["memory_percent"] - after_memory["memory_percent"]
                    logging.info(f"🧹 垃圾回收完成: 回收对象 {total_collected} 个, "
                               f"内存释放 {memory_freed:.1f}%, 耗时 {gc_time:.2f}秒")
                else:
                    logging.info(f"🧹 垃圾回收完成: 回收对象 {total_collected} 个, 耗时 {gc_time:.2f}秒")
                    
                logging.debug(f"🔍 分代回收详情: Gen0={gen0_collected}, Gen1={gen1_collected}, Gen2={gen2_collected}")
            
            return total_collected
            
        except Exception as e:
            logging.error(f"强制垃圾回收时出错: {e}")
            return 0

    def optimize_memory_if_needed(self, force=False):
        """
        根据内存使用情况进行内存优化
        
        Args:
            force: 是否强制执行优化
            
        Returns:
            bool: 是否执行了优化
        """
        memory_info = self.monitor_memory_usage()
        
        # 检查是否需要优化
        should_optimize = force or memory_info["should_optimize"]
        
        # 检查是否到了定期垃圾回收时间
        time_since_last_gc = time.time() - self.last_gc_time
        should_gc_by_time = time_since_last_gc > 300  # 5分钟强制GC一次
        
        if should_optimize or should_gc_by_time:
            if should_optimize:
                logging.info("🔧 内存使用率过高，开始内存优化...")
            else:
                logging.info("🔧 定期内存优化...")
            
            # 强制垃圾回收
            collected = self.force_garbage_collection()
            
            # 启用内存受限模式
            if memory_info["should_optimize"]:
                self.memory_constrained_mode = True
                logging.info("✅ 已启用内存受限模式，降低批处理大小")
            else:
                # 如果内存充足，可以关闭受限模式
                if self.memory_constrained_mode and memory_info["memory_percent"] < self.memory_threshold - 10:
                    self.memory_constrained_mode = False
                    logging.info("✅ 内存充足，关闭内存受限模式")
            
            return True
            
        return False

    def calculate_optimal_batch_size(self, total_rows, base_batch_size=None):
        """
        根据内存情况计算最优批处理大小
        
        Args:
            total_rows: 总行数
            base_batch_size: 基础批处理大小
            
        Returns:
            int: 优化后的批处理大小
        """
        try:
            memory_info = self.monitor_memory_usage()
            
            # 如果没有内存监控，使用保守的默认值
            if not self.psutil_available:
                if total_rows > 100000:
                    return 2000
                elif total_rows > 10000:
                    return 1000
                else:
                    return 500
            
            available_memory = memory_info["available_gb"]
            
            # 估算每行数据的大概大小（字节）
            estimated_row_size = 1000  # 假设每行平均1KB
            
            # 计算可以在内存中安全处理的最大行数（使用可用内存的10%）
            max_rows_in_memory = int(available_memory * 0.1 * 1024**3 / estimated_row_size)
            
            # 根据内存状态调整批次大小
            if self.memory_constrained_mode:
                # 内存受限模式，使用更小的批次
                if total_rows > 1000000:  # 超大数据集
                    batch_size = min(2000, max_rows_in_memory)
                elif total_rows > 100000:  # 大数据集
                    batch_size = min(1000, max_rows_in_memory)
                else:  # 中小数据集
                    batch_size = min(500, max_rows_in_memory)
            else:
                # 正常模式
                if total_rows > 1000000:  # 超大数据集
                    batch_size = min(10000, max_rows_in_memory)
                elif total_rows > 100000:  # 大数据集
                    batch_size = min(5000, max_rows_in_memory)
                elif total_rows > 10000:   # 中等数据集
                    batch_size = min(2000, max_rows_in_memory)
                else:                      # 小数据集
                    batch_size = min(1000, max_rows_in_memory)
            
            # 如果提供了基础批处理大小，进行调整
            if base_batch_size:
                if self.memory_constrained_mode:
                    batch_size = min(batch_size, base_batch_size // 2)  # 受限模式减半
                else:
                    batch_size = min(batch_size, base_batch_size)
            
            # 确保批次大小在合理范围内
            final_batch_size = max(100, min(batch_size, total_rows))
            
            logging.debug(f"📊 批处理大小计算: 总行数={total_rows}, 可用内存={available_memory:.2f}GB, "
                         f"受限模式={self.memory_constrained_mode}, 批次大小={final_batch_size}")
            
            return final_batch_size
            
        except Exception as e:
            logging.warning(f"计算最优批次大小时出错: {e}，使用默认值")
            # 如果无法计算，使用保守的默认值
            if total_rows > 100000:
                return 2000 if self.memory_constrained_mode else 5000
            elif total_rows > 10000:
                return 1000 if self.memory_constrained_mode else 2000
            else:
                return 500 if self.memory_constrained_mode else 1000

    def on_file_processed(self):
        """
        文件处理完成后的回调，用于内存管理
        """
        self.processed_files += 1
        
        # 定期垃圾回收
        if self.processed_files % self.gc_interval == 0:
            logging.info(f"📊 已处理 {self.processed_files} 个文件，执行定期垃圾回收")
            self.force_garbage_collection(detailed_log=False)
        
        # 检查内存状态
        if self.processed_files % (self.gc_interval // 2) == 0:
            memory_info = self.monitor_memory_usage()
            if memory_info["memory_percent"] > 0:
                logging.info(f"📊 内存状态: {memory_info['memory_percent']:.1f}% "
                           f"({memory_info['used_gb']:.2f}GB/{memory_info['total_gb']:.2f}GB)")

    def cleanup_dataframe(self, df):
        """
        清理DataFrame，释放内存
        
        Args:
            df: 要清理的DataFrame
        """
        try:
            if df is not None:
                # 清空DataFrame
                df.drop(df.index, inplace=True)
                del df
                
        except Exception as e:
            logging.warning(f"清理DataFrame时出错: {e}")

    def get_memory_status_report(self):
        """
        获取详细的内存状态报告
        
        Returns:
            str: 内存状态报告
        """
        memory_info = self.monitor_memory_usage()
        
        if not self.psutil_available:
            return "❌ 内存监控不可用（未安装psutil）"
        
        status_lines = [
            "📊 内存状态报告:",
            f"   总内存: {memory_info['total_gb']:.2f} GB",
            f"   已使用: {memory_info['used_gb']:.2f} GB ({memory_info['memory_percent']:.1f}%)",
            f"   可用内存: {memory_info['available_gb']:.2f} GB",
            f"   内存受限模式: {'✅ 启用' if self.memory_constrained_mode else '❌ 关闭'}",
            f"   已处理文件: {self.processed_files}",
            f"   垃圾回收间隔: {self.gc_interval} 文件"
        ]
        
        return "\n".join(status_lines)

# 创建全局内存优化器实例
memory_optimizer = MemoryOptimizer() 