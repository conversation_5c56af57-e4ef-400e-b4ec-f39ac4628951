#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SQLite到PostgreSQL数据迁移工具

此脚本用于将现有SQLite数据库中的数据迁移到PostgreSQL数据库。
它会读取SQLite数据库中的所有表和数据，然后将其插入到PostgreSQL数据库中。
多线程版本：使用线程池并行处理数据批次，提高处理速度。
"""

import os
import sys
import time
import pandas as pd
import sqlite3
import psycopg2
from psycopg2.extras import execute_values
from pathlib import Path
import logging

# 🔧 修复：使用统一的日志配置
from logger_config import setup_script_logger
logger = setup_script_logger('migrate_data')
from tqdm import tqdm
import concurrent.futures
import threading
import configparser

# 创建线程本地存储，用于存储每个线程的数据库连接
thread_local = threading.local()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    # 🔧 修复：使用统一的日志配置
)

# 获取项目文件夹路径
project_dir = Path(__file__).resolve().parent

# 数据库配置
config = configparser.ConfigParser()
config_file = project_dir / 'db_config.ini'

# 如果配置文件不存在，创建默认配置
if not config_file.exists():
    config['PostgreSQL'] = {
        'host': 'localhost',
        'port': '5432',
        'database': 'case_management',
        'user': 'postgres',
        'password': 'postgres'
    }
    with open(config_file, 'w', encoding='utf-8') as f:
        config.write(f)
else:
    config.read(config_file, encoding='utf-8')

# 数据库连接参数
DB_PARAMS = {
    'host': config['PostgreSQL']['host'],
    'port': config['PostgreSQL']['port'],
    'database': config['PostgreSQL']['database'],
    'user': config['PostgreSQL']['user'],
    'password': config['PostgreSQL']['password']
}

# 旧SQLite数据库文件路径(用于迁移)
OLD_SQLITE_PATH = project_dir / 'case_management.db'

def get_db_connection():
    """获取PostgreSQL数据库连接"""
    try:
        conn = psycopg2.connect(**DB_PARAMS)
        return conn
    except psycopg2.Error as e:
        logging.error(f"数据库连接错误: {e}")
        raise

def create_database():
    """创建PostgreSQL数据库(如果不存在)"""
    # 临时使用postgres默认数据库连接
    temp_params = DB_PARAMS.copy()
    temp_params['database'] = 'postgres'
    
    try:
        conn = psycopg2.connect(**temp_params)
        conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 检查数据库是否存在
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (DB_PARAMS['database'],))
        exists = cursor.fetchone()
        
        if not exists:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE {DB_PARAMS['database']}")
            print(f"数据库 {DB_PARAMS['database']} 创建成功")
        
        cursor.close()
        conn.close()
    except psycopg2.Error as e:
        logging.error(f"创建数据库时出错: {e}")
        raise

def get_table_data(sqlite_conn, table_name, batch_size=None, offset=0):
    """使用pandas从SQLite表读取数据，支持分批加载
    
    参数:
        sqlite_conn: SQLite连接
        table_name: 表名
        batch_size: 每批加载的行数，None表示加载全部
        offset: 起始偏移量
    """
    if batch_size is None:
        # 加载整个表
        query = f"SELECT * FROM '{table_name}'"
        df = pd.read_sql_query(query, sqlite_conn)
    else:
        # 分批加载
        query = f"SELECT * FROM '{table_name}' LIMIT {batch_size} OFFSET {offset}"
        df = pd.read_sql_query(query, sqlite_conn)
    
    # 处理列名大小写问题
    df.columns = [col.lower() for col in df.columns]
    
    return df

def get_table_row_count(sqlite_conn, table_name):
    """获取表的总行数"""
    cursor = sqlite_conn.cursor()
    cursor.execute(f"SELECT COUNT(*) FROM '{table_name}'")
    count = cursor.fetchone()[0]
    cursor.close()
    return count

def get_thread_pg_connection():
    """获取当前线程的PostgreSQL连接，如果不存在则创建"""
    if not hasattr(thread_local, 'pg_conn'):
        thread_local.pg_conn = get_db_connection()
    return thread_local.pg_conn

def process_batch(sqlite_db_path, table_name, batch_size, offset, total_rows, batch_num, total_batches):
    """处理单个批次的数据，用于多线程执行"""
    try:
        # 创建新的SQLite连接
        sqlite_conn = sqlite3.connect(sqlite_db_path)
        
        # 获取当前线程的PostgreSQL连接
        pg_conn = get_thread_pg_connection()
        
        logging.info(f"处理批次 {batch_num}/{total_batches}，从第 {offset} 行开始")
        
        # 从SQLite读取批次数据
        df = get_table_data(sqlite_conn, table_name, batch_size, offset)
        
        # 插入数据到PostgreSQL
        rows_inserted = insert_data_to_postgres(pg_conn, table_name, df)
        
        logging.info(f"已完成批次 {batch_num}/{total_batches}，迁移了 {rows_inserted} 行")
        
        # 关闭SQLite连接
        sqlite_conn.close()
        
        return rows_inserted
    except Exception as e:
        logging.error(f"处理批次 {batch_num}/{total_batches} 时出错: {e}")
        return 0

def migrate_sqlite_to_postgres():
    """将SQLite数据库迁移到PostgreSQL"""
    if not OLD_SQLITE_PATH.exists():
        logging.error(f"SQLite数据库文件 {OLD_SQLITE_PATH} 不存在")
        return False
    
    try:
        # 初始化PostgreSQL数据库
        logging.info("确保PostgreSQL数据库存在并已初始化")
        create_database()
        
        # 连接到PostgreSQL
        pg_conn = get_db_connection()
        cursor = pg_conn.cursor()
        
        # 创建表结构
        logging.info("在PostgreSQL中创建表结构")
        create_tables_if_not_exists(cursor)
        pg_conn.commit()
        
        # 连接到SQLite数据库
        logging.info(f"连接到SQLite数据库: {OLD_SQLITE_PATH}")
        sqlite_conn = sqlite3.connect(OLD_SQLITE_PATH)
        
        # 获取所有表
        tables = get_sqlite_tables(sqlite_conn)
        logging.info(f"找到 {len(tables)} 个表: {', '.join(tables)}")
        
        # 统计信息
        total_tables = len(tables)
        tables_migrated = 0
        total_rows_migrated = 0
        
        # 迁移每个表的数据
        for table_name in tables:
            try:
                logging.info(f"开始迁移表: {table_name}")
                
                # 确定是否需要分批处理
                # 账户交易明细表使用分批处理，每批50万行
                if table_name == '账户交易明细表' or table_name == '临时账户交易明细表':
                    # 获取总行数
                    total_rows = get_table_row_count(sqlite_conn, table_name)
                    logging.info(f"表 {table_name} 包含 {total_rows} 行数据，将分批多线程处理")
                    
                    # 每批50万行
                    batch_size = 500000
                    total_batches = (total_rows + batch_size - 1) // batch_size
                    rows_inserted_total = 0
                    
                    # 设置线程池大小
                    max_workers = min(os.cpu_count() or 4, 8)  # 使用CPU核心数，最多8个线程
                    logging.info(f"使用 {max_workers} 个工作线程并行处理数据")
                    
                    # 使用线程池进行并行处理
                    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                        # 提交所有批次任务
                        futures = [
                            executor.submit(
                                process_batch, 
                                str(OLD_SQLITE_PATH), 
                                table_name, 
                                batch_size, 
                                batch_num * batch_size,
                                total_rows,
                                batch_num + 1, 
                                total_batches
                            )
                            for batch_num in range(total_batches)
                        ]
                        
                        # 获取所有任务的结果
                        for future in concurrent.futures.as_completed(futures):
                            rows_inserted = future.result()
                            rows_inserted_total += rows_inserted
                    
                    if rows_inserted_total > 0:
                        tables_migrated += 1
                        total_rows_migrated += rows_inserted_total
                        logging.info(f"成功迁移表 {table_name} 的 {rows_inserted_total} 行数据")
                
                else:
                    # 其他表直接完整加载
                    df = get_table_data(sqlite_conn, table_name)
                    
                    # 记录行数
                    row_count = len(df)
                    logging.info(f"表 {table_name} 包含 {row_count} 行数据")
                    
                    # 插入数据到PostgreSQL
                    rows_inserted = insert_data_to_postgres(pg_conn, table_name, df)
                    
                    if rows_inserted > 0:
                        tables_migrated += 1
                        total_rows_migrated += rows_inserted
                        logging.info(f"成功迁移表 {table_name} 的 {rows_inserted} 行数据")
                
            except Exception as e:
                logging.error(f"迁移表 {table_name} 时出错: {e}")
        
        # 修复序列值
        logging.info("修复PostgreSQL序列值")
        migrate_sequences(pg_conn)
        
        # 创建基本索引
        logging.info("创建基本索引")
        create_basic_indexes(cursor)
        pg_conn.commit()
        
        # 关闭连接
        cursor.close()
        pg_conn.close()
        sqlite_conn.close()
        
        # 关闭线程连接池中的所有连接
        if hasattr(thread_local, 'pg_conn'):
            try:
                thread_local.pg_conn.close()
            except:
                pass
        
        # 总结迁移结果
        logging.info(f"迁移完成! 已迁移 {tables_migrated}/{total_tables} 个表，共 {total_rows_migrated} 行数据")
        
        return True
        
    except Exception as e:
        logging.error(f"迁移过程中发生错误: {e}")
        return False

def view_table_data(table_name, limit=10):
    """查看表数据"""
    conn = psycopg2.connect(**DB_PARAMS)
    query = f'SELECT * FROM "{table_name}" LIMIT {limit}'
    df = pd.read_sql_query(query, conn)
    conn.close()
    return df

def list_tables():
    """查看表名列表"""
    conn = psycopg2.connect(**DB_PARAMS)
    cursor = conn.cursor()
    cursor.execute("""
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public'
    """)
    tables = [row[0] for row in cursor.fetchall()]
    cursor.close()
    conn.close()
    return tables

def create_tables_if_not_exists(cursor):
    """在PostgreSQL中创建表结构（如果不存在）"""
    # 用户信息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 用户信息表 (
            用户名 TEXT PRIMARY KEY,
            密码 TEXT NOT NULL,
            权限 TEXT NOT NULL
        )
    ''')
    
    # 系统信息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 系统信息表 (
            安装日期 TEXT,
            欢迎词 TEXT,
            版本信息 TEXT,
            作者信息 TEXT
        )
    ''')

    # 案件信息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 案件信息表 (
            案件编号 TEXT PRIMARY KEY,
            案件名称 TEXT NOT NULL,
            简要案情 TEXT,
            受理日期 TEXT,
            案件录入时间 TEXT DEFAULT TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS')
        )
    ''')

    # 导入记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 导入记录表 (
            id SERIAL PRIMARY KEY,
            案件编号 TEXT NOT NULL,
            导入批次 INTEGER NOT NULL,
            导入时间 TEXT DEFAULT TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
            文件名 TEXT NOT NULL,
            导入结果 TEXT
        )
    ''')

    # 开户信息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 开户信息表 (
            id SERIAL PRIMARY KEY,
            账户开户名称 TEXT,
            开户人证件号码 TEXT,
            交易卡号 TEXT,
            交易账号 TEXT,
            账号开户时间 TEXT,
            账户余额 DECIMAL(15, 2),
            可用余额 DECIMAL(15, 2),
            币种 TEXT,
            开户网点代码 TEXT,
            开户网点 TEXT,
            账户状态 TEXT,
            钞汇标志名称 TEXT,
            销户日期 TEXT,
            账户类型 TEXT,
            开户联系方式 TEXT,
            联系电话 TEXT,
            通信地址 TEXT,
            代理人 TEXT,
            代理人电话 TEXT,
            备注 TEXT,
            开户省份 TEXT,
            开户城市 TEXT,
            账号开户银行 TEXT,
            客户代码 TEXT,
            法人代表 TEXT,
            客户工商执照号码 TEXT,
            法人代表证件号码 TEXT,
            住宅地址 TEXT,
            邮政编码 TEXT,
            代办人证件号码 TEXT,
            邮箱地址 TEXT,
            关联资金账户 TEXT,
            地税纳税号 TEXT,
            单位电话 TEXT,
            代办人证件类型 TEXT,
            住宅电话 TEXT,
            法人代表证件类型 TEXT,
            国税纳税号 TEXT,
            单位地址 TEXT,
            工作单位 TEXT,
            销户网点 TEXT,
            最后交易时间 TEXT,
            账户销户银行 TEXT,
            案件编号 TEXT,
            源文件位置 TEXT,
            导入批次 TEXT,
            交易卡号_digits TEXT,
            交易账号_digits TEXT
        )
    ''')

    # 账户交易明细表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 账户交易明细表 (
            id SERIAL PRIMARY KEY,
            交易账卡号 TEXT,
            交易账号 TEXT,
            交易户名 TEXT,
            交易证件号码 TEXT,
            交易方开户银行 TEXT,
            交易日期 TEXT,
            交易金额 DECIMAL(15, 2),
            交易余额 DECIMAL(15, 2),
            收付标志 TEXT,
            对手账号 TEXT,
            对手卡号 TEXT,
            现金标志 TEXT,
            对手户名 TEXT,
            对手身份证号 TEXT,
            对手开户银行 TEXT,
            摘要说明 TEXT,
            交易币种 TEXT,
            商户名称 TEXT,
            商户号 TEXT,
            交易网点名称 TEXT,
            交易场所 TEXT,
            交易发生地 TEXT,
            交易是否成功 TEXT,
            传票号 TEXT,
            IP地址 TEXT,
            MAC地址 TEXT,
            对手交易余额 DECIMAL(15, 2),
            交易流水号 TEXT,
            对手余额 DECIMAL(15, 2),
            渠道 TEXT,
            交易类型 TEXT,
            日志号 TEXT,
            凭证种类 TEXT,
            凭证号 TEXT,
            交易柜员号 TEXT,
            备注 TEXT,
            查询账号 TEXT,
            查询卡号 TEXT,
            本方账号 TEXT,
            本方卡号 TEXT,
            案件编号 TEXT,
            源文件位置 TEXT,
            导入批次 TEXT,
            交易账卡号_digits TEXT,
            交易账号_digits TEXT,
            对手账号_digits TEXT,
            对手卡号_digits TEXT
        )
    ''')

    # 临时账户交易明细表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 临时账户交易明细表 (
            交易账卡号 TEXT,
            交易账号 TEXT,
            交易户名 TEXT,
            交易证件号码 TEXT,
            交易方开户银行 TEXT,
            交易日期 TEXT,
            交易时间 TEXT,
            交易金额 DECIMAL(15, 2),
            交易金额正负 DECIMAL(15, 2),
            交易余额 DECIMAL(15, 2),
            收付标志 TEXT,
            借方金额 DECIMAL(15, 2),
            贷方金额 DECIMAL(15, 2),
            对手账号 TEXT,
            对手卡号 TEXT,
            现金标志 TEXT,
            对手户名 TEXT,
            对手身份证号 TEXT,
            对手开户银行 TEXT,
            摘要说明 TEXT,
            交易币种 TEXT,
            商户名称 TEXT,
            商户号 TEXT,
            交易网点名称 TEXT,
            交易场所 TEXT,
            交易发生地 TEXT,
            交易是否成功 TEXT,
            传票号 TEXT,
            IP地址 TEXT,
            MAC地址 TEXT,
            对手交易余额 DECIMAL(15, 2),
            交易流水号 TEXT,
            对手余额 DECIMAL(15, 2),
            渠道 TEXT,
            交易类型 TEXT,
            日志号 TEXT,
            凭证种类 TEXT,
            凭证号 TEXT,
            交易柜员号 TEXT,
            备注 TEXT,
            案件编号 TEXT,
            源文件位置 TEXT,
            导入批次 TEXT,
            查询账号 TEXT,
             TEXT,
            本方账号 TEXT,
            本方卡号 TEXT
        )
    ''')

    # 增值税发票表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 增值税发票表 (
            id SERIAL PRIMARY KEY,
            序号 INTEGER,
            发票类型 TEXT,
            发票代码 TEXT,
            发票号码 TEXT,
            开票日期 TEXT,
            销方纳税人名称 TEXT,
            销方纳税人识别号 TEXT,
            销方大数据ID TEXT,
            销方登记序号 TEXT,
            购方纳税人名称 TEXT,
            购方纳税人识别号 TEXT,
            购方大数据ID TEXT,
            购方登记序号 TEXT,
            销方开户行及账号 TEXT,
            销方银行名称 TEXT,
            购方开户行及账号 TEXT,
            购方银行名称 TEXT,
            作废标志 TEXT,
            作废时间 TEXT,
            特殊票种 TEXT,
            行号 INTEGER,
            商品编码 TEXT,
            货物劳务名称 TEXT,
            数量 DECIMAL(15, 2),
            单价 DECIMAL(15, 2),
            单位 TEXT,
            规格型号 TEXT,
            清单标志 TEXT,
            开具类型 TEXT,
            备注 TEXT,
            金额 DECIMAL(15, 2),
            税率 DECIMAL(5, 2),
            税额 DECIMAL(15, 2),
            价税合计金额 DECIMAL(15, 2),
            mac TEXT,
            主板序列号 TEXT,
            ip TEXT,
            源端口 TEXT,
            案件编号 TEXT,
            源文件位置 TEXT,
            导入批次 TEXT
        )
    ''')
    
    # 系统配置表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 系统配置表 (
            配置项 TEXT PRIMARY KEY,
            配置值 TEXT NOT NULL
        )
    ''')
    
    # 字段匹配规则
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 字段匹配规则 (
            规则ID SERIAL PRIMARY KEY,
            表名 TEXT,
            配置名称 TEXT,
            创建日期 TEXT,
            数据库表字段 TEXT,
            待导入表字段 TEXT
        )
    ''')

def create_basic_indexes(cursor):
    """创建基本索引 - 这些索引对基本业务功能必需"""
    # 为关键字段创建索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_case_id ON 开户信息表 (案件编号)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_case_id_transactions ON 账户交易明细表 (案件编号)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_case_id_invoice ON 增值税发票表 (案件编号)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_case_id_tenpay ON 财付通交易明细表 (案件编号)')

    # 为基本业务查询创建最少的必要索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_riqi ON 账户交易明细表 (交易日期)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_caifutong_riqi ON 财付通交易明细表 (交易时间)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_zengzhishui_riqi ON 增值税发票表 (开票日期)')

def migrate_sequences(pg_conn):
    """修复PostgreSQL序列值，确保自增ID能从正确的值开始"""
    cursor = pg_conn.cursor()
    
    # 获取所有带有序列的表(带SERIAL或BIGSERIAL类型的列)
    cursor.execute("""
        SELECT 
            table_name, 
            column_name
        FROM 
            information_schema.columns
        WHERE 
            table_schema = 'public' AND
            column_default LIKE 'nextval%'
    """)
    
    seq_columns = cursor.fetchall()
    
    for table_name, column_name in seq_columns:
        try:
            # 获取序列名
            cursor.execute(f"""
                SELECT pg_get_serial_sequence('{table_name}', '{column_name}')
            """)
            seq_name = cursor.fetchone()[0]
            
            if seq_name:
                # 🔧 安全修复：使用参数化查询防止SQL注入
                # 验证列名和表名的安全性（只允许字母、数字、下划线和中文）
                import re
                if not re.match(r'^[a-zA-Z0-9_\u4e00-\u9fff]+$', column_name):
                    raise ValueError(f"无效的列名: {column_name}")
                if not re.match(r'^[a-zA-Z0-9_\u4e00-\u9fff]+$', table_name):
                    raise ValueError(f"无效的表名: {table_name}")
                if not re.match(r'^[a-zA-Z0-9_]+$', seq_name):
                    raise ValueError(f"无效的序列名: {seq_name}")

                # 获取当前最大ID（使用安全的标识符）
                cursor.execute(f"""
                    SELECT COALESCE(MAX("{column_name}"), 0) + 1 FROM "{table_name}"
                """)
                next_id = cursor.fetchone()[0]

                # 验证next_id是数字
                if not isinstance(next_id, (int, float)) or next_id < 1:
                    raise ValueError(f"无效的序列值: {next_id}")

                # 设置序列的下一个值（使用参数化查询）
                cursor.execute(f"""
                    ALTER SEQUENCE "{seq_name}" RESTART WITH %s
                """, (int(next_id),))
                
                pg_conn.commit()
                logging.info(f"更新表 {table_name} 的序列 {seq_name} 为 {next_id}")
        
        except Exception as e:
            pg_conn.rollback()
            logging.error(f"更新表 {table_name} 的序列时出错: {e}")
    
    cursor.close()

def get_sqlite_tables(sqlite_conn):
    """获取SQLite数据库中的所有表名"""
    cursor = sqlite_conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    tables = [row[0] for row in cursor.fetchall()]
    cursor.close()
    return tables

def insert_data_to_postgres(pg_conn, table_name, df):
    """将数据插入到PostgreSQL表中"""
    if df.empty:
        logging.info(f"表 {table_name} 为空，跳过")
        return 0
    
    # 获取PostgreSQL表的实际列名（小写）
    cursor = pg_conn.cursor()
    cursor.execute(f"""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = '{table_name.lower()}'
        AND table_schema = 'public'
    """)
    pg_columns_info = {row[0]: row[1] for row in cursor.fetchall()}
    pg_columns = list(pg_columns_info.keys())
    cursor.close()
    
    # 确保DataFrame列名与PostgreSQL表列名匹配（处理大小写）
    df_columns = df.columns.tolist()
    df_renamed = df.copy()
    
    # 将所有列名转为小写
    rename_dict = {col: col.lower() for col in df_columns if col.lower() != col}
    if rename_dict:
        df_renamed = df_renamed.rename(columns=rename_dict)
        logging.info(f"表 {table_name} 中的列已从大写转为小写: {rename_dict}")
    
    # 处理日期相关的列
    date_columns = []
    for col in df_renamed.columns:
        if 'date' in col.lower() or '日期' in col or '时间' in col:
            date_columns.append(col)
    
    # 处理日期值
    for col in date_columns:
        if col in df_renamed.columns:
            # 将空字符串替换为None (SQL NULL)
            df_renamed[col] = df_renamed[col].apply(lambda x: None if pd.isna(x) or x == '' else x)
            
            # 将整数格式的日期转换为字符串格式，不再转换为日期对象
            df_renamed[col] = df_renamed[col].apply(lambda x: 
                str(x).strip() if x is not None else None)
    
    # 处理数值类型的列，将空字符串和特殊字符"-"转换为NULL
    for col in df_renamed.columns:
        if col in pg_columns_info:
            col_type = pg_columns_info[col]
            # 处理数值类型字段(numeric, decimal, integer等)
            if any(num_type in col_type for num_type in ['numeric', 'decimal', 'int', 'float', 'double']):
                df_renamed[col] = df_renamed[col].apply(
                    lambda x: None if pd.isna(x) or x == '' or x == '-' or 
                    (isinstance(x, str) and (x.strip() == '' or x.strip() == '-')) 
                    else x
                )
    
    # 筛选出PostgreSQL表中存在的列
    valid_columns = [col for col in df_renamed.columns if col in pg_columns]
    if len(valid_columns) < len(df_renamed.columns):
        missing_columns = set(df_renamed.columns) - set(valid_columns)
        logging.warning(f"表 {table_name} 中的这些列在PostgreSQL表中不存在，将被忽略: {missing_columns}")
    
    # 检查是否有有效列
    if not valid_columns:
        logging.warning(f"表 {table_name} 没有有效列可以插入，跳过此表")
        return 0
    
    # 只使用有效的列
    df_filtered = df_renamed[valid_columns]
    
    # 获取列名列表
    columns = df_filtered.columns.tolist()
    columns_str = ', '.join([f'"{col}"' for col in columns])
    
    # 对于大型表，分批插入
    batch_size = 5000
    total_rows = len(df_filtered)
    rows_inserted = 0
    
    # 创建进度条
    with tqdm(total=total_rows, desc=f"迁移表 {table_name}") as pbar:
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = df_filtered.iloc[start_idx:end_idx]
            
            # 处理数据中的None值和特殊字符"-"，避免类型错误
            data_tuples = [tuple(None if pd.isna(x) or (isinstance(x, str) and (x.strip() == '' or x.strip() == '-')) else x for x in row) for row in batch_df.values]
            
            # 使用execute_values提高性能
            cursor = pg_conn.cursor()
            
            try:
                # 构建INSERT语句
                query = f"""
                INSERT INTO "{table_name}" ({columns_str})
                VALUES %s
                ON CONFLICT DO NOTHING
                """
                
                # 使用execute_values进行批量插入
                execute_values(cursor, query, data_tuples)
                pg_conn.commit()
                
                # 更新进度
                rows_inserted += len(batch_df)
                pbar.update(len(batch_df))
                
            except Exception as e:
                pg_conn.rollback()
                logging.error(f"插入表 {table_name} 数据时出错: {e}")
                # 对于错误，尝试逐行插入以便跳过有问题的数据
                for _, row in batch_df.iterrows():
                    try:
                        placeholders = ', '.join(['%s'] * len(columns))
                        sql = f'INSERT INTO "{table_name}" ({columns_str}) VALUES ({placeholders}) ON CONFLICT DO NOTHING'
                        # 更彻底地处理空值和特殊字符"-"
                        data = tuple(None if pd.isna(x) or (isinstance(x, str) and (x.strip() == '' or x.strip() == '-')) else x for x in row)
                        cursor.execute(sql, data)
                        pg_conn.commit()
                        rows_inserted += 1
                        pbar.update(1)
                    except Exception as row_e:
                        pg_conn.rollback()
                        logging.error(f"插入行 {row} 时出错: {row_e}")
            
            cursor.close()
    
    return rows_inserted

if __name__ == "__main__":
    start_time = time.time()
    logging.info("开始SQLite到PostgreSQL的数据迁移")
    
    success = migrate_sqlite_to_postgres()
    
    if success:
        elapsed_time = time.time() - start_time
        logging.info(f"迁移成功完成! 耗时 {elapsed_time:.2f} 秒")
    else:
        logging.error("迁移失败!")
        sys.exit(1) 

    print("数据库中的表:")
    tables = list_tables()
    for table in tables:
        print(f"- {table}")
    
    table_name = input("\n请输入要查看的表名: ")
    limit = int(input("要显示多少行数据: "))
    
    data = view_table_data(table_name, limit)
    print(f"\n{table_name} 的前 {limit} 行数据:")
    print(data) 