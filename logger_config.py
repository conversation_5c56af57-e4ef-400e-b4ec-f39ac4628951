import os
import logging
import time
import shutil
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime, timedelta

class CustomTimedRotatingFileHandler(TimedRotatingFileHandler):
    """自定义日志处理器，添加自动清理功能"""
    
    def __init__(self, filename, when='D', interval=1, backupCount=7, encoding=None, 
                 delay=False, utc=False, atTime=None):
        super().__init__(filename, when, interval, backupCount, encoding, delay, utc, atTime)
        self.backupCount = backupCount
        self.log_dir = os.path.dirname(filename)
        
    def doRollover(self):
        """重写轮换方法，添加旧日志清理"""
        # 调用父类的轮换方法
        super().doRollover()
        
        # 清理超过backupCount天的日志文件
        self._cleanup_old_logs()
    
    def _cleanup_old_logs(self):
        """清理超过backupCount天的日志文件"""
        if not os.path.exists(self.log_dir):
            return
            
        # 获取当前时间
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=self.backupCount)
        
        # 检查日志文件夹中的所有文件
        try:
            for filename in os.listdir(self.log_dir):
                if not filename.endswith('.log'):
                    continue
                    
                file_path = os.path.join(self.log_dir, filename)
                file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                # 如果文件修改时间早于截止时间，则删除
                if file_mtime < cutoff_time:
                    try:
                        os.remove(file_path)
                        print(f"清理旧日志文件: {file_path}")
                    except Exception as e:
                        print(f"清理日志文件失败: {file_path}, 错误: {e}")
        except Exception as e:
            print(f"日志清理过程中发生错误: {e}")


def cleanup_temporary_logs(log_dir):
    """清理临时日志文件和其他脚本生成的日志文件"""
    try:
        if not os.path.exists(log_dir):
            return

        # 定义需要清理的临时日志文件模式
        temp_log_patterns = [
            'analyze_*.log',
            'check_*.log',
            'debug_*.log',
            'deep_*.log',
            'diagnose_*.log',
            'comprehensive_*.log',
            'targeted_*.log',
            'verify_*.log',
            'precise_*.log',
            'migration*.log',
            'import_*.log',
            'test_*.log',
            'fix_*.log'
        ]

        # 获取当前时间
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=1)  # 清理1天前的临时日志

        cleaned_count = 0
        for filename in os.listdir(log_dir):
            if not filename.endswith('.log'):
                continue

            # 检查是否匹配临时日志模式
            is_temp_log = False
            for pattern in temp_log_patterns:
                import fnmatch
                if fnmatch.fnmatch(filename, pattern):
                    is_temp_log = True
                    break

            if is_temp_log:
                file_path = os.path.join(log_dir, filename)
                try:
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_mtime < cutoff_time:
                        os.remove(file_path)
                        cleaned_count += 1
                        print(f"清理临时日志文件: {filename}")
                except Exception as e:
                    print(f"清理临时日志文件失败: {filename}, 错误: {e}")

        if cleaned_count > 0:
            print(f"共清理了 {cleaned_count} 个临时日志文件")

    except Exception as e:
        print(f"清理临时日志文件过程中发生错误: {e}")


def setup_logger():
    """设置日志系统"""
    # 🔧 修改：统一使用LOGS文件夹存放所有日志文件
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'LOGS')
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建当前日期的日志文件名
    current_date = datetime.now().strftime('%Y-%m-%d')
    log_file = os.path.join(log_dir, f'app_{current_date}.log')
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # 清除任何现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(funcName)s - %(message)s'
    )
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 创建文件处理器 - 每天轮换一次，保留7天
    file_handler = CustomTimedRotatingFileHandler(
        filename=log_file,
        when='midnight',
        interval=1,
        backupCount=7,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    file_handler.suffix = "%Y-%m-%d.log"
    root_logger.addHandler(file_handler)
    
    # 🔧 增强：立即清理一次旧日志，并添加启动时的全面清理
    file_handler._cleanup_old_logs()

    # 🔧 新增：启动时清理所有临时日志文件
    cleanup_temporary_logs(log_dir)

    logging.info(f"日志系统初始化完成，日志文件: {log_file}")
    logging.info(f"日志文件夹: {log_dir}")
    logging.info(f"日志保留天数: 7天")
    return root_logger

# 全局变量，用于访问日志记录器
logger = None

def get_logger():
    """获取日志记录器实例"""
    global logger
    if logger is None:
        logger = setup_logger()
    return logger

def get_log_file_path(script_name=None):
    """
    获取统一的日志文件路径

    参数:
        script_name: 脚本名称，如果提供则创建专门的日志文件

    返回:
        日志文件的完整路径
    """
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'LOGS')
    os.makedirs(log_dir, exist_ok=True)

    if script_name:
        # 为特定脚本创建专门的日志文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = os.path.join(log_dir, f'{script_name}_{timestamp}.log')
    else:
        # 使用默认的应用日志文件
        current_date = datetime.now().strftime('%Y-%m-%d')
        log_file = os.path.join(log_dir, f'app_{current_date}.log')

    return log_file


def setup_script_logger(script_name, level=logging.INFO):
    """
    为脚本设置统一的日志配置

    参数:
        script_name: 脚本名称
        level: 日志级别

    返回:
        配置好的日志记录器
    """
    log_file = get_log_file_path(script_name)

    # 创建新的日志记录器
    script_logger = logging.getLogger(script_name)
    script_logger.setLevel(level)

    # 清除现有处理器
    for handler in script_logger.handlers[:]:
        script_logger.removeHandler(handler)

    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )

    # 文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(level)
    file_handler.setFormatter(formatter)
    script_logger.addHandler(file_handler)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    script_logger.addHandler(console_handler)

    script_logger.info(f"脚本日志系统初始化完成，日志文件: {log_file}")
    return script_logger


# 当模块加载时自动设置日志系统
get_logger()