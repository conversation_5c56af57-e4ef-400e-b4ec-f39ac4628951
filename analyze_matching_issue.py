#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析市场监管和理财登记表无法自动匹配的问题原因
功能：检查表类型映射、字段匹配规则、数据库表结构等问题
"""

import psycopg2
import json
import os
from database_setup import get_db_connection

def check_table_type_mapping():
    """检查表类型映射"""
    print("=== 1. 检查表类型映射 ===")
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 查询市场监管相关的表类型映射
    print("\n📋 市场监管相关表类型映射:")
    cursor.execute("""
        SELECT 文件关键词, 工作表名, 数据库表名,
               CASE 
                   WHEN 工作表名 IS NULL OR 工作表名 = '' OR 工作表名 = 'None'
                   THEN 文件关键词
                   ELSE 文件关键词 || '_' || 工作表名
               END as 表类型
        FROM 表类型匹配规则表 
        WHERE 文件关键词 LIKE '%市场监管%' OR 数据库表名 LIKE '%市监%'
        ORDER BY 文件关键词, 工作表名
    """)
    market_rules = cursor.fetchall()
    
    for rule in market_rules:
        print(f"  文件关键词: {rule[0]}")
        print(f"  工作表名: {rule[1]}")
        print(f"  数据库表名: {rule[2]}")
        print(f"  生成的表类型: {rule[3]}")
        print("  ---")
    
    # 查询理财登记相关的表类型映射
    print("\n📋 理财登记相关表类型映射:")
    cursor.execute("""
        SELECT 文件关键词, 工作表名, 数据库表名,
               CASE 
                   WHEN 工作表名 IS NULL OR 工作表名 = '' OR 工作表名 = 'None'
                   THEN 文件关键词
                   ELSE 文件关键词 || '_' || 工作表名
               END as 表类型
        FROM 表类型匹配规则表 
        WHERE 文件关键词 LIKE '%理财%' OR 数据库表名 LIKE '%理财%'
        ORDER BY 文件关键词, 工作表名
    """)
    finance_rules = cursor.fetchall()
    
    for rule in finance_rules:
        print(f"  文件关键词: {rule[0]}")
        print(f"  工作表名: {rule[1]}")
        print(f"  数据库表名: {rule[2]}")
        print(f"  生成的表类型: {rule[3]}")
        print("  ---")
        
    cursor.close()
    conn.close()
    
    return market_rules, finance_rules

def check_field_mapping_rules():
    """检查字段匹配规则"""
    print("\n=== 2. 检查字段匹配规则 ===")
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 查看字段匹配规则表结构
    cursor.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = '字段匹配规则'
        ORDER BY ordinal_position
    """)
    columns = cursor.fetchall()
    print("\n📋 字段匹配规则表结构:")
    for col in columns:
        print(f"  {col[0]} ({col[1]})")
    
    # 查询市场监管相关的字段匹配规则
    print("\n📋 市场监管相关字段匹配规则:")
    cursor.execute("""
        SELECT 表名, 配置名称, 待导入表字段, 数据库表字段 
        FROM 字段匹配规则 
        WHERE 表名 LIKE '%市监%' OR 表名 LIKE '%市场监管%'
        ORDER BY 表名, 配置名称
    """)
    market_field_rules = cursor.fetchall()
    
    if market_field_rules:
        current_table = None
        current_config = None
        for rule in market_field_rules:
            if rule[0] != current_table or rule[1] != current_config:
                print(f"\n  表名: {rule[0]}")
                print(f"  配置名称: {rule[1]}")
                current_table = rule[0]
                current_config = rule[1]
            print(f"    {rule[2]} -> {rule[3]}")
    else:
        print("  未找到市场监管相关的字段匹配规则")
    
    # 查询理财登记相关的字段匹配规则
    print("\n📋 理财登记相关字段匹配规则:")
    cursor.execute("""
        SELECT 表名, 配置名称, 待导入表字段, 数据库表字段 
        FROM 字段匹配规则 
        WHERE 表名 LIKE '%理财%'
        ORDER BY 表名, 配置名称
    """)
    finance_field_rules = cursor.fetchall()
    
    if finance_field_rules:
        current_table = None
        current_config = None
        for rule in finance_field_rules:
            if rule[0] != current_table or rule[1] != current_config:
                print(f"\n  表名: {rule[0]}")
                print(f"  配置名称: {rule[1]}")
                current_table = rule[0]
                current_config = rule[1]
            print(f"    {rule[2]} -> {rule[3]}")
    else:
        print("  未找到理财登记相关的字段匹配规则")
        
    cursor.close()
    conn.close()
    
    return market_field_rules, finance_field_rules

def check_database_tables():
    """检查数据库表结构"""
    print("\n=== 3. 检查数据库表结构 ===")
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 查询所有市场监管相关的表
    print("\n📋 市场监管相关数据库表:")
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name LIKE '%市监%'
        ORDER BY table_name
    """)
    market_tables = cursor.fetchall()
    
    for table in market_tables:
        table_name = table[0]
        print(f"  表名: {table_name}")
        
        # 查询表的字段
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = %s
            AND column_name NOT IN ('id', '案件编号', '源文件位置', '导入批次')
            ORDER BY ordinal_position
        """, (table_name,))
        fields = cursor.fetchall()
        print(f"    业务字段数: {len(fields)}")
        print(f"    字段列表: {[f[0] for f in fields[:5]]}" + ("..." if len(fields) > 5 else ""))
        print("  ---")
    
    # 查询所有理财相关的表
    print("\n📋 理财相关数据库表:")
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name LIKE '%理财%'
        ORDER BY table_name
    """)
    finance_tables = cursor.fetchall()
    
    for table in finance_tables:
        table_name = table[0]
        print(f"  表名: {table_name}")
        
        # 查询表的字段
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = %s
            AND column_name NOT IN ('id', '案件编号', '源文件位置', '导入批次')
            ORDER BY ordinal_position
        """, (table_name,))
        fields = cursor.fetchall()
        print(f"    业务字段数: {len(fields)}")
        print(f"    字段列表: {[f[0] for f in fields[:5]]}" + ("..." if len(fields) > 5 else ""))
        print("  ---")
        
    cursor.close()
    conn.close()
    
    return market_tables, finance_tables

def check_temp_mapping():
    """检查临时映射文件"""
    print("\n=== 4. 检查临时映射文件 ===")
    
    temp_file = 'temp_mapping.json'
    if os.path.exists(temp_file):
        with open(temp_file, 'r', encoding='utf-8') as f:
            mappings = json.load(f)
        
        print(f"\n📋 temp_mapping.json中共有 {len(mappings)} 个映射")
        
        # 查找市场监管相关的映射
        market_mappings = []
        finance_mappings = []
        
        for key, value in mappings.items():
            if '市场监管' in key or '市监' in value.get('table_type', ''):
                market_mappings.append((key, value))
            elif '理财' in key or '理财' in value.get('table_type', ''):
                finance_mappings.append((key, value))
        
        print(f"\n📋 市场监管相关映射: {len(market_mappings)} 个")
        for key, value in market_mappings[:3]:  # 只显示前3个
            print(f"  键: {key}")
            print(f"  表类型: {value.get('table_type', 'N/A')}")
            print(f"  匹配状态: {'有规则' if value.get('rule_name') else '无规则'}")
            print("  ---")
            
        print(f"\n📋 理财相关映射: {len(finance_mappings)} 个")
        for key, value in finance_mappings[:3]:  # 只显示前3个
            print(f"  键: {key}")
            print(f"  表类型: {value.get('table_type', 'N/A')}")
            print(f"  匹配状态: {'有规则' if value.get('rule_name') else '无规则'}")
            print("  ---")
    else:
        print("  temp_mapping.json文件不存在")

def analyze_matching_logic():
    """分析匹配逻辑可能的问题"""
    print("\n=== 5. 问题分析与诊断 ===")
    
    # 分析可能的问题
    print("\n🔍 可能的问题原因分析:")
    
    print("\n1️⃣ 表类型映射问题:")
    print("   - 文件名或工作表名与表类型匹配规则不符")
    print("   - 表类型生成逻辑存在问题")
    print("   - 特殊字符或命名格式导致映射失败")
    
    print("\n2️⃣ 字段匹配规则问题:")
    print("   - 缺少对应表类型的字段匹配规则")
    print("   - 保存的规则中字段名称与实际文件字段不完全一致")
    print("   - 字段清理逻辑（去空格、转小写等）导致匹配失败")
    
    print("\n3️⃣ 100分精确匹配标准过严:")
    print("   - 系统要求字段集合完全相同才使用规则")
    print("   - 字段数量必须完全一致")
    print("   - 字段名称必须完全匹配（即使只有微小差异也不行）")
    
    print("\n4️⃣ 数据库表结构问题:")
    print("   - 数据库表不存在或表名不匹配")
    print("   - 表字段结构与预期不符")
    
    print("\n5️⃣ 文件编码或格式问题:")
    print("   - 文件编码导致字段名读取异常")
    print("   - Excel工作表名称不匹配")
    print("   - CSV分隔符识别错误")

def main():
    """主函数"""
    print("🔍 开始分析市场监管和理财登记表无法自动匹配的问题原因")
    print("=" * 80)
    
    try:
        # 1. 检查表类型映射
        market_type_rules, finance_type_rules = check_table_type_mapping()
        
        # 2. 检查字段匹配规则
        market_field_rules, finance_field_rules = check_field_mapping_rules()
        
        # 3. 检查数据库表结构
        market_tables, finance_tables = check_database_tables()
        
        # 4. 检查临时映射文件
        check_temp_mapping()
        
        # 5. 分析匹配逻辑
        analyze_matching_logic()
        
        # 总结分析
        print("\n=== 6. 问题总结与建议 ===")
        print(f"\n📊 统计信息:")
        print(f"  市场监管表类型映射规则: {len(market_type_rules)} 条")
        print(f"  理财表类型映射规则: {len(finance_type_rules)} 条")
        print(f"  市场监管字段匹配规则: {len(market_field_rules)} 条")
        print(f"  理财字段匹配规则: {len(finance_field_rules)} 条")
        print(f"  市场监管数据库表: {len(market_tables)} 个")
        print(f"  理财数据库表: {len(finance_tables)} 个")
        
        print(f"\n💡 建议解决方案:")
        if len(market_field_rules) == 0:
            print("  ⚠️ 市场监管表缺少字段匹配规则，需要手动创建规则")
        if len(finance_field_rules) == 0:
            print("  ⚠️ 理财表缺少字段匹配规则，需要手动创建规则")
        
        print("  1. 检查文件命名是否符合表类型匹配规则")
        print("  2. 为缺少规则的表类型手动创建字段匹配规则")
        print("  3. 验证字段名称是否完全一致（包括空格、大小写等）")
        print("  4. 考虑放宽100分精确匹配标准，允许部分匹配")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 