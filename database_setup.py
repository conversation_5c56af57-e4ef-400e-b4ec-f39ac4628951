import os
from pathlib import Path
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from psycopg2 import sql
from cryptography.fernet import Fernet
import configparser
import logging

# 获取项目文件夹路径
project_dir = Path(__file__).resolve().parent

# 数据库配置
config = configparser.ConfigParser()
config_file = project_dir / 'db_config.ini'

# 如果配置文件不存在，创建默认配置
if not config_file.exists():
    config['PostgreSQL'] = {
        'host': 'localhost',
        'port': '5432',
        'database': 'case_management',
        'user': 'postgres',
        'password': 'postgres'
    }
    with open(config_file, 'w', encoding='utf-8') as f:
        config.write(f)
else:
    config.read(config_file, encoding='utf-8')

# 数据库连接参数
DB_PARAMS = {
    'host': config['PostgreSQL']['host'],
    'port': config['PostgreSQL']['port'],
    'database': config['PostgreSQL']['database'],
    'user': config['PostgreSQL']['user'],
    'password': config['PostgreSQL']['password']
}

# 旧SQLite数据库文件路径(用于迁移)
OLD_SQLITE_PATH = project_dir / 'case_management.db'

# 兼容性变量 - 保持向后兼容性，实际使用PostgreSQL连接
DATABASE_PATH = None  # 已迁移到PostgreSQL，不再使用SQLite路径

ENCRYPTION_KEY = b'2j8RXEaE7UviPO9i1_tnJ822zlkJNQAfJk6zlmKNAuk='  # 使用你生成的密钥替换这一行

cipher_suite = Fernet(ENCRYPTION_KEY)

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = psycopg2.connect(
            host=DB_PARAMS['host'],
            database=DB_PARAMS['database'],
            user=DB_PARAMS['user'],
            password=DB_PARAMS['password'],
            port=DB_PARAMS['port'],
            # 添加性能优化参数
            keepalives=1,
            keepalives_idle=30,
            keepalives_interval=10,
            keepalives_count=5,
            # 设置较大的客户端缓存
            client_encoding='utf8',
            options='-c client_min_messages=warning -c statement_timeout=0'
        )
        
        # 设置连接参数
        conn.set_session(
            autocommit=False,
            isolation_level=psycopg2.extensions.ISOLATION_LEVEL_READ_COMMITTED
        )
        
        # 设置客户端编码
        conn.set_client_encoding('utf8')
        
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        raise

def create_database():
    """创建PostgreSQL数据库(如果不存在)"""
    # 临时使用postgres默认数据库连接
    temp_params = DB_PARAMS.copy()
    temp_params['database'] = 'postgres'
    
    try:
        conn = psycopg2.connect(**temp_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 检查数据库是否存在
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (DB_PARAMS['database'],))
        exists = cursor.fetchone()
        
        if not exists:
            # 创建数据库
            cursor.execute(sql.SQL("CREATE DATABASE {}").format(
                sql.Identifier(DB_PARAMS['database'])
            ))
            print(f"数据库 {DB_PARAMS['database']} 创建成功")
        
        cursor.close()
        conn.close()
    except psycopg2.Error as e:
        logging.error(f"创建数据库时出错: {e}")
        raise

def create_tables_if_not_exists(cursor):
    """
    创建核心系统表（不包括数据表）
    
    核心表：用户信息表、系统信息表、案件信息表、导入记录表、
           临时账户交易明细表、字段匹配规则、系统配置表、
           表类型匹配规则表、对手信息
    
    数据表（由Excel配置动态创建）：财付通交易明细表、账户交易明细表、开户信息表
    """
    # 用户信息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 用户信息表 (
            用户名 TEXT PRIMARY KEY,
            密码 TEXT NOT NULL,
            权限 TEXT NOT NULL
        )
    ''')

    admin_password = cipher_suite.encrypt('Ws6516289!'.encode()).decode()
    test_password = cipher_suite.encrypt('test123'.encode()).decode()

    # 使用ON CONFLICT实现UPSERT
    cursor.execute('''
        INSERT INTO 用户信息表 (用户名, 密码, 权限)
        VALUES (%s, %s, %s)
        ON CONFLICT (用户名) DO NOTHING
    ''', ('admin', admin_password, '超级管理员'))
    
    cursor.execute('''
        INSERT INTO 用户信息表 (用户名, 密码, 权限)
        VALUES (%s, %s, %s)
        ON CONFLICT (用户名) DO NOTHING
    ''', ('test', test_password, '测试用户'))

    # 系统信息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 系统信息表 (
            安装日期 TEXT,
            欢迎词 TEXT,
            版本信息 TEXT,
            作者信息 TEXT
        )
    ''')

    cursor.execute('''
        INSERT INTO 系统信息表 (安装日期, 欢迎词, 版本信息, 作者信息)
        VALUES (CURRENT_DATE, '欢迎使用本系统', '版本: 1.3.0', '作者: 王森')
        ON CONFLICT DO NOTHING
    ''')

    # 案件信息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 案件信息表 (
            案件编号 TEXT PRIMARY KEY,
            案件名称 TEXT NOT NULL,
            简要案情 TEXT,
            受理日期 TEXT,
            案件录入时间 TEXT DEFAULT TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS')
        )
    ''')

    # 导入记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 导入记录表 (
            id SERIAL PRIMARY KEY,
            案件编号 TEXT NOT NULL,
            导入批次 INTEGER NOT NULL,
            导入时间 TEXT DEFAULT TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
            文件名 TEXT NOT NULL,
            导入结果 TEXT
        )
    ''')

    # 临时账户交易明细表（保留为核心表，用于数据处理）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 临时账户交易明细表 (
            交易账卡号 TEXT,
            交易账号 TEXT,
            交易户名 TEXT,
            交易证件号码 TEXT,
            交易方开户银行 TEXT,
            交易日期 TEXT,
            交易时间 TEXT,
            交易金额 DECIMAL(15, 2),
            交易金额正负 DECIMAL(15, 2),
            交易余额 DECIMAL(15, 2),
            收付标志 TEXT,
            借方金额 DECIMAL(15, 2),
            贷方金额 DECIMAL(15, 2),
            对手账号 TEXT,
            对手卡号 TEXT,
            现金标志 TEXT,
            对手户名 TEXT,
            对手身份证号 TEXT,
            对手开户银行 TEXT,
            摘要说明 TEXT,
            交易币种 TEXT,
            商户名称 TEXT,
            商户号 TEXT,
            交易网点名称 TEXT,
            交易场所 TEXT,
            交易发生地 TEXT,
            交易是否成功 TEXT,
            传票号 TEXT,
            IP地址 TEXT,
            MAC地址 TEXT,
            交易流水号 TEXT,
            对手余额 DECIMAL(15, 2),
            渠道 TEXT,
            交易类型 TEXT,
            日志号 TEXT,
            凭证种类 TEXT,
            凭证号 TEXT,
            交易柜员号 TEXT,
            备注 TEXT,
            案件编号 TEXT,
            源文件位置 TEXT,
            导入批次 TEXT,
            查询账号 TEXT,
            查询卡号 TEXT,
            本方账号 TEXT,
            本方卡号 TEXT
        )
    ''')

    # 字段匹配规则
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 字段匹配规则 (
            规则ID SERIAL PRIMARY KEY,
            表名 TEXT,
            配置名称 TEXT,
            创建日期 TEXT,
            数据库表字段 TEXT,
            待导入表字段 TEXT,
            UNIQUE(表名, 数据库表字段, 待导入表字段, 配置名称)
        )
    ''')

    # 系统配置表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 系统配置表 (
            配置项 TEXT PRIMARY KEY,
            配置值 TEXT NOT NULL
        )
    ''')

    reset_password = cipher_suite.encrypt('asan840617ws'.encode()).decode()

    cursor.execute('''
        INSERT INTO 系统配置表 (配置项, 配置值)
        VALUES (%s, %s)
        ON CONFLICT (配置项) DO NOTHING
    ''', ('reset_password', reset_password))

    # 表类型匹配规则表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 表类型匹配规则表 (
            id SERIAL PRIMARY KEY,
            文件关键词 TEXT NOT NULL,
            工作表名 TEXT,
            数据库表名 TEXT NOT NULL,
            创建时间 TEXT DEFAULT TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
            更新时间 TEXT DEFAULT TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
            备注 TEXT,
            UNIQUE(文件关键词, 工作表名)
        )
    ''')

    # 对手信息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS 对手信息 (
            ID SERIAL PRIMARY KEY,
            对手账号 TEXT,
            对手卡号 TEXT,
            对手户名 TEXT,
            对手身份证号 TEXT,
            对手开户银行 TEXT,
            摘要说明 TEXT,
            备注 TEXT,
            案件编号 TEXT,
            创建时间 TEXT DEFAULT TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
            创建人 TEXT
        )
    ''')

    # 注意：以下表已移除，改为数据表（由Excel配置动态创建）：
    # - 开户信息表
    # - 账户交易明细表  
    # - 财付通交易明细表

def create_indexes(cursor):
    # 创建基本索引 - 这些是业务必需的，在导入时就创建
    create_basic_indexes(cursor)
    
    # 创建搜索优化索引 - 这些可以延迟到搜索时创建
    create_search_optimization_indexes(cursor)


def create_basic_indexes(cursor):
    """创建基本索引 - 这些索引对基本业务功能必需，在导入时创建"""
    # 为关键字段创建索引 - 核心表已调整为8个，移除数据表索引
    # 注意：开户信息表、账户交易明细表、财付通交易明细表已改为数据表，不在此创建索引

    # 为对手信息表的案件编号+对手账号建立联合索引，加速大数据量下的对手户名回填与匹配
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_opponentinfo_caseid_account ON 对手信息 (案件编号, 对手账号)')


def create_search_optimization_indexes(cursor):
    """创建搜索优化索引 - 这些索引主要用于加速搜索，可以延迟创建"""
    # 注意：开户信息表、账户交易明细表、财付通交易明细表已改为数据表
    # 这些表的索引将在Excel配置文件中定义，或由数据导入时动态创建
    
    # 目前核心表中没有需要搜索优化索引的表
    # 如果将来需要为核心表添加搜索优化索引，可以在此处添加
    pass


def initialize_database():
    """初始化PostgreSQL数据库"""
    # 确保数据库存在
    create_database()
    
    # 连接到数据库并创建表
    conn = get_db_connection()
    cursor = conn.cursor()
    
    create_tables_if_not_exists(cursor)
    create_basic_indexes(cursor)  # 只创建基本索引
    
    # 添加系统配置项，标记需要创建搜索优化索引
    cursor.execute('''
        INSERT INTO 系统配置表 (配置项, 配置值)
        VALUES (%s, %s)
        ON CONFLICT (配置项) DO UPDATE SET 配置值 = %s
    ''', ('pending_search_indexes', 'true', 'true'))
            
    conn.commit()
    cursor.close()
    conn.close()
    print("数据库初始化成功。")


def reset_installation_date():
    """重置安装日期"""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("UPDATE 系统信息表 SET 安装日期 = CURRENT_DATE")
    conn.commit()
    cursor.close()
    conn.close()
    print("安装日期重置成功。")


def migrate_from_sqlite():
    """从SQLite迁移数据到PostgreSQL - 请使用migrate_data.py进行多线程迁移"""
    import sqlite3
    import pandas as pd
    
    logging.warning("推荐使用migrate_data.py中的多线程迁移功能来提高性能！")
    
    if not OLD_SQLITE_PATH.exists():
        print("未找到SQLite数据库文件，跳过迁移步骤。")
        return
    
    # 连接源SQLite数据库
    sqlite_conn = sqlite3.connect(OLD_SQLITE_PATH)
    
    # 获取所有表名
    tables = pd.read_sql_query("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'", sqlite_conn)
    
    # 连接目标PostgreSQL数据库
    pg_conn = get_db_connection()
    pg_cursor = pg_conn.cursor()
    
    for table_name in tables['name']:
        try:
            print(f"正在迁移表: {table_name}")
            
            # 获取PostgreSQL表的实际列名和数据类型
            pg_cursor.execute(f"""
                SELECT column_name, data_type
                FROM information_schema.columns 
                WHERE table_name = '{table_name.lower()}'
                AND table_schema = 'public'
            """)
            pg_columns_info = {row[0]: row[1] for row in pg_cursor.fetchall()}
            pg_columns = list(pg_columns_info.keys())
            
            if not pg_columns:
                print(f"警告: 在PostgreSQL中找不到表 '{table_name}'，跳过该表")
                continue
            
            # 对于大型表，分批处理
            if table_name in ['账户交易明细表', '临时账户交易明细表', '开户信息表']:
                # 获取总行数
                count_df = pd.read_sql_query(f"SELECT COUNT(*) as count FROM '{table_name}'", sqlite_conn)
                total_rows = count_df['count'].iloc[0]
                print(f"表 {table_name} 包含 {total_rows} 行数据，将分批处理")
                
                # 每批处理数量
                batch_size = 500000 if table_name in ['账户交易明细表', '临时账户交易明细表'] else 10000
                
                # 处理日期列名
                date_columns = []
                schema_df = pd.read_sql_query(f"PRAGMA table_info('{table_name}')", sqlite_conn)
                for _, row in schema_df.iterrows():
                    col_name = row['name']
                    if 'date' in col_name.lower() or '日期' in col_name or '时间' in col_name:
                        date_columns.append(col_name)
                print(f"日期列: {date_columns}")
                
                # 分批迁移
                total_inserted = 0
                for offset in range(0, total_rows, batch_size):
                    end = min(offset + batch_size, total_rows)
                    print(f"处理 {offset+1}-{end}/{total_rows} 行...")
                    
                    # 读取批次数据
                    df = pd.read_sql_query(f"SELECT * FROM '{table_name}' LIMIT {batch_size} OFFSET {offset}", sqlite_conn)
                    
                    if df.empty:
                        continue
                    
                    # 处理列名大小写问题
                    df_renamed = df.copy()
                    rename_dict = {col: col.lower() for col in df.columns}
                    df_renamed = df_renamed.rename(columns=rename_dict)
                    
                    # 筛选出PostgreSQL表中存在的列
                    valid_columns = [col for col in df_renamed.columns if col in pg_columns]
                    if len(valid_columns) < len(df_renamed.columns):
                        missing_columns = set(df_renamed.columns) - set(valid_columns)
                        print(f"警告: 表 {table_name} 中的这些列在PostgreSQL表中不存在，将被忽略: {missing_columns}")
                    
                    df_filtered = df_renamed[valid_columns]
                    
                    # 处理日期列
                    for col in date_columns:
                        col_lower = col.lower()
                        if col_lower in df_filtered.columns:
                            # 将空字符串和None转为NULL
                            df_filtered[col_lower] = df_filtered[col_lower].apply(lambda x: None if pd.isna(x) or x == '' else str(x).strip())
                    
                    # 处理数值类型的列
                    for col in df_filtered.columns:
                        if col in pg_columns_info:
                            col_type = pg_columns_info[col]
                            # 处理数值类型字段
                            if any(num_type in col_type for num_type in ['numeric', 'decimal', 'int', 'float', 'double']):
                                df_filtered[col] = df_filtered[col].apply(
                                    lambda x: None if pd.isna(x) or x == '' or x == '-' or 
                                    (isinstance(x, str) and (x.strip() == '' or x.strip() == '-')) 
                                    else x
                                )
                    
                    # 获取列名
                    columns = df_filtered.columns.tolist()
                    placeholders = ','.join(['%s'] * len(columns))
                    columns_str = ','.join([f'"{col}"' for col in columns])
                    
                    # 准备INSERT语句
                    insert_query = f'INSERT INTO "{table_name}" ({columns_str}) VALUES ({placeholders}) ON CONFLICT DO NOTHING'
                    
                    # 转换数据为元组列表，处理None值
                    batch_data = []
                    for _, row in df_filtered.iterrows():
                        row_data = []
                        for val in row:
                            if pd.isna(val) or (isinstance(val, str) and (val.strip() == '' or val.strip() == '-')):
                                row_data.append(None)
                            else:
                                row_data.append(val)
                        batch_data.append(tuple(row_data))
                    
                    # 插入数据
                    pg_cursor.executemany(insert_query, batch_data)
                    pg_conn.commit()
                    
                    total_inserted += len(df_filtered)
                    print(f"已迁移 {table_name} 表的 {total_inserted}/{total_rows} 条记录")
            
            else:
                # 小型表，一次性处理
                df = pd.read_sql_query(f"SELECT * FROM '{table_name}'", sqlite_conn)
                
                if df.empty:
                    print(f"表 {table_name} 为空，跳过")
                    continue
                
                # 处理列名大小写问题
                df_renamed = df.copy()
                rename_dict = {col: col.lower() for col in df.columns}
                df_renamed = df_renamed.rename(columns=rename_dict)
                
                # 筛选出PostgreSQL表中存在的列
                valid_columns = [col for col in df_renamed.columns if col in pg_columns]
                if len(valid_columns) < len(df_renamed.columns):
                    missing_columns = set(df_renamed.columns) - set(valid_columns)
                    print(f"警告: 表 {table_name} 中的这些列在PostgreSQL表中不存在，将被忽略: {missing_columns}")
                
                df_filtered = df_renamed[valid_columns]
                
                # 处理日期列
                for col in df_filtered.columns:
                    if 'date' in col.lower() or '日期' in col or '时间' in col:
                        # 将空字符串和None转为NULL
                        df_filtered[col] = df_filtered[col].apply(lambda x: None if pd.isna(x) or x == '' else str(x).strip())
                
                # 处理数值类型的列
                for col in df_filtered.columns:
                    if col in pg_columns_info:
                        col_type = pg_columns_info[col]
                        # 处理数值类型字段
                        if any(num_type in col_type for num_type in ['numeric', 'decimal', 'int', 'float', 'double']):
                            df_filtered[col] = df_filtered[col].apply(
                                lambda x: None if pd.isna(x) or x == '' or x == '-' or 
                                (isinstance(x, str) and (x.strip() == '' or x.strip() == '-')) 
                                else x
                            )
                
                # 获取列名
                columns = df_filtered.columns.tolist()
                placeholders = ','.join(['%s'] * len(columns))
                columns_str = ','.join([f'"{col}"' for col in columns])
                
                # 准备INSERT语句
                insert_query = f'INSERT INTO "{table_name}" ({columns_str}) VALUES ({placeholders}) ON CONFLICT DO NOTHING'
                
                # 转换数据为元组列表，处理None值
                batch_data = []
                for _, row in df_filtered.iterrows():
                    row_data = []
                    for val in row:
                        if pd.isna(val) or (isinstance(val, str) and (val.strip() == '' or val.strip() == '-')):
                            row_data.append(None)
                        else:
                            row_data.append(val)
                    batch_data.append(tuple(row_data))
                
                # 插入数据
                pg_cursor.executemany(insert_query, batch_data)
                pg_conn.commit()
                print(f"已迁移 {table_name} 表的 {len(df_filtered)}/{len(df)} 条记录")
                
            print(f"表 {table_name} 迁移完成")
            
        except Exception as e:
            print(f"迁移表 {table_name} 时出错: {e}")
            pg_conn.rollback()
    
    # 关闭连接
    sqlite_conn.close()
    pg_cursor.close()
    pg_conn.close()
    
    print("数据迁移完成")


if __name__ == '__main__':
    initialize_database()
    migrate_from_sqlite()
