#cases_controller.py
import os
import psycopg2
from psycopg2 import extras

from PySide6.QtCore import Qt, QDateTime, QTimer
from PySide6.QtGui import QPixmap, QFont, QCursor, QImage, QPainter, QColor
from PySide6.QtWidgets import (QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
                               QMenu, QDialog, QFormLayout, QLineEdit, QTextEdit, QDateTimeEdit,
                               QMessageBox, QFrame, QToolTip, QScrollArea, QWidget)

from data_analysis import DataAnalysisWidget, create_data_analysis_widget

from styles import BUTTON_STYLE
from visualize import VisualizeWidget, create_visualize_widget

from import_data import ImportDataWindow
# 导入数据库连接函数
from database_setup import get_db_connection

class CasesController:
    def __init__(self, main_window):
        self.main_window = main_window

        # 连接按钮
        self.main_window.ui_pages.btn_add_case.clicked.connect(self.add_case)
        self.main_window.ui_pages.btn_query_case.clicked.connect(self.query_cases)
        self.main_window.ui_pages.btn_delete_case.clicked.connect(self.delete_case)

        self.load_cases()

    def add_case(self):
        dialog = QDialog()
        dialog.setWindowTitle("新增案件")
        dialog.resize(600, 400)
        dialog.setStyleSheet("background-color: #282a36; color: #f8f8f2;")

        form_layout = QFormLayout(dialog)

        # 创建输入框
        self.case_id_input = QLineEdit()
        self.case_name_input = QLineEdit()
        self.case_summary_input = QTextEdit()
        self.case_date_input = QDateTimeEdit(QDateTime.currentDateTime())
        self.case_date_input.setCalendarPopup(True)
        self.case_date_input.setDisplayFormat("yyyy-MM-dd HH:mm:ss")
        self.entry_date_input = QDateTimeEdit(QDateTime.currentDateTime())
        self.entry_date_input.setCalendarPopup(True)
        self.entry_date_input.setDisplayFormat("yyyy-MM-dd HH:mm:ss")

        form_layout.addRow("案件名称:", self.case_name_input)
        form_layout.addRow("案件编号:", self.case_id_input)
        form_layout.addRow("简要案情:", self.case_summary_input)
        form_layout.addRow("受理日期:", self.case_date_input)
        form_layout.addRow("案件录入时间:", self.entry_date_input)

        button_layout = QHBoxLayout()

        generate_id_button = QPushButton("自动生成案件编号")
        generate_id_button.setFixedSize(150, 40)  # 设置按钮大小
        generate_id_button.setStyleSheet(BUTTON_STYLE)
        generate_id_button.clicked.connect(self.generate_case_id)
        button_layout.addWidget(generate_id_button)

        save_button = QPushButton("确定")
        save_button.setFixedSize(150, 40)  # 设置按钮大小
        save_button.setStyleSheet(BUTTON_STYLE)
        cancel_button = QPushButton("取消")
        cancel_button.setFixedSize(150, 40)  # 设置按钮大小
        cancel_button.setStyleSheet(BUTTON_STYLE)
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)

        form_layout.addRow(button_layout)

        save_button.clicked.connect(lambda: self.save_case(dialog))
        cancel_button.clicked.connect(dialog.reject)

        dialog.setLayout(form_layout)
        dialog.exec()

    def generate_case_id(self):
        # 生成案件编号，规则是当前系统时间到秒
        case_id = QDateTime.currentDateTime().toString("yyyyMMddHHmmss")
        self.case_id_input.setText(case_id)

    def save_case(self, dialog):
        case_id = self.case_id_input.text()
        case_name = self.case_name_input.text()
        case_summary = self.case_summary_input.toPlainText()
        case_date = self.case_date_input.dateTime().toString("yyyy-MM-dd HH:mm:ss")
        entry_date = self.entry_date_input.dateTime().toString("yyyy-MM-dd HH:mm:ss")

        if not case_id or not case_name:
            QMessageBox.warning(dialog, "错误", "所有字段都是必填的")
            return

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # 检查案件编号是否重复 - PostgreSQL语法
            cursor.execute('SELECT COUNT(*) FROM "案件信息表" WHERE "案件编号"=%s OR "案件名称"=%s', (case_id, case_name))
            if cursor.fetchone()[0] > 0:
                QMessageBox.warning(dialog, "错误", "案件编号或案件名称重复，请重新输入")
                return

            # 插入新案件 - PostgreSQL语法
            cursor.execute(
                'INSERT INTO "案件信息表" ("案件编号", "案件名称", "简要案情", "受理日期", "案件录入时间") VALUES (%s, %s, %s, %s, %s)',
                (case_id, case_name, case_summary, case_date, entry_date))
            conn.commit()
            QMessageBox.information(dialog, "成功", "案件已成功添加")
            dialog.accept()
        except psycopg2.IntegrityError as e:
            if conn:
                conn.rollback()
            QMessageBox.warning(dialog, "数据库错误", f"无法插入数据: {e}")
        except psycopg2.OperationalError as e:
            if conn:
                conn.rollback()
            QMessageBox.warning(dialog, "数据库错误", f"数据库操作失败: {e}")
        except Exception as e:
            if conn:
                conn.rollback()
            QMessageBox.warning(dialog, "错误", f"操作失败: {e}")
        finally:
            if conn:
                conn.close()
            self.query_cases()

    def load_cases(self):
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            # PostgreSQL语法，使用ORDER BY优化查询
            cursor.execute('SELECT "案件编号", "案件名称", "案件录入时间" FROM "案件信息表" ORDER BY "案件录入时间" DESC')
            cases = cursor.fetchall()
            self.display_cases(cases)
        except Exception as e:
            QMessageBox.warning(self.main_window, "错误", f"加载案件数据失败: {e}")
            self.display_cases([])  # 显示空列表
        finally:
            if conn:
                conn.close()

    def display_cases(self, cases):
        # 清空现有案件图标
        while self.main_window.ui_pages.folder_layout.count() > 0:
            item = self.main_window.ui_pages.folder_layout.takeAt(0)
            widget = item.widget()
            if widget is not None:
                widget.deleteLater()

        row = 0
        col = 0
        for index, (case_id, case_name, _) in enumerate(cases):
            self.display_case(case_name, case_id, row, col)
            col += 1
            if col >= 5:  # 5 列后换行
                col = 0
                row += 1

    def display_case(self, case_name, case_id, row, col):
        case_frame = QFrame()
        case_frame.setFixedSize(200, 200)
        case_frame.setStyleSheet("border: 2px solid transparent;")
        layout = QVBoxLayout(case_frame)

        # 确保图标路径正确
        png_path = os.path.join(os.path.dirname(__file__), 'gui/images/icons/文件夹.png')

        png_widget = self.create_colored_png_widget(png_path, case_name)
        png_widget.setFixedSize(200, 200)
        layout.addWidget(png_widget, alignment=Qt.AlignCenter)

        case_frame.setLayout(layout)

        case_frame.mousePressEvent = lambda event: self.handle_case_click(event, case_id, case_frame)
        case_frame.enterEvent = lambda event: self.on_case_hover(case_frame, True)
        case_frame.leaveEvent = lambda event: self.on_case_hover(case_frame, False)

        # 使用QGridLayout来排列图标
        self.main_window.ui_pages.folder_layout.addWidget(case_frame, row, col)

    def create_colored_png_widget(self, png_path, text):
        image = QImage(png_path)
        painter = QPainter(image)
        painter.setPen(QColor(255, 255, 255))
        font = QFont()
        font.setPointSize(10)
        painter.setFont(font)
        rect = image.rect()
        rect.adjust(10, 10, -10, -10)
        painter.drawText(rect, Qt.AlignCenter | Qt.TextWordWrap, text)
        painter.end()
        png_widget = QLabel()
        png_widget.setPixmap(QPixmap.fromImage(image))
        return png_widget

    def handle_case_click(self, event, case_id, case_frame):
        if event.button() == Qt.LeftButton:
            self.highlight_case(case_frame, case_id)
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event, case_id, case_frame)

    def highlight_case(self, case_frame, case_id):
        self.selected_case_id = case_id
        self.selected_case_frame = case_frame
        for i in range(self.main_window.ui_pages.folder_layout.count()):
            widget = self.main_window.ui_pages.folder_layout.itemAt(i).widget()
            if widget == case_frame:
                widget.setStyleSheet("""
                    background-color: rgba(67, 133, 200, 0.3);
                    border-radius: 10px;
                """)  # 高亮显示，背景颜色
            else:
                widget.setStyleSheet("border: 2px solid transparent;")

    def show_context_menu(self, event, case_id, case_frame):
        context_menu = QMenu(case_frame)
        context_menu.setStyleSheet("""
            QMenu {
                background-color: #ffffff;
                color: #000000;
                border-radius: 8px;
                padding: 10px;
                border: 1px solid #cccccc;
            }
            QMenu::item {
                background-color: transparent;
                padding: 8px 20px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #e6e6e6;
            }
        """)

        import_data_action = context_menu.addAction("导入数据")
        data_analysis_action = context_menu.addAction("数据分析")
        visualize_action = context_menu.addAction("可视化分析")

        action = context_menu.exec(QCursor.pos())

        if action == import_data_action:
            self.open_import_data_window(case_id)
        elif action == data_analysis_action:
            self.open_data_analysis_window(case_id)
        elif action == visualize_action:
            self.open_visualize_window(case_id)

    def open_import_data_window(self, case_id):
        case_name = self.get_case_name(case_id)
        if case_name:
            self.import_data_window = ImportDataWindow(case_id, case_name)
            self.import_data_window.show()

    def open_data_analysis_window(self, case_id):
        """打开数据分析界面（开发中版本）"""
        case_name = self.get_case_name(case_id)
        if case_name:
            # 使用新的开发中界面
            self.data_analysis_widget = create_data_analysis_widget(case_id, case_name, "current_user")

            # 创建窗口容器
            from PySide6.QtWidgets import QMainWindow
            self.data_analysis_window = QMainWindow()
            self.data_analysis_window.setWindowTitle(f"数据分析 - {case_name}")
            self.data_analysis_window.setCentralWidget(self.data_analysis_widget)
            self.data_analysis_window.resize(1000, 700)
            self.data_analysis_window.show()

    def open_visualize_window(self, case_id):
        """打开可视化分析界面（开发中版本）"""
        case_name = self.get_case_name(case_id)
        if case_name:
            # 使用新的开发中界面
            self.visualize_widget = create_visualize_widget(case_id, case_name, "current_user")

            # 创建窗口容器
            from PySide6.QtWidgets import QMainWindow
            self.visualize_window = QMainWindow()
            self.visualize_window.setWindowTitle(f"可视化分析 - {case_name}")
            self.visualize_window.setCentralWidget(self.visualize_widget)
            self.visualize_window.resize(1000, 700)
            self.visualize_window.show()

    def get_case_name(self, case_id):
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            # PostgreSQL语法
            cursor.execute('SELECT "案件名称" FROM "案件信息表" WHERE "案件编号"=%s', (case_id,))
            result = cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            QMessageBox.warning(self.main_window, "错误", f"获取案件名称失败: {e}")
            return None
        finally:
            if conn:
                conn.close()

    def on_case_hover(self, case_frame, is_hovering):
        if is_hovering:
            self.start_hover_timer(case_frame)  # 开始悬停计时
        else:
            if not hasattr(self, 'selected_case_frame') or case_frame != self.selected_case_frame:
                case_frame.setStyleSheet("border: 2px solid transparent;")
            self.stop_hover_timer(case_frame)  # 停止悬停计时

    def start_hover_timer(self, case_frame):
        self.hover_timer = QTimer()
        self.hover_timer.setSingleShot(True)
        self.hover_timer.timeout.connect(lambda: self.show_tooltip(case_frame))
        self.hover_timer.start(1000)  # 悬停1秒后显示提示

    def stop_hover_timer(self, case_frame):
        if hasattr(self, 'hover_timer'):
            self.hover_timer.stop()

    def show_tooltip(self, case_frame):
        QToolTip.showText(QCursor.pos(), "点击右键进入案件", case_frame)

    def query_cases(self):
        self.load_cases()

    def delete_case(self):
        if not hasattr(self, 'selected_case_id'):
            QMessageBox.warning(self.main_window, "错误", "请选择一个案件")
            return

        case_name = self.get_case_name(self.selected_case_id)
        if not case_name:
            QMessageBox.warning(self.main_window, "错误", "无法获取案件信息")
            return
            
        reply = QMessageBox.question(self.main_window, '确认删除',
                                     f'此操作将删除 {case_name} 的所有数据，请谨慎操作。确定删除吗?',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            conn = None
            try:
                conn = get_db_connection()
                cursor = conn.cursor()
                
                # 使用事务确保数据一致性
                conn.autocommit = False
                
                # 删除案件信息和相关数据 - PostgreSQL语法，使用引号包围表名和字段名
                cursor.execute('DELETE FROM "案件信息表" WHERE "案件编号"=%s', (self.selected_case_id,))
                
                # 动态查询所有包含"案件编号"字段的表
                cursor.execute("""
                    SELECT table_name 
                    FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND column_name = '案件编号'
                    AND table_name != '案件信息表'
                """)
                
                tables_with_case_id = cursor.fetchall()
                
                # 删除所有包含当前案件编号的数据
                for (table_name,) in tables_with_case_id:
                    try:
                        cursor.execute(f'DELETE FROM "{table_name}" WHERE "案件编号"=%s', (self.selected_case_id,))
                        deleted_count = cursor.rowcount
                        if deleted_count > 0:
                            print(f"已从表 {table_name} 删除 {deleted_count} 条记录")
                    except Exception as e:
                        print(f"删除表 {table_name} 数据时出错: {e}")
                        # 继续删除其他表，不因单个表错误而中断
                
                # 提交事务
                conn.commit()
                
                # PostgreSQL不需要VACUUM命令，会自动管理空间

                self.selected_case_frame.deleteLater()
                del self.selected_case_id
                del self.selected_case_frame

                QMessageBox.information(self.main_window, "成功", "案件已成功删除")
                self.query_cases()
                
            except psycopg2.Error as e:
                if conn:
                    conn.rollback()
                QMessageBox.warning(self.main_window, "数据库错误", f"删除数据时发生错误: {e}")
            except Exception as e:
                if conn:
                    conn.rollback()
                QMessageBox.warning(self.main_window, "错误", f"删除数据时发生错误: {e}")
            finally:
                if conn:
                    conn.autocommit = True
                    conn.close()