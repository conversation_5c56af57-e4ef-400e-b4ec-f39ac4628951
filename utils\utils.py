import os
import glob
import psycopg2  # 替换 sqlite3 为 psycopg2
from psycopg2 import extras  # 添加extras模块用于字典游标
import logging
import winreg
import traceback
from database_setup import get_db_connection  # 导入数据库连接函数

logger = logging.getLogger(__name__)

def find_database_files(base_dir=None, quick_scan=True):
    """
    查找目录中的数据库文件（现在支持PostgreSQL配置文件）
    
    参数:
        base_dir: 要搜索的基础目录，默认为None时使用当前目录
        quick_scan: 是否使用快速扫描模式，不验证连接
        
    返回:
        数据库配置文件列表
    """
    if base_dir is None:
        base_dir = os.getcwd()
        
    try:
        logger.info(f"开始在 {base_dir} 查找数据库配置文件")
        
        # 查找配置文件而不是.db文件
        config_patterns = [
            os.path.join(base_dir, "**", "*.conf"),
            os.path.join(base_dir, "**", "*.config"),
            os.path.join(base_dir, "**", "database_config.py"),
            os.path.join(base_dir, "**", "database_setup.py")
        ]
        
        config_files = []
        for pattern in config_patterns:
            config_files.extend(glob.glob(pattern, recursive=True))
        
        # 限制扫描数量，防止过多文件导致UI阻塞
        if len(config_files) > 20:
            logger.warning(f"发现超过20个配置文件，仅处理前20个")
            config_files = config_files[:20]
            
        logger.info(f"找到 {len(config_files)} 个配置文件")
        
        # 如果使用快速扫描模式，跳过连接测试
        if quick_scan:
            logger.info("使用快速扫描模式，跳过连接测试")
            return config_files
        
        # 验证PostgreSQL数据库连接是否有效
        valid_configs = []
        for config_file in config_files:
            try:
                # 测试数据库连接（设置较短的超时时间）
                conn = get_db_connection()
                if conn:
                    cursor = conn.cursor()
                    # 使用简单的查询测试连接
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    cursor.close()
                    conn.close()
                    
                    valid_configs.append(config_file)
                    logger.debug(f"验证数据库配置成功: {config_file}")
            except Exception as e:
                logger.error(f"验证数据库配置 {config_file} 时出错: {str(e)}")
                
        logger.info(f"验证完成，有效配置文件数量: {len(valid_configs)}")
        return valid_configs
    except Exception as e:
        logger.error(f"查找数据库配置文件时出错: {str(e)}")
        logger.error(traceback.format_exc())
        # 发生错误时返回空列表，避免程序崩溃
        return []

def format_size(size_bytes):
    """
    格式化文件大小显示
    
    参数:
        size_bytes: 文件大小（字节）
        
    返回:
        格式化后的文件大小字符串
    """
    if size_bytes < 0:
        return "0 B"
        
    units = ["B", "KB", "MB", "GB", "TB"]
    unit_index = 0
    
    while size_bytes >= 1024 and unit_index < len(units) - 1:
        size_bytes /= 1024
        unit_index += 1
        
    return f"{size_bytes:.2f} {units[unit_index]}"

def get_app_icon():
    """
    获取应用程序图标
    
    返回:
        应用程序图标路径
    """
    return os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "assets", "icon.png")

def save_to_registry(key_name, value_name, value):
    """
    保存值到注册表
    
    参数:
        key_name: 注册表键名
        value_name: 值名称
        value: 要保存的值
    
    返回:
        成功返回True，失败返回False
    """
    try:
        # 在HKEY_CURRENT_USER下创建键
        registry_key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, key_name)
        
        # 写入值
        winreg.SetValueEx(registry_key, value_name, 0, winreg.REG_SZ, str(value))
        
        # 关闭键
        winreg.CloseKey(registry_key)
        return True
    except Exception as e:
        logger.error(f"保存到注册表时出错: {str(e)}")
        return False

def read_from_registry(key_name, value_name, default=None):
    """
    从注册表读取值
    
    参数:
        key_name: 注册表键名
        value_name: 值名称
        default: 默认值，如果读取失败则返回此值
    
    返回:
        读取的值，如果读取失败则返回默认值
    """
    try:
        # 打开键
        registry_key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_name)
        
        # 读取值
        value, _ = winreg.QueryValueEx(registry_key, value_name)
        
        # 关闭键
        winreg.CloseKey(registry_key)
        return value
    except Exception as e:
        logger.debug(f"从注册表读取失败: {str(e)}")
        return default

def get_icon(icon_name):
    """
    获取图标文件的路径
    
    参数:
        icon_name: 图标文件名
        
    返回:
        图标文件的完整路径
    """
    icon_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "assets", "icons")
    icon_path = os.path.join(icon_dir, icon_name)
    
    # 检查图标文件是否存在，如果不存在则返回默认图标
    if not os.path.exists(icon_path):
        return get_app_icon()
        
    return icon_path 