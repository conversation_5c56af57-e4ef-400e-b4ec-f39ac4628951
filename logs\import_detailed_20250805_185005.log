2025-08-05 18:50:05.819 - INFO - [MainThread:22204] - enhanced_logging_patch.py:84 - setup_enhanced_logging() - 🔍 增强日志记录已启动
2025-08-05 18:50:05.825 - INFO - [MainThread:22204] - memory_optimizer.py:38 - __init__() - ✅ 内存监控模块已加载
2025-08-05 18:50:05.825 - INFO - [MainThread:22204] - memory_optimizer.py:42 - __init__() - 📊 系统内存信息: 总计 31.7GB, 可用 19.6GB, 使用率 38.3%
2025-08-05 18:50:06.072 - INFO - [MainThread:22204] - database_table_checker.py:417 - check_database_tables() - 开始数据库表格完整性检查...
2025-08-05 18:50:06.140 - INFO - [MainThread:22204] - database_table_checker.py:115 - load_excel_table_config() - 成功读取Excel文件，共94行数据
2025-08-05 18:50:06.159 - INFO - [MainThread:22204] - database_table_checker.py:169 - load_excel_table_config() - 从Excel文件解析出94个有效表配置（处理了94行数据）
2025-08-05 18:50:06.159 - INFO - [MainThread:22204] - database_table_checker.py:182 - load_excel_table_config() - Excel文件统计：总行数94，重复表名0个，有效表格94个
2025-08-05 18:50:06.255 - INFO - [MainThread:22204] - database_table_checker.py:444 - check_database_tables() - 数据库中现有表格总数：106
2025-08-05 18:50:06.255 - INFO - [MainThread:22204] - database_table_checker.py:456 - check_database_tables() - 核心系统表检查：9个必需，9个已存在
