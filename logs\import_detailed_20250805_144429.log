2025-08-05 14:44:29.231 - INFO - [MainThread:9448] - enhanced_logging_patch.py:84 - setup_enhanced_logging() - 🔍 增强日志记录已启动
2025-08-05 14:44:29.232 - WARNING - [MainThread:9448] - memory_optimizer.py:48 - __init__() - ⚠️ psutil模块未安装，无法进行详细内存监控
2025-08-05 14:44:29.232 - WARNING - [MainThread:9448] - memory_optimizer.py:49 - __init__() - 💡 建议安装: pip install psutil
2025-08-05 14:44:29.233 - WARNING - [MainThread:9448] - import_error_handler.py:33 - <module>() - psutil模块不可用，将使用替代的内存监控方案
2025-08-05 14:44:29.238 - INFO - [MainThread:9448] - main.py:63 - setup_comprehensive_logging() - ✅ 全面日志系统初始化完成
2025-08-05 14:44:29.239 - INFO - [MainThread:9448] - main.py:141 - setup_exit_monitoring() - ✅ 程序退出监控系统初始化完成
2025-08-05 14:44:29.240 - DEBUG - [MainThread:9448] - test_enhanced_logging.py:54 - test_different_log_levels() - 这是一条DEBUG日志
2025-08-05 14:44:29.240 - INFO - [MainThread:9448] - test_enhanced_logging.py:55 - test_different_log_levels() - 这是一条INFO日志
2025-08-05 14:44:29.240 - WARNING - [MainThread:9448] - test_enhanced_logging.py:56 - test_different_log_levels() - 这是一条WARNING日志
2025-08-05 14:44:29.240 - ERROR - [MainThread:9448] - test_enhanced_logging.py:57 - test_different_log_levels() - 这是一条ERROR日志
2025-08-05 14:44:29.241 - CRITICAL - [MainThread:9448] - test_enhanced_logging.py:58 - test_different_log_levels() - 这是一条CRITICAL日志
2025-08-05 14:44:29.241 - INFO - [MainThread:9448] - test_enhanced_logging.py:62 - test_different_log_levels() - 这是导入过程日志
2025-08-05 14:44:29.241 - ERROR - [MainThread:9448] - test_enhanced_logging.py:63 - test_different_log_levels() - 这是导入过程错误日志
2025-08-05 14:44:29.241 - ERROR - [MainThread:9448] - test_enhanced_logging.py:66 - test_different_log_levels() - 这是错误跟踪日志
2025-08-05 14:44:29.242 - CRITICAL - [MainThread:9448] - test_enhanced_logging.py:67 - test_different_log_levels() - 这是错误跟踪严重日志
2025-08-05 14:44:29.247 - ERROR - [MainThread:9448] - test_enhanced_logging.py:88 - test_exception_handling() - 捕获到异常: 这是一个测试异常
2025-08-05 14:44:29.250 - ERROR - [MainThread:9448] - test_enhanced_logging.py:93 - test_exception_handling() - 异常详情: Traceback (most recent call last):
  File "G:\数据分析系统20250725\test_enhanced_logging.py", line 86, in test_exception_handling
    cause_exception()
    ~~~~~~~~~~~~~~~^^
  File "G:\数据分析系统20250725\test_enhanced_logging.py", line 83, in cause_exception
    raise ValueError("这是一个测试异常")
ValueError: 这是一个测试异常

2025-08-05 14:44:29.259 - INFO - [MainThread:9448] - test_enhanced_logging.py:110 - test_import_logging() - ================================================================================
2025-08-05 14:44:29.259 - INFO - [MainThread:9448] - test_enhanced_logging.py:111 - test_import_logging() - 🚀 模拟数据导入开始
2025-08-05 14:44:29.259 - INFO - [MainThread:9448] - test_enhanced_logging.py:112 - test_import_logging() - 线程ID: 12345
2025-08-05 14:44:29.260 - INFO - [MainThread:9448] - test_enhanced_logging.py:113 - test_import_logging() - 进程ID: 26856
2025-08-05 14:44:29.260 - INFO - [MainThread:9448] - test_enhanced_logging.py:114 - test_import_logging() - 文件数量: 5
2025-08-05 14:44:29.261 - INFO - [MainThread:9448] - test_enhanced_logging.py:115 - test_import_logging() - ================================================================================
2025-08-05 14:44:29.261 - INFO - [MainThread:9448] - test_enhanced_logging.py:119 - test_import_logging() - 📁 开始处理文件 1/3: test_file_1.xlsx
2025-08-05 14:44:29.261 - INFO - [MainThread:9448] - test_enhanced_logging.py:120 - test_import_logging() -   文件路径: /path/to/test_file_1.xlsx
2025-08-05 14:44:29.261 - INFO - [MainThread:9448] - test_enhanced_logging.py:121 - test_import_logging() -   文件类型: 账户交易明细表
2025-08-05 14:44:29.262 - INFO - [MainThread:9448] - test_enhanced_logging.py:122 - test_import_logging() -   数据行数: 1000
2025-08-05 14:44:29.362 - INFO - [MainThread:9448] - test_enhanced_logging.py:126 - test_import_logging() - ✅ 文件 1 处理完成，导入 1000 条记录
2025-08-05 14:44:29.365 - INFO - [MainThread:9448] - test_enhanced_logging.py:119 - test_import_logging() - 📁 开始处理文件 2/3: test_file_2.xlsx
2025-08-05 14:44:29.366 - INFO - [MainThread:9448] - test_enhanced_logging.py:120 - test_import_logging() -   文件路径: /path/to/test_file_2.xlsx
2025-08-05 14:44:29.367 - INFO - [MainThread:9448] - test_enhanced_logging.py:121 - test_import_logging() -   文件类型: 账户交易明细表
2025-08-05 14:44:29.367 - INFO - [MainThread:9448] - test_enhanced_logging.py:122 - test_import_logging() -   数据行数: 1500
2025-08-05 14:44:29.471 - INFO - [MainThread:9448] - test_enhanced_logging.py:126 - test_import_logging() - ✅ 文件 2 处理完成，导入 1500 条记录
2025-08-05 14:44:29.475 - INFO - [MainThread:9448] - test_enhanced_logging.py:119 - test_import_logging() - 📁 开始处理文件 3/3: test_file_3.xlsx
2025-08-05 14:44:29.476 - INFO - [MainThread:9448] - test_enhanced_logging.py:120 - test_import_logging() -   文件路径: /path/to/test_file_3.xlsx
2025-08-05 14:44:29.477 - INFO - [MainThread:9448] - test_enhanced_logging.py:121 - test_import_logging() -   文件类型: 账户交易明细表
2025-08-05 14:44:29.477 - INFO - [MainThread:9448] - test_enhanced_logging.py:122 - test_import_logging() -   数据行数: 2000
2025-08-05 14:44:29.578 - INFO - [MainThread:9448] - test_enhanced_logging.py:126 - test_import_logging() - ✅ 文件 3 处理完成，导入 2000 条记录
2025-08-05 14:44:29.581 - INFO - [MainThread:9448] - test_enhanced_logging.py:128 - test_import_logging() - ✅ 模拟导入完成
2025-08-05 14:44:29.602 - INFO - [MainThread:9448] - test_enhanced_logging.py:190 - test_memory_and_system_info() - psutil未安装，跳过系统信息记录
2025-08-05 14:44:29.603 - CRITICAL - [MainThread:9448] - test_enhanced_logging.py:215 - simulate_crash_scenario() - 🚨 模拟程序崩溃: {'exception_type': 'MemoryError', 'exception_message': '内存不足', 'timestamp': '2025-08-05T14:44:29.603490', 'thread_id': 12345, 'process_id': 26856}
2025-08-05 14:44:29.618 - CRITICAL - [MainThread:9448] - enhanced_logging_patch.py:322 - monitored_exit() - 🚨 程序即将退出! 退出码: 0
2025-08-05 14:44:29.619 - CRITICAL - [MainThread:9448] - enhanced_logging_patch.py:323 - monitored_exit() - 退出时的堆栈跟踪:
2025-08-05 14:44:29.620 - CRITICAL - [MainThread:9448] - enhanced_logging_patch.py:328 - monitored_exit() -   File "G:\数据分析系统20250725\test_enhanced_logging.py", line 293, in <module>
    sys.exit(0 if success else 1)
2025-08-05 14:44:29.621 - CRITICAL - [MainThread:9448] - enhanced_logging_patch.py:328 - monitored_exit() -   File "G:\数据分析系统20250725\enhanced_logging_patch.py", line 326, in monitored_exit
    stack = traceback.format_stack()
2025-08-05 14:44:29.622 - DEBUG - [MainThread:9448] - enhanced_logging_patch.py:168 - log_memory_usage() - psutil未安装，无法监控内存使用
2025-08-05 14:44:29.622 - DEBUG - [MainThread:9448] - enhanced_logging_patch.py:210 - log_thread_status() - 🧵 当前线程: MainThread (ID: 9448)
2025-08-05 14:44:29.622 - DEBUG - [MainThread:9448] - enhanced_logging_patch.py:211 - log_thread_status() - 🧵 活跃线程数: 1
2025-08-05 14:44:29.622 - DEBUG - [MainThread:9448] - enhanced_logging_patch.py:215 - log_thread_status() -    - MainThread: 运行中
2025-08-05 14:44:29.622 - INFO - [MainThread:9448] - main.py:116 - cleanup_on_exit() - 程序正常退出，执行清理操作
2025-08-05 14:44:29.623 - INFO - [MainThread:9448] - main.py:122 - cleanup_on_exit() - 程序运行时间: 0:00:00.383601
