# IMPORTS
import os

# IMPORT QT CORE
from qt_core import *

class PyPushButton(QPushButton):
    def __init__(
        self,
        text = "",
        height = 40,
        minimum_width = 50,
        text_padding = 55,
        text_color = "#c3ccdf",
        icon_path = "",
        icon_color = "#c3ccdf",
        btn_color = "#44475a",
        btn_hover = "#4f5368",
        btn_pressed = "#282a36",
        is_active = False
    ):
        super().__init__()

        # Set default parametros
        self.setText(text)
        self.setMaximumHeight(height)
        self.setMinimumHeight(height)
        self.setCursor(Qt.PointingHandCursor)

        # Custom parameters
        self.minimum_width = minimum_width
        self.text_padding = text_padding
        self.text_color = text_color
        self.icon_path = icon_path
        self.icon_color = icon_color
        self.btn_color = btn_color
        self.btn_hover = btn_hover
        self.btn_pressed = btn_pressed
        self.is_active = is_active

        # Set style
        self.set_style(
            text_padding = self.text_padding,
            text_color = self.text_color,
            btn_color = self.btn_color,
            btn_hover = self.btn_hover,
            btn_pressed = self.btn_pressed,
            is_active = self.is_active
        )
    
    def set_active(self, is_active_menu):
        self.set_style(
            text_padding = self.text_padding,
            text_color = self.text_color,
            btn_color = self.btn_color,
            btn_hover = self.btn_hover,
            btn_pressed = self.btn_pressed,
            is_active = is_active_menu
        )

    def set_style(
        self,
        text_padding = 55,
        text_color = "#c3ccdf",
        btn_color = "#44475a",
        btn_hover = "#4f5368",
        btn_pressed = "#282a36",
        is_active = False
    ):
        style = f"""
        QPushButton {{
            color: {text_color};
            background-color: {btn_color};
            padding-left: {text_padding}px;
            text-align: left;
            border: none;
        }}
        QPushButton:hover {{
            background-color: {btn_hover};
        }}
        QPushButton:pressed {{
            background-color: {btn_pressed};
        }}
        """

        active_style = f"""
        QPushButton {{
            background-color: {btn_hover};
            border-right: 5px solid #282a36;
        }}
        """
        if not is_active:
            self.setStyleSheet(style)
        else:
            self.setStyleSheet(style + active_style)

    def paintEvent(self, event):
        # Return default style
        QPushButton.paintEvent(self, event)

        # Painter
        qp = QPainter()
        qp.begin(self)
        qp.setRenderHint(QPainter.Antialiasing)
        qp.setPen(Qt.NoPen)

        rect = QRect(0, 0, self.minimum_width, self.height())

        # 只有当图标路径不为空时才绘制图标
        if self.icon_path:
            self.draw_icon(qp, self.icon_path, rect, self.icon_color)

        qp.end()

    def draw_icon(self, qp, image, rect, color):
        try:
            # 构建图标路径
            app_path = os.path.abspath(os.getcwd())
            folder = "gui/images/icons"
            path = os.path.join(app_path, folder)
            icon_path = os.path.normpath(os.path.join(path, image))
            
            # 检查文件是否存在
            if not os.path.exists(icon_path):
                print(f"⚠️ 图标文件不存在: {icon_path}")
                return
            
            # 处理SVG图标
            if icon_path.lower().endswith('.svg'):
                # 使用QSvgRenderer处理SVG文件
                from PySide6.QtSvg import QSvgRenderer
                renderer = QSvgRenderer(icon_path)
                if renderer.isValid():
                    # 创建一个pixmap来渲染SVG
                    icon_size = min(rect.width() - 10, rect.height() - 10)  # 留一些边距
                    icon = QPixmap(icon_size, icon_size)
                    icon.fill(Qt.transparent)
                    
                    painter = QPainter(icon)
                    painter.setRenderHint(QPainter.Antialiasing)
                    renderer.render(painter)
                    painter.end()
                    
                    # 应用颜色
                    colored_icon = QPixmap(icon.size())
                    colored_icon.fill(Qt.transparent)
                    
                    painter = QPainter(colored_icon)
                    painter.setCompositionMode(QPainter.CompositionMode_SourceOver)
                    painter.drawPixmap(0, 0, icon)
                    painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
                    painter.fillRect(colored_icon.rect(), QColor(color))
                    painter.end()
                    
                    # 绘制到按钮上
                    x = (rect.width() - colored_icon.width()) // 2
                    y = (rect.height() - colored_icon.height()) // 2
                    qp.drawPixmap(x, y, colored_icon)
                else:
                    print(f"⚠️ 无效的SVG文件: {icon_path}")
            else:
                # 处理其他格式的图标（PNG, ICO等）
                icon = QPixmap(icon_path)
                if not icon.isNull():
                    # 缩放图标以适应按钮
                    icon_size = min(rect.width() - 10, rect.height() - 10)
                    icon = icon.scaled(icon_size, icon_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    
                    # 应用颜色
                    colored_icon = QPixmap(icon.size())
                    colored_icon.fill(Qt.transparent)
                    
                    painter = QPainter(colored_icon)
                    painter.setCompositionMode(QPainter.CompositionMode_SourceOver)
                    painter.drawPixmap(0, 0, icon)
                    painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
                    painter.fillRect(colored_icon.rect(), QColor(color))
                    painter.end()
                    
                    # 绘制到按钮上
                    x = (rect.width() - colored_icon.width()) // 2
                    y = (rect.height() - colored_icon.height()) // 2
                    qp.drawPixmap(x, y, colored_icon)
                else:
                    print(f"⚠️ 无法加载图标: {icon_path}")
                    
        except Exception as e:
            print(f"⚠️ 绘制图标时发生错误: {e}")
            print(f"   图标路径: {icon_path if 'icon_path' in locals() else 'N/A'}")
