#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查表类型匹配规则脚本
"""

from database_setup import get_db_connection
import logging

# 🔧 修复：使用统一的日志配置
from logger_config import setup_script_logger
logger = setup_script_logger('check_rules')



def check_table_type_rules():
    """检查表类型匹配规则"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print('=== 检查表类型匹配规则 ===')
        
        # 查找账户信息和金融账户信息相关规则
        cursor.execute("""
            SELECT id, 文件关键词, 工作表名, 数据库表名, 备注 
            FROM 表类型匹配规则表 
            WHERE 文件关键词 LIKE '%账户信息%' OR 文件关键词 LIKE '%金融账户%'
            ORDER BY 文件关键词, 工作表名
        """)
        
        rules = cursor.fetchall()
        print(f'找到 {len(rules)} 条相关规则:')
        for rule in rules:
            print(f'  ID={rule[0]}: "{rule[1]}" + "{rule[2]}" -> {rule[3]}')
        
        print('\n=== 检查数据库中的表 ===')
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND (table_name LIKE '%账户信息%' OR table_name LIKE '%金融账户%')
            ORDER BY table_name
        """)
        
        tables = cursor.fetchall()
        print(f'找到 {len(tables)} 个相关表:')
        for table in tables:
            print(f'  - {table[0]}')
        
        print('\n=== 测试具体匹配场景 ===')
        test_cases = [
            ("账户信息_客户基本信息.xlsx", "客户基本信息"),
            ("金融账户信息_某文件.xlsx", "某工作表"),
            ("中国航空_航班同行人信息_001_胡迪_522701198211250718.xlsx", "同订单同行人已成行")
        ]
        
        for file_name, worksheet_name in test_cases:
            print(f"\n测试: {file_name} + 工作表 '{worksheet_name}'")
            
            # 提取关键词（模拟系统逻辑）
            base_name = file_name.replace('.xlsx', '')
            parts = base_name.split('_')
            
            if len(parts) >= 2:
                primary_keyword = parts[0]
                secondary_keyword = '_'.join(parts[:2])
            else:
                primary_keyword = parts[0] if parts else base_name
                secondary_keyword = base_name
            
            print(f"  提取关键词: 主要='{primary_keyword}', 次要='{secondary_keyword}'")
            
            # 测试精确匹配（文件关键词 + 工作表名）
            cursor.execute("""
                SELECT 数据库表名 FROM 表类型匹配规则表 
                WHERE %s LIKE '%%' || 文件关键词 || '%%' AND 工作表名 = %s
                ORDER BY LENGTH(文件关键词) DESC
                LIMIT 1
            """, (base_name, worksheet_name))
            result = cursor.fetchone()
            
            if result:
                print(f"  ✅ 精确匹配成功: {result[0]}")
            else:
                # 测试固定规则匹配
                fixed_rules = {
                    "开户信息表": ["账户基本信息", "开户信息", "开户", "人民银行"],
                    "临时账户交易明细表": ["交易流水", "交易明细", "银行流水", "流水明细"],
                    "财付通交易明细表": ["tenpaytrades", "腾讯", "财付通", "微信支付"],
                }
                
                file_name_lower = file_name.lower()
                matched_fixed = False
                for table_type, keywords in fixed_rules.items():
                    for keyword in keywords:
                        if keyword.lower() in file_name_lower:
                            print(f"  ✅ 固定规则匹配: 文件名包含'{keyword}' → {table_type}")
                            matched_fixed = True
                            break
                    if matched_fixed:
                        break
                
                if not matched_fixed:
                    print(f"  ❌ 未找到匹配规则")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f'错误: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_table_type_rules() 