"""
数据库表检查器
功能：在系统启动时检查数据库中是否存在必需的表格，如果缺少则自动创建
实现逻辑：
1. 检查12个核心系统表（硬编码定义）
2. 从Excel配置文件读取额外的表格定义（第一列序号，第二列表名，第三列开始是字段）
3. 智能处理字段重复、表名重复问题
4. 自动推断字段类型并创建缺失的表格
5. 生成详细的检查和创建报告
"""

import psycopg2
import pandas as pd
import re
from pathlib import Path
from database_setup import get_db_connection

# 🔧 修复：使用统一的日志配置
from logger_config import setup_script_logger
logger = setup_script_logger('database_table_checker')

def infer_field_type(field_name):
    """根据字段名推断PostgreSQL数据类型 - 简化版本，主要使用TEXT类型避免数据类型问题"""
    field_name = str(field_name).lower()
    
    # 系统字段保持原有类型
    if field_name in ['id']:
        return 'SERIAL PRIMARY KEY'
    
    # 案件编号使用VARCHAR
    if field_name in ['案件编号']:
        return 'VARCHAR(50)'
    
    # 源文件位置使用TEXT（可能很长）
    if field_name in ['源文件位置']:
        return 'TEXT'
    
    # 导入批次使用VARCHAR
    if field_name in ['导入批次']:
        return 'VARCHAR(50)'
    
    # 所有其他字段统一使用TEXT类型，避免数据类型不匹配问题
    # 这样可以确保：
    # 1. 不会出现"整数超出范围"错误
    # 2. 不会出现日期格式不匹配错误
    # 3. 不会出现小数精度问题
    # 4. 可以存储任何格式的数据
    return 'TEXT'

def sanitize_identifier(name):
    """清理标识符，确保符合PostgreSQL规范"""
    # 移除或替换特殊字符
    name = re.sub(r'[^\w\u4e00-\u9fff]', '_', str(name))
    # 确保不以数字开头
    if name and name[0].isdigit():
        name = 'T_' + name
    # PostgreSQL会自动截断超过63字节的标识符
    # 对于中文字符，每个字符占3字节，所以实际限制约为21个中文字符
    if len(name.encode('utf-8')) > 63:
        # 保持前60字节，并计算实际字符数
        encoded = name.encode('utf-8')
        if len(encoded) > 60:
            # 截断到60字节，避免截断中文字符
            truncated = encoded[:60]
            # 处理可能的不完整字符
            while len(truncated) > 0:
                try:
                    name = truncated.decode('utf-8')
                    break
                except UnicodeDecodeError:
                    truncated = truncated[:-1]
        logger.debug(f"表名过长已截断: 原始{len(name)}字符 -> 截断后{len(name)}字符")
    return name

def get_truncated_name(original_name):
    """获取PostgreSQL可能截断后的表名"""
    return sanitize_identifier(original_name)

def deduplicate_fields(fields):
    """去重字段名，对重复字段添加序号"""
    seen = {}
    result = []
    
    for field in fields:
        field = str(field).strip()
        if not field:
            continue
            
        clean_field = sanitize_identifier(field)
        
        if clean_field in seen:
            seen[clean_field] += 1
            numbered_field = f"{clean_field}_{seen[clean_field]}"
            result.append(numbered_field)
        else:
            seen[clean_field] = 0
            result.append(clean_field)
    
    return result

def load_excel_table_config():
    """
    从Excel文件加载表格配置
    文件结构：第一列序号，第二列表名，第三列开始是字段
    """
    excel_path = Path('数据库表格配置.xlsx')
    
    if not excel_path.exists():
        logger.warning(f"Excel配置文件不存在: {excel_path}")
        return {}
    
    try:
        # 读取Excel文件，没有标题行
        df = pd.read_excel(excel_path, sheet_name='Sheet1', header=None)
        logger.info(f"成功读取Excel文件，共{len(df)}行数据")
        
        table_configs = {}
        table_name_counter = {}
        processed_count = 0
        
        for index, row in df.iterrows():
            # 第一列是序号（索引0），第二列是表名（索引1）
            table_name = row.iloc[1]
            
            if pd.isna(table_name) or str(table_name).strip() == '':
                logger.debug(f"第{index+1}行表名为空，跳过")
                continue
            
            table_name = str(table_name).strip()
            original_table_name = table_name
            
            # 处理重复表名
            if table_name in table_name_counter:
                table_name_counter[table_name] += 1
                table_name = f"{table_name}_{table_name_counter[table_name]}"
                logger.warning(f"发现重复表名'{original_table_name}'，重命名为'{table_name}'")
            else:
                table_name_counter[original_table_name] = 0
            
            # 从第三列开始获取字段（索引2开始）
            row_fields = row.iloc[2:]
            fields = []
            for field in row_fields:
                if pd.notna(field) and str(field).strip() != '':
                    field_clean = str(field).strip()
                    if field_clean:
                        fields.append(field_clean)
            
            if not fields:
                logger.warning(f"表'{table_name}'没有定义字段，跳过")
                continue
            
            # 去重字段
            deduplicated_fields = deduplicate_fields(fields)
            
            if len(deduplicated_fields) != len(fields):
                logger.info(f"表'{table_name}'存在重复字段，已去重：{len(fields)} -> {len(deduplicated_fields)}")
            
            # 用户已完善数据库表名，不再需要表名清理
            # clean_table_name = sanitize_identifier(table_name)
            # if clean_table_name != table_name:
            #     logger.info(f"表名已清理：'{table_name}' -> '{clean_table_name}'")
            #     table_name = clean_table_name
            
            table_configs[table_name] = deduplicated_fields
            processed_count += 1
            logger.debug(f"配置表'{table_name}'，字段数：{len(deduplicated_fields)}")
        
        logger.info(f"从Excel文件解析出{len(table_configs)}个有效表配置（处理了{processed_count}行数据）")
        
        # 显示重复表名统计
        duplicate_count = sum(1 for count in table_name_counter.values() if count > 0)
        if duplicate_count > 0:
            logger.info(f"处理了{duplicate_count}个重复表名")
            duplicate_tables = {name: count for name, count in table_name_counter.items() if count > 0}
            for name, count in duplicate_tables.items():
                logger.info(f"  '{name}' 重复了{count}次")
        
        # 解释71 vs 69的差异
        total_rows = len(df)
        unique_tables = len(table_configs)
        logger.info(f"Excel文件统计：总行数{total_rows}，重复表名{duplicate_count}个，有效表格{unique_tables}个")
        
        return table_configs
        
    except Exception as e:
        logger.error(f"读取Excel配置文件时出错：{e}")
        return {}

def generate_create_table_sql(table_name, fields):
    """生成CREATE TABLE SQL语句"""
    if not fields:
        raise ValueError(f"表{table_name}没有字段定义")
    
    field_definitions = []
    has_primary_key = False
    
    for field in fields:
        field_type = infer_field_type(field)
        
        # 检查是否已有主键字段
        if 'PRIMARY KEY' in field_type:
            has_primary_key = True
        
        field_definitions.append(f'"{field}" {field_type}')
    
    # 如果没有主键字段，添加一个自增ID主键
    if not has_primary_key:
        field_definitions.insert(0, 'id SERIAL PRIMARY KEY')
    
    # 添加系统字段（如果不存在的话）
    system_fields = {
        '案件编号': 'VARCHAR(50)',
        '源文件位置': 'TEXT', 
        '导入批次': 'VARCHAR(50)'
    }
    
    existing_fields = {field.lower() for field in fields}
    for sys_field, sys_type in system_fields.items():
        if sys_field not in existing_fields:
            field_definitions.append(f'"{sys_field}" {sys_type}')
    
    fields_sql = ',\n    '.join(field_definitions)
    
    sql = f'''CREATE TABLE IF NOT EXISTS "{table_name}" (
    {fields_sql}
)'''
    
    return sql

def check_table_exists_smart(cursor, table_name):
    """智能检查表是否存在，考虑PostgreSQL的表名截断"""
    try:
        # 首先尝试精确匹配
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = %s
            )
        """, (table_name,))
        
        if cursor.fetchone()[0]:
            logger.debug(f"表'{table_name}'精确匹配存在")
            return True, table_name
        
        # 如果精确匹配失败，尝试截断后的名称
        truncated_name = get_truncated_name(table_name)
        if truncated_name != table_name:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_name = %s
                )
            """, (truncated_name,))
            
            if cursor.fetchone()[0]:
                logger.debug(f"表'{table_name}'通过截断名称'{truncated_name}'匹配存在")
                return True, truncated_name
        
        # 如果还是失败，尝试模糊匹配（处理可能的编码问题）
        cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            AND table_name LIKE %s
        """, (table_name[:20] + '%',))  # 用前20个字符做模糊匹配
        
        matches = cursor.fetchall()
        if matches:
            matched_name = matches[0][0]
            logger.debug(f"表'{table_name}'通过模糊匹配找到'{matched_name}'")
            return True, matched_name
        
        logger.debug(f"表'{table_name}'不存在")
        return False, None
        
    except Exception as e:
        logger.error(f"检查表'{table_name}'存在性时出错：{e}")
        return False, None

def check_table_exists(cursor, table_name):
    """检查表是否存在（兼容性接口）"""
    exists, _ = check_table_exists_smart(cursor, table_name)
    return exists

def create_table_with_retry(table_name, fields, max_retries=3):
    """创建表格，支持重试机制"""
    for attempt in range(max_retries):
        conn = None
        try:
            # 使用独立连接
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 再次检查表是否已存在（双重检查）
            if check_table_exists(cursor, table_name):
                logger.debug(f"表'{table_name}'已存在，跳过创建")
                cursor.close()
                conn.close()
                return True, "表已存在"
            
            # 生成并记录SQL
            create_sql = generate_create_table_sql(table_name, fields)
            logger.debug(f"准备创建表'{table_name}'，SQL语句：\n{create_sql}")
            
            # 执行创建
            cursor.execute(create_sql)
            conn.commit()
            
            # 验证表是否真的创建成功
            if check_table_exists(cursor, table_name):
                logger.info(f"✓ 成功创建表'{table_name}'，字段数：{len(fields)}")
                cursor.close()
                conn.close()
                return True, "创建成功"
            else:
                logger.error(f"表'{table_name}'创建后验证失败，可能创建未成功")
                cursor.close()
                conn.close()
                return False, "创建后验证失败"
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"创建表'{table_name}'失败 (尝试{attempt+1}/{max_retries})：{error_msg}")
            
            if conn:
                try:
                    conn.rollback()
                    conn.close()
                except Exception as close_e:
                    logger.error(f"关闭连接时出错：{close_e}")
            
            if attempt == max_retries - 1:
                return False, f"重试{max_retries}次后仍失败：{error_msg}"
            
            # 短暂等待后重试
            import time
            time.sleep(0.1 * (attempt + 1))  # 递增等待时间
    
    return False, "重试次数用尽"

def update_table_column_types(conn, cursor):
    """更新现有表的字段类型，使其与推断类型一致"""
    try:
        logger.info("开始检查并更新表字段类型...")
        
        # 获取所有表及其字段信息
        cursor.execute("""
            SELECT c.table_name, c.column_name, c.data_type 
            FROM information_schema.columns c
            JOIN information_schema.tables t ON c.table_name = t.table_name AND c.table_schema = t.table_schema
            WHERE c.table_schema = 'public' AND t.table_type = 'BASE TABLE'
            ORDER BY c.table_name, c.ordinal_position
        """)
        columns_info = cursor.fetchall()
        
        # 按表名分组
        tables = {}
        for table_name, column_name, data_type in columns_info:
            if table_name not in tables:
                tables[table_name] = []
            tables[table_name].append((column_name, data_type))
        
        # 统计信息
        updated_columns = 0
        skipped_columns = 0
        failed_columns = 0
        
        # 遍历每个表的每个字段
        for table_name, columns in tables.items():
            logger.info(f"检查表 '{table_name}' 的字段类型...")
            
            for column_name, current_type in columns:
                # 跳过id字段和系统字段
                if column_name.lower() == 'id' or column_name.startswith('_'):
                    skipped_columns += 1
                    continue
                
                # 推断理想的字段类型
                inferred_type = infer_field_type(column_name)
                
                # 简化当前类型（去掉精度等信息）
                current_type_base = current_type.split('(')[0].upper()
                inferred_type_base = inferred_type.split('(')[0].upper()
                
                # 特殊处理日期时间类型
                if (current_type_base == 'TEXT' or current_type_base == 'VARCHAR') and (
                    inferred_type_base == 'DATE' or inferred_type_base == 'TIMESTAMP'):
                    try:
                        # 尝试更新字段类型
                        alter_sql = f'ALTER TABLE "{table_name}" ALTER COLUMN "{column_name}" TYPE {inferred_type} USING "{column_name}"::{inferred_type};'
                        logger.info(f"更新字段类型: {table_name}.{column_name}: {current_type} -> {inferred_type}")
                        cursor.execute(alter_sql)
                        conn.commit()
                        updated_columns += 1
                    except Exception as e:
                        logger.error(f"更新字段类型失败: {table_name}.{column_name}: {e}")
                        conn.rollback()
                        failed_columns += 1
                else:
                    # 其他类型暂不更新，避免数据丢失
                    skipped_columns += 1
        
        # 输出统计信息
        logger.info(f"字段类型更新完成: 已更新 {updated_columns} 个字段, 跳过 {skipped_columns} 个字段, 失败 {failed_columns} 个字段")
        return updated_columns > 0
    
    except Exception as e:
        logger.error(f"更新表字段类型时出错: {e}")
        return False

def check_database_tables():
    """
    检查数据库表格完整性
    支持Excel配置自动更新、详细报告、错误恢复
    """
    try:
        logger.info("开始数据库表格完整性检查...")
        
        # 定义核心系统表（硬编码，必需存在）- 已调整为8个核心表
        # 注意：开户信息表、账户交易明细表、财付通交易明细表已改为数据表（由Excel配置动态创建）
        core_tables = [
            '用户信息表', '系统信息表', '案件信息表', '导入记录表',
            '临时账户交易明细表', '字段匹配规则', '系统配置表', 
            '表类型匹配规则表', '对手信息'
        ]
        
        # 从Excel配置文件加载额外表格
        excel_table_configs = load_excel_table_config()
        
        # 连接数据库检查现有表
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取数据库中现有的表（详细查询）
        cursor.execute("""
            SELECT table_name, table_type, table_schema 
            FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            ORDER BY table_name
        """)
        existing_tables_raw = cursor.fetchall()
        existing_tables = set(row[0] for row in existing_tables_raw)
        
        logger.info(f"数据库中现有表格总数：{len(existing_tables)}")
        logger.debug(f"现有表格列表：{sorted(existing_tables)}")
        
        # 检查核心表
        missing_core_tables = []
        existing_core_tables = []
        for table in core_tables:
            if table in existing_tables:
                existing_core_tables.append(table)
            else:
                missing_core_tables.append(table)
                
        logger.info(f"核心系统表检查：{len(core_tables)}个必需，{len(existing_core_tables)}个已存在")
        if missing_core_tables:
            logger.warning(f"缺失的核心表：{missing_core_tables}")
            
            # 自动创建缺失的核心表
            logger.info("正在自动创建缺失的核心系统表...")
            try:
                from database_setup import create_tables_if_not_exists
                create_tables_if_not_exists(cursor)
                logger.info("✓ 核心系统表创建完成")
                
                # 重新检查核心表状态
                cursor.execute("""
                    SELECT table_name FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
                """)
                updated_existing_tables = set(row[0] for row in cursor.fetchall())
                
                # 更新核心表状态
                missing_core_tables = []
                existing_core_tables = []
                for table in core_tables:
                    if table in updated_existing_tables:
                        existing_core_tables.append(table)
                    else:
                        missing_core_tables.append(table)
                        
                logger.info(f"核心表创建后状态：{len(existing_core_tables)}个已存在，{len(missing_core_tables)}个仍缺失")
                
            except Exception as e:
                logger.error(f"创建核心系统表时出错：{e}")
                # 继续执行，不中断检查流程
        
        # 检查Excel配置表
        missing_excel_tables = []
        existing_excel_tables = []
        for table_name in excel_table_configs.keys():
            # 使用智能匹配检查表是否存在
            conn_temp = get_db_connection()
            cursor_temp = conn_temp.cursor()
            exists, matched_name = check_table_exists_smart(cursor_temp, table_name)
            cursor_temp.close()
            conn_temp.close()
            
            if exists:
                existing_excel_tables.append(table_name)
                if matched_name != table_name:
                    logger.debug(f"表'{table_name}'通过匹配名称'{matched_name}'找到")
            else:
                missing_excel_tables.append(table_name)
        
        logger.info(f"Excel配置表检查：{len(excel_table_configs)}个定义，{len(existing_excel_tables)}个已存在")
        if missing_excel_tables:
            logger.info(f"需要创建的Excel表：{missing_excel_tables}")
        else:
            logger.info("所有Excel配置表都已存在，无需创建")
        
        # 创建缺失的表
        created_tables = []
        failed_tables = []
        skipped_tables = []
        
        # 只处理真正缺失的表
        tables_to_create = missing_excel_tables
        
        if tables_to_create:
            logger.info(f"开始创建{len(tables_to_create)}个缺失的表...")
            
            for table_name in tables_to_create:
                fields = excel_table_configs[table_name]
                logger.info(f"正在创建表'{table_name}'...")
                
                success, message = create_table_with_retry(table_name, fields)
                
                if success:
                    if "已存在" in message:
                        skipped_tables.append(table_name)
                        logger.info(f"  → 跳过（表已存在）：{table_name}")
                    else:
                        created_tables.append(table_name)
                        logger.info(f"  ✓ 创建成功：{table_name}")
                else:
                    failed_tables.append({'table': table_name, 'error': message})
                    logger.error(f"  ✗ 创建失败：{table_name} - {message}")
        else:
            logger.info("所有表都已存在，无需创建任何表")
        
        # 更新现有表的字段类型
        types_updated = update_table_column_types(conn, cursor)
        if types_updated:
            logger.info("✅ 已更新部分表字段的数据类型")
        
        # 最终验证 - 重新检查数据库状态
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        """)
        final_existing_tables = set(row[0] for row in cursor.fetchall())
        cursor.close()
        conn.close()
        
        # 统计结果
        total_required = len(core_tables) + len(excel_table_configs)
        total_existing = len(final_existing_tables.intersection(set(core_tables + list(excel_table_configs.keys()))))
        
        # 生成检查报告
        check_results = {
            'check_success': len(missing_core_tables) == 0,
            'total_required': total_required,
            'existing_tables': final_existing_tables,
            'missing_tables': missing_core_tables + missing_excel_tables,
            'created_tables': created_tables,
            'skipped_tables': skipped_tables,
            'failed_tables': failed_tables,
            'core_tables_count': len(core_tables),
            'excel_tables_count': len(excel_table_configs),
            'excel_tables_created': len([t for t in created_tables if t in excel_table_configs]),
            'excel_tables_failed': len([t for t in failed_tables if t['table'] in excel_table_configs]),
            'types_updated': types_updated
        }
        
        # 输出详细报告
        logger.info("=== 数据库表格检查报告 ===")
        logger.info(f"总计检查表格：{total_required}个 (核心{len(core_tables)}个 + Excel{len(excel_table_configs)}个)")
        logger.info(f"最终存在表格：{total_existing}个")
        
        if created_tables:
            logger.info(f"本次新创建表格：{len(created_tables)}个")
            for table in created_tables:
                logger.info(f"  ✓ {table}")
        else:
            logger.info("本次未创建任何新表格（所有表都已存在）")
        
        if skipped_tables:
            logger.info(f"跳过的表格（已存在）：{len(skipped_tables)}个")
            for table in skipped_tables[:3]:  # 只显示前3个
                logger.info(f"  - {table}")
            if len(skipped_tables) > 3:
                logger.info(f"  ... 还有{len(skipped_tables)-3}个")
        
        if failed_tables:
            logger.error(f"创建失败的表格：{len(failed_tables)}个")
            for failed in failed_tables:
                logger.error(f"  ✗ {failed['table']}: {failed['error']}")
        
        if missing_core_tables:
            logger.error(f"缺失核心表格：{missing_core_tables}")
            check_results['error_message'] = f"缺失核心系统表：{missing_core_tables}"
        
        return check_results
        
    except Exception as e:
        error_msg = f"数据库表格检查过程中发生错误：{str(e)}"
        logger.error(error_msg)
        return {
            'check_success': False,
            'error_message': error_msg,
            'total_required': 0,
            'existing_tables': set(),
            'missing_tables': [],
            'created_tables': [],
            'skipped_tables': [],
            'failed_tables': [],
            'core_tables_count': 0,
            'excel_tables_count': 0
        }

def is_system_ready():
    """检查系统是否准备就绪（所有核心表都存在）"""
    try:
        results = check_database_tables()
        return results['check_success']
    except Exception as e:
        logger.error(f"系统就绪检查失败：{e}")
        return False

if __name__ == "__main__":
    # 测试代码
    results = check_database_tables()
    print(f"检查结果：{results}") 