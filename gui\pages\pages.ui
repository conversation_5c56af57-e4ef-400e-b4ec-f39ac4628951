<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>application_pages</class>
 <widget class="QStackedWidget" name="application_pages">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1056</width>
    <height>657</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>StackedWidget</string>
  </property>
  <widget class="QWidget" name="page_1">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QLineEdit" name="lineEdit">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>36</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>36</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QLineEdit {
	background-color: rgb(68, 71, 90);
	padding: 8px;
	border: 2px solid #c3ccdf;
	color: rgb(255, 255, 255);
	border-radius: 10px;
}</string>
      </property>
      <property name="placeholderText">
       <string>Escreva o seu nome</string>
      </property>
     </widget>
    </item>
    <item alignment="Qt::AlignHCenter">
     <widget class="QFrame" name="frame">
      <property name="minimumSize">
       <size>
        <width>500</width>
        <height>70</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>500</width>
        <height>70</height>
       </size>
      </property>
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_4">
       <property name="spacing">
        <number>4</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QLabel" name="label_3">
         <property name="styleSheet">
          <string notr="true">font: 700 14pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);</string>
         </property>
         <property name="text">
          <string>Olá...</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QGridLayout" name="gridLayout">
         <item row="0" column="1">
          <widget class="QPushButton" name="btn_change_text">
           <property name="minimumSize">
            <size>
             <width>120</width>
             <height>36</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>36</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton {
	background-color: rgb(67, 133, 200);
	border: 2px solid #c3ccdf;
	color: rgb(255, 255, 255);
	border-radius: 10px;
}
QPushButton:hover {
	background-color: rgb(85, 170, 255);
}
QPushButton:pressed {
	background-color: rgb(255, 0, 127);
}</string>
           </property>
           <property name="text">
            <string>Alterar Texto</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="page_2">
   <layout class="QVBoxLayout" name="verticalLayout_2">
    <item>
     <widget class="QLabel" name="label_2">
      <property name="text">
       <string>Página 2</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="page_3">
   <layout class="QVBoxLayout" name="verticalLayout_3">
    <item>
     <widget class="QLabel" name="label">
      <property name="text">
       <string>Pagina 3</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
