import re
import os
import math
import logging
import pandas as pd
import psycopg2  # 替换 sqlite3 为 psycopg2
from sqlalchemy import create_engine  # 添加 SQLAlchemy 支持
from PySide6.QtCore import QObject, Signal, QThread, QTimer, Qt
from PySide6.QtWidgets import QFileDialog, QMessageBox, QDialog, QVBoxLayout, QLabel, QProgressBar, QPushButton, QHBoxLayout, QCheckBox, QScrollArea, QWidget, QTextEdit, QApplication, QLineEdit, QFrame, QTabWidget
import multiprocessing
import time
# 添加try-except处理psutil导入
try:
    import psutil  # 如果系统没有安装此库，可以使用pip安装: pip install psutil
    has_psutil = True
except ImportError:
    has_psutil = False
    logging.warning("未能导入psutil模块，将使用默认配置。请使用pip install psutil安装此模块以获得更好的性能。")
from concurrent.futures import ThreadPoolExecutor, as_completed
import configparser  # 导入配置解析器

# 导入新的日志配置
from logger_config import get_logger
logger = get_logger()

class PartitionedExportWorker(QObject):
    progress_signal = Signal(int)  # 总体进度
    file_progress_signal = Signal(str, int)  # 当前文件名和进度
    finished_signal = Signal()
    error_signal = Signal(str)

    def __init__(self, case_id, case_name, export_dir):
        super().__init__()
        self.case_id = case_id
        self.case_name = case_name
        self.export_dir = export_dir
        self.max_rows_per_file = 1000000  # 每个文件最大100万行
        self.selected_tables = None  # 用户选择的表，如果为None则导出所有表
        
        # 根据系统资源设置参数
        self.cpu_count = multiprocessing.cpu_count()
        
        # 检查psutil是否可用
        if has_psutil:
            self.memory_info = psutil.virtual_memory()
            
            # 更激进的资源使用策略，特别针对高性能硬件
            if self.memory_info.total > 32 * 1024 * 1024 * 1024:  # 32GB+
                self.chunk_size = 2000000  # 增加到200万行级别的chunk
                self.max_workers = max(min(self.cpu_count - 2, 10), 4)  # 保留2个CPU核心给系统，最多10个，最少4个
            elif self.memory_info.total > 16 * 1024 * 1024 * 1024:  # 16-32GB
                self.chunk_size = 1000000  # 增加到100万行级别
                self.max_workers = min(self.cpu_count - 1, 6)
            else:  # <16GB
                self.chunk_size = 500000
                self.max_workers = min(max(self.cpu_count // 2, 2), 4)  # 至少2个，最多4个
                
            logger.info(f"系统资源配置: CPU核心={self.cpu_count}, 内存={self.memory_info.total/1024/1024/1024:.1f}GB, "
                        f"设置并行工作线程={self.max_workers}, chunk大小={self.chunk_size}")
        else:
            # 如果psutil不可用，使用保守的默认值
            self.chunk_size = 500000
            self.max_workers = min(max(self.cpu_count // 2, 2), 4)
            logger.info(f"未检测到psutil，使用默认配置: CPU核心={self.cpu_count}, "
                       f"设置并行工作线程={self.max_workers}, chunk大小={self.chunk_size}")

        # 读取数据库配置
        try:
            config = configparser.ConfigParser()
            config.read('db_config.ini')
            
            # 构建数据库连接URL
            db_url = f"postgresql://{config['PostgreSQL']['user']}:{config['PostgreSQL']['password']}@" \
                    f"{config['PostgreSQL']['host']}:{config['PostgreSQL']['port']}/{config['PostgreSQL']['database']}"
            
            # 创建SQLAlchemy引擎
            self.engine = create_engine(
                db_url,
                pool_size=self.max_workers + 2,  # 连接池大小比最大工作线程数多2个
                max_overflow=5,  # 允许额外创建5个连接
                pool_timeout=30,  # 连接池超时时间30秒
                pool_recycle=1800,  # 每30分钟回收连接
                echo=False  # 不输出SQL语句
            )
            
            logger.info("成功创建PostgreSQL数据库连接")
            
        except Exception as e:
            logger.error(f"创建数据库连接时发生错误: {e}")
            raise
            
    def export_data_with_partitioning(self):
        """导出数据（PostgreSQL版本）"""
        try:
            # 记录开始时间
            start_time = time.time()
            
            if has_psutil:
                logger.info(f"系统资源: CPU核心数={self.cpu_count}, 内存总量={self.memory_info.total/1024/1024/1024:.1f}GB, "
                            f"可用内存={self.memory_info.available/1024/1024/1024:.1f}GB")
            else:
                logger.info(f"系统资源: CPU核心数={self.cpu_count}，无法获取内存信息(psutil不可用)")
            
            # 诊断案件ID问题
            logger.info(f"当前导出使用的案件ID: {self.case_id}, 案件名称: {self.case_name}")
            
            # 检查案件ID是否有效
            try:
                with self.engine.connect() as conn:
                    # 检查案件信息表
                    case_query = """
                        SELECT COUNT(*) FROM "案件信息表" WHERE "案件编号" = %s
                    """
                    case_result = pd.read_sql_query(case_query, conn, params=(self.case_id,))
                    case_count = case_result.iloc[0, 0]
                    
                    # 检查各表数据量 - 核心表已调整为11个
                    tables = ["账户交易明细表", "开户信息表", "财付通交易明细表"]
                    table_counts = {}
                    
                    for table in tables:
                        try:
                            count_query = f"""
                                SELECT COUNT(*) FROM "{table}" WHERE "案件编号" = %s
                            """
                            result = pd.read_sql_query(count_query, conn, params=(self.case_id,))
                            table_counts[table] = result.iloc[0, 0]
                        except Exception as e:
                            logger.error(f"检查表 {table} 时出错: {e}")
                            table_counts[table] = f"错误: {e}"
                    
                    logger.info(f"案件 {self.case_id} 在案件信息表中有 {case_count} 条记录")
                    logger.info(f"各表数据量: {table_counts}")
            except Exception as e:
                logger.error(f"诊断案件ID时出错: {e}")
            
            # 确保导出目录存在
            os.makedirs(self.export_dir, exist_ok=True)
            
            # 定义需要导出的表
            if self.selected_tables:
                # 使用用户选择的表
                tables = self.selected_tables
                logger.info(f"根据用户选择，将导出以下表: {tables}")
            else:
                # 动态发现所有包含该案件数据的表
                logger.info("未指定表，动态发现所有包含案件数据的表")
                tables = []
                
                # 动态查询所有包含"案件编号"字段的表
                with self.engine.connect() as conn:
                    try:
                        # 查询所有包含"案件编号"字段的表
                        discover_query = """
                            SELECT DISTINCT table_name 
                            FROM information_schema.columns 
                            WHERE column_name = '案件编号'
                              AND table_schema = 'public'
                              AND table_name NOT LIKE '%temp%'
                              AND table_name NOT LIKE '%_backup%'
                              AND table_name NOT IN ('对手信息表', '案件信息表', '用户信息表', '系统信息表', '导入记录表', '字段匹配规则', '系统配置表', '表类型匹配规则表')
                            ORDER BY table_name
                        """
                        
                        result = pd.read_sql_query(discover_query, conn)
                        potential_tables = result['table_name'].tolist()
                        
                        logger.info(f"发现 {len(potential_tables)} 个包含案件编号的数据表")
                        
                        # 获取所有包含案件编号字段的表（不过滤无数据的表）
                        for table_name in potential_tables:
                            try:
                                count_query = f"""
                                    SELECT COUNT(*) 
                                    FROM "{table_name}" 
                                    WHERE "案件编号" = %s
                                """
                                count_result = pd.read_sql_query(count_query, conn, params=(self.case_id,))
                                count = count_result.iloc[0, 0]
                                
                                # 添加所有表，不论是否有数据
                                tables.append(table_name)
                                if count > 0:
                                    logger.info(f"✅ 表 {table_name} 包含 {count:,} 条案件数据，将导出")
                                else:
                                    logger.info(f"⚪ 表 {table_name} 无案件数据，但仍可选择导出")
                                    
                            except Exception as e:
                                logger.error(f"检查表 {table_name} 数据量时出错: {e}")
                                continue
                        
                        # 按重要性排序：账户交易明细表优先
                        priority_tables = ["账户交易明细表", "开户信息表", "财付通交易明细表"]
                        sorted_tables = []
                        
                        # 先添加优先级表
                        for priority_table in priority_tables:
                            if priority_table in tables:
                                sorted_tables.append(priority_table)
                                tables.remove(priority_table)
                        
                        # 再添加其他表（按字母顺序）
                        sorted_tables.extend(sorted(tables))
                        tables = sorted_tables
                        
                        logger.info(f"✅ 将导出 {len(tables)} 个包含案件数据的表: {tables}")
                        
                    except Exception as e:
                        logger.error(f"动态发现数据表时出错: {e}")
                        # 回退到默认表列表
                        tables = ["账户交易明细表", "开户信息表", "财付通交易明细表"]
                        logger.info(f"回退到默认表列表: {tables}")
                
                if not tables:
                    logger.warning("未找到任何包含案件数据的表")
                    return
            
            # 计算总体进度
            total_tables = len(tables)
            current_table_index = 0
            
            for table_name in tables:
                table_start_time = time.time()
                current_table_index += 1
                    
                try:
                    # 查询数据的总行数
                    with self.engine.connect() as conn:
                        count_query = f"""
                            SELECT COUNT(*) 
                            FROM "{table_name}" 
                            WHERE "案件编号" = %s
                        """
                        result = pd.read_sql_query(count_query, conn, params=(self.case_id,))
                        total_rows = result.iloc[0, 0]
                
                    logger.info(f"开始导出表 '{table_name}', 总行数: {total_rows}, "
                                f"已用时间: {time.time() - start_time:.1f}秒")
                
                    if total_rows == 0:
                        logger.info(f"表 '{table_name}' 无数据，创建空的Excel文件。")
                        # 创建空的Excel文件
                        self._create_empty_excel_file(table_name)
                        # 更新总体进度
                        overall_progress = int((current_table_index / total_tables) * 100)
                        self.progress_signal.emit(overall_progress)
                        continue
                
                    # 如果总行数不超过100万，直接导出整个表
                    if total_rows <= self.max_rows_per_file:
                        export_start = time.time()
                        self._export_single_file(table_name)
                        logger.info(f"单文件导出表 '{table_name}' 完成, 耗时: {time.time() - export_start:.1f}秒")
                        # 更新总体进度
                        overall_progress = int((current_table_index / total_tables) * 100)
                        self.progress_signal.emit(overall_progress)
                    else:
                        # 导出大表，按交易户名分组
                        export_start = time.time()
                        self._export_partitioned_table(table_name, total_rows)
                        logger.info(f"分组导出表 '{table_name}' 完成, 耗时: {time.time() - export_start:.1f}秒")
                        # 更新总体进度
                        overall_progress = int((current_table_index / total_tables) * 100)
                        self.progress_signal.emit(overall_progress)
                
                    # 记录每个表导出后的内存使用情况
                    if has_psutil:
                        memory_after = psutil.virtual_memory()
                        logger.info(f"导出表 '{table_name}' 后: 内存使用率={memory_after.percent}%, "
                                    f"可用内存={memory_after.available/1024/1024/1024:.1f}GB, "
                                    f"已用时间: {time.time() - start_time:.1f}秒")
                    else:
                        logger.info(f"导出表 '{table_name}' 后: 已用时间: {time.time() - start_time:.1f}秒")
                except Exception as e:
                    logger.error(f"导出表 {table_name} 时发生错误: {e}")
                    continue
            
            total_time = time.time() - start_time
            logger.info(f"数据导出完成，总耗时: {total_time:.1f}秒")
            self.finished_signal.emit()
                
        except Exception as e:
            logger.error(f"导出过程中发生错误: {e}")
            self.error_signal.emit(str(e))
            raise

    def _export_single_file(self, table_name):
        """导出小于100万行的表格，PostgreSQL优化版本"""
        try:
            # 构建文件名
            timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{self.case_name}_{table_name}_{timestamp}.xlsx"
            file_path = os.path.join(self.export_dir, filename)
            
            # 发送当前文件进度信息
            self.file_progress_signal.emit(filename, 0)
            
            # 分析表结构，获取列名以便后续格式化
            with self.engine.connect() as conn:
                # 获取表列信息
                column_query = f"""
                    SELECT column_name 
                FROM information_schema.columns 
                    WHERE table_name = '{table_name}'
                ORDER BY ordinal_position
                """
                columns_df = pd.read_sql_query(column_query, conn)
                columns = columns_df['column_name'].tolist()
                logger.info(f"表 {table_name} 的列名: {columns}")
            
            # 构建排除字段的列表
            exclude_columns = ['id', 'ID']  # 所有表都不导出ID字段
            
            # 开户信息表特定排除字段
            if table_name == "开户信息表":
                exclude_columns.extend(['交易卡号_digits', '交易账号_digits'])
            
            # 账户交易明细表特定排除字段
            elif table_name == "账户交易明细表":
                exclude_columns.extend(['交易账卡号_digits', '交易账号_digits', '对手账号_digits', '对手卡号_digits'])
                
            logger.info(f"导出表 '{table_name}' 时排除字段: {exclude_columns}")
            
            # 使用分块读取和写入
            try:
                # 计算总记录数
                with self.engine.connect() as conn:
                    count_query = f"""
                        SELECT COUNT(*) 
                        FROM "{table_name}" 
                        WHERE "案件编号" = %s
                    """
                    total_count = pd.read_sql_query(count_query, conn, params=(self.case_id,)).iloc[0, 0]
                    
                    if total_count == 0:
                        logger.warning(f"表 {table_name} 没有符合条件的数据，案件编号: {self.case_id}")
                        self.file_progress_signal.emit(filename, 100)
                        return
                        
                    logger.info(f"开始导出表 {table_name}，总记录数: {total_count}")
                
                # 直接执行主查询获取全部数据 - 对于小的表这样更高效
                if total_count <= self.chunk_size:
                    # 确定是否需要排序
                    order_by_clause = ""
                    if table_name == "账户交易明细表":
                        # 检查是否有交易日期字段
                        if "交易日期" in columns:
                            order_by_clause = " ORDER BY \"交易日期\" ASC"
                        elif "交易时间" in columns:
                            order_by_clause = " ORDER BY \"交易时间\" ASC"
                        logger.info(f"账户交易明细表将按{order_by_clause}排序")
                    
                    # 直接获取数据
                    logger.info(f"表 {table_name} 数据量较小，一次性获取全部数据")
                    with self.engine.connect() as conn:
                        query = f"""
                            SELECT * 
                            FROM "{table_name}" 
                            WHERE "案件编号" = %s
                            {order_by_clause}
                        """
                        df = pd.read_sql_query(query, conn, params=(self.case_id,))
                        
                        if df.empty:
                            logger.warning(f"表 {table_name} 查询结果为空，跳过此表")
                            self.file_progress_signal.emit(filename, 100)
                            return
                        
                        # 处理数据格式
                        self._format_dataframe(df, table_name)
                        
                        # 移除不需要导出的列
                        for col in exclude_columns:
                            if col in df.columns:
                                df = df.drop(columns=[col])
                        
                        # 将DataFrame转换为字典列表并重新创建DataFrame以避免类型问题
                        records = df.to_dict('records')
                        df = pd.DataFrame.from_records(records)
                        
                        # 导出到Excel
                        with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                            df.to_excel(writer, sheet_name=table_name, index=False)
                            # 设置Excel格式
                            self._format_excel_columns(writer, df, table_name)
                        
                        logger.info(f"成功导出 {len(df)} 行数据到文件: {os.path.basename(file_path)}")
                        self.file_progress_signal.emit(filename, 100)
                        return
                
                # 对于大的表，使用分块处理
                logger.info(f"表 {table_name} 数据量较大，使用分块处理")
                
                # 确定是否需要排序
                order_by_clause = ""
                if table_name == "账户交易明细表":
                    # 检查是否有交易日期字段
                    if "交易日期" in columns:
                        order_by_clause = " ORDER BY \"交易日期\" ASC"
                    elif "交易时间" in columns:
                        order_by_clause = " ORDER BY \"交易时间\" ASC"
                    logger.info(f"账户交易明细表将按{order_by_clause}排序")
                
                # 尝试使用带options参数的ExcelWriter (更新版本pandas)
                try:
                    with pd.ExcelWriter(file_path, engine='xlsxwriter', mode='w') as writer:
                        first_chunk = True
                        row_offset = 0
                        total_processed = 0
                        
                        # 使用分块读取数据
                        for i in range(0, total_count, self.chunk_size):
                            chunk_start = time.time()
                            
                            # 使用LIMIT和OFFSET进行分页查询
                            with self.engine.connect() as conn:
                                paged_query = f"""
                                    SELECT * 
                                    FROM "{table_name}" 
                                    WHERE "案件编号" = %s
                                    {order_by_clause}
                                    LIMIT {self.chunk_size} OFFSET {i}
                                """
                                
                                logger.info(f"查询第 {i//self.chunk_size + 1} 块数据，OFFSET={i}, LIMIT={self.chunk_size}")
                                chunk = pd.read_sql_query(paged_query, conn, params=(self.case_id,))
                                chunk_size = len(chunk)
                                
                                if chunk_size == 0:
                                    logger.warning(f"第 {i//self.chunk_size + 1} 块数据为空，结束分块处理")
                                    break
                            
                                total_processed += chunk_size
                                
                                # 处理特定字段的格式
                                self._format_dataframe(chunk, table_name)
                                
                                # 移除不需要导出的列
                                for col in exclude_columns:
                                    if col in chunk.columns:
                                        chunk = chunk.drop(columns=[col])
                                
                                # 将DataFrame转换为字典列表并重新创建DataFrame以避免类型问题
                                records = chunk.to_dict('records')
                                chunk = pd.DataFrame.from_records(records)
                                
                                if first_chunk:
                                    # 第一个数据块包含列名
                                    chunk.to_excel(writer, sheet_name=table_name, index=False, startrow=row_offset)
                                    row_offset += len(chunk) + 1  # +1 是为了表头行
                                    first_chunk = False
                                else:
                                    # 后续数据块不包含列名
                                    chunk.to_excel(writer, sheet_name=table_name, index=False, header=False, startrow=row_offset)
                                    row_offset += len(chunk)
                                
                                # 更新进度，每5个chunk或是最后一个chunk
                                if i % 5 == 0 or (i + self.chunk_size >= total_count):
                                    progress = min(int((total_processed / total_count) * 100), 99)
                                    self.file_progress_signal.emit(filename, progress)
                                    
                                    chunk_time = time.time() - chunk_start
                                    if chunk_time > 0:
                                        speed = chunk_size / chunk_time
                                        logger.info(f"导出 {table_name}: 已处理 {total_processed}/{total_count} 行, "
                                                f"速度: {speed:.0f} 行/秒, 当前块耗时: {chunk_time:.2f}秒")
                        
                        # 设置特定列的格式
                        if not first_chunk:  # 确保至少有一块数据被写入
                            self._format_excel_columns(writer, None, table_name)
                            logger.info(f"成功导出 {total_processed} 行数据到文件: {os.path.basename(file_path)}")
                        else:
                            logger.warning(f"表 {table_name} 没有数据被写入文件")
                
                except TypeError as te:
                    if "options" in str(te) or "mode" in str(te):
                        # 旧版本pandas不支持options参数，使用备用方式
                        logger.info("检测到旧版本pandas，使用备用方式创建Excel文件")
                        with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                            first_chunk = True
                            row_offset = 0
                            total_processed = 0
                            
                            # 使用分块读取数据
                            for i in range(0, total_count, self.chunk_size):
                                chunk_start = time.time()
                                
                                # 使用LIMIT和OFFSET进行分页查询
                                with self.engine.connect() as conn:
                                    paged_query = f"""
                                        SELECT * 
                                        FROM "{table_name}" 
                                        WHERE "案件编号" = %s
                                        {order_by_clause}
                                        LIMIT {self.chunk_size} OFFSET {i}
                                    """
                                    
                                    logger.info(f"查询第 {i//self.chunk_size + 1} 块数据，OFFSET={i}, LIMIT={self.chunk_size}")
                                    chunk = pd.read_sql_query(paged_query, conn, params=(self.case_id,))
                                    chunk_size = len(chunk)
                                    
                                    if chunk_size == 0:
                                        logger.warning(f"第 {i//self.chunk_size + 1} 块数据为空，结束分块处理")
                                        break
                                        
                                    total_processed += chunk_size
                                    
                                    # 处理特定字段的格式
                                    self._format_dataframe(chunk, table_name)
                                    
                                    # 移除不需要导出的列
                                    for col in exclude_columns:
                                        if col in chunk.columns:
                                            chunk = chunk.drop(columns=[col])
                                    
                                    # 将DataFrame转换为字典列表并重新创建DataFrame以避免类型问题
                                    records = chunk.to_dict('records')
                                    chunk = pd.DataFrame.from_records(records)
                                    
                                    if first_chunk:
                                        # 第一个数据块包含列名
                                        chunk.to_excel(writer, sheet_name=table_name, index=False, startrow=row_offset)
                                        row_offset += len(chunk) + 1  # +1 是为了表头行
                                        first_chunk = False
                                    else:
                                        # 后续数据块不包含列名
                                        chunk.to_excel(writer, sheet_name=table_name, index=False, header=False, startrow=row_offset)
                                        row_offset += len(chunk)
                        
                                    # 更新进度
                                    if i % 5 == 0 or (i + self.chunk_size >= total_count):
                                        progress = min(int((total_processed / total_count) * 100), 99)
                                        self.file_progress_signal.emit(filename, progress)
                        
                                        chunk_time = time.time() - chunk_start
                                        if chunk_time > 0:
                                            speed = chunk_size / chunk_time
                                            logger.info(f"导出 {table_name}: 已处理 {total_processed}/{total_count} 行, "
                                                    f"速度: {speed:.0f} 行/秒, 当前块耗时: {chunk_time:.2f}秒")
                            
                            # 设置特定列的格式
                            if not first_chunk:  # 确保至少有一块数据被写入
                                self._format_excel_columns(writer, None, table_name)
                                logger.info(f"成功导出 {total_processed} 行数据到文件: {os.path.basename(file_path)}")
                            else:
                                logger.warning(f"表 {table_name} 没有数据被写入文件")
                    else:
                        # 其他类型的TypeError，重新抛出
                        raise
                    
            except Exception as e:
                logger.error(f"导出{table_name}时发生错误: {e}")
                raise
                
        except Exception as e:
            logger.error(f"导出{table_name}时发生错误: {e}")
            raise

    def _export_partitioned_table(self, table_name, total_records):
        """按交易户名分组导出超过100万行的表格，PostgreSQL优化版本"""
        try:
            # 对于账户交易明细表和财付通交易明细表，按交易户名/用户侧账号名称分组导出
            if table_name in ["账户交易明细表", "财付通交易明细表"]:
                # 性能跟踪 - 记录分组开始时间
                grouping_start = time.time()
                logger.info(f"开始高效分析 {table_name} 数据...")
                
                # 获取分组字段 - 账户交易明细表和财付通交易明细表的分组字段不同
                group_field = "交易户名" if table_name == "账户交易明细表" else "用户侧账号名称"
                logger.info(f"使用 {group_field} 作为分组字段")
                
                # 检查表结构，确认列名格式
                with self.engine.connect() as conn:
                    column_query = f"""
                        SELECT column_name
                        FROM information_schema.columns
                        WHERE table_name = '{table_name}'
                        ORDER BY ordinal_position
                    """
                    columns_df = pd.read_sql_query(column_query, conn)
                    logger.info(f"表 {table_name} 的列名: {columns_df['column_name'].tolist()}")
                
                # 获取一条示例数据查看格式
                with self.engine.connect() as conn:
                    sample_query = f"""
                        SELECT *
                        FROM "{table_name}"
                        WHERE "案件编号" = %s
                        LIMIT 1
                    """
                    try:
                        sample_df = pd.read_sql_query(sample_query, conn, params=(self.case_id,))
                        if not sample_df.empty:
                            logger.info(f"示例数据：\n{sample_df.to_dict('records')[0]}")
                        else:
                            logger.warning(f"未找到示例数据，可能是案件编号 {self.case_id} 在表 {table_name} 中没有记录")
                    except Exception as e:
                        logger.error(f"获取示例数据出错: {e}")
                
                # 1. 快速查询该案件有多少条交易明细总数
                with self.engine.connect() as conn:
                    cursor = conn.connection.cursor()
                    count_query = f"""
                        SELECT COUNT(*) 
                        FROM "{table_name}" 
                        WHERE "案件编号" = %s
                    """
                    logger.info(f"查询 {table_name} 总记录数...")
                    cursor.execute(count_query, (self.case_id,))
                    total_records = cursor.fetchone()[0]
                    
                logger.info(f"{table_name} 总记录数: {total_records}")
                
                if total_records == 0:
                    logger.info(f"{table_name} 无数据需要导出")
                    return
                
                # 2. 查询包含多少个分组字段值
                with self.engine.connect() as conn:
                    cursor = conn.connection.cursor()
                    names_query = f"""
                        SELECT COUNT(DISTINCT COALESCE("{group_field}", '未知{group_field}'))
                        FROM "{table_name}" 
                        WHERE "案件编号" = %s
                    """
                    logger.info(f"查询 {table_name} {group_field} 数量...")
                    cursor.execute(names_query, (self.case_id,))
                    unique_names_count = cursor.fetchone()[0]
                
                logger.info(f"{group_field}总数: {unique_names_count}，查询耗时: {time.time() - grouping_start:.2f}秒")
                
                # 获取一些实际的分组字段值作为示例
                with self.engine.connect() as conn:
                    sample_names_query = f"""
                        SELECT DISTINCT COALESCE("{group_field}", '未知{group_field}') as 分组值
                        FROM "{table_name}" 
                        WHERE "案件编号" = %s
                        LIMIT 10
                    """
                    try:
                        sample_names_df = pd.read_sql_query(sample_names_query, conn, params=(self.case_id,))
                        logger.info(f"{group_field}示例: {sample_names_df['分组值'].tolist()}")
                    except Exception as e:
                        logger.error(f"获取{group_field}示例出错: {e}")
                
                # 3. 智能计算分组数量 - 根据最大行数限制计算
                # 计算需要的组数 - 优先使用接近 max_rows_per_file 的值
                num_groups = math.ceil(total_records / self.max_rows_per_file)
                # 确保组数不超过最大工作线程数，除非必须分成更多组
                num_groups = min(num_groups, max(self.max_workers, math.ceil(total_records / self.max_rows_per_file)))
                
                logger.info(f"智能分组策略：需要 {num_groups} 组进行导出，接近每组 {self.max_rows_per_file} 行的目标")
                
                # 4. 使用更高效的方式获取分组字段值及其记录数
                name_count_start = time.time()
                with self.engine.connect() as conn:
                    cursor = conn.connection.cursor()
                    cursor.execute(f"""
                        SELECT 
                            COALESCE("{group_field}", '未知{group_field}') as 分组值, 
                            COUNT(*) as 记录数
                        FROM "{table_name}" 
                        WHERE "案件编号" = %s
                        GROUP BY 分组值
                        ORDER BY 记录数 DESC
                    """, (self.case_id,))
                    name_counts = cursor.fetchall()
                    
                logger.info(f"获取{group_field}记录数耗时: {time.time() - name_count_start:.2f}秒")
                
                # 验证获取到的分组信息
                if not name_counts:
                    logger.error(f"未获取到任何{group_field}记录数信息")
                    return
                else:
                    logger.info(f"获取到 {len(name_counts)} 个{group_field}的记录数信息")
                    # 记录前10个分组值及其记录数
                    for i, (name, count) in enumerate(name_counts[:10]):
                        logger.info(f"{group_field}{i+1}: {name}, 记录数: {count}")
                
                # 5. 优化的分组算法 - 尽量接近 max_rows_per_file 的大小
                groups = [[] for _ in range(num_groups)]
                group_sizes = [0] * num_groups
                
                # 处理超大户情况 - 如果单个账户超过 max_rows_per_file 则单独分配一组
                big_accounts = []
                normal_accounts = []
                
                for name, count in name_counts:
                    if count > self.max_rows_per_file:
                        big_accounts.append((name, count))
                    else:
                        normal_accounts.append((name, count))
                
                # 处理超大户，每个单独一组
                extra_groups = []
                extra_group_sizes = []
                for name, count in big_accounts:
                    # 计算需要分几组
                    account_groups = math.ceil(count / self.max_rows_per_file)
                    logger.info(f"超大{group_field} '{name}' 数据量 {count} 行，需单独分配 {account_groups} 组")
                    
                    # 每组单独添加这个超大户
                    for _ in range(account_groups):
                        extra_groups.append([name])
                        extra_group_sizes.append(count // account_groups) 
                
                # 处理普通户 - 使用贪心算法填充到接近 max_rows_per_file
                for name, count in normal_accounts:
                    # 找到当前大小最小且加上当前户后不超过 max_rows_per_file 的组
                    suitable_group = -1
                    min_size = float('inf')
                    
                    for i, size in enumerate(group_sizes):
                        if size + count <= self.max_rows_per_file and size < min_size:
                            suitable_group = i
                            min_size = size
                    
                    # 如果没找到合适的组，放入当前最小的组
                    if suitable_group == -1:
                        suitable_group = group_sizes.index(min(group_sizes))
                    
                    groups[suitable_group].append(name)
                    group_sizes[suitable_group] += count
                
                # 合并常规组和超大户组
                if extra_groups:
                    groups.extend(extra_groups)
                    group_sizes.extend(extra_group_sizes)
                
                # 移除空组
                non_empty_groups = []
                non_empty_sizes = []
                for g, s in zip(groups, group_sizes):
                    if g:  # 如果组不为空
                        non_empty_groups.append(g)
                        non_empty_sizes.append(s)
                
                groups = non_empty_groups
                group_sizes = non_empty_sizes
                
                logger.info(f"智能分组完成，共 {len(groups)} 组，耗时: {time.time() - grouping_start:.2f}秒")
                
                # 输出每组大小统计信息
                min_size = min(group_sizes) if group_sizes else 0
                max_size = max(group_sizes) if group_sizes else 0
                avg_size = sum(group_sizes) / len(group_sizes) if group_sizes else 0
                logger.info(f"组大小统计：最小={min_size}，最大={max_size}，平均={avg_size:.0f}")
                
                # 检查分组是否为空
                if not groups:
                    logger.error("分组结果为空，无法继续导出")
                    return
                
                # 确保导出目录存在
                os.makedirs(self.export_dir, exist_ok=True)
                
                # 准备索引文件数据结构
                timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
                
                # 根据表类型定义索引字段
                if table_name == "账户交易明细表":
                    index_data = {
                        "交易户名": [],
                        "交易账号": [],
                        "交易账卡号": [],
                        "所在文件": [],
                        "数据量": []
                    }
                else:  # 财付通交易明细表
                    index_data = {
                        "用户侧账号名称": [],
                        "用户侧账号": [],
                        "交易流水号示例": [],  # 使用交易流水号替代平台企业侧账号名称
                        "所在文件": [],
                        "数据量": []
                    }
                
                # 6. 优化线程池并行导出
                parallel_start = time.time()
                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    # 提交所有导出任务
                    future_to_group = {}
                    for group_idx, group_names in enumerate(groups):
                        if not group_names:
                            continue
                            
                        # 构建文件名
                        group_suffix = f"_第{group_idx+1}组"
                        filename = f"{self.case_name}_{table_name}_{timestamp}{group_suffix}.xlsx"
                        file_path = os.path.join(self.export_dir, filename)
                        
                        # 更新当前处理的文件名
                        self.file_progress_signal.emit(filename, 0)
                        
                        # 记录当前分组信息
                        logger.info(f"提交第 {group_idx+1} 组导出任务，包含 {len(group_names)} 个{group_field}，预计 {group_sizes[group_idx]} 行数据")
                        logger.info(f"第 {group_idx+1} 组{group_field}: {group_names[:5]}{'...' if len(group_names) > 5 else ''}")
                        
                        # 提交导出任务
                        future = executor.submit(
                            self._export_group,
                            table_name,
                            group_names,
                            file_path,
                            group_idx,
                            len(groups),
                            group_sizes[group_idx],
                            group_field  # 传递分组字段名称
                        )
                        future_to_group[future] = (group_idx, filename, group_names)
                    
                    # 处理完成的任务
                    completed_count = 0
                    success_count = 0
                    total_groups = len(future_to_group)
                    
                    for future in as_completed(future_to_group):
                        group_idx, filename, group_names = future_to_group[future]
                        completed_count += 1
                        
                        try:
                            # 获取返回的索引数据
                            group_index_data = future.result()
                            
                            # 记录索引数据情况
                            logger.info(f"获取第 {group_idx+1} 组索引数据: {len(group_index_data)} 条记录")
                            
                            if group_index_data:
                                success_count += 1
                                
                                # 合并索引数据 - 根据不同表类型处理
                                if table_name == "账户交易明细表":
                                    for record in group_index_data:
                                        name = record[0]
                                        account = record[1] if len(record) > 1 and record[1] else ""
                                        card = record[2] if len(record) > 2 and record[2] else ""
                                        count = record[3] if len(record) > 3 and record[3] else 0
                                        
                                        index_data["交易户名"].append(name)
                                        index_data["交易账号"].append(account)
                                        index_data["交易账卡号"].append(card)
                                        index_data["所在文件"].append(filename)
                                        index_data["数据量"].append(count)
                                else:  # 财付通交易明细表
                                    for record in group_index_data:
                                        # 解析索引记录，适应不同的索引字段数量情况
                                        name = record[0] if len(record) > 0 else ""  # 用户侧账号名称
                                        
                                        # 动态判断索引数据格式
                                        record_len = len(record)
                                        last_idx = record_len - 1  # 最后一个是记录数
                                        
                                        # 默认值
                                        account = ""
                                        transaction_id = ""
                                        count = 0
                                        
                                        # 根据记录长度获取不同字段
                                        if record_len == 2:  # 只有分组值和记录数
                                            count = record[1]
                                        elif record_len == 3:  # 分组值、用户侧账号、记录数
                                            account = record[1] if record[1] else ""
                                            count = record[2]
                                        elif record_len == 4:  # 分组值、用户侧账号、交易流水号示例、记录数
                                            account = record[1] if record[1] else ""
                                            transaction_id = record[2] if record[2] else ""
                                            count = record[3]
                                        
                                        # 添加到索引数据
                                        index_data["用户侧账号名称"].append(name)
                                        index_data["用户侧账号"].append(account)
                                        index_data["交易流水号示例"].append(transaction_id)
                                        index_data["所在文件"].append(filename)
                                        index_data["数据量"].append(count)
                            else:
                                logger.warning(f"第 {group_idx+1} 组没有返回索引数据")
                                # 添加默认索引信息
                                for name in group_names:
                                    if table_name == "账户交易明细表":
                                        index_data["交易户名"].append(name)
                                        index_data["交易账号"].append("")
                                        index_data["交易账卡号"].append("")
                                        index_data["所在文件"].append(filename)
                                        index_data["数据量"].append(0)
                                    else:  # 财付通交易明细表
                                        index_data["用户侧账号名称"].append(name)
                                        index_data["用户侧账号"].append("")
                                        index_data["交易流水号示例"].append("")
                                        index_data["所在文件"].append(filename)
                                        index_data["数据量"].append(0)
                            
                            # 更新总体进度
                            progress = int((completed_count / total_groups) * 100)
                            self.file_progress_signal.emit(filename, 100)
                            
                        except Exception as e:
                            logger.error(f"导出第 {group_idx+1} 组时发生错误: {e}")
                            # 继续处理其他组
                            # 添加错误组的索引记录
                            for name in group_names:
                                if table_name == "账户交易明细表":
                                    index_data["交易户名"].append(name)
                                    index_data["交易账号"].append("")
                                    index_data["交易账卡号"].append("")
                                    index_data["所在文件"].append(f"{filename} (导出失败)")
                                    index_data["数据量"].append(-1)  # -1表示导出失败
                                else:  # 财付通交易明细表
                                    index_data["用户侧账号名称"].append(name)
                                    index_data["用户侧账号"].append("")
                                    index_data["交易流水号示例"].append("")
                                    index_data["所在文件"].append(f"{filename} (导出失败)")
                                    index_data["数据量"].append(-1)  # -1表示导出失败
                            continue
                
                logger.info(f"并行导出完成，成功 {success_count}/{total_groups} 组，耗时: {time.time() - parallel_start:.2f}秒")
                
                # 8. 生成并导出索引文件
                index_start = time.time()
                
                # 记录索引数据状态
                logger.info(f"准备生成索引文件，收集到的索引数据量: {len(index_data.get('交易户名', index_data.get('用户侧账号名称', [])))}")
                
                # 获取主索引字段名
                main_index_field = "交易户名" if table_name == "账户交易明细表" else "用户侧账号名称"
                
                if len(index_data[main_index_field]) == 0:
                    logger.error("索引数据为空，这可能意味着所有分组的索引数据收集都失败了")
                    # 尝试构建应急索引数据
                    try:
                        logger.info("尝试构建应急索引数据")
                        with self.engine.connect() as conn:
                            # 获取所有分组字段值及其数量
                            emergency_query = f"""
                                SELECT 
                                    COALESCE("{group_field}", '未知{group_field}') as 分组值, 
                                    COUNT(*) as 记录数
                                FROM "{table_name}" 
                                WHERE "案件编号" = %s
                                GROUP BY 分组值
                            """
                            emergency_df = pd.read_sql_query(emergency_query, conn, params=(self.case_id,))
                            
                            if not emergency_df.empty:
                                for _, row in emergency_df.iterrows():
                                    if table_name == "账户交易明细表":
                                        index_data["交易户名"].append(row['分组值'])
                                        index_data["交易账号"].append("")
                                        index_data["交易账卡号"].append("")
                                        index_data["所在文件"].append("未知文件")
                                        index_data["数据量"].append(row['记录数'])
                                    else:  # 财付通交易明细表
                                        index_data["用户侧账号名称"].append(row['分组值'])
                                        index_data["用户侧账号"].append("")
                                        index_data["交易流水号示例"].append("")
                                        index_data["所在文件"].append("未知文件")
                                        index_data["数据量"].append(row['记录数'])
                                
                                logger.info(f"成功构建应急索引数据: {len(emergency_df)} 条记录")
                            else:
                                logger.warning("构建应急索引数据失败，仍然为空")
                    except Exception as e:
                        logger.error(f"构建应急索引数据出错: {e}")
                
                # 添加索引文件摘要信息
                if table_name == "账户交易明细表":
                    unique_names = set(index_data["交易户名"])
                    unique_accounts = set([x for x in index_data["交易账号"] if x])
                    unique_cards = set([x for x in index_data["交易账卡号"] if x])
                    
                    summary_df = pd.DataFrame({
                        "项目": ["总记录数", "导出文件数", "交易户名数", "交易账号数", "交易账卡号数"],
                        "数量": [
                            total_records,
                            len(set([x for x in index_data["所在文件"] if "导出失败" not in x])),
                            len(unique_names),
                            len(unique_accounts),
                            len(unique_cards)
                        ]
                    })
                else:  # 财付通交易明细表
                    unique_names = set(index_data["用户侧账号名称"])
                    unique_accounts = set([x for x in index_data["用户侧账号"] if x])
                    unique_platforms = set([x for x in index_data["交易流水号示例"] if x])
                    
                    summary_df = pd.DataFrame({
                        "项目": ["总记录数", "导出文件数", "用户侧账号名称数", "用户侧账号数", "交易流水号示例数"],
                        "数量": [
                            total_records,
                            len(set([x for x in index_data["所在文件"] if "导出失败" not in x])),
                            len(unique_names),
                            len(unique_accounts),
                            len(unique_platforms)
                        ]
                    })
                
                # 导出索引到Excel文件
                index_df = pd.DataFrame(index_data)
                index_filename = f"{self.case_name}_{table_name}_{timestamp}_索引.xlsx"
                index_path = os.path.join(self.export_dir, index_filename)
                
                try:
                    logger.info(f"开始写入索引文件: {index_filename}, 数据行数: {len(index_df)}")
                    
                    # 尝试不同的Excel引擎
                    excel_written = False
                    
                    # 尝试使用xlsxwriter引擎
                    try:
                        logger.info("尝试使用xlsxwriter引擎写入索引文件")
                        with pd.ExcelWriter(index_path, engine='xlsxwriter') as writer:
                            # 导出索引数据表
                            index_df.to_excel(writer, sheet_name="导出索引", index=False)
                            # 添加摘要信息表
                            summary_df.to_excel(writer, sheet_name="数据摘要", index=False)
                            
                            # 设置自动筛选和列宽
                            if len(index_df) > 0:
                                worksheet = writer.sheets["导出索引"]
                                worksheet.autofilter(0, 0, len(index_df), len(index_df.columns) - 1)
                                for idx, col in enumerate(index_df.columns):
                                    worksheet.set_column(idx, idx, max(15, len(col) * 1.5))
                            
                            # 格式化摘要工作表
                            summary_worksheet = writer.sheets["数据摘要"]
                            summary_worksheet.set_column(0, 0, 20)
                            summary_worksheet.set_column(1, 1, 15)
                        
                        excel_written = True
                        logger.info("使用xlsxwriter引擎写入索引文件成功")
                    except Exception as e:
                        logger.error(f"使用xlsxwriter引擎写入索引文件失败: {e}")
                    
                    # 如果xlsxwriter失败，尝试使用openpyxl引擎
                    if not excel_written:
                        try:
                            logger.info("尝试使用openpyxl引擎写入索引文件")
                            with pd.ExcelWriter(index_path, engine='openpyxl') as writer:
                                # 导出索引数据表
                                index_df.to_excel(writer, sheet_name="导出索引", index=False)
                                # 添加摘要信息表
                                summary_df.to_excel(writer, sheet_name="数据摘要", index=False)
                            
                            excel_written = True
                            logger.info("使用openpyxl引擎写入索引文件成功")
                        except Exception as e:
                            logger.error(f"使用openpyxl引擎写入索引文件失败: {e}")
                    
                    # 如果两种Excel引擎都失败，尝试使用CSV格式
                    if not excel_written:
                        csv_path = index_path.replace('.xlsx', '.csv')
                        logger.info(f"尝试使用CSV格式写入索引数据: {csv_path}")
                        index_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                        
                        # 写入摘要数据到单独的CSV文件
                        summary_csv_path = index_path.replace('.xlsx', '_summary.csv')
                        summary_df.to_csv(summary_csv_path, index=False, encoding='utf-8-sig')
                        
                        logger.info(f"使用CSV格式写入索引数据成功: {csv_path}, {summary_csv_path}")
                    
                    logger.info(f"索引文件生成成功，耗时: {time.time() - index_start:.2f}秒")
                except Exception as e:
                    logger.error(f"生成索引文件时发生错误: {e}")
                
                # 验证索引文件是否创建成功
                if os.path.exists(index_path):
                    file_size = os.path.getsize(index_path)
                    logger.info(f"索引文件已保存: {index_path}, 文件大小: {file_size/1024/1024:.2f}MB")
                else:
                    logger.error(f"索引文件创建失败: {index_path}")
                
                # 总结导出统计信息
                total_export_time = time.time() - grouping_start
                avg_speed = total_records / total_export_time if total_export_time > 0 else 0
                logger.info(f"{table_name}分组导出完成, 总耗时: {total_export_time:.1f}秒, "
                           f"平均速度: {avg_speed:.0f}行/秒, 导出{total_records}行数据, "
                           f"分{len(groups)}组, 涉及{unique_names_count}个{group_field}")
            else:
                # 其他表格，按常规方式导出
                self._export_single_file(table_name)
                
        except Exception as e:
            logger.error(f"分组导出{table_name}时发生错误: {e}")
            raise

    def _format_dataframe(self, df, table_name):
        """处理特定表格的字段格式"""
        if table_name == "账户交易明细表":
            # 确保金额和余额字段是数值类型
            for col in df.columns:
                col_lower = col.lower() if isinstance(col, str) else ""
                if any(term in col_lower for term in ["金额", "余额", "发生额"]):
                    try:
                        # 先尝试转换为数值类型
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    except Exception as e:
                        logger.warning(f"转换列 {col} 为数值类型时出错: {e}")
            
            if '商户号' in df.columns:
                df['商户号'] = df['商户号'].astype(str)
        elif table_name == "开户信息表":
            if '最后交易时间' in df.columns:
                df['最后交易时间'] = df['最后交易时间'].astype(str)
            if '账号开户时间' in df.columns:
                df['账号开户时间'] = df['账号开户时间'].astype(str)
        elif table_name == "财付通交易明细表":
            # 处理财付通交易明细表特定字段
            if '交易时间' in df.columns:
                df['交易时间'] = df['交易时间'].astype(str)
            if '商户号' in df.columns:
                df['商户号'] = df['商户号'].astype(str)
            
            # 确保金额字段是数值类型
            for col in df.columns:
                col_lower = col.lower() if isinstance(col, str) else ""
                if "金额" in col_lower:
                    try:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    except Exception as e:
                        logger.warning(f"转换列 {col} 为数值类型时出错: {e}")
    
    def _create_empty_excel_file(self, table_name):
        """为无数据的表创建空的Excel文件"""
        try:
            # 构建文件名
            timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{self.case_name}_{table_name}_{timestamp}_空表.xlsx"
            file_path = os.path.join(self.export_dir, filename)
            
            # 发送当前文件进度信息
            self.file_progress_signal.emit(filename, 0)
            
            # 获取表结构（列名）
            with self.engine.connect() as conn:
                column_query = f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = '{table_name}'
                    ORDER BY ordinal_position
                """
                columns_df = pd.read_sql_query(column_query, conn)
                columns = columns_df['column_name'].tolist()
            
            # 创建空的DataFrame，只包含列名
            empty_df = pd.DataFrame(columns=columns)
            
            # 导出到Excel
            with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                empty_df.to_excel(writer, sheet_name=table_name, index=False)
                
                # 获取工作表和工作簿对象
                workbook = writer.book
                worksheet = writer.sheets[table_name]
                
                # 添加说明信息
                note_format = workbook.add_format({
                    'font_color': 'red',
                    'italic': True,
                    'font_size': 12,
                })
                worksheet.write(1, 0, f"注意：此表在案件 {self.case_name} 中无数据", note_format)
                worksheet.write(2, 0, f"表名：{table_name}", note_format)
                worksheet.write(3, 0, f"导出时间：{pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}", note_format)
            
            logger.info(f"✅ 成功创建空表文件: {filename}")
            self.file_progress_signal.emit(filename, 100)
            
        except Exception as e:
            logger.error(f"❌ 创建空表文件失败 {table_name}: {e}")

    def _format_excel_columns(self, writer, df, table_name):
        """设置Excel中特定列的格式"""
        # 当使用CSV格式导出时，writer为None，此时直接返回
        if writer is None:
            logger.warning(f"Excel格式化失败：writer为None")
            return
        
        try:
            workbook = writer.book
            worksheet = writer.sheets[table_name]
            
            # 定义文本格式
            text_format = workbook.add_format({'num_format': '@'})
            
            # 定义日期格式
            date_format = workbook.add_format({'num_format': 'yyyy-mm-dd'})
            datetime_format = workbook.add_format({'num_format': 'yyyy-mm-dd hh:mm:ss'})
            
            # 定义金额格式 - 移除千位分隔符
            amount_format = workbook.add_format({'num_format': '0.00'})
            
            # 定义表头格式
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'vcenter',
                'fg_color': '#D9D9D9',
                'border': 1
            })
            
            # 获取列索引（如果df为None，则从工作表中获取列名）
            if df is None:
                # 尝试从工作表获取列名
                try:
                    # 获取第一行作为列名
                    column_names = []
                    for col_idx in range(1000):  # 假设不超过1000列
                        try:
                            value = worksheet.table[0][col_idx]
                            if value:
                                column_names.append(value)
                            else:
                                break
                        except:
                            break
                    
                    # 如果从worksheet.table获取失败，尝试使用read_string
                    if not column_names:
                        for col_idx in range(100):  # 假设不超过100列
                            try:
                                value = worksheet.read_string(0, col_idx)
                                if value:
                                    column_names.append(value)
                                else:
                                    break
                            except:
                                break
                    
                    logger.info(f"从工作表获取到 {len(column_names)} 个列名")
                except Exception as e:
                    logger.error(f"从工作表获取列名失败: {e}")
                    column_names = []
            else:
                column_names = df.columns.tolist()
                logger.info(f"从DataFrame获取到 {len(column_names)} 个列名")
            
            # 如果没有获取到列名，直接返回
            if not column_names:
                logger.warning("未能获取列名，跳过格式化")
                return
            
            # 冻结首行
            worksheet.freeze_panes(1, 0)
            
            # 🔧 修复：智能设置列格式，与字段值匹配
            for idx, col in enumerate(column_names):
                col_lower = col.lower() if isinstance(col, str) else ""

                # 🔧 修复：智能计算列宽，基于列名和数据内容
                # 先计算列名长度
                header_width = len(str(col)) if col else 8

                # 计算数据内容的最大长度（检查前50行以提高性能）
                max_data_width = header_width
                try:
                    if df is not None and len(df) > 0:
                        # 获取该列的前50行数据
                        sample_data = df[col].head(50) if col in df.columns else []
                        for value in sample_data:
                            if value is not None:
                                value_length = len(str(value))
                                if value_length > max_data_width:
                                    max_data_width = value_length
                except Exception as e:
                    logger.debug(f"计算列 {col} 数据宽度时出错: {e}")

                # 设置合理的列宽（最小8，最大60）
                width = max(8, min(max_data_width + 2, 60))

                # 设置表头格式
                worksheet.write(0, idx, col, header_format)

                # 🔧 修复：根据列名和内容类型设置特定格式
                if "日期" in col_lower and "时间" not in col_lower:
                    worksheet.set_column(idx, idx, width, date_format)
                elif "时间" in col_lower or "日期时间" in col_lower:
                    worksheet.set_column(idx, idx, width, datetime_format)
                elif any(term in col_lower for term in ["金额", "余额", "发生额", "价格", "费用"]):
                    worksheet.set_column(idx, idx, width, amount_format)
                elif any(term in col_lower for term in ["号码", "编号", "账号", "卡号", "流水号", "商户号", "证件号"]):
                    # 数字类型的字段设置为文本格式，防止科学计数法
                    worksheet.set_column(idx, idx, width, text_format)
                elif table_name == "账户交易明细表" and col in ["商户号", "交易账号", "交易账卡号", "对手账号", "对手卡号"]:
                    worksheet.set_column(idx, idx, width, text_format)
                elif table_name == "开户信息表" and col in ["最后交易时间", "账号开户时间", "交易卡号", "交易账号"]:
                    worksheet.set_column(idx, idx, width, text_format)
                elif table_name == "财付通交易明细表":
                    # 财付通交易明细表特定格式
                    if col in ["交易流水号", "商户号", "用户侧账号", "平台企业侧账号"]:
                        worksheet.set_column(idx, idx, width, text_format)
                    elif col == "交易时间":
                        worksheet.set_column(idx, idx, width, datetime_format)
                    elif "金额" in col:
                        worksheet.set_column(idx, idx, width, amount_format)
                    else:
                        worksheet.set_column(idx, idx, width)
                else:
                    # 默认格式
                    worksheet.set_column(idx, idx, width)

                logger.debug(f"列 {col} 设置宽度: {width} (标题长度: {header_width}, 数据长度: {max_data_width})")
            
            # 添加自动筛选
            last_row = worksheet.dim_rowmax
            last_col = len(column_names) - 1
            worksheet.autofilter(0, 0, last_row, last_col)
            
            logger.info(f"完成Excel格式化，表名: {table_name}，应用了 {len(column_names)} 列的格式设置")
            
        except Exception as e:
            logger.error(f"设置Excel列格式失败: {e}")
            # 不抛出异常，避免中断导出过程

    def _export_group(self, table_name, group_names, file_path, group_idx, total_groups, estimated_rows, group_field=None):
        """导出单个交易户名组的数据，返回索引数据。这个方法在线程池中并行执行"""
        try:
            # 创建新的数据库连接，避免线程冲突
            thread_conn = self.engine.connect()
            cursor = thread_conn.connection.cursor()
            
            # 优化PostgreSQL参数，高内存机器使用更激进的设置
            thread_conn.connection.set_session(
                isolation_level=psycopg2.extensions.ISOLATION_LEVEL_READ_COMMITTED,
                readonly=True,  # 导出操作只读
                deferrable=True
            )
            thread_conn.connection.set_client_encoding('UTF8')
            
            # 如果没有传入分组字段，则根据表名确定
            if group_field is None:
                group_field = "交易户名" if table_name == "账户交易明细表" else "用户侧账号名称"
                
            logger.info(f"启动线程处理第 {group_idx+1}/{total_groups} 组，包含 {len(group_names)} 个{group_field}")
            logger.info(f"处理的{group_field}: {group_names[:5]}{'...' if len(group_names) > 5 else ''}")
            
            # 构建排除字段的列表
            exclude_columns = ['id', 'ID']  # 所有表都不导出ID字段
            
            # 账户交易明细表特定排除字段
            if table_name == "账户交易明细表":
                exclude_columns.extend(['交易账卡号_digits', '交易账号_digits', '对手账号_digits', '对手卡号_digits'])
            elif table_name == "财付通交易明细表":
                # 财付通交易明细表可能的需要排除的字段
                exclude_columns.extend(['用户侧账号_digits', '平台企业侧账号_digits'])
                
            logger.info(f"导出分组 {group_idx+1} 时排除字段: {exclude_columns}")
            
            # 简化查询逻辑 - 直接使用SQL的字符串构建功能
            # 注意：这里直接构建SQL字符串，而不是使用参数化查询，但户名已经是从数据库中获取的安全值
            sql_conditions = []
            
            # 添加未知户名条件
            if '未知户名' in group_names or f'未知{group_field}' in group_names:
                sql_conditions.append(f"(\"{group_field}\" IS NULL OR \"{group_field}\" = '')")
                if '未知户名' in group_names:
                    group_names.remove('未知户名')  # 从列表中移除未知户名，因为已单独处理
                if f'未知{group_field}' in group_names:
                    group_names.remove(f'未知{group_field}')  # 从列表中移除未知户名，因为已单独处理
            
            # 添加其他户名
            if group_names:
                name_list = ", ".join([f"'{name}'" for name in group_names])
                sql_conditions.append(f"\"{group_field}\" IN ({name_list})")
            
            # 组合WHERE子句
            if not sql_conditions:
                logger.warning(f"第 {group_idx+1} 组没有有效的{group_field}，跳过")
                thread_conn.close()
                return []
            
            combined_where = " OR ".join(sql_conditions)
            
            # 根据表类型添加排序
            order_by_clause = ""
            if table_name == "账户交易明细表":
                # 检查是否有交易日期字段
                cursor.execute("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = '账户交易明细表'
                """)
                available_columns = [row[0] for row in cursor.fetchall()]
                
                # 对于账户交易明细表，按交易日期升序排序
                if "交易日期" in available_columns:
                    order_by_clause = " ORDER BY \"交易日期\" ASC"
                elif "交易时间" in available_columns:
                    order_by_clause = " ORDER BY \"交易时间\" ASC"
                
                logger.info(f"账户交易明细表将按{order_by_clause}排序")
            
            query = f"""
                SELECT * 
                FROM "{table_name}" 
                WHERE ({combined_where}) AND "案件编号" = '{self.case_id}'
                {order_by_clause}
            """
            
            # 记录查询信息
            logger.info(f"第 {group_idx+1} 组执行数据查询...")
            
            # 获取实际数据量
            count_start = time.time()
            count_query = query.replace("SELECT *", "SELECT COUNT(*)")
            # 移除排序，提高计数查询性能
            count_query = count_query.split("ORDER BY")[0] if "ORDER BY" in count_query else count_query
            cursor.execute(count_query)
            actual_count = cursor.fetchone()[0]
            logger.info(f"第 {group_idx+1} 组实际数据量: {actual_count} 行，计数耗时: {time.time() - count_start:.2f}秒")
            
            if actual_count == 0:
                logger.warning(f"第 {group_idx+1} 组没有数据，跳过此组")
                thread_conn.close()
                return []
            
            # 获取索引数据 - 使用改进的查询方式获取更完整的账号信息
            index_start = time.time()
            logger.info(f"第 {group_idx+1} 组开始查询索引数据")
            
            try:
                # 根据表类型选择不同的索引查询
                if table_name == "账户交易明细表":
                    # 检查哪些列实际存在
                    cursor.execute("""
                        SELECT column_name 
                        FROM information_schema.columns 
                        WHERE table_name = '账户交易明细表'
                    """)
                    available_columns = [row[0] for row in cursor.fetchall()]
                    
                    # 构建查询
                    select_fields = [f'COALESCE("{group_field}", \'未知{group_field}\') as 分组值']
                    group_by_fields = ['分组值']
                    
                    # 添加交易账号字段(如果存在)
                    if "交易账号" in available_columns:
                        select_fields.append('"交易账号"')
                        group_by_fields.append('"交易账号"')
                    
                    # 添加交易账卡号字段(如果存在)
                    if "交易账卡号" in available_columns:
                        select_fields.append('"交易账卡号"')
                        group_by_fields.append('"交易账卡号"')
                    
                    # 添加计数字段
                    select_fields.append('COUNT(*) as 记录数')
                    
                    improved_index_query = f"""
                        SELECT {', '.join(select_fields)}
                        FROM "{table_name}" 
                        WHERE ({combined_where}) AND "案件编号" = '{self.case_id}'
                        GROUP BY {', '.join(group_by_fields)}
                        ORDER BY 记录数 DESC
                    """
                elif table_name == "财付通交易明细表":
                    # 检查哪些列实际存在
                    cursor.execute("""
                        SELECT column_name 
                        FROM information_schema.columns 
                        WHERE table_name = '财付通交易明细表'
                    """)
                    available_columns = [row[0] for row in cursor.fetchall()]
                    logger.info(f"财付通交易明细表可用列: {available_columns}")
                    
                    # 构建查询
                    select_fields = [f'COALESCE("{group_field}", \'未知{group_field}\') as 分组值']
                    group_by_fields = ['分组值']
                    
                    # 添加用户侧账号字段(如果存在)
                    if "用户侧账号" in available_columns:
                        select_fields.append('"用户侧账号"')
                        group_by_fields.append('"用户侧账号"')
                    
                    # 添加交易流水号字段(如果存在) - 只获取一个示例值，不参与分组
                    if "交易流水号" in available_columns:
                        select_fields.append('MAX("交易流水号") as 交易流水号示例')
                    elif "商户单号" in available_columns:  # 备选字段
                        select_fields.append('MAX("商户单号") as 交易流水号示例')
                    
                    # 添加计数字段
                    select_fields.append('COUNT(*) as 记录数')
                    
                    improved_index_query = f"""
                        SELECT {', '.join(select_fields)}
                        FROM "{table_name}" 
                        WHERE ({combined_where}) AND "案件编号" = '{self.case_id}'
                        GROUP BY {', '.join(group_by_fields)}
                        ORDER BY 记录数 DESC
                    """
                else:
                    # 默认的简单查询
                    improved_index_query = f"""
                        SELECT 
                            COALESCE("{group_field}", '未知{group_field}') as 分组值, 
                            COUNT(*) as 记录数
                        FROM "{table_name}" 
                        WHERE ({combined_where}) AND "案件编号" = '{self.case_id}'
                        GROUP BY 分组值
                        ORDER BY 记录数 DESC
                    """
                
                cursor.execute(improved_index_query)
                index_results = cursor.fetchall()
                logger.info(f"第 {group_idx+1} 组索引查询返回 {len(index_results)} 条记录")
                
                # 如果没有获取到详细数据，再尝试简化查询
                if not index_results:
                    logger.warning(f"未获取到详细索引数据，尝试简化查询")
                    simple_index_query = f"""
                        SELECT 
                            COALESCE("{group_field}", '未知{group_field}') as 分组值, 
                            COUNT(*) as 记录数
                        FROM "{table_name}" 
                        WHERE ({combined_where}) AND "案件编号" = '{self.case_id}'
                        GROUP BY 分组值
                        ORDER BY 记录数 DESC
                    """
                    cursor.execute(simple_index_query)
                    simple_results = cursor.fetchall()
                    
                    # 构建简单的索引数据
                    if table_name == "账户交易明细表":
                        index_data = [(name, "", "", count) for name, count in simple_results]
                    elif table_name == "财付通交易明细表":
                        index_data = [(name, "", "", count) for name, count in simple_results]
                    else:
                        index_data = [(name, count) for name, count in simple_results]
                else:
                    # 使用详细索引数据
                    index_data = index_results
                
                # 打印一些示例记录进行调试
                if index_data and len(index_data) > 0:
                    logger.info(f"第 {group_idx+1} 组索引数据示例: {index_data[0]}")
            except Exception as e:
                logger.error(f"索引查询出错: {e}")
                # 构建最小索引数据
                if table_name == "账户交易明细表":
                    index_data = [(name, "", "", 0) for name in group_names]
                elif table_name == "财付通交易明细表":
                    index_data = [(name, "", "", 0) for name in group_names]
                else:
                    index_data = [(name, 0) for name in group_names]
            
            logger.info(f"第 {group_idx+1} 组索引查询完成，获取到 {len(index_data)} 条索引记录，耗时: {time.time() - index_start:.2f}秒")
            
            # 记录数据导出开始
            logger.info(f"第 {group_idx+1} 组开始导出数据到文件: {os.path.basename(file_path)}")
            
            # 直接查询数据并导出到Excel - 使用最简单直接的方式
            export_start = time.time()
            try:
                # 直接查询所有数据
                logger.info(f"第 {group_idx+1} 组开始执行主查询")
                data_df = pd.read_sql_query(query, thread_conn)
                
                if data_df.empty:
                    logger.warning(f"第 {group_idx+1} 组查询结果为空，跳过此组")
                    thread_conn.close()
                    return index_data
                
                # 记录获取到的数据量
                logger.info(f"第 {group_idx+1} 组成功获取 {len(data_df)} 行数据")
                
                # 数据处理
                logger.info(f"第 {group_idx+1} 组开始处理数据格式")
                
                # 处理数据格式
                self._format_dataframe(data_df, table_name)
                
                # 移除不需要导出的列
                for col in exclude_columns:
                    if col in data_df.columns:
                        data_df = data_df.drop(columns=[col])
                
                # 输出最终用于写入的数据列
                logger.info(f"第 {group_idx+1} 组最终写入列: {data_df.columns.tolist()}")
                logger.info(f"第 {group_idx+1} 组数据大小: {len(data_df)}行 x {len(data_df.columns)}列")
                
                # 使用to_excel直接写入文件
                logger.info(f"第 {group_idx+1} 组开始写入Excel文件: {file_path}")
                
                # 确保父目录存在
                os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
                
                # 使用不同的Excel引擎尝试
                try:
                    logger.info(f"第 {group_idx+1} 组尝试使用xlsxwriter引擎写入Excel")
                    with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                        data_df.to_excel(writer, sheet_name=table_name, index=False)
                        # 设置Excel格式
                        self._format_excel_columns(writer, data_df, table_name)
                except Exception as e:
                    logger.error(f"使用xlsxwriter引擎写入失败: {e}")
                    logger.info(f"第 {group_idx+1} 组尝试使用备用openpyxl引擎写入Excel")
                    # 尝试使用openpyxl引擎
                    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                        data_df.to_excel(writer, sheet_name=table_name, index=False)
                
                # 验证文件是否创建成功
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    logger.info(f"第 {group_idx+1} 组Excel文件创建成功: {file_path}, 文件大小: {file_size/1024/1024:.2f}MB")
                else:
                    logger.error(f"第 {group_idx+1} 组Excel文件未能创建: {file_path}")
                
                # 导出完成
                export_time = time.time() - export_start
                logger.info(f"第 {group_idx+1} 组导出完成，总耗时: {export_time:.2f}秒，平均速度: {len(data_df)/export_time:.0f}行/秒")
                
                return index_data
                
            except Exception as e:
                logger.error(f"导出第 {group_idx+1} 组时发生错误: {str(e)}")
                # 尝试使用最基本的CSV导出方式作为备用方案
                try:
                    csv_path = file_path.replace('.xlsx', '.csv')
                    logger.info(f"尝试使用CSV格式导出: {csv_path}")
                    data_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                    logger.info(f"CSV导出成功: {csv_path}")
                except Exception as csv_e:
                    logger.error(f"CSV导出也失败: {str(csv_e)}")
                
                raise
            finally:
                # 确保连接被关闭
                try:
                    thread_conn.close()
                except:
                    pass
            
        except Exception as e:
            logger.error(f"_export_group 方法执行失败: {str(e)}")
            # 确保资源被释放
            try:
                if 'thread_conn' in locals() and thread_conn:
                    thread_conn.close()
            except:
                pass
            # 返回空列表而不是抛出异常，以防止整个导出过程中断
            return []

class ExportProgressDialog(QDialog):
    """显示导出进度的对话框，包括当前文件和总体进度"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("导出进度")
        self.setMinimumWidth(500)
        self.setMinimumHeight(200)

        # 🔧 修复：初始化关闭标志
        self._is_closing = False

        # 创建布局
        layout = QVBoxLayout(self)
        
        # 当前文件标签
        self.file_label = QLabel("准备导出...")
        layout.addWidget(self.file_label)
        
        # 当前文件进度条
        self.file_progress = QProgressBar()
        self.file_progress.setRange(0, 100)
        layout.addWidget(self.file_progress)
        
        # 总体进度标签
        layout.addWidget(QLabel("总体进度:"))
        
        # 总体进度条
        self.overall_progress = QProgressBar()
        self.overall_progress.setRange(0, 100)
        layout.addWidget(self.overall_progress)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def update_file_progress(self, filename, progress):
        """更新当前文件的进度"""
        self.file_label.setText(f"当前导出: {filename}")
        self.file_progress.setValue(progress)
    
    def update_overall_progress(self, progress):
        """更新总体进度"""
        self.overall_progress.setValue(progress)
        
    def set_finished(self):
        """设置为完成状态"""
        self.file_label.setText("导出完成!")
        self.file_progress.setValue(100)
        self.overall_progress.setValue(100)
        self.cancel_button.setText("关闭")

class ImprovedTableSelectionDialog(QDialog):
    """
    改进的表选择对话框

    功能说明：
    - 本文件的功能和实现逻辑：提供改进的表选择界面，支持多选、默认全选、排除特定表
    - 显示有数据的表供用户选择
    - 默认全选所有表（除了对手信息表）
    - 支持全选/全不选功能
    - 显示每个表的数据量
    """
    def __init__(self, available_tables, parent=None):
        super().__init__(parent)
        self.available_tables = available_tables
        self.checkboxes = {}

        self.setWindowTitle("选择要导出的数据表")
        self.setMinimumWidth(600)
        self.setMinimumHeight(500)
        self.resize(700, 600)

        self.init_ui()

        # 默认全选（除了对手信息表）
        self.select_all_tables()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("📊 选择要导出的数据表")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #1976D2; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 说明信息
        info_text = f"""
💡 <b>导出说明:</b>
• 显示当前案件中有数据的表（共 {len(self.available_tables)} 个）
• 默认已全选所有表，可根据需要调整选择
• 选中的表将按分类导出到不同的Excel文件中
• 同类表会合并到一个文件的不同工作表
        """.strip()

        info_label = QLabel(info_text)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #E3F2FD;
                border: 1px solid #BBDEFB;
                border-radius: 5px;
                padding: 10px;
                margin: 5px 0px;
            }
        """)
        layout.addWidget(info_label)

        # 全选/全不选按钮
        button_layout = QHBoxLayout()

        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all_tables)
        select_all_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 15px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        select_none_btn = QPushButton("全不选")
        select_none_btn.clicked.connect(self.select_none_tables)
        select_none_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 15px;
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)

        button_layout.addWidget(select_all_btn)
        button_layout.addWidget(select_none_btn)
        button_layout.addStretch()

        # 选择计数标签
        self.selection_label = QLabel()
        self.update_selection_count()
        button_layout.addWidget(self.selection_label)

        layout.addLayout(button_layout)

        # 表格列表（滚动区域）
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.tables_layout = QVBoxLayout(scroll_widget)

        # 按数据量降序排序
        sorted_tables = dict(sorted(self.available_tables.items(), key=lambda x: x[1], reverse=True))

        for table_name, count in sorted_tables.items():
            checkbox_frame = self.create_table_checkbox(table_name, count)
            self.tables_layout.addWidget(checkbox_frame)

        self.tables_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        # 确定和取消按钮
        dialog_buttons = QHBoxLayout()
        dialog_buttons.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                background-color: #757575;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #616161;
            }
        """)

        ok_btn = QPushButton("确定导出")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                background-color: #1976D2;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1565C0;
            }
        """)

        dialog_buttons.addWidget(cancel_btn)
        dialog_buttons.addWidget(ok_btn)
        layout.addLayout(dialog_buttons)

    def create_table_checkbox(self, table_name, count):
        """创建表格复选框"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                margin: 2px;
                padding: 5px;
            }
            QFrame:hover {
                border-color: #2196F3;
                background-color: #F5F5F5;
            }
        """)

        layout = QHBoxLayout(frame)
        layout.setContentsMargins(10, 8, 10, 8)

        # 复选框
        checkbox = QCheckBox()
        checkbox.stateChanged.connect(self.update_selection_count)
        self.checkboxes[table_name] = checkbox
        layout.addWidget(checkbox)

        # 表名
        name_label = QLabel(table_name)
        name_label.setStyleSheet("font-weight: bold; color: #333;")
        layout.addWidget(name_label)

        layout.addStretch()

        # 数据量
        count_label = QLabel(f"{count:,} 条记录")
        if count > 1000000:
            count_label.setStyleSheet("color: #FF6D00; font-weight: bold;")  # 橙色：大数据量
        elif count > 10000:
            count_label.setStyleSheet("color: #1976D2; font-weight: bold;")  # 蓝色：中等数据量
        else:
            count_label.setStyleSheet("color: #4CAF50; font-weight: bold;")  # 绿色：小数据量
        layout.addWidget(count_label)

        return frame

    def select_all_tables(self):
        """全选所有表"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(True)
        self.update_selection_count()

    def select_none_tables(self):
        """全不选"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(False)
        self.update_selection_count()

    def update_selection_count(self):
        """更新选择计数"""
        selected_count = sum(1 for cb in self.checkboxes.values() if cb.isChecked())
        total_count = len(self.checkboxes)
        self.selection_label.setText(f"已选择: {selected_count}/{total_count} 个表")
        self.selection_label.setStyleSheet("font-weight: bold; color: #1976D2;")

    def get_selected_tables(self):
        """获取选择的表"""
        selected = {}
        for table_name, checkbox in self.checkboxes.items():
            if checkbox.isChecked():
                selected[table_name] = self.available_tables[table_name]
        return selected

class TableSelectionDialog(QDialog):
    """优化的表选择对话框，支持搜索、分类显示和更好的用户体验"""
    def __init__(self, available_tables, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择要导出的数据表")
        self.setMinimumWidth(800)  # 增加宽度
        self.setMinimumHeight(600)  # 增加高度
        self.resize(900, 700)  # 设置默认大小
        
        # 存储原始数据
        self.available_tables = available_tables
        self.filtered_tables = available_tables.copy()
        
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 创建顶部信息区域
        self.create_header_section(layout)
        
        # 创建搜索区域
        self.create_search_section(layout)
        
        # 创建表格选择区域
        self.create_table_selection_section(layout)
        
        # 创建底部按钮区域
        self.create_button_section(layout)
        
        # 初始化显示
        self.update_table_display()
    
    def create_header_section(self, layout):
        """创建顶部信息区域"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 10px;
                margin-bottom: 10px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        
        # 统计信息
        total_count = sum(self.available_tables.values())
        tables_with_data = {k: v for k, v in self.available_tables.items() if v > 0}
        tables_without_data = {k: v for k, v in self.available_tables.items() if v == 0}
        
        self.header_label = QLabel(f"📊 发现 {len(self.available_tables)} 个数据表，其中 {len(tables_with_data)} 个有数据")
        self.header_label.setStyleSheet("font-weight: bold; color: #2E7D32; font-size: 14px;")
        header_layout.addWidget(self.header_label)
        
        self.count_label = QLabel(f"📈 总计 {total_count:,} 条记录")
        self.count_label.setStyleSheet("color: #1976D2; font-size: 12px;")
        header_layout.addWidget(self.count_label)
        
        # 添加说明
        info_label = QLabel("💡 提示：有数据的表已默认选中，您可以搜索和筛选需要的表")
        info_label.setStyleSheet("color: #666; font-size: 11px; margin-top: 5px;")
        header_layout.addWidget(info_label)
        
        layout.addWidget(header_frame)
    
    def create_search_section(self, layout):
        """创建搜索区域"""
        search_frame = QFrame()
        search_layout = QHBoxLayout(search_frame)
        
        # 搜索输入框
        search_label = QLabel("🔍 搜索表:")
        search_label.setStyleSheet("font-weight: bold; color: #555;")
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入表名关键词进行搜索...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #1976D2;
            }
        """)
        self.search_input.textChanged.connect(self.filter_tables)
        search_layout.addWidget(self.search_input)
        
        # 清除搜索按钮
        clear_button = QPushButton("清除")
        clear_button.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        clear_button.clicked.connect(self.clear_search)
        search_layout.addWidget(clear_button)
        
        layout.addWidget(search_frame)
    
    def create_table_selection_section(self, layout):
        """创建表格选择区域"""
        # 创建选项卡
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QTabBar::tab {
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #1976D2;
                color: white;
            }
        """)
        
        # 全部表格选项卡
        self.all_tables_tab = QScrollArea()
        self.all_tables_widget = QWidget()
        self.all_tables_layout = QVBoxLayout(self.all_tables_widget)
        self.all_tables_tab.setWidget(self.all_tables_widget)
        self.all_tables_tab.setWidgetResizable(True)
        
        # 有数据的表选项卡
        self.data_tables_tab = QScrollArea()
        self.data_tables_widget = QWidget()
        self.data_tables_layout = QVBoxLayout(self.data_tables_widget)
        self.data_tables_tab.setWidget(self.data_tables_widget)
        self.data_tables_tab.setWidgetResizable(True)
        
        # 无数据的表选项卡
        self.empty_tables_tab = QScrollArea()
        self.empty_tables_widget = QWidget()
        self.empty_tables_layout = QVBoxLayout(self.empty_tables_widget)
        self.empty_tables_tab.setWidget(self.empty_tables_widget)
        self.empty_tables_tab.setWidgetResizable(True)
        
        # 添加选项卡
        self.tab_widget.addTab(self.all_tables_tab, "全部表格")
        self.tab_widget.addTab(self.data_tables_tab, "有数据的表")
        self.tab_widget.addTab(self.empty_tables_tab, "空表")
        
        # 创建复选框字典
        self.checkboxes = {}
        
        layout.addWidget(self.tab_widget)
    
    def create_button_section(self, layout):
        """创建底部按钮区域"""
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)
        
        # 左侧操作按钮
        left_buttons = QHBoxLayout()
        
        # 全选按钮
        self.select_all_button = QPushButton("全选当前")
        self.select_all_button.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.select_all_button.clicked.connect(self.select_all_current)
        left_buttons.addWidget(self.select_all_button)
        
        # 取消全选按钮
        self.deselect_all_button = QPushButton("取消全选")
        self.deselect_all_button.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e68900;
            }
        """)
        self.deselect_all_button.clicked.connect(self.deselect_all)
        left_buttons.addWidget(self.deselect_all_button)
        
        # 只选有数据的表
        self.select_data_only_button = QPushButton("只选有数据的表")
        self.select_data_only_button.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.select_data_only_button.clicked.connect(self.select_data_only)
        left_buttons.addWidget(self.select_data_only_button)
        
        button_layout.addLayout(left_buttons)
        button_layout.addStretch()
        
        # 右侧确认按钮
        right_buttons = QHBoxLayout()
        
        # 显示选中统计
        self.selection_label = QLabel("已选择: 0 个表")
        self.selection_label.setStyleSheet("color: #666; font-weight: bold; margin-right: 20px;")
        right_buttons.addWidget(self.selection_label)
        
        # 确定按钮
        self.ok_button = QPushButton("确定导出")
        self.ok_button.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                background-color: #1976D2;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        self.ok_button.clicked.connect(self.accept)
        right_buttons.addWidget(self.ok_button)
        
        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                background-color: #666;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        right_buttons.addWidget(self.cancel_button)
        
        button_layout.addLayout(right_buttons)
        layout.addWidget(button_frame)
    
    def create_checkbox(self, table_name, count):
        """创建单个表的复选框"""
        checkbox_frame = QFrame()
        checkbox_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #eee;
                border-radius: 4px;
                padding: 8px;
                margin: 2px;
            }
            QFrame:hover {
                background-color: #f8f9fa;
                border-color: #1976D2;
            }
        """)
        
        checkbox_layout = QHBoxLayout(checkbox_frame)
        checkbox_layout.setContentsMargins(8, 4, 8, 4)
        
        # 复选框
        checkbox = QCheckBox()
        if count > 0:
            checkbox.setChecked(True)  # 有数据的表默认选中
        else:
            checkbox.setChecked(False)  # 无数据的表默认不选中
        
        checkbox.stateChanged.connect(self.update_selection_count)
        checkbox_layout.addWidget(checkbox)
        
        # 表名标签
        name_label = QLabel(table_name)
        if count > 100000:
            name_label.setStyleSheet("color: #1976D2; font-weight: bold; font-size: 12px;")
        elif count > 10000:
            name_label.setStyleSheet("color: #388E3C; font-weight: bold; font-size: 12px;")
        elif count > 0:
            name_label.setStyleSheet("color: #555; font-size: 12px;")
        else:
            name_label.setStyleSheet("color: #999; font-style: italic; font-size: 12px;")
        
        checkbox_layout.addWidget(name_label)
        checkbox_layout.addStretch()
        
        # 记录数标签
        if count > 0:
            count_label = QLabel(f"{count:,} 条")
            if count > 100000:
                count_label.setStyleSheet("""
                    background-color: #1976D2; 
                    color: white; 
                    padding: 2px 8px; 
                    border-radius: 10px; 
                    font-size: 10px; 
                    font-weight: bold;
                """)
            elif count > 10000:
                count_label.setStyleSheet("""
                    background-color: #4CAF50; 
                    color: white; 
                    padding: 2px 8px; 
                    border-radius: 10px; 
                    font-size: 10px; 
                    font-weight: bold;
                """)
            else:
                count_label.setStyleSheet("""
                    background-color: #FF9800; 
                    color: white; 
                    padding: 2px 8px; 
                    border-radius: 10px; 
                    font-size: 10px; 
                    font-weight: bold;
                """)
        else:
            count_label = QLabel("无数据")
            count_label.setStyleSheet("""
                background-color: #ccc; 
                color: white; 
                padding: 2px 8px; 
                border-radius: 10px; 
                font-size: 10px;
            """)
        
        checkbox_layout.addWidget(count_label)
        
        return checkbox, checkbox_frame
    
    def update_table_display(self):
        """更新表格显示"""
        # 清空现有的复选框
        self.clear_layouts()
        self.checkboxes.clear()
        
        # 按数据量排序
        sorted_tables = dict(sorted(self.filtered_tables.items(), key=lambda x: x[1], reverse=True))
        
        # 分类表格
        tables_with_data = {k: v for k, v in sorted_tables.items() if v > 0}
        tables_without_data = {k: v for k, v in sorted_tables.items() if v == 0}
        
        # 添加到全部表格选项卡
        for table_name, count in sorted_tables.items():
            checkbox, checkbox_frame = self.create_checkbox(table_name, count)
            self.checkboxes[table_name] = checkbox
            self.all_tables_layout.addWidget(checkbox_frame)
        
        # 添加到有数据的表选项卡
        for table_name, count in tables_with_data.items():
            checkbox, checkbox_frame = self.create_checkbox(table_name, count)
            self.data_tables_layout.addWidget(checkbox_frame)
        
        # 添加到无数据的表选项卡
        for table_name, count in tables_without_data.items():
            checkbox, checkbox_frame = self.create_checkbox(table_name, count)
            self.empty_tables_layout.addWidget(checkbox_frame)
        
        # 添加弹簧
        self.all_tables_layout.addStretch()
        self.data_tables_layout.addStretch()
        self.empty_tables_layout.addStretch()
        
        # 更新选项卡标题
        self.tab_widget.setTabText(0, f"全部表格 ({len(sorted_tables)})")
        self.tab_widget.setTabText(1, f"有数据的表 ({len(tables_with_data)})")
        self.tab_widget.setTabText(2, f"空表 ({len(tables_without_data)})")
        
        # 更新选择计数
        self.update_selection_count()
    
    def clear_layouts(self):
        """清空所有布局"""
        for layout in [self.all_tables_layout, self.data_tables_layout, self.empty_tables_layout]:
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
    
    def filter_tables(self, text):
        """根据搜索文本过滤表格"""
        if not text.strip():
            self.filtered_tables = self.available_tables.copy()
        else:
            search_text = text.lower()
            self.filtered_tables = {
                name: count for name, count in self.available_tables.items() 
                if search_text in name.lower()
            }
        
        self.update_table_display()
    
    def clear_search(self):
        """清除搜索"""
        self.search_input.clear()
    
    def select_all_current(self):
        """选择当前显示的所有表"""
        for table_name, checkbox in self.checkboxes.items():
            if table_name in self.filtered_tables:
                checkbox.setChecked(True)
    
    def deselect_all(self):
        """取消选择所有表"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(False)
    
    def select_data_only(self):
        """只选择有数据的表"""
        for table_name, checkbox in self.checkboxes.items():
            if table_name in self.filtered_tables:
                has_data = self.filtered_tables[table_name] > 0
                checkbox.setChecked(has_data)
    
    def update_selection_count(self):
        """更新选择计数"""
        selected_count = sum(1 for checkbox in self.checkboxes.values() if checkbox.isChecked())
        selected_records = sum(
            self.available_tables[table_name] 
            for table_name, checkbox in self.checkboxes.items() 
            if checkbox.isChecked() and table_name in self.available_tables
        )
        
        self.selection_label.setText(f"已选择: {selected_count} 个表 ({selected_records:,} 条记录)")
        
        # 更新确定按钮状态
        self.ok_button.setEnabled(selected_count > 0)
    
    def get_selected_tables(self):
        """获取所有被选中的表"""
        return [table_name for table_name, checkbox in self.checkboxes.items() if checkbox.isChecked()]

def export_data_with_partitioning(case_id, case_name):
    """
    导出数据 - 现已修改为按分类导出

    功能说明：
    - 本文件的功能和实现逻辑：原本是单表导出，现已修改为按分类导出
    - 按分类导出：将相同类别的表放在同一个Excel文件中，不同表作为不同工作表
    - 支持14个主要分类，涵盖90个数据表的智能分类导出
    - 保持原有的表选择界面，但导出时按分类生成文件
    """
    try:
        # 动态查询所有包含案件编号字段的数据表
        available_tables = {}
        
        # 创建数据库连接
        config = configparser.ConfigParser()
        config.read('db_config.ini')
        
        # 构建数据库连接URL
        db_url = f"postgresql://{config['PostgreSQL']['user']}:{config['PostgreSQL']['password']}@" \
                f"{config['PostgreSQL']['host']}:{config['PostgreSQL']['port']}/{config['PostgreSQL']['database']}"
        
        # 创建SQLAlchemy引擎
        engine = create_engine(db_url, echo=False)
        
        # 动态发现所有包含"案件编号"字段的表
        try:
            # 使用直接的数据库连接而不是SQLAlchemy
            from database_setup import get_db_connection
            db_conn = get_db_connection()
            cursor = db_conn.cursor()
            
            # 查询所有包含"案件编号"字段的表
            discover_query = """
                SELECT DISTINCT table_name 
                FROM information_schema.columns 
                WHERE column_name = '案件编号'
                  AND table_schema = 'public'
                  AND table_name NOT LIKE '%temp%'
                  AND table_name NOT LIKE '%_backup%'
                  AND table_name NOT IN ('对手信息表', '案件信息表')
                ORDER BY table_name
            """
            
            cursor.execute(discover_query)
            table_results = cursor.fetchall()
            table_names = [row[0] for row in table_results]
            
            logger.info(f"发现 {len(table_names)} 个包含案件编号的数据表: {table_names}")
            
            # 查询每个表中该案件的数据量
            for table_name in table_names:
                try:
                    count_query = f"""
                        SELECT COUNT(*) 
                        FROM "{table_name}" 
                        WHERE "案件编号" = %s
                    """
                    cursor.execute(count_query, (case_id,))
                    count = cursor.fetchone()[0]
                    available_tables[table_name] = count
                    
                    if count > 0:
                        logger.info(f"📊 表 {table_name} 中案件 {case_id} 的数据量: {count:,}")
                    else:
                        logger.debug(f"⚪ 表 {table_name} 中案件 {case_id} 无数据")
                        
                except Exception as e:
                    logger.error(f"查询表 {table_name} 数据量时出错: {e}")
                    available_tables[table_name] = 0
            
            cursor.close()
            db_conn.close()
            
            # 显示所有包含案件编号字段的表，不过滤无数据的表
            # 按数据量降序排序，有数据的表排在前面
            available_tables = dict(sorted(available_tables.items(), key=lambda x: x[1], reverse=True))
            
            logger.info(f"✅ 找到 {len(available_tables)} 个包含案件编号字段的表（包含无数据的表）")
            
            # 统计有数据和无数据的表
            tables_with_data = {k: v for k, v in available_tables.items() if v > 0}
            tables_without_data = {k: v for k, v in available_tables.items() if v == 0}
            logger.info(f"📊 其中有数据的表: {len(tables_with_data)} 个，无数据的表: {len(tables_without_data)} 个")
            
        except Exception as e:
            logger.error(f"动态发现数据表时出错: {e}")
            # 回退到默认表列表
            default_tables = ["账户交易明细表", "开户信息表", "财付通交易明细表"]
            logger.info("回退到默认表列表")
            
            try:
                # 使用直接的数据库连接查询默认表
                from database_setup import get_db_connection
                db_conn = get_db_connection()
                cursor = db_conn.cursor()
                
                for table_name in default_tables:
                    try:
                        count_query = f"""
                            SELECT COUNT(*) 
                            FROM "{table_name}" 
                            WHERE "案件编号" = %s
                        """
                        cursor.execute(count_query, (case_id,))
                        count = cursor.fetchone()[0]
                        available_tables[table_name] = count
                    except Exception as table_e:
                        logger.error(f"查询默认表 {table_name} 失败: {table_e}")
                        available_tables[table_name] = 0
                        
                cursor.close()
                db_conn.close()
                
            except Exception as fallback_e:
                logger.error(f"查询默认表时也出错: {fallback_e}")
                # 如果连默认表都查询不了，至少提供一个空的表列表
                available_tables = {"开户信息表": 0}
        
        # 🔧 修改：显示表选择对话框，但使用分类导出
        logger.info("🚀 导入数据界面：显示表选择对话框，选择后按分类导出")

        # 过滤有数据的表，排除对手信息表
        tables_with_data = {k: v for k, v in available_tables.items() if v > 0 and k != '对手信息表'}

        if not tables_with_data:
            # 🔧 修复：获取主窗口作为父窗口，避免使用None导致程序退出
            from PySide6.QtWidgets import QApplication
            main_window = QApplication.activeWindow()
            QMessageBox.information(main_window, "提示", "当前案件没有数据表包含数据，无法导出")
            logger.info("当前案件没有数据表包含数据，取消导出")
            return

        # 弹出改进的表选择对话框
        table_dialog = ImprovedTableSelectionDialog(tables_with_data)
        if table_dialog.exec_() != QDialog.Accepted:
            logger.info("用户取消了导出操作")
            return

        # 获取用户选择的表
        selected_tables = table_dialog.get_selected_tables()

        if not selected_tables:
            logger.info("未选择任何表，取消导出")
            return

        logger.info(f"用户选择了以下表进行导出: {list(selected_tables.keys())}")

        # 选择导出目录
        file_dialog = QFileDialog(None, "选择导出目录", "", "All Files (*)")
        file_dialog.setFileMode(QFileDialog.Directory)

        if file_dialog.exec_():
            export_dir = file_dialog.selectedFiles()[0]

            # 🔧 修改：使用分类导出功能，但只导出选中的表
            logger.info(f"📁 导出目录: {export_dir}")
            logger.info(f"🎯 开始按分类导出案件 {case_id} 的数据，案件名称: {case_name}")

            # 创建分类导出进度对话框
            progress_dialog = CategoryExportProgressDialog()
            progress_dialog.show()

            # 创建分类导出工作线程
            export_thread = QThread()
            export_worker = CategoryExportWorker(case_id, case_name, export_dir)
            export_worker.selected_tables = selected_tables  # 设置选择的表
            export_worker.moveToThread(export_thread)

            # 连接信号和槽
            export_thread.started.connect(export_worker.start_export)
            export_worker.progress_signal.connect(progress_dialog.update_progress)
            export_worker.status_signal.connect(progress_dialog.update_status)
            export_worker.file_exported_signal.connect(progress_dialog.add_exported_file)
            # 🔧 修复：改进信号连接，显示完成状态等待用户确认
            def on_export_finished(files):
                """导出完成处理"""
                try:
                    logger.info(f"✅ 导出完成，共导出 {len(files)} 个文件")

                    # 🔧 修复：显示完成状态，等待用户确认，不立即关闭
                    if progress_dialog and not progress_dialog.isHidden():
                        progress_dialog.set_finished(files)

                    # 🔧 修复：用户点击确认后才清理线程
                    # cleanup_thread() 将在用户关闭对话框时调用

                except Exception as e:
                    logger.error(f"处理导出完成信号时发生错误: {e}")
                    # 如果出错，启用关闭按钮让用户可以关闭
                    try:
                        if progress_dialog:
                            progress_dialog.close_button.setEnabled(True)
                            progress_dialog.close_button.setText("关闭")
                    except:
                        pass

            def on_export_error(error):
                """导出错误处理"""
                try:
                    logger.error(f"❌ 导出过程中发生错误: {error}")

                    # 🔧 修复：显示错误状态，等待用户确认
                    if progress_dialog and not progress_dialog.isHidden():
                        progress_dialog.set_error(error)

                    # 用户点击关闭后才清理线程

                except Exception as e:
                    logger.error(f"处理导出错误信号时发生错误: {e}")
                    # 如果出错，启用关闭按钮让用户可以关闭
                    try:
                        if progress_dialog:
                            progress_dialog.close_button.setEnabled(True)
                            progress_dialog.close_button.setText("关闭")
                    except:
                        pass

            def cleanup_thread():
                """清理线程"""
                try:
                    # 🔧 修复：改进线程清理，避免线程等待自身和Qt资源泄漏
                    logger.info("开始清理导出线程和Qt资源...")

                    # 强制处理所有待处理的Qt事件
                    QApplication.processEvents()

                    if export_thread and export_thread.isRunning():
                        # 不在同一线程中等待自身
                        if QThread.currentThread() != export_thread:
                            export_thread.quit()
                            if not export_thread.wait(3000):  # 等待3秒
                                logger.warning("导出线程未能正常退出，强制终止")
                                export_thread.terminate()
                                export_thread.wait(1000)
                        else:
                            # 如果在同一线程中，只发送quit信号
                            export_thread.quit()

                    # 🔧 修复：确保Qt资源正确释放
                    # 延迟删除对象，避免立即删除导致Qt资源问题
                    if export_worker:
                        QTimer.singleShot(100, export_worker.deleteLater)
                    if export_thread:
                        QTimer.singleShot(200, export_thread.deleteLater)

                    # 🔧 修复：强制垃圾回收，释放内存
                    import gc
                    gc.collect()

                    logger.info("✅ 导出线程和Qt资源已正确清理")
                except Exception as e:
                    logger.error(f"清理导出线程时发生错误: {e}")

            export_worker.finished_signal.connect(on_export_finished)
            export_worker.error_signal.connect(on_export_error)

            # 对话框关闭时的线程清理
            def cleanup_on_dialog_close():
                """对话框关闭时清理线程"""
                try:
                    if export_thread and export_thread.isRunning():
                        logger.info("⚠️ 用户关闭对话框，正在停止导出线程...")
                        export_thread.quit()
                        if not export_thread.wait(3000):  # 等待3秒
                            logger.warning("导出线程未能正常退出，强制终止")
                            export_thread.terminate()
                            export_thread.wait(1000)
                        export_worker.deleteLater()
                        export_thread.deleteLater()
                        logger.info("✅ 导出线程已清理（用户取消）")
                except Exception as e:
                    logger.error(f"清理导出线程时发生错误: {e}")

            # 重写对话框的closeEvent
            original_close_event = progress_dialog.closeEvent
            def custom_close_event(event):
                cleanup_on_dialog_close()
                original_close_event(event)
            progress_dialog.closeEvent = custom_close_event

            # 启动线程
            export_thread.start()

            # 显示对话框
            result = progress_dialog.exec_()

            logger.info(f"✅ 导入数据界面按分类导出完成")
    except Exception as e:
        logger.error(f"导出数据过程中发生错误: {e}")
        # 🔧 修复：获取主窗口作为父窗口，避免使用None导致程序退出
        from PySide6.QtWidgets import QApplication
        main_window = QApplication.activeWindow()
        QMessageBox.critical(main_window, "导出错误", f"导出数据过程中发生错误: {e}")

# 在文件末尾添加新的按分类导出功能

def export_data_by_category(case_id, case_name):
    """
    按照文件分类导出数据，同一类的表放在同一个文件中，不同的表作为不同的工作表
    
    功能说明：
    - 本文件的功能和实现逻辑：使用后台线程按照预定义的表分类映射，将数据库表按类别分组导出
    - 每个类别对应一个Excel文件，类别内的不同表作为不同的工作表
    - 支持自定义工作表名称，提供更好的用户体验
    - 特殊处理账户交易明细表和财付通交易明细表，保持原有导出模式
    - 使用后台线程避免UI卡死，实时显示导出进度
    """
    try:
        logger.info(f"开始按分类导出案件 {case_id} 的数据，案件名称: {case_name}")
        
        # 选择导出目录
        export_dir = QFileDialog.getExistingDirectory(None, "选择导出目录", "")
        if not export_dir:
            return
        
        # 🔧 修复：创建简化的进度对话框，避免Qt绘图问题
        progress_dialog = SimpleExportProgressDialog()
        progress_dialog.show()
        
        # 创建后台工作线程
        worker_thread = QThread()
        worker = CategoryExportWorker(case_id, case_name, export_dir)
        worker.moveToThread(worker_thread)
        
        # 🔧 修复：添加线程管理变量，确保线程正确清理
        progress_dialog.worker_thread = worker_thread  # 保持对线程的引用
        progress_dialog.worker = worker  # 保持对worker的引用
        
        # 连接信号
        worker.progress_signal.connect(progress_dialog.update_progress)
        worker.status_signal.connect(progress_dialog.update_status)
        worker.file_exported_signal.connect(progress_dialog.add_exported_file)
        worker.finished_signal.connect(lambda files: _on_export_finished(progress_dialog, files, export_dir))
        worker.error_signal.connect(lambda error: _on_export_error(progress_dialog, error))
        
        # 🔧 修复：改进线程生命周期管理
        worker_thread.started.connect(worker.start_export)
        
        # 完成信号处理
        def on_worker_finished():
            """工作完成时的清理函数"""
            try:
                if worker_thread.isRunning():
                    worker_thread.quit()
                    worker_thread.wait(5000)  # 等待最多5秒
                if worker_thread.isFinished():
                    worker.deleteLater()
                    worker_thread.deleteLater()
                    logger.info("✅ 导出线程已正确清理")
            except Exception as e:
                logger.error(f"清理导出线程时发生错误: {e}")
        
        worker.finished_signal.connect(on_worker_finished)
        worker.error_signal.connect(on_worker_finished)
        
        # 🔧 修复：对话框关闭时的线程清理
        def cleanup_on_dialog_close():
            """对话框关闭时清理线程"""
            try:
                if worker_thread and worker_thread.isRunning():
                    logger.info("⚠️ 用户关闭对话框，正在停止导出线程...")
                    worker_thread.quit()
                    if not worker_thread.wait(3000):  # 等待3秒
                        logger.warning("导出线程未能正常退出，强制终止")
                        worker_thread.terminate()
                        worker_thread.wait(1000)
                    worker.deleteLater()
                    worker_thread.deleteLater()
                    logger.info("✅ 导出线程已清理（用户取消）")
            except Exception as e:
                logger.error(f"清理导出线程时发生错误: {e}")
        
        # 重写对话框的closeEvent
        original_close_event = progress_dialog.closeEvent
        def custom_close_event(event):
            cleanup_on_dialog_close()
            original_close_event(event)
        progress_dialog.closeEvent = custom_close_event
        
        # 启动线程
        worker_thread.start()
        
        # 🔧 修复：确保对话框正确显示，避免立即销毁
        progress_dialog.exec_()  # 使用模态对话框，确保不会被意外销毁
        
    except Exception as e:
        logger.error(f"按分类导出过程中发生错误: {e}")
        # 🔧 修复：获取主窗口作为父窗口，避免使用None导致程序退出
        from PySide6.QtWidgets import QApplication
        main_window = QApplication.activeWindow()
        QMessageBox.critical(main_window, "导出错误", f"导出过程中发生错误: {str(e)}")


def _on_export_finished(progress_dialog, exported_files, export_dir):
    """导出完成回调函数"""
    progress_dialog.set_finished(exported_files)

    # 🔧 修复：显示导出结果，使用progress_dialog作为父窗口，避免程序退出
    if exported_files:
        files_info = "\n".join(exported_files)
        QMessageBox.information(
            progress_dialog,  # 🔧 修复：使用progress_dialog作为父窗口，而不是None
            "导出成功",
            f"按分类导出完成！\n\n"
            f"📊 导出统计：\n"
            f"• 导出文件数量：{len(exported_files)} 个\n"
            f"• 导出目录：{export_dir}\n\n"
            f"📁 导出文件列表：\n{files_info}"
        )
        logger.info(f"按分类导出完成，共导出 {len(exported_files)} 个文件")
    else:
        QMessageBox.warning(progress_dialog, "导出完成", "没有找到可导出的数据")  # 🔧 修复：使用progress_dialog作为父窗口
        logger.info("按分类导出完成，但没有数据可导出")


def _on_export_error(progress_dialog, error_message):
    """导出错误回调函数"""
    progress_dialog.set_error(error_message)
    # 🔧 修复：使用progress_dialog作为父窗口，避免程序退出
    QMessageBox.critical(progress_dialog, "导出错误", f"导出过程中发生错误: {error_message}")


def _format_dataframe_for_export(df, table_name):
    """
    为导出格式化DataFrame数据
    
    功能说明：
    - 处理特殊的数据类型和格式
    - 确保Excel导出的兼容性
    - 优化数据显示效果
    - 空值处理：不显示None，显示为空字符串
    """
    try:
        # 处理时间字段
        for col in df.columns:
            if '时间' in col or '日期' in col:
                try:
                    df[col] = pd.to_datetime(df[col], errors='ignore')
                    if df[col].dtype == 'datetime64[ns]':
                        df[col] = df[col].dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass
        
        # 处理金额字段
        amount_columns = ['金额', '余额', '资本', '价值', '收入', '支出']
        for col in df.columns:
            if any(keyword in col for keyword in amount_columns):
                try:
                    df[col] = pd.to_numeric(df[col], errors='ignore')
                except:
                    pass
        
        # 处理身份证号码等长数字字段，确保作为文本显示
        id_columns = ['身份证', '证件号', '账号', '卡号', '手机号', '电话']
        for col in df.columns:
            if any(keyword in col for keyword in id_columns):
                try:
                    df[col] = df[col].astype(str)
                    # 替换NaN值和None值
                    df[col] = df[col].replace(['nan', 'None', 'null'], '')
                except:
                    pass
        
        # 处理空值 - 不显示None，统一显示为空字符串
        df = df.fillna('')
        
        # 进一步处理可能的None字符串
        df = df.replace(['None', 'null', 'NULL', 'nan', 'NaN'], '')
        
        return df
        
    except Exception as e:
        logger.error(f"格式化DataFrame时发生错误: {e}")
        return df


class SimpleExportProgressDialog(QDialog):
    """
    简化的导出进度对话框

    功能说明：
    - 本文件的功能和实现逻辑：避免Qt绘图资源问题的简化进度对话框
    - 最小化UI更新，避免绘图冲突
    - 导出完成后立即关闭
    - 避免复杂的进度条和文本更新
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("导出进度")
        self.setFixedSize(400, 150)
        self.setModal(True)

        # 🔧 修复：初始化关闭标志
        self._is_closing = False

        layout = QVBoxLayout(self)

        # 简单的状态标签
        self.status_label = QLabel("正在导出数据，请稍候...")
        self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; padding: 20px;")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)

        # 简单的进度条（不频繁更新）
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)

        # 关闭按钮（初始禁用）
        self.close_button = QPushButton("请稍候...")
        self.close_button.setEnabled(False)
        self.close_button.clicked.connect(self.accept)
        layout.addWidget(self.close_button)

        # 标记对话框状态
        self._is_closing = False

    def update_status(self, status_text):
        """更新状态信息（简化版）"""
        try:
            if not self._is_closing and self.isVisible():
                self.status_label.setText(status_text)
        except Exception as e:
            logger.debug(f"更新状态时出错: {e}")

    def update_progress(self, progress):
        """更新进度（简化版）"""
        try:
            # 🔧 修复：检查对话框状态和组件有效性
            if (hasattr(self, '_is_closing') and self._is_closing):
                return

            if self.isVisible() and hasattr(self, 'progress_bar') and self.progress_bar:
                try:
                    self.progress_bar.setValue(progress)
                except RuntimeError:
                    # Qt对象已被删除，忽略此错误
                    return
        except Exception as e:
            logger.debug(f"更新进度时出错: {e}")

    def add_exported_file(self, filename, row_count):
        """添加导出文件信息（简化版）"""
        try:
            if not self._is_closing and self.isVisible():
                self.status_label.setText(f"已导出: {filename}")
        except Exception as e:
            logger.debug(f"更新文件信息时出错: {e}")

    def set_finished(self, exported_files):
        """设置完成状态（等待用户确认）"""
        try:
            logger.info(f"导出完成，共导出 {len(exported_files)} 个文件")

            # 🔧 修复：显示完成状态，等待用户确认
            self.status_label.setText("✅ 导出完成！")
            self.progress_bar.setValue(100)

            # 启用关闭按钮，等待用户点击
            self.close_button.setEnabled(True)
            self.close_button.setText("确认")
            self.close_button.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2ecc71;
                }
            """)

            logger.info("等待用户点击确认按钮...")

        except Exception as e:
            logger.error(f"设置完成状态时发生错误: {e}")
            # 如果设置完成状态失败，启用关闭按钮
            try:
                self.close_button.setEnabled(True)
                self.close_button.setText("关闭")
            except:
                pass

    def set_error(self, error_message):
        """设置错误状态（等待用户确认）"""
        try:
            logger.error(f"导出错误: {error_message}")

            # 🔧 修复：显示错误状态，等待用户确认
            self.status_label.setText("❌ 导出出错！")
            self.progress_bar.setValue(0)

            # 启用关闭按钮，等待用户点击
            self.close_button.setEnabled(True)
            self.close_button.setText("关闭")
            self.close_button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            logger.info("等待用户点击关闭按钮...")

        except Exception as e:
            logger.error(f"设置错误状态时发生错误: {e}")
            # 如果设置错误状态失败，启用关闭按钮
            try:
                self.close_button.setEnabled(True)
                self.close_button.setText("关闭")
            except:
                pass



    def closeEvent(self, event):
        """关闭事件处理（用户确认后清理）"""
        try:
            self._is_closing = True
            logger.info("用户确认关闭导出进度对话框，开始清理资源...")

            # 🔧 修复：立即停止所有定时器
            for child in self.findChildren(QTimer):
                if child.isActive():
                    child.stop()

            # 🔧 修复：清理UI组件，避免QPainter问题
            try:
                if hasattr(self, 'progress_bar'):
                    self.progress_bar.deleteLater()
                if hasattr(self, 'status_label'):
                    self.status_label.deleteLater()
                if hasattr(self, 'close_button'):
                    self.close_button.deleteLater()
            except Exception as ui_error:
                logger.debug(f"清理UI组件时出错: {ui_error}")

            # 🔧 修复：延迟清理资源，避免Qt绘图冲突
            QTimer.singleShot(50, self.cleanup_export_resources)

            # 🔧 修复：延迟调用父类关闭事件
            QTimer.singleShot(150, lambda: super(ExportProgressDialog, self).closeEvent(event))

        except Exception as e:
            logger.error(f"处理关闭事件时发生错误: {e}")
            event.accept()

    def cleanup_export_resources(self):
        """清理导出资源"""
        try:
            logger.info("开始清理导出线程和Qt资源...")

            # 🔧 修复：清理线程资源
            if hasattr(self, 'worker_thread') and self.worker_thread:
                try:
                    if self.worker_thread.isRunning():
                        self.worker_thread.quit()
                        self.worker_thread.wait(2000)  # 等待2秒
                    self.worker_thread.deleteLater()
                    self.worker_thread = None
                except Exception as thread_error:
                    logger.debug(f"清理线程时出错: {thread_error}")

            # 🔧 修复：清理worker引用
            if hasattr(self, 'worker'):
                try:
                    self.worker = None
                except:
                    pass

            # 🔧 修复：强制处理所有待处理的Qt事件
            from PySide6.QtWidgets import QApplication
            QApplication.processEvents()

            # 🔧 修复：强制垃圾回收，释放内存
            import gc
            gc.collect()

            logger.info("✅ 导出资源清理完成")
        except Exception as e:
            logger.error(f"清理导出资源时发生错误: {e}")

class CategoryExportProgressDialog(QDialog):
    """
    分类导出进度对话框
    
    功能说明：
    - 显示分类导出的进度信息
    - 提供用户友好的进度反馈
    - 实时显示导出的文件与进度
    - 支持取消操作
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("分类导出进度")
        self.setFixedSize(600, 400)
        self.setModal(True)

        # 🔧 修复：初始化关闭标志
        self._is_closing = False

        layout = QVBoxLayout(self)

        # 当前状态标签
        self.status_label = QLabel("准备开始导出...")
        self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.status_label)
        
        # 总体进度条
        self.overall_progress = QProgressBar()
        self.overall_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.overall_progress)
        
        # 进度信息标签
        self.progress_info_label = QLabel("正在初始化...")
        self.progress_info_label.setStyleSheet("color: #7f8c8d; font-size: 12px; margin-bottom: 10px;")
        layout.addWidget(self.progress_info_label)
        
        # 导出文件列表区域
        files_group_label = QLabel("已导出文件:")
        files_group_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #34495e;")
        layout.addWidget(files_group_label)
        
        # 文件列表文本框
        self.files_text = QTextEdit()
        self.files_text.setReadOnly(True)
        self.files_text.setMaximumHeight(200)
        self.files_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #f8f9fa;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                padding: 5px;
            }
        """)
        layout.addWidget(self.files_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        self.close_button = QPushButton("关闭")
        self.close_button.setEnabled(False)
        self.close_button.clicked.connect(self.accept)
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)
        layout.addLayout(button_layout)
        
        # 导出文件计数器
        self.exported_count = 0
        
        # 🔧 修复：添加线程管理变量
        self.worker_thread = None
        self.worker = None
    
    def update_status(self, status_text):
        """更新状态信息"""
        try:
            # 🔧 修复：检查对话框是否仍然可见且未在关闭过程中
            if (hasattr(self, '_is_closing') and self._is_closing):
                return  # 如果正在关闭，直接返回

            if self.isVisible() and not self.isHidden():
                # 🔧 修复：安全地更新状态标签
                if hasattr(self, 'status_label') and self.status_label:
                    try:
                        self.status_label.setText(status_text)
                    except RuntimeError:
                        # Qt对象已被删除，忽略此错误
                        return
                # 🔧 修复：减少processEvents调用，避免绘图问题
                # QApplication.processEvents()
        except Exception as e:
            logger.debug(f"更新状态信息时发生错误: {e}")

    def update_progress(self, progress):
        """更新总体进度"""
        try:
            # 🔧 修复：检查对话框状态和组件有效性
            if (hasattr(self, '_is_closing') and self._is_closing):
                return

            if self.isVisible() and not self.isHidden():
                # 🔧 修复：安全地更新进度条
                if hasattr(self, 'overall_progress') and self.overall_progress:
                    try:
                        self.overall_progress.setValue(progress)
                    except RuntimeError:
                        return

                # 🔧 修复：安全地更新进度信息标签
                if hasattr(self, 'progress_info_label') and self.progress_info_label:
                    try:
                        self.progress_info_label.setText(f"总体进度: {progress}% | 已导出文件: {self.exported_count} 个")
                    except RuntimeError:
                        return

                # 🔧 修复：减少processEvents调用，避免绘图问题
                # QApplication.processEvents()
        except Exception as e:
            logger.debug(f"更新进度信息时发生错误: {e}")

    def add_exported_file(self, filename, row_count):
        """添加已导出的文件信息"""
        try:
            # 🔧 修复：检查对话框是否仍然可见，避免在关闭后更新UI
            if not self.isVisible() or self.isHidden():
                return

            self.exported_count += 1

            # 添加到文件列表
            file_info = f"✅ {filename} ({row_count:,} 行数据)"
            current_text = self.files_text.toPlainText()
            if current_text:
                new_text = current_text + "\n" + file_info
            else:
                new_text = file_info

            self.files_text.setPlainText(new_text)

            # 滚动到底部
            cursor = self.files_text.textCursor()
            cursor.movePosition(cursor.MoveOperation.End)
            self.files_text.setTextCursor(cursor)

            # 更新进度信息
            self.progress_info_label.setText(f"总体进度: {self.overall_progress.value()}% | 已导出文件: {self.exported_count} 个")
            # 🔧 修复：减少processEvents调用，避免绘图问题
            # QApplication.processEvents()
        except Exception as e:
            logger.debug(f"添加导出文件信息时发生错误: {e}")
    
    def set_finished(self, exported_files):
        """设置导出完成状态（等待用户确认）"""
        try:
            logger.info(f"导出完成，共导出 {len(exported_files)} 个文件")

            # 🔧 修复：显示完成状态，等待用户确认
            self.status_label.setText("✅ 导出完成！")
            self.overall_progress.setValue(100)

            # 启用关闭按钮，等待用户点击
            self.close_button.setEnabled(True)
            self.close_button.setText("确认")
            self.close_button.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2ecc71;
                }
            """)

            logger.info("等待用户点击确认按钮...")

        except Exception as e:
            logger.error(f"设置完成状态时发生错误: {e}")
            # 如果设置完成状态失败，启用关闭按钮
            try:
                self.close_button.setEnabled(True)
                self.close_button.setText("关闭")
            except:
                pass
    
    def set_error(self, error_message):
        """设置错误状态"""
        try:
            self.status_label.setText("❌ 导出出错！")
            self.progress_info_label.setText(f"错误: {error_message}")
            self.close_button.setEnabled(True)
            self.close_button.setText("关闭")
            self.close_button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            QApplication.processEvents()  # 强制更新UI
        except Exception as e:
            logger.error(f"设置错误状态时发生错误: {e}")
    
    def closeEvent(self, event):
        """🔧 修复：重写关闭事件，确保线程正确清理和Qt资源释放"""
        try:
            logger.info("进度对话框正在关闭，清理资源...")

            # 🔧 修复：设置关闭标志，防止后续UI更新
            self._is_closing = True

            # 🔧 修复：停止所有定时器，避免后续更新
            for child in self.findChildren(QTimer):
                if child.isActive():
                    child.stop()

            # 🔧 修复：清理可能的绘图资源
            if hasattr(self, 'files_text'):
                try:
                    self.files_text.clear()
                    self.files_text.deleteLater()
                except:
                    pass

            # 🔧 修复：清理进度条资源
            if hasattr(self, 'overall_progress'):
                try:
                    self.overall_progress.deleteLater()
                except:
                    pass

            # 🔧 修复：断开所有信号连接，避免资源泄漏
            try:
                if hasattr(self, 'worker') and self.worker:
                    # 安全地断开信号连接
                    try:
                        self.worker.progress_signal.disconnect()
                    except:
                        pass
                    try:
                        self.worker.status_signal.disconnect()
                    except:
                        pass
                    try:
                        self.worker.file_exported_signal.disconnect()
                    except:
                        pass
                    try:
                        self.worker.finished_signal.disconnect()
                    except:
                        pass
                    try:
                        self.worker.error_signal.disconnect()
                    except:
                        pass

                    # 清理worker引用
                    self.worker = None

                # 🔧 修复：清理线程资源
                if hasattr(self, 'worker_thread') and self.worker_thread:
                    try:
                        if self.worker_thread.isRunning():
                            self.worker_thread.quit()
                            self.worker_thread.wait(3000)  # 等待3秒
                        self.worker_thread.deleteLater()
                        self.worker_thread = None
                    except Exception as thread_error:
                        logger.debug(f"清理线程时出错: {thread_error}")

            except Exception as disconnect_error:
                logger.debug(f"断开信号连接时出错: {disconnect_error}")

            # 🔧 修复：延迟处理关闭事件，确保所有资源清理完成
            QTimer.singleShot(100, lambda: self._finalize_close(event))

        except Exception as e:
            logger.error(f"处理对话框关闭事件时发生错误: {e}")
            event.accept()  # 确保对话框能正常关闭

    def _finalize_close(self, event):
        """最终关闭处理"""
        try:
            # 🔧 修复：确保所有Qt绘图操作完成
            QApplication.processEvents()

            # 调用父类的关闭事件
            super().closeEvent(event)

            logger.info("✅ 进度对话框已正确关闭")

        except Exception as e:
            logger.error(f"最终关闭处理时发生错误: {e}")
            try:
                event.accept()
            except:
                pass

class CategoryExportWorker(QObject):
    """
    分类导出后台工作线程
    
    功能说明：
    - 在后台线程中执行分类导出操作
    - 实时发送进度信号更新UI
    - 避免UI卡死问题
    """
    # 信号定义
    progress_signal = Signal(int)  # 总体进度 (0-100)
    status_signal = Signal(str)  # 状态信息
    file_exported_signal = Signal(str, int)  # 导出的文件名和行数
    finished_signal = Signal(list)  # 完成信号，传递导出文件列表
    error_signal = Signal(str)  # 错误信号
    
    def __init__(self, case_id, case_name, export_dir):
        super().__init__()
        self.case_id = case_id
        self.case_name = case_name
        self.export_dir = export_dir
        self.exported_files = []
        self.selected_tables = None  # 用户选择的表
        
        # 表分类映射配置
        self.table_category_mapping = {
            # 医保信息类别
            "医保信息": {
                "医保_参保信息": "参保信息",
                "医保_普通门诊": "普通门诊", 
                "医保_药店购药": "药店购药",
                "医保_药店购药明细": "药店购药明细",
                "医保_住院结算数据": "住院结算数据"
            },
            
            # 通讯信息类别
            "通讯信息": {
                "电话_登记信息_运营商登记信息表": "运营商登记信息",
                "电话_话单信息_运营商话单信息表": "运营商话单信息",
                "虚拟运营商_登记信息_虚拟运营商登记信息表": "虚拟运营商登记信息"
            },
            
            # 公安信息类别
            "公安信息": {
                "公安部_出国_境_证件_出入境证件信息": "出入境证件信息",
                "公安部_出入境记录_出入境记录信息表": "出入境记录信息",
                "公安部_户籍人口_基本人员信息表": "基本人员信息",
                "公安部_机动车_机动车信息": "机动车信息",
                "公安部_驾驶证_驾驶证信息表": "驾驶证信息",
                "公安部_交通违法_机动车违章信息表": "机动车违章信息",
                "公安部_旅馆住宿_旅馆住宿人员信息表": "旅馆住宿人员信息",
                "公安部_同车违章_同车违章表": "同车违章",
                "公安部_同户人_同户人表": "同户人",
                "公安部_同住址_同住址表": "同住址",
                "公安部_在逃撤销_在逃人员撤销信息": "在逃人员撤销信息",
                "公安部_在逃人员_在逃人员登记信息": "在逃人员登记信息",
                "公安部_在逃同案撤销人员_在逃同案撤销人员": "在逃同案撤销人员"
            },
            
            # 账户信息类别
            "账户信息": {
                "账户信息_关联子账户信息表本地": "关联子账户信息",
                "开户信息表": "账号信息",
                "本地银行_客户信息本地表": "账户信息（本地）",
                "账户信息（本地）_优先权信息表": "优先权信息",
                "账户信息_共有权优先权信息表": "共有权、优先权信息",
                "账户信息_关联子账户信息表": "关联子账户信息",
                "账户信息_客户基本信息表": "客户基本信息",
                "账户信息_强制措施信息表": "强制措施信息"
            },
            
            # 税务纳税信息类别
            "税务纳税信息": {
                "国家税务总局_纳税人登记信息_登记信息表": "登记信息",
                "国家税务总局_纳税信息_税务缴纳信息表": "税务缴纳信息"
            },
            
            # 增值税发票信息类别
            "增值税发票信息": {
                "税务_增值税发票_普票货物或应税劳务服务名": "普票货物或应税劳务、服务名称",
                "税务_增值税发票_增值税普通发票表": "增值税普通发票",
                "税务_增值税发票_增值税专用发票表": "增值税专用发票",
                "税务_增值税发票_专票货物或应税劳务名称表": "专票货物或应税劳务名称"
            },
            
            # 理财信息类别
            "理财信息": {
                "金融理财_金融理财信息表": "金融理财信息",
                "金融理财_金融理财账户信息表": "金融理财账户信息",
                "理财登记中心_理财产品_持有信息表": "持有信息",
                "理财登记中心_理财产品_理财产品信息表": "理财产品信息",
                "理财登记中心_理财产品_投资行业信息表": "投资行业信息"
            },
            
            # 工商信息类别
            "工商信息": {
                "市监_企业登记_企业公示_变更备案信息表": "变更备案信息",
                "市监_企业登记_企业公示_财务负责人信息表": "财务负责人信息",
                "市监_企业登记_企业公示_吊销信息表": "吊销信息",
                "市监_企业登记_企业公示_非自然人出资信息表": "非自然人出资信息",
                "市监_企业登记_企业公示_分支机构备案信息表": "分支机构备案信息",
                "市监_企业登记_企业公示_联络员信息表": "联络员信息",
                "市监_企业登记_企业公示_内资补充信息表": "内资补充信息",
                "市监_企业登记_企业公示_农专补充信息表": "农专补充信息",
                "市监_企业登记_企业公示_许可信息表": "企业公示_许可信息",
                "市监_企业登记_企业基本信息表": "企业基本信息",
                "市监_企业登记_企业公示_清算成员信息表": "清算成员信息",
                "市监_企业登记_企业公示_清算基本信息表": "清算基本信息",
                "市监_企业登记_企业公示_外资补充信息表": "外资补充信息",
                "市监_企业登记_企业公示_主要人员表": "主要人员",
                "市监_企业登记_企业公示_注销信息表": "注销信息",
                "市监_企业登记_企业公示_自然人出资信息表": "自然人出资信息",
                "市监_统一社会信用代码_统一社会信用代码表": "统一社会信用代码"
            },
            
            # 信托信息类别
            "信托信息": {
                "信托登记公司_产品信息表": "产品信息",
                "信托登记公司_登记信息_合同信息表": "合同信息",
                "信托登记公司_登记信息_受益权结构表": "受益权结构",
                "信托登记公司_委托人或受益人变动信息表": "委托人或受益人变动信息",
                "信托登记公司_信托产品_登记信息_受益权结构": "登记信息_受益权结构",
                "信托登记公司_信托产品_登记信息_合同信息": "登记信息_合同信息",
                "信托登记公司_信托产品_终止登记": "终止登记",
                "信托登记公司_信托产品_委托人信息": "委托人信息",
                "信托登记公司_终止登记表": "终止登记"
            },
            
            # 保险信息类别
            "保险信息": {
                "银保信_保险产品_保险保单信息表": "保险保单信息",
                "银保信_保险产品_保险赔案信息表": "保险赔案信息",
                "银保信_保险产品_保险人员信息表": "保险人员信息",
                "银保信_保险产品_航空延误保险表": "航空延误保险",
                "银保信_保险产品_家庭财产保险表": "家庭财产保险"
            },
            
            # 航空信息类别
            "航空信息": {
                "中国航空_航班进出港_航班进出港未成行表": "航班进出港(未成行)",
                "中国航空_航班进出港_航班进出港已成行表": "航班进出港(已成行)",
                "中国航空_航班同行人信息_同乘三次以上同行人": "同乘三次以上同行人",
                "中国航空_航班同行人信息_同订单同行人未成行": "同订单同行人(未成行)",
                "中国航空_航班同行人信息_同订单同行人已成行": "同订单同行人(已成行)"
            },
            
            # 铁路信息类别
            "铁路信息": {
                "中国铁路总公司_铁路客票_交易信息表": "交易信息",
                "中国铁路总公司_铁路客票_票面信息表": "票面信息",
                "中国铁路总公司_同订单同行人_同行人员客票": "同行人员客票信息",
                "中国铁路总公司_同订单同行人_同行人员信息表": "同行人员信息",
                "中国铁路总公司_用户注册_常用联系人信息表": "常用联系人信息",
                "中国铁路总公司_用户注册_互联网注册信息表": "互联网注册信息"
            },
            
            # 证券信息类别
            "证券信息": {
                "中国证券登记结算有限公司_证券持有变动_持": "持有信息",
                "中国证券登记结算有限公司_证券账户_证券账户": "证券账户"
            },
            
            # 不动产类别
            "不动产": {
                "不动产查询_不动产全国总库_查封登记表": "查封登记",
                "不动产查询_不动产全国总库_抵押权表": "抵押权",
                "不动产查询_不动产全国总库_房地产权表": "房地产权",
                "不动产查询_不动产全国总库_建设用地宅基地": "建设用地、宅基地使用权",
                "不动产查询_不动产全国总库_预告登记表": "预告登记"
            }
        }
        
        # 特殊处理的表（保持原有导出模式）
        self.special_tables = ["账户交易明细表", "财付通交易明细表"]

    def _build_export_map_from_rules(self, rule_df):
        """从规则文件构建导出映射"""
        logger.info(f"📊 规则文件包含 {len(rule_df)} 行，列名: {rule_df.columns.tolist()}")

        # 兼容不同表头，自动查找三列
        db_col, ws_col, file_col = None, None, None
        for c in rule_df.columns:
            if '数据库表' in c:
                db_col = c
                logger.info(f"找到数据库表列: {c}")
            if '工作表' in c:
                ws_col = c
                logger.info(f"找到工作表列: {c}")
            if '导出文件' in c:
                file_col = c
                logger.info(f"找到导出文件列: {c}")

        if not (db_col and ws_col and file_col):
            missing = []
            if not db_col: missing.append('数据库表相关列')
            if not ws_col: missing.append('工作表相关列')
            if not file_col: missing.append('导出文件相关列')
            raise Exception(f'规则表缺少必要列: {missing}，现有列: {rule_df.columns.tolist()}')

        # 构建 {导出文件名: {工作表名: [数据库表名, ...]}} 嵌套字典
        export_map = {}
        for _, row in rule_df.iterrows():
            try:
                db = str(row[db_col]).strip() if pd.notna(row[db_col]) else ""
                ws = str(row[ws_col]).strip() if pd.notna(row[ws_col]) else ""
                fn = str(row[file_col]).strip() if pd.notna(row[file_col]) else ""

                # 过滤无效值
                if not (db and ws and fn) or db == 'nan' or ws == 'nan' or fn == 'nan':
                    logger.debug(f"跳过无效行: 数据库表名={db}, 工作表名={ws}, 导出文件名={fn}")
                    continue

                export_map.setdefault(fn, {}).setdefault(ws, []).append(db)
                logger.debug(f"添加映射: {fn} -> {ws} -> {db}")
            except Exception as e:
                logger.error(f"处理规则行时出错: {e}, 行数据: {row}")
                continue

        return export_map

    def _build_export_map_from_builtin(self):
        """使用内置分类映射构建导出映射"""
        export_map = {}

        # 将内置的table_category_mapping转换为导出映射格式
        for category_name, tables_mapping in self.table_category_mapping.items():
            export_map[category_name] = {}
            for table_name, worksheet_name in tables_mapping.items():
                if worksheet_name not in export_map[category_name]:
                    export_map[category_name][worksheet_name] = []
                export_map[category_name][worksheet_name].append(table_name)

        logger.info(f"📋 使用内置分类映射，共{len(export_map)}个分类：{list(export_map.keys())}")
        return export_map
    
    def start_export(self):
        """
        按照分类导出数据：
        - 优先使用规则表文件，如果不存在则使用内置分类映射
        - 每个分类生成一个Excel文件，每个sheet为工作表名，合并所有相关表的数据
        - 字段为所有表的并集，缺失字段补空，数据顺序追加
        - 无数据sheet不导出
        - 保留特殊表单独导出逻辑
        """
        import pandas as pd
        try:
            logger.info(f"开始按分类导出案件 {self.case_id} 的数据，案件名称: {self.case_name}")

            # 1. 尝试读取规则表，如果失败则使用内置映射
            export_map = {}
            rule_path = '表类型匹配规则_导出文件名分类.xlsx'

            if os.path.exists(rule_path):
                logger.info(f"📖 读取规则文件: {rule_path}")
                try:
                    rule_df = pd.read_excel(rule_path)
                    export_map = self._build_export_map_from_rules(rule_df)
                    logger.info(f"✅ 成功从规则文件构建导出映射，共{len(export_map)}个分类")
                except Exception as e:
                    logger.warning(f"⚠️ 读取规则文件失败: {e}，将使用内置分类映射")
                    export_map = self._build_export_map_from_builtin()
            else:
                logger.info(f"📋 规则文件不存在，使用内置分类映射")
                export_map = self._build_export_map_from_builtin()
            if not export_map:
                logger.error("❌ 无法构建导出映射，终止导出")
                self.error_signal.emit("无法构建导出映射")
                return

            # 2. 创建数据库连接
            config = configparser.ConfigParser()
            config.read('db_config.ini')
            db_url = f"postgresql://{config['PostgreSQL']['user']}:{config['PostgreSQL']['password']}@" \
                    f"{config['PostgreSQL']['host']}:{config['PostgreSQL']['port']}/{config['PostgreSQL']['database']}"
            engine = create_engine(db_url, echo=False)
            os.makedirs(self.export_dir, exist_ok=True)

            logger.info(f"📋 成功构建导出映射，共{len(export_map)}个导出文件：{list(export_map.keys())}")
            
            # 3. 导出主循环
            total_files = len(export_map)
            if total_files == 0:
                logger.warning("⚠️ 没有找到任何导出映射，请检查规则文件内容")
                self.error_signal.emit("没有找到任何导出映射，请检查表类型匹配规则_导出文件名分类.xlsx文件内容")
                return
                
            logger.info(f"🚀 开始导出 {total_files} 个文件...")
            file_idx = 0

            # 🔧 修复：首先处理特殊表（账户交易明细表、财付通交易明细表）
            special_tables_to_export = []
            if self.selected_tables:
                # 检查用户选择的表中是否包含特殊表
                for table_name in self.special_tables:
                    if table_name in self.selected_tables:
                        special_tables_to_export.append(table_name)
            else:
                # 如果没有选择表，检查所有特殊表
                special_tables_to_export = self.special_tables

            # 导出特殊表
            for table_name in special_tables_to_export:
                try:
                    logger.info(f"🔧 开始处理特殊表: {table_name}")
                    self.status_signal.emit(f"正在导出特殊表 {table_name} ...")
                    self._export_special_table(engine, table_name)
                except Exception as e:
                    logger.error(f"导出特殊表 {table_name} 时发生错误: {e}")
                    # 继续处理其他表，不中断整个导出过程

            for file_name, ws_dict in export_map.items():
                file_idx += 1
                logger.info(f"📄 开始处理文件 {file_idx}/{total_files}: {file_name}")
                self.status_signal.emit(f"正在导出 {file_name} ({file_idx}/{total_files}) ...")

                # 🔧 修复：改进文件命名，添加系统当前时间
                from datetime import datetime
                current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                safe_file_name = re.sub(r'[<>:"/\\|?*]', '_', file_name)
                file_path = os.path.join(self.export_dir, f"{self.case_name}_{safe_file_name}_{current_time}.xlsx")
                logger.info(f"📁 导出路径: {file_path}")

                sheet_data = {}  # {工作表名: DataFrame}
                
                with engine.connect() as conn:
                    for ws_name, db_list in ws_dict.items():
                        dfs = []
                        all_columns = set()
                        
                        # 依次读取所有数据库表，合并字段
                        for db_table in db_list:
                            # 🔧 修复：检查表是否在用户选择的表中
                            if self.selected_tables and db_table not in self.selected_tables:
                                logger.debug(f"表 {db_table} 未被用户选择，跳过")
                                continue

                            # 检查表是否存在
                            exists_sql = """
                                SELECT EXISTS (
                                    SELECT FROM information_schema.tables
                                    WHERE table_name = %s AND table_schema = 'public'
                                )
                            """
                            exists = pd.read_sql_query(exists_sql, conn, params=(db_table,)).iloc[0,0]
                            if not exists:
                                logger.debug(f"表 {db_table} 不存在，跳过")
                                continue

                            # 🔧 修复：获取表的原始字段顺序
                            columns_sql = """
                                SELECT column_name
                                FROM information_schema.columns
                                WHERE table_name = %s AND table_schema = 'public'
                                ORDER BY ordinal_position
                            """
                            columns_df = pd.read_sql_query(columns_sql, conn, params=(db_table,))
                            original_columns = columns_df['column_name'].tolist()

                            # 查询数据，保持字段顺序
                            exclude_cols = ['id','导入时间','导入批次','源文件位置']
                            select_columns = [col for col in original_columns if col not in exclude_cols]

                            if not select_columns:
                                logger.warning(f"表 {db_table} 没有可导出的字段")
                                continue

                            # 构建SQL查询，保持字段顺序
                            columns_str = ', '.join([f'"{col}"' for col in select_columns])
                            sql = f'SELECT {columns_str} FROM "{db_table}" WHERE "案件编号" = %s'

                            try:
                                df = pd.read_sql_query(sql, conn, params=(self.case_id,))
                            except Exception as e:
                                logger.error(f"读取表 {db_table} 出错: {e}")
                                continue

                            if df.empty:
                                logger.debug(f"表 {db_table} 无数据，跳过")
                                continue

                            dfs.append(df)
                            # 🔧 修复：保持字段顺序
                            for col in df.columns:
                                if col not in all_columns:
                                    all_columns.add(col)
                        
                        if not dfs:
                            continue  # 无数据不导出该sheet

                        # 🔧 修复：保持字段顺序，优先使用第一个表的字段顺序
                        if dfs:
                            # 以第一个DataFrame的字段顺序为基准
                            base_columns = list(dfs[0].columns)
                            # 添加其他表中的额外字段
                            for df in dfs[1:]:
                                for col in df.columns:
                                    if col not in base_columns:
                                        base_columns.append(col)

                            # 按照确定的字段顺序重新排列所有DataFrame
                            dfs_padded = [df.reindex(columns=base_columns) for df in dfs]
                            merged_df = pd.concat(dfs_padded, ignore_index=True)
                            sheet_data[ws_name] = merged_df
                            logger.info(f"工作表 {ws_name} 合并{len(dfs)}个表, 共{len(merged_df)}行, 字段数{len(base_columns)}")
                        else:
                            logger.debug(f"工作表 {ws_name} 无数据")
                
                # 写入Excel
                if not sheet_data:
                    logger.info(f"文件 {file_name} 无数据，跳过导出")
                    continue
                
                # 🔧 修复：改进Excel格式，添加筛选和优化列宽
                try:
                    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                        for ws_name, df in sheet_data.items():
                            try:
                                safe_ws = re.sub(r'[<>:"/\\|?*\[\]]', '_', ws_name)
                                if len(safe_ws) > 31:
                                    safe_ws = safe_ws[:28] + '...'
                                df.to_excel(writer, sheet_name=safe_ws, index=False)

                                # 获取工作表对象
                                worksheet = writer.sheets[safe_ws]

                                # 🔧 修复：添加筛选功能
                                if len(df) > 0:
                                    try:
                                        # 计算最大列和行
                                        max_row = len(df) + 1  # +1 for header
                                        max_col = len(df.columns)

                                        # 将列索引转换为Excel列字母
                                        def get_column_letter(col_idx):
                                            """将列索引转换为Excel列字母"""
                                            result = ""
                                            while col_idx > 0:
                                                col_idx -= 1
                                                result = chr(col_idx % 26 + ord('A')) + result
                                                col_idx //= 26
                                            return result

                                        max_col_letter = get_column_letter(max_col)

                                        # 为整个数据区域添加筛选
                                        worksheet.auto_filter.ref = f"A1:{max_col_letter}{max_row}"
                                        logger.debug(f"为工作表 {safe_ws} 添加筛选，范围: A1:{max_col_letter}{max_row}")
                                    except Exception as filter_error:
                                        logger.warning(f"为工作表 {safe_ws} 添加筛选时出错: {filter_error}")

                                # 🔧 修复：智能调整列宽，与字段值匹配
                                try:
                                    for column in worksheet.columns:
                                        max_length = 0
                                        column_letter = column[0].column_letter
                                        column_name = column[0].value  # 获取列名

                                        # 计算列名长度
                                        if column_name:
                                            max_length = len(str(column_name))

                                        # 计算数据值的最大长度（检查前100行以提高性能）
                                        for i, cell in enumerate(column[1:], 1):  # 跳过标题行
                                            if i > 100:  # 只检查前100行数据
                                                break
                                            try:
                                                if cell.value is not None:
                                                    cell_length = len(str(cell.value))
                                                    if cell_length > max_length:
                                                        max_length = cell_length
                                            except:
                                                pass

                                        # 设置合理的列宽（最小8，最大50）
                                        adjusted_width = max(8, min(max_length + 2, 50))
                                        worksheet.column_dimensions[column_letter].width = adjusted_width

                                        logger.debug(f"列 {column_letter}({column_name}) 设置宽度: {adjusted_width}")
                                except Exception as width_error:
                                    logger.warning(f"为工作表 {safe_ws} 调整列宽时出错: {width_error}")

                                # 🔧 修复：冻结首行（标题行）
                                try:
                                    worksheet.freeze_panes = "A2"
                                    logger.debug(f"为工作表 {safe_ws} 冻结首行")
                                except Exception as freeze_error:
                                    logger.warning(f"为工作表 {safe_ws} 冻结首行时出错: {freeze_error}")

                            except Exception as ws_error:
                                logger.error(f"处理工作表 {ws_name} 时出错: {ws_error}")
                                # 继续处理其他工作表
                                continue

                except Exception as excel_error:
                    logger.error(f"创建Excel文件 {file_path} 时出错: {excel_error}")
                    # 发送错误信号但继续处理其他文件
                    self.status_signal.emit(f"⚠️ 文件 {file_name} 导出失败: {str(excel_error)}")
                    continue
                
                total_rows = sum(len(df) for df in sheet_data.values())
                self.exported_files.append(f"{file_name}.xlsx ({len(sheet_data)}个工作表, {total_rows:,}条记录)")
                self.file_exported_signal.emit(f"{file_name}.xlsx", total_rows)
                logger.info(f"✅ 文件 {file_name} 导出完成: {file_path}")
                
                progress = int((file_idx / total_files) * 100)
                self.progress_signal.emit(progress)
            
            # 完成导出
            self.finished_signal.emit(self.exported_files)
            
        except Exception as e:
            logger.error(f"按规则表导出过程中发生错误: {e}")
            self.error_signal.emit(f"导出过程中发生错误: {str(e)}")
    
    def _export_special_table(self, engine, table_name):
        """
        导出特殊表（账户交易明细表、财付通交易明细表）

        功能说明：
        - 本文件的功能和实现逻辑：处理大数据量表的特殊导出逻辑
        - 使用传统的PartitionedExportWorker进行导出
        - 支持分组导出和单文件导出
        - 完善的异常处理，避免程序崩溃
        """
        try:
            logger.info(f"🔧 开始处理特殊表: {table_name}")

            with engine.connect() as conn:
                # 检查表是否存在
                table_exists_query = """
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = %s AND table_schema = 'public'
                    )
                """
                exists_result = pd.read_sql_query(table_exists_query, conn, params=(table_name,))
                table_exists = exists_result.iloc[0, 0]

                if not table_exists:
                    logger.info(f"特殊表 {table_name} 不存在，跳过")
                    return

                # 查询数据量
                count_query = f'SELECT COUNT(*) FROM "{table_name}" WHERE "案件编号" = %s'
                count_result = pd.read_sql_query(count_query, conn, params=(self.case_id,))
                record_count = count_result.iloc[0, 0]

                if record_count == 0:
                    logger.info(f"特殊表 {table_name} 无数据，跳过")
                    return

                logger.info(f"特殊表 {table_name} 有 {record_count:,} 条记录，使用传统导出模式")

                # 🔧 修复：使用更安全的导出方式
                try:
                    # 对于特殊表，使用传统导出模式
                    temp_worker = PartitionedExportWorker(self.case_id, self.case_name, self.export_dir)
                    temp_worker.selected_tables = {table_name: record_count}

                    # 导出该表
                    if record_count > 1000000:  # 超过100万条记录使用分组导出
                        logger.info(f"表 {table_name} 数据量超过100万，使用分组导出")
                        temp_worker._export_partitioned_table(table_name, record_count)
                    else:
                        logger.info(f"表 {table_name} 数据量较小，使用单文件导出")
                        temp_worker._export_single_file(table_name)

                    # 🔧 修复：更新文件名显示，包含时间戳
                    from datetime import datetime
                    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                    display_filename = f"{self.case_name}_{table_name}_{current_time}.xlsx"
                    self.exported_files.append(f"{display_filename} ({record_count:,} 行数据)")
                    self.file_exported_signal.emit(display_filename, record_count)
                    logger.info(f"✅ 成功导出特殊表: {table_name}")

                except Exception as export_error:
                    logger.error(f"❌ 导出特殊表 {table_name} 时发生错误: {export_error}")
                    # 发送错误信号但不中断整个导出过程
                    self.status_signal.emit(f"⚠️ 特殊表 {table_name} 导出失败: {str(export_error)}")

        except Exception as e:
            logger.error(f"❌ 处理特殊表 {table_name} 时发生严重错误: {e}")
            # 发送错误信号但不中断整个导出过程
            self.status_signal.emit(f"⚠️ 特殊表 {table_name} 处理失败: {str(e)}")
    
    def _export_category(self, engine, category_name, tables_mapping):
        """
        按分类导出数据，同类表合并到一个文件中

        功能改进：
        - 将同类表合并到一个Excel文件中
        - 每个表作为独立的工作表
        - 优化工作表命名，使用友好的中文名称
        - 排除源文件位置等技术字段
        """
        try:
            # 🔧 修复：添加系统当前时间到文件名
            from datetime import datetime
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = os.path.join(self.export_dir, f"{self.case_name}_{category_name}_{current_time}.xlsx")
            logger.info(f"开始导出分类: {category_name} 到文件: {file_path}")

            # 使用ExcelWriter创建多工作表文件
            with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                total_records = 0
                exported_sheets = 0

                # 遍历该分类下的所有工作表
                for sheet_name, db_tables in tables_mapping.items():
                    logger.info(f"处理工作表: {sheet_name}, 包含数据库表: {db_tables}")

                    # 合并多个数据库表的数据
                    combined_df = pd.DataFrame()

                    for db_table in db_tables:
                        try:
                            # 查询数据
                            sql = f'SELECT * FROM "{db_table}" WHERE "案件编号" = %s'
                            df = pd.read_sql_query(sql, engine, params=(self.case_id,))

                            if not df.empty:
                                logger.info(f"从表 {db_table} 读取到 {len(df)} 条记录")

                                # 如果是第一个表，直接使用
                                if combined_df.empty:
                                    combined_df = df.copy()
                                else:
                                    # 合并数据，使用外连接保留所有列
                                    combined_df = pd.concat([combined_df, df], ignore_index=True, sort=False)

                        except Exception as e:
                            logger.error(f"读取表 {db_table} 出错: {e}")
                            continue

                    # 如果有数据，导出到工作表
                    if not combined_df.empty:
                        # 清理工作表名称，确保符合Excel规范
                        safe_sheet_name = self._clean_sheet_name(sheet_name)

                        # 排除技术字段
                        excluded_columns = ['源文件位置', 'id', 'created_at', 'updated_at']
                        for col in excluded_columns:
                            if col in combined_df.columns:
                                combined_df = combined_df.drop(columns=[col])

                        # 导出到Excel工作表
                        combined_df.to_excel(writer, sheet_name=safe_sheet_name, index=False)

                        # 格式化Excel工作表
                        self._format_excel_sheet(writer, combined_df, safe_sheet_name)

                        total_records += len(combined_df)
                        exported_sheets += 1

                        logger.info(f"成功导出工作表 '{safe_sheet_name}': {len(combined_df)} 条记录")
                    else:
                        logger.warning(f"工作表 '{sheet_name}' 没有数据，跳过导出")

            if exported_sheets > 0:
                self.exported_files.append(f"{category_name}.xlsx ({exported_sheets} 个工作表, {total_records:,} 行数据)")
                self.file_exported_signal.emit(f"{category_name}.xlsx", total_records)
                logger.info(f"成功导出分类文件: {category_name}.xlsx, {exported_sheets} 个工作表, {total_records:,} 条记录")
            else:
                logger.warning(f"分类 '{category_name}' 没有数据，未生成文件")

        except Exception as e:
            logger.error(f"导出分类 {category_name} 时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def _clean_sheet_name(self, name):
        """清理工作表名称，确保符合Excel规范"""
        # Excel工作表名称限制：不能超过31个字符，不能包含特殊字符
        invalid_chars = ['\\', '/', '*', '?', ':', '[', ']']
        cleaned_name = str(name)

        for char in invalid_chars:
            cleaned_name = cleaned_name.replace(char, '_')

        # 限制长度
        if len(cleaned_name) > 31:
            cleaned_name = cleaned_name[:31]

        return cleaned_name

    def _format_excel_sheet(self, writer, df, sheet_name):
        """格式化Excel工作表"""
        try:
            workbook = writer.book
            worksheet = writer.sheets[sheet_name]

            # 设置列宽
            for i, col in enumerate(df.columns):
                # 计算列宽：取列名长度和数据最大长度的较大值
                max_len = max(
                    len(str(col)),
                    df[col].astype(str).str.len().max() if not df.empty else 0
                )
                # 限制最大宽度
                col_width = min(max_len + 2, 50)
                worksheet.set_column(i, i, col_width)

            # 设置表头格式
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })

            # 应用表头格式
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)

        except Exception as e:
            logger.warning(f"格式化工作表 {sheet_name} 时出错: {e}")