
def get_common_columns_with_case_mapping(temp_columns, main_columns):
    """
    获取共同字段，支持大小写映射
    """
    common_columns = []
    field_mapping = {}
    
    # 首先添加完全匹配的字段
    for col in temp_columns:
        if col in main_columns:
            common_columns.append(col)
            field_mapping[col] = col
    
    # 然后添加大小写不匹配但语义相同的字段
    case_mappings = {
        'IP地址': 'ip地址',
        'MAC地址': 'mac地址'
    }
    
    for temp_field, main_field in case_mappings.items():
        if temp_field in temp_columns and main_field in main_columns:
            if temp_field not in common_columns:  # 避免重复
                common_columns.append(temp_field)
                field_mapping[temp_field] = main_field
    
    return common_columns, field_mapping

def move_temp_data_to_main_table_fixed(case_id, max_retries=3, retry_delay=5):
    """
    修复版本的转存函数，支持字段大小写映射
    """
    import time
    import psycopg2
    
    current_retry = 0
    
    while current_retry < max_retries:
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 获取两个表的字段列表
            cursor.execute("""
                SELECT column_name FROM information_schema.columns 
                WHERE table_name = '临时账户交易明细表'
                ORDER BY ordinal_position
            """)
            temp_columns = [row[0] for row in cursor.fetchall()]
            
            cursor.execute("""
                SELECT column_name FROM information_schema.columns 
                WHERE table_name = '账户交易明细表'
                ORDER BY ordinal_position
            """)
            main_columns = [row[0] for row in cursor.fetchall()]
            
            # 使用修复的字段映射逻辑
            common_columns, field_mapping = get_common_columns_with_case_mapping(temp_columns, main_columns)
            
            if not common_columns:
                logging.error("没有找到共同字段")
                return
            
            logging.info(f"共同字段数量: {len(common_columns)}")
            logging.info(f"字段映射: {field_mapping}")
            
            # 构建SELECT和INSERT的字段列表
            select_fields = []
            insert_fields = []
            
            for temp_field in common_columns:
                main_field = field_mapping.get(temp_field, temp_field)
                select_fields.append(f'"{temp_field}"')
                insert_fields.append(f'"{main_field}"')
            
            select_str = ', '.join(select_fields)
            insert_str = ', '.join(insert_fields)
            
            # 执行转存
            transfer_sql = f"""
                INSERT INTO 账户交易明细表({insert_str})
                SELECT {select_str}
                FROM 临时账户交易明细表
                WHERE 案件编号 = %s
            """
            
            logging.info("执行转存SQL...")
            cursor.execute(transfer_sql, (case_id,))
            
            transferred_count = cursor.rowcount
            logging.info(f"成功转存 {transferred_count} 条记录")
            
            # 删除临时表数据
            cursor.execute("DELETE FROM 临时账户交易明细表 WHERE 案件编号 = %s", (case_id,))
            deleted_count = cursor.rowcount
            logging.info(f"删除临时表 {deleted_count} 条记录")
            
            conn.commit()
            cursor.close()
            conn.close()
            
            return transferred_count
            
        except Exception as e:
            current_retry += 1
            if current_retry >= max_retries:
                logging.error(f"转存失败，已达到最大重试次数: {e}")
                raise e
            else:
                wait_time = retry_delay * current_retry
                logging.warning(f"转存失败，{wait_time}秒后重试 ({current_retry}/{max_retries}): {e}")
                time.sleep(wait_time)
