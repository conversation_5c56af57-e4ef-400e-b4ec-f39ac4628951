from PySide6.QtWidgets import QFileDialog, QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, \
    QComboBox, QMainWindow, QTableView, QLineEdit, QWidget, QTabWidget, QProgressDialog, QApplication, QCheckBox, \
    QFormLayout, QTreeWidget, QTreeWidgetItem, QSplitter, QFrame, QGridLayout, QScrollArea
import pandas as pd
import os
import math
from datetime import datetime
from styles import BUTTON_STYLE
import pdfplumber
from PyPDF2 import PdfReader, PdfWriter
from docx import Document
import shutil
import threading
import sys
import logging
from PySide6.QtCore import Qt, QAbstractTableModel, QModelIndex, QEvent
from PySide6.QtGui import QColor, QBrush, QIcon
from openpyxl.styles import PatternFill

# 替换sqlite3导入为psycopg2
import psycopg2
import psycopg2.extras

# 导入database_setup模块
from database_setup import create_search_optimization_indexes, get_db_connection, DB_PARAMS

# 自定义事件类型
DB_SEARCH_COMPLETED_EVENT = QEvent.Type(QEvent.registerEventType())
DB_SEARCH_ERROR_EVENT = QEvent.Type(QEvent.registerEventType())

class DbSearchCompletedEvent(QEvent):
    def __init__(self, tabs_to_add):
        super().__init__(DB_SEARCH_COMPLETED_EVENT)
        self.tabs_to_add = tabs_to_add

class DbSearchErrorEvent(QEvent):
    def __init__(self, error_message):
        super().__init__(DB_SEARCH_ERROR_EVENT)
        self.error_message = error_message

class AnimatedProgressDialog(QWidget):
    """自定义美化的进度对话框"""
    def __init__(self, parent=None):
        super().__init__(parent, Qt.Dialog | Qt.FramelessWindowHint)
        self.setWindowModality(Qt.WindowModal)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 设置阴影效果
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 220);
                border-radius: 10px;
                border: 1px solid #c3ccdf;
            }
        """)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        self.title_label = QLabel("正在搜索")
        self.title_label.setStyleSheet("font-size: 16px; font-weight: bold; border: none;")
        self.title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.title_label)
        
        # 状态文本
        self.status_label = QLabel("正在搜索数据库...")
        self.status_label.setStyleSheet("font-size: 12px; color: #666666; border: none;")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 设置固定大小
        self.setFixedSize(300, 120)
        
        # 居中显示
        if parent:
            self.center_on_parent()
    
    def update_status(self, text):
        """更新状态文本"""
        self.status_label.setText(text)
        # 确保UI立即更新
        QApplication.processEvents()
    
    def center_on_parent(self):
        """居中显示在父窗口上"""
        if self.parentWidget():
            parent_geo = self.parentWidget().geometry()
            self.move(
                parent_geo.x() + (parent_geo.width() - self.width()) // 2,
                parent_geo.y() + (parent_geo.height() - self.height()) // 2
            )

class DataFrameModel(QAbstractTableModel):
    def __init__(self, df=pd.DataFrame(), keywords=None, parent=None):
        super().__init__(parent)
        self.df = df
        self.keywords = [k.lower() for k in keywords] if keywords else []  # 转换为小写

    def rowCount(self, parent=QModelIndex()):
        return len(self.df)

    def columnCount(self, parent=QModelIndex()):
        return len(self.df.columns)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return None
            
        # 显示数据
        if role == Qt.DisplayRole:
            # 跳过高亮信息列
            if self.df.columns[index.column()] == '_highlighted_cells':
                return None
                
            # 获取单元格值，并处理NaN值
            value = self.df.iloc[index.row(), index.column()]
            if pd.isna(value):
                return ""  # 对于NaN值，返回空字符串
            return str(value)
            
        # 设置背景颜色
        elif role == Qt.BackgroundRole:
            # 检查当前单元格是否需要高亮显示
            highlighted_cells = self.df.iloc[index.row()]['_highlighted_cells']
            if highlighted_cells is not None and index.column() in highlighted_cells:
                return QBrush(QColor(255, 0, 0, 100))  # 半透明红色
                
        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            # 跳过高亮信息列
            if self.df.columns[section] == '_highlighted_cells':
                return None
            return str(self.df.columns[section])
        elif orientation == Qt.Vertical and role == Qt.DisplayRole:
            return str(section + 1)
        return None

class DatabaseSearchWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # 不再使用本地SQLite文件路径，而是使用PostgreSQL连接参数
        self.db_params = DB_PARAMS
        self.setWindowTitle("数据库搜索")
        self.setWindowIcon(QIcon('gui/images/icons/icon.ico'))
        self.setStyleSheet("background-color: white; color: black;")
        self.showMaximized()
        self.current_page = 1
        self.page_size = 1000  # 每页显示1000条记录
        self.total_records = 0  # 当前表的总记录数
        self.total_pages = 1  # 总页数
        self.search_results = {}  # 按表名存储搜索结果
        self.current_table = None  # 当前显示的表名
        self.search_keywords = []  # 存储搜索关键词列表，用于高亮显示
        self.progress_dialog = None  # 进度对话框
        self.search_thread = None  # 搜索线程
        # 缓存表结构信息
        self.table_columns_cache = {}  # 缓存表列名
        self.text_columns_cache = {}   # 缓存表中的文本列
        # 初始化界面
        self.init_ui()
        # 预加载表结构信息
        self.preload_table_structure()

    def init_ui(self):
        main_layout = QVBoxLayout()
        
        # 创建搜索区域
        search_layout = QHBoxLayout()
        search_label = QLabel("请输入关键词:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键词搜索整个数据库（多个关键词请用空格分隔）...")
        self.search_input.returnPressed.connect(self.on_search_clicked)
        
        # 新增：搜索方式选项
        self.search_mode_combo = QComboBox()
        self.search_mode_combo.addItems(["精确搜索", "模糊搜索"])
        self.search_mode_combo.setCurrentIndex(0)  # 默认精确
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input, 1)
        search_layout.addWidget(QLabel("搜索方式:"))
        search_layout.addWidget(self.search_mode_combo)
        
        search_button = QPushButton("搜索")
        search_button.setStyleSheet(BUTTON_STYLE)
        search_button.clicked.connect(self.on_search_clicked)
        
        search_layout.addWidget(search_button)
        
        main_layout.addLayout(search_layout)
        
        # 状态信息
        self.status_label = QLabel("请输入关键词搜索整个数据库相关数据")
        main_layout.addWidget(self.status_label)
        
        # 导出按钮
        export_layout = QHBoxLayout()
        export_button = QPushButton("导出查询结果")
        export_button.setStyleSheet(BUTTON_STYLE)
        export_button.clicked.connect(self.export_data)
        export_layout.addStretch()
        export_layout.addWidget(export_button)
        
        main_layout.addLayout(export_layout)

        # 创建主要内容区域 - 左右分割界面
        main_content_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：数据表树形结构
        left_panel = QWidget()
        left_panel.setMaximumWidth(350)
        left_panel.setMinimumWidth(250)
        left_layout = QVBoxLayout(left_panel)
        
        # 左侧标题
        tables_title = QLabel("搜索结果表列表")
        tables_title.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        left_layout.addWidget(tables_title)
        
        # 数据表树形控件
        self.tables_tree = QTreeWidget()
        self.tables_tree.setHeaderLabels(["数据表", "匹配数量"])
        self.tables_tree.setColumnWidth(0, 180)
        self.tables_tree.setColumnWidth(1, 80)
        self.tables_tree.setStyleSheet("background-color: white; color: black; border: 1px solid #cccccc; border-radius: 5px;")
        self.tables_tree.itemClicked.connect(self.on_table_tree_clicked)
        left_layout.addWidget(self.tables_tree)
        
        # 右侧：数据展示区域
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # 右侧标题
        self.data_title = QLabel("请先搜索数据")
        self.data_title.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        right_layout.addWidget(self.data_title)

        # 创建单个数据表格视图（替代原来的标签页组件）
        self.table_view = QTableView()
        self.table_view.setSortingEnabled(True)
        self.table_view.setStyleSheet("background-color: white; color: black; border: 1px solid #cccccc; border-radius: 5px;")
        right_layout.addWidget(self.table_view)
        
        # 分页工具栏
        paging_layout = QHBoxLayout()
        
        prev_button = QPushButton("上一页")
        prev_button.setStyleSheet(BUTTON_STYLE)
        prev_button.clicked.connect(self.prev_page)
        
        self.paging_label = QLabel("共0页，当前第1页")
        
        next_button = QPushButton("下一页")
        next_button.setStyleSheet(BUTTON_STYLE)
        next_button.clicked.connect(self.next_page)
        
        paging_layout.addStretch()
        paging_layout.addWidget(prev_button)
        paging_layout.addWidget(self.paging_label)
        paging_layout.addWidget(next_button)
        paging_layout.addStretch()
        
        right_layout.addLayout(paging_layout)
        
        # 添加左右面板到分割器
        main_content_splitter.addWidget(left_panel)
        main_content_splitter.addWidget(right_panel)
        main_content_splitter.setSizes([300, 900])  # 设置左右面板的初始大小比例
        
        # 添加分割器到主布局
        main_layout.addWidget(main_content_splitter, 1)  # 占据最大空间

        # 设置主窗口
        container = QWidget()
        container.setLayout(main_layout)
        self.setCentralWidget(container)

    def on_search_clicked(self):
        keyword_text = self.search_input.text().strip()
        if not keyword_text:
            QMessageBox.warning(self, "搜索提示", "请输入关键词进行搜索")
            return
            
        # 将关键词文本拆分为多个关键词，并去除每个关键词前后的空白字符
        self.search_keywords = []
        for kw in keyword_text.split():
            cleaned_kw = kw.strip()
            if cleaned_kw:  # 确保处理后的关键词不为空
                self.search_keywords.append(cleaned_kw)
                
        if not self.search_keywords:
            QMessageBox.warning(self, "搜索提示", "请输入有效的关键词")
            return
        
        # 重置分页
        self.current_page = 1
        
        # 创建美化进度对话框
        self.progress_dialog = AnimatedProgressDialog(self)
        self.progress_dialog.show()
        self.progress_dialog.title_label.setText("正在搜索")
        self.progress_dialog.update_status("正在搜索数据库...")
        
        # 在单独的线程中执行搜索，避免界面冻结
        self.search_thread = threading.Thread(target=self.search_data, args=(self.search_keywords,))
        self.search_thread.daemon = True
        self.search_thread.start()
    
    def search_data(self, keywords):
        """根据关键词列表搜索数据库相关数据，并按表分组结果"""
        try:
            conn = get_db_connection()
            self.search_results = {}
            self.total_records = 0
            tabs_to_add = []
            search_mode = self.search_mode_combo.currentText()  # 获取搜索方式
            for table in self.table_columns_cache.keys():
                text_columns = self.text_columns_cache.get(table, [])
                if not text_columns:
                    continue
                query_parts = []
                params = []
                for keyword in keywords:
                    if search_mode == "精确搜索":
                        # 精确：等于
                        column_conditions = [f'"{column}" = %s' for column in text_columns]
                        params.extend([keyword] * len(text_columns))
                    else:
                        # 模糊：包含
                        column_conditions = [f'"{column}" ILIKE %s' for column in text_columns]
                        params.extend([f"%{keyword}%"] * len(text_columns))
                    if column_conditions:
                        query_parts.append(f"({' OR '.join(column_conditions)})")
                if query_parts:
                    query = f"""
                    SELECT DISTINCT * FROM "{table}" 
                    WHERE {' OR '.join(query_parts)}
                    """
                    try:
                        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
                        cursor.execute(query, params)
                        columns = [desc[0] for desc in cursor.description]
                        df = pd.DataFrame(cursor.fetchall(), columns=columns)
                        if not df.empty:
                            self.process_highlighting(df, keywords)
                            self.search_results[table] = df
                            self.total_records += len(df)
                            tabs_to_add.append((table, len(df)))
                    except Exception as e:
                        print(f"查询表 {table} 时出错: {str(e)}")
            conn.close()
            app = QApplication.instance()
            if app:
                app.postEvent(self, DbSearchCompletedEvent(tabs_to_add))
        except Exception as e:
            app = QApplication.instance()
            if app:
                app.postEvent(self, DbSearchErrorEvent(str(e)))
                
    def process_highlighting(self, df, keywords):
        """使用向量化操作处理高亮信息，减少循环"""
        # 添加高亮列
        df['_highlighted_cells'] = None
        
        # 仅处理字符串列
        string_columns = df.select_dtypes(include=['object']).columns
        string_columns = [col for col in string_columns if col != '_highlighted_cells']
        
        if not string_columns:
            return
            
        # 将所有关键词转为小写
        lowercase_keywords = [kw.lower() for kw in keywords]
        
        # 使用apply方法批量处理每行
        def highlight_row(row):
            highlighted_cells = {}
            
            for col_idx, col_name in enumerate(string_columns):
                cell_value = row[col_name]
                if isinstance(cell_value, str):
                    cell_value_lower = cell_value.lower()
                    matched_keywords = []
                    
                    for kw in lowercase_keywords:
                        if kw in cell_value_lower:
                            matched_keywords.append(kw)
                    
                    if matched_keywords:
                        col_position = df.columns.get_loc(col_name)
                        highlighted_cells[col_position] = matched_keywords
            
            return highlighted_cells if highlighted_cells else None
        
        # 使用向量化操作处理所有行
        highlighted_cells_series = df.apply(highlight_row, axis=1)
        df.loc[highlighted_cells_series.notna(), '_highlighted_cells'] = highlighted_cells_series[highlighted_cells_series.notna()]

    def event(self, event):
        """处理自定义事件"""
        try:
            if event.type() == DB_SEARCH_COMPLETED_EVENT:
                # 搜索完成，关闭进度对话框
                if self.progress_dialog:
                    self.progress_dialog.close()
                    self.progress_dialog = None
                    
                # 更新UI
                self.update_tabs_with_results(event.tabs_to_add)
                return True
                
            elif event.type() == DB_SEARCH_ERROR_EVENT:
                # 搜索出错，关闭进度对话框
                if self.progress_dialog:
                    self.progress_dialog.close()
                    self.progress_dialog = None
                    
                # 显示错误信息
                QMessageBox.critical(self, "搜索错误", f"搜索时发生错误：{event.error_message}")
                self.status_label.setText("搜索失败")
                return True
                
            return super().event(event)
            
        except KeyboardInterrupt:
            # 处理键盘中断（Ctrl+C）
            print("操作被用户中断")
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None
            return True
        except Exception as e:
            # 处理其他异常
            logging.error(f"事件处理出错: {e}")
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None
            return True

    def update_tabs_with_results(self, tabs_to_add):
        """在主线程中更新树形结构和数据显示"""
        # 清空现有树形结构
        self.tables_tree.clear()
        
        # 添加搜索结果
        if tabs_to_add:
            # 按照搜索结果数量对表进行排序（降序）
            tabs_to_add.sort(key=lambda x: x[1], reverse=True)
            
            # 更新左侧树形结构 - 按数据量降序显示
            for table_name, count in tabs_to_add:
                tree_item = QTreeWidgetItem([table_name, f"{count:,}"])
                # 根据数据量设置不同颜色
                if count >= 1000:
                    tree_item.setForeground(0, QColor("#2E7D32"))  # 深绿色
                    tree_item.setForeground(1, QColor("#2E7D32"))
                elif count >= 100:
                    tree_item.setForeground(0, QColor("#FF6F00"))  # 橙色
                    tree_item.setForeground(1, QColor("#FF6F00"))
                else:
                    tree_item.setForeground(0, QColor("#1976D2"))  # 蓝色
                    tree_item.setForeground(1, QColor("#1976D2"))
                
                # 存储表名用于点击时识别
                tree_item.setData(0, Qt.UserRole, table_name)
                self.tables_tree.addTopLevelItem(tree_item)
            
            # 更新状态信息
            table_count = len(self.search_results)
            keywords_str = "、".join(self.search_keywords)
            self.status_label.setText(f'在{table_count}个表中找到包含关键词"{keywords_str}"(匹配任一关键词)的记录共{self.total_records}条')
            self.data_title.setText(f"搜索结果：{table_count}个表，{self.total_records}条记录")
            
            # 选中左侧树的第一项并显示数据
            if self.tables_tree.topLevelItemCount() > 0:
                first_item = self.tables_tree.topLevelItem(0)
                self.tables_tree.setCurrentItem(first_item)
                self.current_table = first_item.data(0, Qt.UserRole)
                self.current_page = 1
            self.display_current_page()
        else:
            # 没有搜索结果
            keywords_str = "、".join(self.search_keywords)
            self.status_label.setText(f'未找到包含关键词"{keywords_str}"(匹配任一关键词)的记录')
            self.data_title.setText("没有搜索结果")
            # 清空表格显示
            self.table_view.setModel(None)
            self.paging_label.setText("共0页，当前第0页")
    
    def on_table_tree_clicked(self, item, column):
        """左侧树形结构点击事件 - 加载对应数据表的内容"""
        try:
            if item:
                table_name = item.data(0, Qt.UserRole)
                if table_name and table_name in self.search_results:
                    # 切换到新表
                    self.current_table = table_name
                    self.current_page = 1  # 重置为第一页
                    
                    # 更新右侧标题
                    count = len(self.search_results[table_name])
                    self.data_title.setText(f"当前表：{table_name} (共{count:,}条记录)")
                    
                    # 显示数据
                    self.display_current_page()
        except Exception as e:
            logging.error(f"切换表格时出错: {str(e)}")

    def display_current_page(self):
        """显示当前表的当前页数据"""
        # 确保有当前表和搜索结果
        if not self.current_table or self.current_table not in self.search_results:
            self.paging_label.setText("共0页，当前第0页")
            self.table_view.setModel(None)
            return
            
        # 获取当前表的数据
        df = self.search_results[self.current_table]
        
        # 计算分页信息
        total_records = len(df)
        total_pages = (total_records + self.page_size - 1) // self.page_size if total_records > 0 else 1
        
        # 边界检查
        if self.current_page > total_pages:
            self.current_page = 1
        elif self.current_page < 1:
            self.current_page = 1
            
        # 获取当前页数据
        start_idx = (self.current_page - 1) * self.page_size
        end_idx = min(start_idx + self.page_size, total_records)
        page_data = df.iloc[start_idx:end_idx] if total_records > 0 else df
        
        # 创建模型并设置到表格视图
        model = DataFrameModel(page_data, self.search_keywords)
        self.table_view.setModel(model)
            
            # 调整列宽
        self.table_view.resizeColumnsToContents()
        
        # 更新分页信息
        if total_records > 0:
            self.paging_label.setText(f"共{total_pages}页，当前第{self.current_page}页，显示 {start_idx+1} - {end_idx} 条，共 {total_records} 条")
        else:
            self.paging_label.setText("共0页，当前第0页")

    def export_data(self):
        """
        使用与数据导入界面相同的导出逻辑：
        - 显示表选择对话框，让用户选择要导出的表
        - 使用分类导出功能，按类别分组导出
        - 每个类别对应一个Excel文件，不同表作为不同工作表
        - 提供完整的进度反馈和错误处理
        """
        try:
            # 🔧 修改：使用与数据导入界面相同的导出逻辑
            from pivot_export import export_data_with_partitioning
            import logging

            # 设置日志
            logger = logging.getLogger(__name__)

            # 正确获取案件ID和案件名称
            case_id = ''
            case_name = '案件'

            # 尝试从父窗口获取案件信息
            if hasattr(self, 'parent') and self.parent:
                # 从父窗口获取
                if hasattr(self.parent, 'cases_controller') and self.parent.cases_controller:
                    if hasattr(self.parent.cases_controller, 'selected_case_id'):
                        case_id = self.parent.cases_controller.selected_case_id or ''
                        if case_id:
                            case_name = self.parent.cases_controller.get_case_name(case_id) or '案件'

            # 如果仍然没有案件ID，提示用户
            if not case_id:
                QMessageBox.warning(self, "警告", "请先在主界面选择一个案件，然后再进行导出操作")
                return

            # 🔧 修改：直接调用数据导入界面使用的导出函数
            logger.info(f"🚀 搜索界面：调用数据导入界面的导出逻辑，案件ID: {case_id}, 案件名称: {case_name}")
            export_data_with_partitioning(case_id, case_name)

        except Exception as e:
            logger.error(f"导出数据时发生错误: {e}")
            QMessageBox.critical(self, "导出错误", f"导出数据时发生错误: {str(e)}")

    def _export_by_transaction_name(self, df, table_name, keywords_part, current_time, folder):
        """
        按交易户名分组导出数据 - 参考pivot_export.py的智能分组算法
        将交易户名相同的记录放在同一个文件中，支持大数据量智能分组
        """
        exported_files = []
        
        # 检查是否是账户交易明细表且包含交易户名字段
        if table_name != "账户交易明细表" or "交易户名" not in df.columns:
            # 不是账户交易明细表或没有交易户名字段，按原来的方式导出
            filename = f"{table_name}_{keywords_part}_{current_time}.xlsx"
            file_path = os.path.join(folder, filename)
            df.to_excel(file_path, index=False, engine='xlsxwriter')
            exported_files.append(f"{filename} ({len(df):,} 行)")
            return exported_files
        
        try:
            total_records = len(df)
            max_rows_per_file = 1000000  # 每个文件最大100万行
            
            logging.info(f"开始智能分组导出，总记录数: {total_records}")
            
            # 获取交易户名统计信息
            name_counts = df['交易户名'].fillna('未知交易户名').value_counts().to_dict()
            logging.info(f"共有 {len(name_counts)} 个不同的交易户名")
            
            # 计算需要的分组数量
            num_groups = math.ceil(total_records / max_rows_per_file)
            logging.info(f"智能分组策略：需要 {num_groups} 组进行导出")
            
            # 处理超大户和普通户
            big_accounts = []
            normal_accounts = []
            
            for name, count in name_counts.items():
                if count > max_rows_per_file:
                    big_accounts.append((name, count))
                else:
                    normal_accounts.append((name, count))
            
            # 按记录数量排序
            normal_accounts.sort(key=lambda x: x[1], reverse=True)
            
            # 智能分组算法
            groups = [[] for _ in range(num_groups)]
            group_sizes = [0] * num_groups
            
            # 处理超大户，每个需要单独分组
            extra_groups = []
            extra_group_sizes = []
            for name, count in big_accounts:
                # 计算需要分几组
                account_groups = math.ceil(count / max_rows_per_file)
                logging.info(f"超大交易户名 '{name}' 数据量 {count} 行，需单独分配 {account_groups} 组")
                
                for _ in range(account_groups):
                    extra_groups.append([name])
                    extra_group_sizes.append(count // account_groups)
            
            # 处理普通户 - 使用贪心算法
            for name, count in normal_accounts:
                # 找到当前大小最小且加上当前户后不超过max_rows_per_file的组
                suitable_group = -1
                min_size = float('inf')
                
                for i, size in enumerate(group_sizes):
                    if size + count <= max_rows_per_file and size < min_size:
                        suitable_group = i
                        min_size = size
                
                # 如果没找到合适的组，放入当前最小的组
                if suitable_group == -1:
                    suitable_group = group_sizes.index(min(group_sizes))
                
                groups[suitable_group].append(name)
                group_sizes[suitable_group] += count
            
            # 合并常规组和超大户组
            if extra_groups:
                groups.extend(extra_groups)
                group_sizes.extend(extra_group_sizes)
            
            # 移除空组
            non_empty_groups = []
            non_empty_sizes = []
            for g, s in zip(groups, group_sizes):
                if g:
                    non_empty_groups.append(g)
                    non_empty_sizes.append(s)
            
            groups = non_empty_groups
            group_sizes = non_empty_sizes
            
            logging.info(f"智能分组完成，共 {len(groups)} 组")
            
            # 如果分组数量过多，给用户提示
            if len(groups) > 50:
                from PySide6.QtWidgets import QMessageBox
                # 显示前10个分组的统计信息
                preview_info = []
                for i, (group_names, size) in enumerate(zip(groups[:10], group_sizes[:10])):
                    preview_info.append(f"第{i+1}组: {len(group_names)}个户名, {size:,}条记录")
                
                reply = QMessageBox.question(
                    self, 
                    "分组数量较多", 
                    f"按交易户名分组将生成 {len(groups)} 个文件。\n\n"
                    f"前10个分组统计：\n" + 
                    "\n".join(preview_info) +
                    f"\n...\n\n是否继续导出？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                if reply != QMessageBox.Yes:
                    return ["用户取消了按交易户名分组导出"]
            
            # 准备索引数据
            index_data = {
                "交易户名": [],
                "所在文件": [],
                "数据量": [],
                "分组编号": []
            }
            
            # 开始分组导出
            for group_idx, group_names in enumerate(groups):
                if not group_names:
                    continue
                
                # 构建文件名
                group_suffix = f"_第{group_idx+1}组_共{len(groups)}组"
                filename = f"{table_name}_{keywords_part}_{current_time}{group_suffix}.xlsx"
                file_path = os.path.join(folder, filename)
                
                # 获取该组的数据
                group_condition = df['交易户名'].fillna('未知交易户名').isin(group_names)
                group_df = df[group_condition].copy()
                
                if group_df.empty:
                    logging.warning(f"第 {group_idx+1} 组数据为空，跳过")
                    continue
                
                # 导出到Excel
                try:
                    group_df.to_excel(file_path, index=False, engine='xlsxwriter')
                    exported_files.append(f"{filename} ({len(group_df):,} 行)")
                    logging.info(f"成功导出第 {group_idx+1} 组: {len(group_df)} 行数据")
                    
                    # 记录索引信息
                    for name in group_names:
                        name_count = (group_df['交易户名'].fillna('未知交易户名') == name).sum()
                        if name_count > 0:
                            index_data["交易户名"].append(name)
                            index_data["所在文件"].append(filename)
                            index_data["数据量"].append(name_count)
                            index_data["分组编号"].append(group_idx + 1)
                    
                except Exception as e:
                    logging.error(f"导出第 {group_idx+1} 组时出错: {e}")
                    exported_files.append(f"{filename} (导出失败: {str(e)})")
            
            # 生成Excel格式的索引文件
            if index_data["交易户名"]:
                index_df = pd.DataFrame(index_data)
                index_filename = f"{table_name}_分组索引_{keywords_part}_{current_time}.xlsx"
                index_path = os.path.join(folder, index_filename)
                
                try:
                    # 创建汇总统计
                    summary_df = pd.DataFrame({
                        "项目": ["总记录数", "导出文件数", "交易户名数", "分组数"],
                        "数量": [
                            total_records,
                            len([f for f in exported_files if "导出失败" not in f]),
                            len(set(index_data["交易户名"])),
                            len(groups)
                        ]
                    })
                    
                    # 写入Excel文件
                    with pd.ExcelWriter(index_path, engine='xlsxwriter') as writer:
                        # 导出索引数据
                        index_df.to_excel(writer, sheet_name="分组索引", index=False)
                        # 导出汇总信息
                        summary_df.to_excel(writer, sheet_name="导出汇总", index=False)
                        
                        # 设置格式
                        workbook = writer.book
                        
                        # 索引工作表格式
                        index_worksheet = writer.sheets["分组索引"]
                        index_worksheet.autofilter(0, 0, len(index_df), len(index_df.columns) - 1)
                        for i, col in enumerate(index_df.columns):
                            index_worksheet.set_column(i, i, max(15, len(col) * 1.5))
                        
                        # 汇总工作表格式
                        summary_worksheet = writer.sheets["导出汇总"]
                        summary_worksheet.set_column(0, 0, 20)
                        summary_worksheet.set_column(1, 1, 15)
                    
                    exported_files.append(f"{index_filename} (索引文件)")
                    logging.info(f"索引文件创建成功: {index_filename}")
                    
                except Exception as e:
                    logging.error(f"创建索引文件失败: {e}")
                    # 创建文本格式的汇总文件作为备用
                    summary_filename = f"{table_name}_分组汇总_{keywords_part}_{current_time}.txt"
                    summary_path = os.path.join(folder, summary_filename)
                    
                    with open(summary_path, 'w', encoding='utf-8') as f:
                        f.write(f"按交易户名分组导出汇总\n")
                        f.write(f"导出时间: {current_time}\n")
                        f.write(f"搜索关键词: {keywords_part}\n")
                        f.write(f"总记录数: {total_records:,} 条\n")
                        f.write(f"分组数量: {len(groups)} 个\n\n")
                        f.write("各分组详情:\n")
                        f.write("-" * 50 + "\n")
                        
                        for name, count in sorted(name_counts.items(), key=lambda x: x[1], reverse=True):
                            percentage = (count / total_records) * 100
                            f.write(f"{name}: {count:,} 条记录 ({percentage:.1f}%)\n")
                    
                    exported_files.append(f"{summary_filename} (汇总信息)")
            
            logging.info(f"按交易户名分组导出完成，共生成 {len(exported_files)} 个文件")
            
        except Exception as e:
            # 如果分组导出失败，回退到普通导出
            logging.error(f"按交易户名分组导出失败: {e}")
            filename = f"{table_name}_{keywords_part}_{current_time}_分组失败回退.xlsx"
            file_path = os.path.join(folder, filename)
            df.to_excel(file_path, index=False, engine='xlsxwriter')
            exported_files.append(f"{filename} ({len(df):,} 行) [分组失败，回退到单文件]")
        
        return exported_files

    def _make_safe_filename(self, name):
        """
        将交易户名转换为安全的文件名
        移除或替换不能用于文件名的字符
        """
        import re
        
        # 移除或替换特殊字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', str(name))
        
        # 限制长度，避免文件名过长
        if len(safe_name) > 50:
            safe_name = safe_name[:47] + "..."
        
        # 处理空字符串
        if not safe_name.strip():
            safe_name = "未知交易户名"
            
        return safe_name

    def prev_page(self):
        """显示上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.display_current_page()

    def next_page(self):
        """显示下一页"""
        # 确保有当前表
        if not self.current_table or self.current_table not in self.search_results:
            return
            
        # 计算总页数
        df = self.search_results[self.current_table]
        total_records = len(df)
        total_pages = (total_records + self.page_size - 1) // self.page_size if total_records > 0 else 1
        
        if self.current_page < total_pages:
            self.current_page += 1
            self.display_current_page()

    def preload_table_structure(self):
        """预加载数据库所有表的结构信息，提高后续搜索速度"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 获取所有表名 - PostgreSQL语法
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema='public' 
                AND table_type='BASE TABLE'
            """)
            tables = [table[0] for table in cursor.fetchall()]
            
            # 缓存每个表的列名和文本列
            for table in tables:
                # 获取表的列名和类型 - PostgreSQL语法
                cursor.execute("""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_name = %s
                """, (table,))
                columns_info = cursor.fetchall()
                
                # 提取列名
                columns = [column[0] for column in columns_info]
                self.table_columns_cache[table] = columns
                
                # 识别文本类型列
                text_columns = [
                    column[0] for column in columns_info
                    if column[1].lower() in ('text', 'varchar', 'char', 'character varying')
                ]
                self.text_columns_cache[table] = text_columns
            
            conn.close()
            
        except Exception as e:
            print(f"预加载表结构信息出错: {str(e)}")

class ToolsWindow:
    def __init__(self, parent=None):
        self.parent = parent

    def merge_files(self):
        files, _ = QFileDialog.getOpenFileNames(self.parent, "选择要合并的文件", "",
                                                "All Files (*);;PDF Files (*.pdf);;Excel Files (*.xls *.xlsx);;CSV Files (*.csv);;Text Files (*.txt)")
        if not files:
            return

        if files[0].endswith('.pdf'):
            self.merge_pdfs(files)
        elif files[0].endswith('.xls') or files[0].endswith('.xlsx') or files[0].endswith('.csv'):
            self.merge_excel_csv(files)
        elif files[0].endswith('.txt'):
            self.merge_text(files)
        else:
            QMessageBox.warning(self.parent, "文件类型不支持", "仅支持合并PDF、Excel、CSV和文本文件。")

    def merge_pdfs(self, files):
        pdf_writer = PdfWriter()
        for file in files:
            pdf_reader = PdfReader(file)
            for page_num in range(len(pdf_reader.pages)):
                pdf_writer.add_page(pdf_reader.pages[page_num])

        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        default_name = f"合并文件_{current_time}.pdf"
        output_path, _ = QFileDialog.getSaveFileName(self.parent, "保存合并后的PDF文件", default_name,
                                                     "PDF Files (*.pdf)")
        if output_path:
            with open(output_path, 'wb') as output_pdf:
                pdf_writer.write(output_pdf)
            QMessageBox.information(self.parent, "完成", "PDF文件合并成功！")

    def merge_excel_csv(self, files):
        combined_df = pd.DataFrame()
        for file in files:
            df = None
            if file.endswith('.csv'):
                encodings = ['utf-8', 'gbk', 'gb2312']
                for encoding in encodings:
                    try:
                        df = pd.read_csv(file, encoding=encoding, dtype=str)
                        break
                    except UnicodeDecodeError:
                        continue
                if df is None:
                    QMessageBox.warning(self.parent, "文件读取错误", f"无法读取CSV文件: {file}")
                    return
            else:
                df = pd.read_excel(file, dtype=str)
            combined_df = combined_df.append(df, ignore_index=True)

        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        ext = os.path.splitext(files[0])[-1]
        default_name = f"合并文件_{current_time}{ext}"
        output_path, _ = QFileDialog.getSaveFileName(self.parent, "保存合并后的文件", default_name,
                                                     f"Excel Files (*.xls *.xlsx);;CSV Files (*.csv)")
        if output_path:
            if output_path.endswith('.csv'):
                combined_df.to_csv(output_path, index=False, float_format='%.15g')
            else:
                with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
                    combined_df.to_excel(writer, index=False, sheet_name='Sheet1')
                    workbook = writer.book
                    worksheet = writer.sheets['Sheet1']
                    for col_num, value in enumerate(combined_df.columns.values):
                        worksheet.set_column(col_num, col_num, 20, workbook.add_format({'num_format': '0'}))
            QMessageBox.information(self.parent, "完成", f"{ext.upper()}文件合并成功！")

    def merge_text(self, files):
        combined_text = ""
        encodings = ['utf-8', 'gbk', 'gb2312', 'ansi', 'latin1']
        for file in files:
            file_content = None
            for encoding in encodings:
                try:
                    with open(file, 'r', encoding=encoding) as f:
                        file_content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            if file_content is None:
                QMessageBox.warning(self.parent, "文件读取错误", f"无法读取文本文件: {file}")
                return
            combined_text += file_content + "\n"

        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        default_name = f"合并文件_{current_time}.txt"
        output_path, _ = QFileDialog.getSaveFileName(self.parent, "保存合并后的文本文件", default_name,
                                                     "Text Files (*.txt)")
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as output_file:
                output_file.write(combined_text)
            QMessageBox.information(self.parent, "完成", "文本文件合并成功！")

    def split_table(self):
        file, _ = QFileDialog.getOpenFileName(self.parent, "选择要拆分的Excel文件", "", "Excel Files (*.xls *.xlsx)")
        if not file:
            return

        df = pd.read_excel(file)
        dialog = SplitDialog(df.columns, self.parent)
        if dialog.exec_() == QDialog.Accepted:
            split_type = dialog.split_type
            column_name = dialog.column_name

            if split_type == "拆分单独的文件":
                self.split_to_files(df, column_name)
            elif split_type == "拆分成不同的工作表":
                self.split_to_sheets(df, column_name)

    def split_to_files(self, df, column_name):
        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        groups = df.groupby(column_name)
        save_path, _ = QFileDialog.getSaveFileName(self.parent, "选择保存位置", f"拆分文件_{current_time}.xlsx",
                                                   "Excel Files (*.xlsx)")
        if save_path:
            base_path = os.path.splitext(save_path)[0]
            for value, group in groups:
                output_path = f"{base_path}_{value}.xlsx"
                group.to_excel(output_path, index=False)
            QMessageBox.information(self.parent, "完成", "Excel文件拆分成功！")

    def split_to_sheets(self, df, column_name):
        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        groups = df.groupby(column_name)
        default_name = f"拆分表格_{current_time}.xlsx"
        output_path, _ = QFileDialog.getSaveFileName(self.parent, "保存拆分后的Excel文件", default_name,
                                                     "Excel Files (*.xlsx)")
        if output_path:
            with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
                for value, group in groups:
                    group.to_excel(writer, sheet_name=str(value), index=False)
                    worksheet = writer.sheets[str(value)]
                    for col_num, col_name in enumerate(group.columns):
                        worksheet.set_column(col_num, col_num, 20, writer.book.add_format({'num_format': '0'}))
            QMessageBox.information(self.parent, "完成", "Excel文件拆分成功！")

    def pdf_split(self):
        file, _ = QFileDialog.getOpenFileName(self.parent, "选择要拆分的PDF文件", "", "PDF Files (*.pdf)")
        if not file:
            return

        pdf_reader = PdfReader(file)
        base_output_path, _ = QFileDialog.getSaveFileName(self.parent, "选择保存位置",
                                                          os.path.splitext(file)[0] + "_拆分.pdf", "PDF Files (*.pdf)")
        if base_output_path:
            base_output_path = os.path.splitext(base_output_path)[0]
            for page_num in range(len(pdf_reader.pages)):
                pdf_writer = PdfWriter()
                pdf_writer.add_page(pdf_reader.pages[page_num])

                output_path = f"{base_output_path}_{str(page_num + 1).zfill(3)}.pdf"
                with open(output_path, 'wb') as output_pdf:
                    pdf_writer.write(output_pdf)
            QMessageBox.information(self.parent, "完成", "PDF文件拆分成功！")

    def pdf_convert(self):
        file, _ = QFileDialog.getOpenFileName(self.parent, "选择要转换的PDF文件", "", "PDF Files (*.pdf)")
        if not file:
            return

        # 添加转换类型选择
        convert_type_dialog = QDialog(self.parent)
        convert_type_dialog.setWindowTitle("选择转换类型")
        layout = QVBoxLayout()

        label = QLabel("选择转换类型：")
        layout.addWidget(label)

        self.combo_box_convert_type = QComboBox()
        self.combo_box_convert_type.addItems(["转换为Word", "转换为Excel"])
        layout.addWidget(self.combo_box_convert_type)

        button_layout = QHBoxLayout()

        ok_button = QPushButton("确定")
        ok_button.setFixedSize(100, 40)
        ok_button.setStyleSheet(BUTTON_STYLE)
        ok_button.clicked.connect(convert_type_dialog.accept)
        button_layout.addWidget(ok_button)

        cancel_button = QPushButton("取消")
        cancel_button.setFixedSize(100, 40)
        cancel_button.setStyleSheet(BUTTON_STYLE)
        cancel_button.clicked.connect(convert_type_dialog.reject)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)
        convert_type_dialog.setLayout(layout)

        if convert_type_dialog.exec_() != QDialog.Accepted:
            return

        convert_type = self.combo_box_convert_type.currentText()
        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        if convert_type == "转换为Word":
            default_name = f"PDF转换文件_{current_time}.docx"
        else:
            default_name = f"PDF转换文件_{current_time}.xlsx"

        output_path, _ = QFileDialog.getSaveFileName(self.parent, "保存转换后的文件", default_name,
                                                     "Word Files (*.docx);;Excel Files (*.xlsx)")
        if output_path.endswith('.docx'):
            self.pdf_to_word(file, output_path)
        elif output_path.endswith('.xlsx'):
            self.pdf_to_excel(file, output_path)
        else:
            QMessageBox.warning(self.parent, "文件类型不支持", "仅支持将PDF文件转换为Word或Excel文件。")

    def pdf_to_word(self, pdf_file, output_path):
        pdf_reader = PdfReader(pdf_file)
        doc = Document()

        for page in pdf_reader.pages:
            text = page.extract_text()
            doc.add_paragraph(text)

        doc.save(output_path)
        QMessageBox.information(self.parent, "完成", "PDF文件转换为Word文件成功！")

    def pdf_to_excel(self, pdf_file, output_path):
        with pdfplumber.open(pdf_file) as pdf:
            all_tables = []
            for page in pdf.pages:
                tables = page.extract_tables()
                for table in tables:
                    all_tables.append(table)

        if all_tables:
            with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
                for i, table in enumerate(all_tables):
                    df = pd.DataFrame(table[1:], columns=table[0])
                    df.to_excel(writer, index=False, sheet_name=f'Sheet{i + 1}')
            QMessageBox.information(self.parent, "完成", "PDF文件转换为Excel文件成功！")
        else:
            QMessageBox.warning(self.parent, "错误", "未能在PDF文件中找到表格。")

    def database_search(self):
        """打开数据库搜索窗口"""
        self.db_search_window = DatabaseSearchWindow()
        self.db_search_window.show()


class SplitDialog(QDialog):
    def __init__(self, columns, parent=None):
        super().__init__(parent)
        self.setWindowTitle("拆分表格设置")
        self.setFixedSize(600, 400)

        self.split_type = None
        self.column_name = None

        layout = QVBoxLayout()

        label = QLabel("选择拆分方式：")
        layout.addWidget(label)

        self.combo_box_split_type = QComboBox()
        self.combo_box_split_type.addItems(["拆分单独的文件", "拆分成不同的工作表"])
        layout.addWidget(self.combo_box_split_type)

        label = QLabel("选择字段：")
        layout.addWidget(label)

        self.combo_box_column = QComboBox()
        self.combo_box_column.addItems(columns)
        layout.addWidget(self.combo_box_column)

        button_layout = QHBoxLayout()

        self.ok_button = QPushButton("确定")
        self.ok_button.setFixedSize(100, 40)
        self.ok_button.setStyleSheet(BUTTON_STYLE)
        self.ok_button.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_button)

        self.cancel_button = QPushButton("取消")
        self.cancel_button.setFixedSize(100, 40)
        self.cancel_button.setStyleSheet(BUTTON_STYLE)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def accept(self):
        self.split_type = self.combo_box_split_type.currentText()
        self.column_name = self.combo_box_column.currentText()
        super().accept()


class DatabaseExportDialog(QDialog):
    """优化的数据库搜索导出对话框"""
    def __init__(self, search_results, parent=None):
        super().__init__(parent)
        self.search_results = search_results
        self.filtered_results = search_results.copy()
        self.checkboxes = {}
        
        self.setWindowTitle("导出搜索数据")
        self.setMinimumWidth(800)
        self.setMinimumHeight(650)
        self.resize(900, 750)
        
        self.init_ui()
        self.update_display()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建顶部信息区域
        self.create_header_section(layout)
        
        # 创建搜索区域
        self.create_search_section(layout)
        
        # 创建表格选择区域
        self.create_table_selection_section(layout)
        
        # 创建底部按钮区域
        self.create_button_section(layout)
    
    def create_header_section(self, layout):
        """创建顶部信息区域"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #e3f2fd, stop:1 #f5f5f5);
                border: 1px solid #1976D2;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        
        # 计算统计信息
        total_rows = sum(len(df) for df in self.search_results.values())
        tables_with_data = {k: len(v) for k, v in self.search_results.items() if len(v) > 0}
        tables_without_data = {k: len(v) for k, v in self.search_results.items() if len(v) == 0}
        large_tables = {k: v for k, v in tables_with_data.items() if v > 1000000}
        
        # 标题
        title_label = QLabel("📊 搜索结果导出")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #1976D2; margin-bottom: 10px;")
        header_layout.addWidget(title_label)
        
        # 统计信息网格
        stats_layout = QGridLayout()
        
        # 第一行统计
        stats_layout.addWidget(QLabel("📋 搜索到表格:"), 0, 0)
        stats_layout.addWidget(QLabel(f"<b>{len(self.search_results)}</b> 个"), 0, 1)
        
        stats_layout.addWidget(QLabel("📈 有数据表格:"), 0, 2)
        stats_layout.addWidget(QLabel(f"<b style='color: #4CAF50'>{len(tables_with_data)}</b> 个"), 0, 3)
        
        # 第二行统计
        stats_layout.addWidget(QLabel("📊 总记录数:"), 1, 0)
        stats_layout.addWidget(QLabel(f"<b style='color: #2196F3'>{total_rows:,}</b> 条"), 1, 1)
        
        stats_layout.addWidget(QLabel("⚡ 大数据表:"), 1, 2)
        stats_layout.addWidget(QLabel(f"<b style='color: #FF9800'>{len(large_tables)}</b> 个 (>100万行)"), 1, 3)
        
        header_layout.addLayout(stats_layout)
        
        # 说明信息
        info_text = """
💡 <b>导出说明:</b>
• 有数据的表已默认选中，可自由调整选择
• 超过100万行的表将自动使用智能分组导出
• 小于100万行的表将导出为Excel格式
• 智能分组按交易户名分组，便于后续分析
        """.strip()
        
        info_label = QLabel(info_text)
        info_label.setStyleSheet("""
            color: #555; 
            font-size: 11px; 
            background-color: rgba(255,255,255,0.7);
            border: 1px solid #ddd; 
            padding: 10px; 
            border-radius: 6px;
            margin-top: 10px;
        """)
        header_layout.addWidget(info_label)
        
        layout.addWidget(header_frame)
    
    def create_search_section(self, layout):
        """创建搜索区域"""
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #fafafa;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 10px;
                margin-bottom: 10px;
            }
        """)
        search_layout = QHBoxLayout(search_frame)
        
        # 搜索标签
        search_label = QLabel("🔍")
        search_label.setStyleSheet("font-size: 16px; color: #666;")
        search_layout.addWidget(search_label)
        
        # 搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入表名关键词进行搜索...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 10px 15px;
                border: 2px solid #ddd;
                border-radius: 25px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #1976D2;
                background-color: #fff;
            }
        """)
        self.search_input.textChanged.connect(self.filter_tables)
        search_layout.addWidget(self.search_input)
        
        # 清除按钮
        clear_button = QPushButton("清除")
        clear_button.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 20px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        clear_button.clicked.connect(self.clear_search)
        search_layout.addWidget(clear_button)
        
        layout.addWidget(search_frame)
    
    def create_table_selection_section(self, layout):
        """创建表格选择区域"""
        # 创建选项卡
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                padding: 12px 20px;
                margin-right: 3px;
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-bottom: none;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background-color: #1976D2;
                color: white;
                font-weight: bold;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e3f2fd;
            }
        """)
        
        # 全部表格选项卡
        self.all_tables_tab = QScrollArea()
        self.all_tables_widget = QWidget()
        self.all_tables_layout = QVBoxLayout(self.all_tables_widget)
        self.all_tables_tab.setWidget(self.all_tables_widget)
        self.all_tables_tab.setWidgetResizable(True)
        
        # 有数据的表选项卡
        self.data_tables_tab = QScrollArea()
        self.data_tables_widget = QWidget()
        self.data_tables_layout = QVBoxLayout(self.data_tables_widget)
        self.data_tables_tab.setWidget(self.data_tables_widget)
        self.data_tables_tab.setWidgetResizable(True)
        
        # 大数据表选项卡
        self.large_tables_tab = QScrollArea()
        self.large_tables_widget = QWidget()
        self.large_tables_layout = QVBoxLayout(self.large_tables_widget)
        self.large_tables_tab.setWidget(self.large_tables_widget)
        self.large_tables_tab.setWidgetResizable(True)
        
        # 空表选项卡
        self.empty_tables_tab = QScrollArea()
        self.empty_tables_widget = QWidget()
        self.empty_tables_layout = QVBoxLayout(self.empty_tables_widget)
        self.empty_tables_tab.setWidget(self.empty_tables_widget)
        self.empty_tables_tab.setWidgetResizable(True)
        
        # 添加选项卡
        self.tab_widget.addTab(self.all_tables_tab, "全部表格")
        self.tab_widget.addTab(self.data_tables_tab, "有数据")
        self.tab_widget.addTab(self.large_tables_tab, "大数据表")
        self.tab_widget.addTab(self.empty_tables_tab, "空表")
        
        layout.addWidget(self.tab_widget)
    
    def create_button_section(self, layout):
        """创建底部按钮区域"""
        button_frame = QFrame()
        button_frame.setStyleSheet("""
            QFrame {
                background-color: #fafafa;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 15px;
                margin-top: 10px;
            }
        """)
        button_layout = QHBoxLayout(button_frame)
        
        # 左侧操作按钮
        left_buttons = QHBoxLayout()
        
        # 全选按钮
        select_all_button = QPushButton("全选当前")
        select_all_button.setStyleSheet("""
            QPushButton {
                padding: 10px 16px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        select_all_button.clicked.connect(self.select_all_current)
        left_buttons.addWidget(select_all_button)
        
        # 取消全选按钮
        deselect_all_button = QPushButton("取消全选")
        deselect_all_button.setStyleSheet("""
            QPushButton {
                padding: 10px 16px;
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e68900;
            }
        """)
        deselect_all_button.clicked.connect(self.deselect_all)
        left_buttons.addWidget(deselect_all_button)
        
        # 只选有数据的表
        select_data_button = QPushButton("只选有数据")
        select_data_button.setStyleSheet("""
            QPushButton {
                padding: 10px 16px;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        select_data_button.clicked.connect(self.select_data_only)
        left_buttons.addWidget(select_data_button)
        
        button_layout.addLayout(left_buttons)
        button_layout.addStretch()
        
        # 右侧信息和确认按钮
        right_section = QHBoxLayout()
        
        # 选择统计
        self.selection_label = QLabel("已选择: 0 个表")
        self.selection_label.setStyleSheet("""
            color: #555; 
            font-weight: bold; 
            font-size: 13px;
            background-color: rgba(33, 150, 243, 0.1);
            padding: 8px 15px;
            border-radius: 15px;
            border: 1px solid rgba(33, 150, 243, 0.3);
        """)
        right_section.addWidget(self.selection_label)
        
        # 确定按钮
        self.ok_button = QPushButton("确定导出")
        self.ok_button.setStyleSheet("""
            QPushButton {
                padding: 12px 25px;
                background-color: #1976D2;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #999;
            }
        """)
        self.ok_button.clicked.connect(self.accept)
        right_section.addWidget(self.ok_button)
        
        # 取消按钮
        cancel_button = QPushButton("取消")
        cancel_button.setStyleSheet("""
            QPushButton {
                padding: 12px 25px;
                background-color: #757575;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #616161;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        right_section.addWidget(cancel_button)
        
        button_layout.addLayout(right_section)
        layout.addWidget(button_frame)
    
    def create_table_checkbox(self, table_name, row_count):
        """创建单个表格的复选框卡片"""
        card_frame = QFrame()
        card_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 12px;
                margin: 3px;
            }
            QFrame:hover {
                background-color: #f8f9fa;
                border-color: #1976D2;
                box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
            }
        """)
        
        card_layout = QHBoxLayout(card_frame)
        card_layout.setContentsMargins(10, 8, 10, 8)
        
        # 复选框
        checkbox = QCheckBox()
        checkbox.setChecked(row_count > 0)  # 有数据的表默认选中
        checkbox.setObjectName(table_name)
        checkbox.stateChanged.connect(self.update_selection_count)
        card_layout.addWidget(checkbox)
        
        # 表名
        name_label = QLabel(table_name)
        if row_count > 1000000:
            name_label.setStyleSheet("color: #FF6D00; font-weight: bold; font-size: 13px;")
        elif row_count > 10000:
            name_label.setStyleSheet("color: #2E7D32; font-weight: bold; font-size: 13px;")
        elif row_count > 0:
            name_label.setStyleSheet("color: #1976D2; font-size: 13px;")
        else:
            name_label.setStyleSheet("color: #9E9E9E; font-style: italic; font-size: 13px;")
        
        card_layout.addWidget(name_label)
        card_layout.addStretch()
        
        # 记录数和导出方式标签
        if row_count > 0:
            export_method = "智能分组" if row_count > 1000000 else "Excel导出"
            count_text = f"{row_count:,} 行 · {export_method}"
            
            if row_count > 1000000:
                badge_style = """
                    background-color: #FF6D00; 
                    color: white; 
                    padding: 4px 12px; 
                    border-radius: 12px; 
                    font-size: 11px; 
                    font-weight: bold;
                """
            elif row_count > 10000:
                badge_style = """
                    background-color: #4CAF50; 
                    color: white; 
                    padding: 4px 12px; 
                    border-radius: 12px; 
                    font-size: 11px; 
                    font-weight: bold;
                """
            else:
                badge_style = """
                    background-color: #2196F3; 
                    color: white; 
                    padding: 4px 12px; 
                    border-radius: 12px; 
                    font-size: 11px; 
                    font-weight: bold;
                """
        else:
            count_text = "无数据"
            badge_style = """
                background-color: #9E9E9E; 
                color: white; 
                padding: 4px 12px; 
                border-radius: 12px; 
                font-size: 11px;
            """
        
        count_label = QLabel(count_text)
        count_label.setStyleSheet(badge_style)
        card_layout.addWidget(count_label)
        
        return checkbox, card_frame
    
    def update_display(self):
        """更新显示"""
        # 清空现有内容
        self.clear_layouts()
        self.checkboxes.clear()
        
        # 按行数排序
        sorted_tables = dict(sorted(self.filtered_results.items(), key=lambda x: len(x[1]), reverse=True))
        
        # 分类表格
        tables_with_data = {k: len(v) for k, v in sorted_tables.items() if len(v) > 0}
        large_tables = {k: v for k, v in tables_with_data.items() if v > 1000000}
        empty_tables = {k: len(v) for k, v in sorted_tables.items() if len(v) == 0}
        
        # 添加到各个选项卡
        for table_name, df in sorted_tables.items():
            row_count = len(df)
            checkbox, card_frame = self.create_table_checkbox(table_name, row_count)
            self.checkboxes[table_name] = checkbox
            
            # 添加到全部表格
            self.all_tables_layout.addWidget(card_frame)
            
            # 根据数据量添加到相应选项卡
            if row_count > 0:
                # 创建新的卡片用于有数据选项卡
                checkbox2, card_frame2 = self.create_table_checkbox(table_name, row_count)
                self.data_tables_layout.addWidget(card_frame2)
                
                if row_count > 1000000:
                    # 创建新的卡片用于大数据表选项卡
                    checkbox3, card_frame3 = self.create_table_checkbox(table_name, row_count)
                    self.large_tables_layout.addWidget(card_frame3)
            else:
                # 创建新的卡片用于空表选项卡
                checkbox4, card_frame4 = self.create_table_checkbox(table_name, row_count)
                self.empty_tables_layout.addWidget(card_frame4)
        
        # 添加弹簧
        self.all_tables_layout.addStretch()
        self.data_tables_layout.addStretch()
        self.large_tables_layout.addStretch()
        self.empty_tables_layout.addStretch()
        
        # 更新选项卡标题
        self.tab_widget.setTabText(0, f"全部表格 ({len(sorted_tables)})")
        self.tab_widget.setTabText(1, f"有数据 ({len(tables_with_data)})")
        self.tab_widget.setTabText(2, f"大数据表 ({len(large_tables)})")
        self.tab_widget.setTabText(3, f"空表 ({len(empty_tables)})")
        
        # 更新选择统计
        self.update_selection_count()
    
    def clear_layouts(self):
        """清空所有布局"""
        for layout in [self.all_tables_layout, self.data_tables_layout, 
                      self.large_tables_layout, self.empty_tables_layout]:
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
    
    def filter_tables(self, text):
        """根据搜索文本过滤表格"""
        if not text.strip():
            self.filtered_results = self.search_results.copy()
        else:
            search_text = text.lower()
            self.filtered_results = {
                name: df for name, df in self.search_results.items() 
                if search_text in name.lower()
            }
        
        self.update_display()
    
    def clear_search(self):
        """清除搜索"""
        self.search_input.clear()
    
    def select_all_current(self):
        """选择当前显示的所有表"""
        for table_name, checkbox in self.checkboxes.items():
            if table_name in self.filtered_results:
                checkbox.setChecked(True)
    
    def deselect_all(self):
        """取消选择所有表"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(False)
    
    def select_data_only(self):
        """只选择有数据的表"""
        for table_name, checkbox in self.checkboxes.items():
            if table_name in self.filtered_results:
                has_data = len(self.filtered_results[table_name]) > 0
                checkbox.setChecked(has_data)
    
    def update_selection_count(self):
        """更新选择统计"""
        selected_count = sum(1 for checkbox in self.checkboxes.values() if checkbox.isChecked())
        selected_rows = sum(
            len(self.search_results[table_name]) 
            for table_name, checkbox in self.checkboxes.items() 
            if checkbox.isChecked() and table_name in self.search_results
        )
        
        self.selection_label.setText(f"已选择: {selected_count} 个表 ({selected_rows:,} 条记录)")
        self.ok_button.setEnabled(selected_count > 0)
    
    def get_selected_tables(self):
        """获取选中的表名列表"""
        return [table_name for table_name, checkbox in self.checkboxes.items() if checkbox.isChecked()]
