#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度现金识别调试脚本

本文件的功能和实现逻辑：
1. 深入分析案件007的交易明细数据
2. 查看实际的摘要说明和交易类型内容
3. 验证关键词匹配逻辑是否正确
4. 找出为什么没有记录被识别为现金

重点调试：
- 显示实际的摘要说明和交易类型样本
- 测试不同的关键词匹配策略
- 分析对手信息的实际状态
- 提供手动现金标记功能
"""

import psycopg2
import configparser
import logging
from datetime import datetime

# 设置日志
log_file = f'deep_cash_debug_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class DeepCashDebugger:
    """深度现金识别调试器"""
    
    def __init__(self, case_id):
        self.case_id = case_id
        self.conn = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            config = configparser.ConfigParser()
            config.read('db_config.ini')
            
            self.conn = psycopg2.connect(
                host=config['PostgreSQL']['host'],
                port=config['PostgreSQL']['port'],
                database=config['PostgreSQL']['database'],
                user=config['PostgreSQL']['user'],
                password=config['PostgreSQL']['password']
            )
            self.cursor = self.conn.cursor()
            logging.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logging.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def analyze_transaction_data(self):
        """分析交易数据"""
        logging.info("🔍 分析案件交易数据...")
        
        try:
            # 1. 基本统计
            self.cursor.execute('SELECT COUNT(*) FROM "账户交易明细表" WHERE "案件编号" = %s', (self.case_id,))
            total_count = self.cursor.fetchone()[0]
            logging.info(f"📊 总交易记录数: {total_count}")
            
            if total_count == 0:
                logging.error("❌ 没有找到任何交易记录")
                return False
            
            # 2. 对手信息统计
            self.cursor.execute('''
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN "对手户名" IS NULL OR "对手户名" = '' THEN 1 END) as empty_name,
                    COUNT(CASE WHEN "对手账号" IS NULL OR "对手账号" = '' THEN 1 END) as empty_account,
                    COUNT(CASE WHEN "对手卡号" IS NULL OR "对手卡号" = '' THEN 1 END) as empty_card,
                    COUNT(CASE WHEN ("对手户名" IS NULL OR "对手户名" = '') 
                                AND ("对手账号" IS NULL OR "对手账号" = '') 
                                AND ("对手卡号" IS NULL OR "对手卡号" = '') THEN 1 END) as all_empty
                FROM "账户交易明细表" 
                WHERE "案件编号" = %s
            ''', (self.case_id,))
            
            stats = self.cursor.fetchone()
            logging.info(f"📊 对手信息统计:")
            logging.info(f"   总记录数: {stats[0]}")
            logging.info(f"   对手户名为空: {stats[1]}")
            logging.info(f"   对手账号为空: {stats[2]}")
            logging.info(f"   对手卡号为空: {stats[3]}")
            logging.info(f"   三项全为空: {stats[4]}")
            
            # 3. 显示摘要说明样本
            logging.info("📄 摘要说明样本 (前20条):")
            self.cursor.execute('''
                SELECT DISTINCT "摘要说明" 
                FROM "账户交易明细表" 
                WHERE "案件编号" = %s 
                AND "摘要说明" IS NOT NULL 
                AND "摘要说明" != ''
                LIMIT 20
            ''', (self.case_id,))
            
            summaries = self.cursor.fetchall()
            for i, (summary,) in enumerate(summaries, 1):
                logging.info(f"   {i:2d}. {summary}")
            
            # 4. 显示交易类型样本
            logging.info("📄 交易类型样本:")
            self.cursor.execute('''
                SELECT "交易类型", COUNT(*) as count
                FROM "账户交易明细表" 
                WHERE "案件编号" = %s 
                AND "交易类型" IS NOT NULL 
                AND "交易类型" != ''
                GROUP BY "交易类型"
                ORDER BY count DESC
                LIMIT 15
            ''', (self.case_id,))
            
            types = self.cursor.fetchall()
            for type_name, count in types:
                logging.info(f"   {type_name}: {count} 条")
            
            # 5. 检查是否包含用户提到的关键词
            logging.info("🔍 检查用户提到的关键词:")
            user_keywords = ['现金存入', '现金支取', '网络ATM取款', 'ATM取款', 'ATM存款', 'ATM取现']
            
            found_any = False
            for keyword in user_keywords:
                # 检查摘要说明
                self.cursor.execute('''
                    SELECT COUNT(*),
                           STRING_AGG(DISTINCT "摘要说明", '; ' ORDER BY "摘要说明") as samples
                    FROM "账户交易明细表"
                    WHERE "案件编号" = %s
                    AND "摘要说明" ILIKE %s
                ''', (self.case_id, f'%{keyword}%'))
                
                result = self.cursor.fetchone()
                if result and result[0] > 0:
                    found_any = True
                    logging.info(f"   ✅ '{keyword}' 在摘要说明中: {result[0]} 条")
                    if result[1]:
                        samples = result[1][:200] + "..." if len(result[1]) > 200 else result[1]
                        logging.info(f"      样本: {samples}")
                else:
                    logging.info(f"   ❌ '{keyword}' 在摘要说明中: 0 条")
                
                # 检查交易类型
                self.cursor.execute('''
                    SELECT COUNT(*),
                           STRING_AGG(DISTINCT "交易类型", '; ' ORDER BY "交易类型") as samples
                    FROM "账户交易明细表"
                    WHERE "案件编号" = %s
                    AND "交易类型" ILIKE %s
                ''', (self.case_id, f'%{keyword}%'))
                
                result = self.cursor.fetchone()
                if result and result[0] > 0:
                    found_any = True
                    logging.info(f"   ✅ '{keyword}' 在交易类型中: {result[0]} 条")
                    if result[1]:
                        logging.info(f"      样本: {result[1]}")
                else:
                    logging.info(f"   ❌ '{keyword}' 在交易类型中: 0 条")
            
            if not found_any:
                logging.warning("⚠️ 没有找到用户提到的任何关键词")
                
                # 尝试更宽泛的关键词搜索
                logging.info("🔍 尝试更宽泛的关键词搜索:")
                broad_keywords = ['现金', 'ATM', 'atm', '取款', '存款', '取现', '存现']
                
                for keyword in broad_keywords:
                    self.cursor.execute('''
                        SELECT COUNT(*) FROM "账户交易明细表" 
                        WHERE "案件编号" = %s 
                        AND ("摘要说明" ILIKE %s OR "交易类型" ILIKE %s)
                    ''', (self.case_id, f'%{keyword}%', f'%{keyword}%'))
                    
                    count = self.cursor.fetchone()[0]
                    if count > 0:
                        logging.info(f"   ✅ '{keyword}': {count} 条")
                        
                        # 显示样本
                        self.cursor.execute('''
                            SELECT "摘要说明", "交易类型", "对手户名"
                            FROM "账户交易明细表" 
                            WHERE "案件编号" = %s 
                            AND ("摘要说明" ILIKE %s OR "交易类型" ILIKE %s)
                            LIMIT 3
                        ''', (self.case_id, f'%{keyword}%', f'%{keyword}%'))
                        
                        samples = self.cursor.fetchall()
                        for i, (summary, type_name, counterparty) in enumerate(samples, 1):
                            logging.info(f"      样本{i}: 摘要='{summary}', 类型='{type_name}', 对手='{counterparty}'")
                    else:
                        logging.info(f"   ❌ '{keyword}': 0 条")
            
            return True
            
        except Exception as e:
            logging.error(f"❌ 分析交易数据时出错: {e}")
            return False
    
    def test_manual_cash_marking(self):
        """测试手动现金标记"""
        logging.info("🧪 测试手动现金标记...")
        
        try:
            # 查找可能的现金交易（对手信息为空的记录）
            self.cursor.execute('''
                SELECT COUNT(*) FROM "账户交易明细表" 
                WHERE "案件编号" = %s 
                AND ("对手户名" IS NULL OR "对手户名" = '')
                AND ("对手账号" IS NULL OR "对手账号" = '')
                AND ("对手卡号" IS NULL OR "对手卡号" = '')
                AND "对手户名" != '现金'
            ''', (self.case_id,))
            
            empty_counterparty_count = self.cursor.fetchone()[0]
            logging.info(f"📊 对手信息为空且未标记为现金的记录: {empty_counterparty_count}")
            
            if empty_counterparty_count > 0:
                # 显示这些记录的样本
                logging.info("📄 对手信息为空的记录样本:")
                self.cursor.execute('''
                    SELECT "摘要说明", "交易类型", "交易金额", "交易日期"
                    FROM "账户交易明细表" 
                    WHERE "案件编号" = %s 
                    AND ("对手户名" IS NULL OR "对手户名" = '')
                    AND ("对手账号" IS NULL OR "对手账号" = '')
                    AND ("对手卡号" IS NULL OR "对手卡号" = '')
                    AND "对手户名" != '现金'
                    ORDER BY "交易日期" DESC
                    LIMIT 10
                ''', (self.case_id,))
                
                samples = self.cursor.fetchall()
                for i, (summary, type_name, amount, date) in enumerate(samples, 1):
                    logging.info(f"   {i:2d}. 摘要: {summary}")
                    logging.info(f"       类型: {type_name}")
                    logging.info(f"       金额: {amount}")
                    logging.info(f"       日期: {date}")
                    logging.info("")
                
                # 询问是否要将这些记录标记为现金
                response = input(f"\n是否要将这 {empty_counterparty_count} 条对手信息为空的记录标记为现金？(y/n): ").strip().lower()
                
                if response == 'y':
                    logging.info("🚀 开始标记对手信息为空的记录为现金...")
                    
                    self.cursor.execute('''
                        UPDATE "账户交易明细表"
                        SET "对手户名" = '现金'
                        WHERE "案件编号" = %s 
                        AND ("对手户名" IS NULL OR "对手户名" = '')
                        AND ("对手账号" IS NULL OR "对手账号" = '')
                        AND ("对手卡号" IS NULL OR "对手卡号" = '')
                        AND "对手户名" != '现金'
                    ''', (self.case_id,))
                    
                    updated_count = self.cursor.rowcount
                    self.conn.commit()
                    
                    logging.info(f"✅ 成功标记了 {updated_count} 条记录为现金")
                    return True
                else:
                    logging.info("ℹ️ 用户选择不标记")
            else:
                logging.info("ℹ️ 没有找到需要标记的记录")
            
            return False
            
        except Exception as e:
            logging.error(f"❌ 测试手动现金标记时出错: {e}")
            return False
    
    def run_deep_debug(self):
        """运行深度调试"""
        logging.info("🚀 开始深度现金识别调试...")
        
        if not self.connect_database():
            return False
        
        try:
            # 分析交易数据
            if not self.analyze_transaction_data():
                return False
            
            # 测试手动现金标记
            marked = self.test_manual_cash_marking()
            
            if marked:
                logging.info("✅ 现金识别问题已通过手动标记解决")
            else:
                logging.info("ℹ️ 现金识别问题需要进一步分析")
            
            return True
            
        finally:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()

def main():
    """主函数"""
    print("🔍 深度现金识别调试工具")
    print("=" * 50)
    
    case_id = "20250716182021"  # 直接使用案件007
    
    debugger = DeepCashDebugger(case_id)
    
    print(f"\n🚀 开始深度调试案件: {case_id}")
    print(f"📄 详细日志保存到: {log_file}")
    
    success = debugger.run_deep_debug()
    
    if success:
        print("\n✅ 深度调试完成！")
        return 0
    else:
        print("\n❌ 深度调试发现问题")
        return 1

if __name__ == "__main__":
    exit(main())
