2025-08-05 21:09:46.446 - CRITICAL - [MainThread:22048] - enhanced_logging_patch.py:324 - monitored_exit() - 🚨 程序即将退出! 退出码: 0
2025-08-05 21:09:46.446 - CRITICAL - [MainThread:22048] - enhanced_logging_patch.py:325 - monitored_exit() - 退出时的堆栈跟踪:
2025-08-05 21:09:46.448 - CRITICAL - [MainThread:22048] - enhanced_logging_patch.py:330 - monitored_exit() -   File "G:\数据分析系统20250725\main.py", line 327, in <module>
    main()
2025-08-05 21:09:46.448 - CRITICAL - [MainThread:22048] - enhanced_logging_patch.py:330 - monitored_exit() -   File "G:\数据分析系统20250725\main.py", line 324, in main
    sys.exit(app.exec())
2025-08-05 21:09:46.448 - CRITICAL - [MainThread:22048] - enhanced_logging_patch.py:330 - monitored_exit() -   File "G:\数据分析系统20250725\enhanced_logging_patch.py", line 328, in monitored_exit
    stack = traceback.format_stack()
