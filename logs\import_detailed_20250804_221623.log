2025-08-04 22:16:23.091 - INFO - [MainThread:1036] - enhanced_logging_patch.py:84 - setup_enhanced_logging() - 🔍 增强日志记录已启动
2025-08-04 22:16:23.101 - INFO - [MainThread:1036] - memory_optimizer.py:38 - __init__() - ✅ 内存监控模块已加载
2025-08-04 22:16:23.101 - INFO - [MainThread:1036] - memory_optimizer.py:42 - __init__() - 📊 系统内存信息: 总计 31.7GB, 可用 17.6GB, 使用率 44.5%
2025-08-04 22:16:23.326 - INFO - [MainThread:1036] - database_table_checker.py:417 - check_database_tables() - 开始数据库表格完整性检查...
2025-08-04 22:16:23.395 - INFO - [MainThread:1036] - database_table_checker.py:115 - load_excel_table_config() - 成功读取Excel文件，共94行数据
2025-08-04 22:16:23.416 - INFO - [MainThread:1036] - database_table_checker.py:169 - load_excel_table_config() - 从Excel文件解析出94个有效表配置（处理了94行数据）
2025-08-04 22:16:23.416 - INFO - [MainThread:1036] - database_table_checker.py:182 - load_excel_table_config() - Excel文件统计：总行数94，重复表名0个，有效表格94个
2025-08-04 22:16:23.503 - INFO - [MainThread:1036] - database_table_checker.py:444 - check_database_tables() - 数据库中现有表格总数：106
2025-08-04 22:16:23.504 - INFO - [MainThread:1036] - database_table_checker.py:456 - check_database_tables() - 核心系统表检查：9个必需，9个已存在
2025-08-04 22:16:30.891 - INFO - [MainThread:1036] - database_table_checker.py:507 - check_database_tables() - Excel配置表检查：94个定义，94个已存在
2025-08-04 22:16:30.891 - INFO - [MainThread:1036] - database_table_checker.py:511 - check_database_tables() - 所有Excel配置表都已存在，无需创建
2025-08-04 22:16:30.891 - INFO - [MainThread:1036] - database_table_checker.py:541 - check_database_tables() - 所有表都已存在，无需创建任何表
2025-08-04 22:16:30.891 - INFO - [MainThread:1036] - database_table_checker.py:344 - update_table_column_types() - 开始检查并更新表字段类型...
2025-08-04 22:16:31.111 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_建设用地宅基地' 的字段类型...
2025-08-04 22:16:31.111 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_房地产权表' 的字段类型...
2025-08-04 22:16:31.111 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_抵押权表' 的字段类型...
2025-08-04 22:16:31.111 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_查封登记表' 的字段类型...
2025-08-04 22:16:31.111 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_预告登记表' 的字段类型...
2025-08-04 22:16:31.111 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同乘三次以上同行人' 的字段类型...
2025-08-04 22:16:31.111 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同订单同行人已成行' 的字段类型...
2025-08-04 22:16:31.111 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同订单同行人未成行' 的字段类型...
2025-08-04 22:16:31.111 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班进出港_航班进出港已成行表' 的字段类型...
2025-08-04 22:16:31.116 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班进出港_航班进出港未成行表' 的字段类型...
2025-08-04 22:16:31.116 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券持有_持有信息' 的字段类型...
2025-08-04 22:16:31.116 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券持有变动_持' 的字段类型...
2025-08-04 22:16:31.116 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券账户_证券账户' 的字段类型...
2025-08-04 22:16:31.117 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_同订单同行人_同行人员信息表' 的字段类型...
2025-08-04 22:16:31.117 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_同订单同行人_同行人员客票' 的字段类型...
2025-08-04 22:16:31.117 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_用户注册_互联网注册信息表' 的字段类型...
2025-08-04 22:16:31.117 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_用户注册_常用联系人信息表' 的字段类型...
2025-08-04 22:16:31.117 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_铁路客票_交易信息表' 的字段类型...
2025-08-04 22:16:31.117 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_铁路客票_票面信息表' 的字段类型...
2025-08-04 22:16:31.117 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '临时账户交易明细表' 的字段类型...
2025-08-04 22:16:31.117 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_产品信息表' 的字段类型...
2025-08-04 22:16:31.119 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_受益人信息' 的字段类型...
2025-08-04 22:16:31.119 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_委托人信息' 的字段类型...
2025-08-04 22:16:31.119 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_登记信息_受益权结构' 的字段类型...
2025-08-04 22:16:31.119 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_登记信息_合同信息' 的字段类型...
2025-08-04 22:16:31.119 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_终止登记' 的字段类型...
2025-08-04 22:16:31.119 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_委托人或受益人变动信息表' 的字段类型...
2025-08-04 22:16:31.120 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_登记信息_受益权结构表' 的字段类型...
2025-08-04 22:16:31.120 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_登记信息_合同信息表' 的字段类型...
2025-08-04 22:16:31.120 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_终止登记表' 的字段类型...
2025-08-04 22:16:31.120 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_交通违法_机动车违章信息表' 的字段类型...
2025-08-04 22:16:31.121 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_出入境记录_出入境记录信息表' 的字段类型...
2025-08-04 22:16:31.121 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_出国_境_证件_出入境证件信息' 的字段类型...
2025-08-04 22:16:31.121 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同住址_同住址表' 的字段类型...
2025-08-04 22:16:31.121 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同户人_同户人表' 的字段类型...
2025-08-04 22:16:31.121 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同车违章_同车违章表' 的字段类型...
2025-08-04 22:16:31.122 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃人员_在逃人员登记信息' 的字段类型...
2025-08-04 22:16:31.122 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃同案撤销人员_在逃同案撤销人员' 的字段类型...
2025-08-04 22:16:31.122 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃撤销_在逃人员撤销信息' 的字段类型...
2025-08-04 22:16:31.122 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_户籍人口_基本人员信息表' 的字段类型...
2025-08-04 22:16:31.122 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_旅馆住宿_旅馆住宿人员信息表' 的字段类型...
2025-08-04 22:16:31.123 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_机动车_机动车信息' 的字段类型...
2025-08-04 22:16:31.123 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_驾驶证_驾驶证信息表' 的字段类型...
2025-08-04 22:16:31.123 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_住院结算数据' 的字段类型...
2025-08-04 22:16:31.123 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_参保信息' 的字段类型...
2025-08-04 22:16:31.123 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_普通门诊' 的字段类型...
2025-08-04 22:16:31.123 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_药店购药' 的字段类型...
2025-08-04 22:16:31.123 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_药店购药明细' 的字段类型...
2025-08-04 22:16:31.123 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '国家税务总局_纳税人登记信息_登记信息表' 的字段类型...
2025-08-04 22:16:31.123 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '国家税务总局_纳税信息_税务缴纳信息表' 的字段类型...
2025-08-04 22:16:31.124 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '增值税发票表' 的字段类型...
2025-08-04 22:16:31.124 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '字段匹配规则' 的字段类型...
2025-08-04 22:16:31.124 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '对手信息' 的字段类型...
2025-08-04 22:16:31.124 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '导入记录表' 的字段类型...
2025-08-04 22:16:31.124 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_主要人员表' 的字段类型...
2025-08-04 22:16:31.124 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_内资补充信息表' 的字段类型...
2025-08-04 22:16:31.124 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_农专补充信息表' 的字段类型...
2025-08-04 22:16:31.126 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_分支机构备案信息表' 的字段类型...
2025-08-04 22:16:31.126 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_变更备案信息表' 的字段类型...
2025-08-04 22:16:31.126 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_吊销信息表' 的字段类型...
2025-08-04 22:16:31.126 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_外资补充信息表' 的字段类型...
2025-08-04 22:16:31.126 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_注销信息表' 的字段类型...
2025-08-04 22:16:31.127 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_清算基本信息表' 的字段类型...
2025-08-04 22:16:31.127 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_清算成员信息表' 的字段类型...
2025-08-04 22:16:31.127 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_联络员信息表' 的字段类型...
2025-08-04 22:16:31.127 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_自然人出资信息表' 的字段类型...
2025-08-04 22:16:31.127 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_许可信息表' 的字段类型...
2025-08-04 22:16:31.128 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_财务负责人信息表' 的字段类型...
2025-08-04 22:16:31.128 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_非自然人出资信息表' 的字段类型...
2025-08-04 22:16:31.128 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业基本信息表' 的字段类型...
2025-08-04 22:16:31.129 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_统一社会信用代码_统一社会信用代码表' 的字段类型...
2025-08-04 22:16:31.129 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '开户信息表' 的字段类型...
2025-08-04 22:16:31.129 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '本地银行_客户信息本地表' 的字段类型...
2025-08-04 22:16:31.129 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '案件信息表' 的字段类型...
2025-08-04 22:16:31.130 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_投资行业信息表' 的字段类型...
2025-08-04 22:16:31.130 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_持有信息表' 的字段类型...
2025-08-04 22:16:31.130 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_理财产品信息表' 的字段类型...
2025-08-04 22:16:31.130 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '用户信息表' 的字段类型...
2025-08-04 22:16:31.130 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '电话_登记信息_运营商登记信息表' 的字段类型...
2025-08-04 22:16:31.130 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '电话_话单信息_运营商话单信息表' 的字段类型...
2025-08-04 22:16:31.130 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_专票货物或应税劳务名称表' 的字段类型...
2025-08-04 22:16:31.130 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_增值税专用发票表' 的字段类型...
2025-08-04 22:16:31.130 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_增值税普通发票表' 的字段类型...
2025-08-04 22:16:31.131 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_普票货物或应税劳务服务名' 的字段类型...
2025-08-04 22:16:31.131 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '系统信息表' 的字段类型...
2025-08-04 22:16:31.131 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '系统配置表' 的字段类型...
2025-08-04 22:16:31.132 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '虚拟运营商_登记信息_虚拟运营商登记信息表' 的字段类型...
2025-08-04 22:16:31.132 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '表类型匹配规则_导出文件名分类表' 的字段类型...
2025-08-04 22:16:31.132 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '表类型匹配规则表' 的字段类型...
2025-08-04 22:16:31.132 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '证券登记结算_证券持有变动_持' 的字段类型...
2025-08-04 22:16:31.132 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '证券登记结算_证券持有变动_证券持有变动' 的字段类型...
2025-08-04 22:16:31.132 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '财付通交易明细表' 的字段类型...
2025-08-04 22:16:31.133 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户交易明细表' 的字段类型...
2025-08-04 22:16:31.133 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_共有权优先权信息表' 的字段类型...
2025-08-04 22:16:31.134 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_关联子账户信息表' 的字段类型...
2025-08-04 22:16:31.134 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_关联子账户信息表本地' 的字段类型...
2025-08-04 22:16:31.134 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_客户基本信息表' 的字段类型...
2025-08-04 22:16:31.134 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_强制措施信息表' 的字段类型...
2025-08-04 22:16:31.134 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息（本地）_优先权信息表' 的字段类型...
2025-08-04 22:16:31.134 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '金融理财_金融理财信息表' 的字段类型...
2025-08-04 22:16:31.134 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '金融理财_金融理财账户信息表' 的字段类型...
2025-08-04 22:16:31.134 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险人员信息表' 的字段类型...
2025-08-04 22:16:31.134 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险保单信息表' 的字段类型...
2025-08-04 22:16:31.135 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险赔案信息表' 的字段类型...
2025-08-04 22:16:31.135 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_家庭财产保险表' 的字段类型...
2025-08-04 22:16:31.135 - INFO - [MainThread:1036] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_航空延误保险表' 的字段类型...
2025-08-04 22:16:31.135 - INFO - [MainThread:1036] - database_table_checker.py:404 - update_table_column_types() - 字段类型更新完成: 已更新 0 个字段, 跳过 2702 个字段, 失败 0 个字段
2025-08-04 22:16:31.198 - INFO - [MainThread:1036] - database_table_checker.py:580 - check_database_tables() - === 数据库表格检查报告 ===
2025-08-04 22:16:31.205 - INFO - [MainThread:1036] - database_table_checker.py:581 - check_database_tables() - 总计检查表格：103个 (核心9个 + Excel94个)
2025-08-04 22:16:31.205 - INFO - [MainThread:1036] - database_table_checker.py:582 - check_database_tables() - 最终存在表格：103个
2025-08-04 22:16:31.205 - INFO - [MainThread:1036] - database_table_checker.py:589 - check_database_tables() - 本次未创建任何新表格（所有表都已存在）
2025-08-04 22:16:37.876 - INFO - [MainThread:1036] - import_error_handler.py:523 - start_heartbeat_monitor() - 心跳监控已启动
2025-08-04 22:16:37.888 - INFO - [MainThread:1036] - import_data.py:148 - __init__() - ✅ 自动化管理器已初始化
2025-08-04 22:16:40.988 - INFO - [Dummy-2:8076] - data_cleaning.py:2151 - start_cleaning() - 用户选择了 15 个清洗步骤: ['clean_customer_basic_info', 'clean_transaction_details', 'clean_account_opening_info', 'clean_special_characters', 'complement_transaction_account_fields', 'clean_numeric_account_names', 'enrich_account_opening_info', 'preprocess_data', 'match_transaction_names', 'match_certificate_numbers', 'match_opponent_names', 'check_and_correct_shoufu', 'fill_counterparty_name_with_cash', 'finalize_cleaning', 'deduplicate_all_tables']
2025-08-04 22:16:40.988 - INFO - [Dummy-2:8076] - data_cleaning.py:2169 - start_cleaning() - 开始执行清洗步骤 1/15: clean_customer_basic_info
2025-08-04 22:16:41.062 - INFO - [Dummy-2:8076] - data_cleaning.py:1754 - clean_customer_basic_info() - 🔄 开始清洗账户信息_客户基本信息表...
2025-08-04 22:16:41.071 - INFO - [Dummy-2:8076] - data_cleaning.py:1796 - clean_customer_basic_info() - ✅ 账户信息_客户基本信息表清洗完成，更新 0 条记录
2025-08-04 22:16:41.071 - INFO - [Dummy-2:8076] - data_cleaning.py:1797 - clean_customer_basic_info() - 📝 处理内容：证件类型为'账号/卡号'时，查询对象名称→卡号，证件号码→账号
2025-08-04 22:16:41.071 - INFO - [Dummy-2:8076] - data_cleaning.py:2184 - start_cleaning() - 清洗步骤 clean_customer_basic_info 完成
2025-08-04 22:16:41.071 - INFO - [Dummy-2:8076] - data_cleaning.py:2169 - start_cleaning() - 开始执行清洗步骤 2/15: clean_transaction_details
2025-08-04 22:16:41.140 - INFO - [Dummy-2:8076] - data_cleaning.py:326 - clean_transaction_details() - 开始清洗账户交易明细表
2025-08-04 22:16:41.143 - INFO - [Dummy-2:8076] - data_cleaning.py:331 - clean_transaction_details() - 🔧 清理交易账卡号和交易账号中的'-'值
2025-08-04 22:16:41.440 - INFO - [Dummy-2:8076] - data_cleaning.py:345 - clean_transaction_details() - ✅ 已清理 0 条记录中的'-'值
2025-08-04 22:16:41.676 - INFO - [Dummy-2:8076] - data_cleaning.py:363 - clean_transaction_details() - 🔍 发现 166903 条记录需要清洗币种数据
2025-08-04 22:16:41.685 - INFO - [Dummy-2:8076] - data_cleaning.py:366 - clean_transaction_details() - 🌐 标准化交易币种格式
2025-08-04 22:16:41.947 - INFO - [Dummy-2:8076] - data_cleaning.py:394 - clean_transaction_details() - ✅ 币种标准化完成，处理 0 条记录
2025-08-04 22:16:42.156 - INFO - [Dummy-2:8076] - data_cleaning.py:408 - clean_transaction_details() - 📊 账户交易明细表清洗完成统计：
2025-08-04 22:16:42.161 - INFO - [Dummy-2:8076] - data_cleaning.py:409 - clean_transaction_details() -   • 清理'-'值记录数：0
2025-08-04 22:16:42.162 - INFO - [Dummy-2:8076] - data_cleaning.py:410 - clean_transaction_details() -   • 币种标准化记录数：0
2025-08-04 22:16:42.162 - INFO - [Dummy-2:8076] - data_cleaning.py:411 - clean_transaction_details() -   • 总处理记录数：0
2025-08-04 22:16:42.162 - INFO - [Dummy-2:8076] - data_cleaning.py:2184 - start_cleaning() - 清洗步骤 clean_transaction_details 完成
2025-08-04 22:16:42.162 - INFO - [Dummy-2:8076] - data_cleaning.py:2169 - start_cleaning() - 开始执行清洗步骤 3/15: clean_account_opening_info
2025-08-04 22:16:42.229 - INFO - [Dummy-2:8076] - data_cleaning.py:428 - clean_account_opening_info() - 开始清洗开户信息表
2025-08-04 22:16:42.235 - INFO - [Dummy-2:8076] - data_cleaning.py:438 - clean_account_opening_info() - 🗑️ 删除交易账号和交易卡号都为空的记录
2025-08-04 22:16:42.237 - INFO - [Dummy-2:8076] - data_cleaning.py:448 - clean_account_opening_info() - ✅ 已删除 0 条交易账号和交易卡号都为空的记录
2025-08-04 22:16:42.238 - INFO - [Dummy-2:8076] - data_cleaning.py:458 - clean_account_opening_info() - 🔧 标准化剩余数据格式
2025-08-04 22:16:42.681 - INFO - [Dummy-2:8076] - data_cleaning.py:473 - clean_account_opening_info() - 📊 开户信息表清洗完成统计：
2025-08-04 22:16:42.681 - INFO - [Dummy-2:8076] - data_cleaning.py:474 - clean_account_opening_info() -   • 清洗前记录数：1122
2025-08-04 22:16:42.681 - INFO - [Dummy-2:8076] - data_cleaning.py:475 - clean_account_opening_info() -   • 删除无效记录：0
2025-08-04 22:16:42.682 - INFO - [Dummy-2:8076] - data_cleaning.py:476 - clean_account_opening_info() -   • 清洗后记录数：1122
2025-08-04 22:16:42.682 - INFO - [Dummy-2:8076] - data_cleaning.py:477 - clean_account_opening_info() -   • 标准化记录数：1122
2025-08-04 22:16:42.685 - INFO - [Dummy-2:8076] - data_cleaning.py:2184 - start_cleaning() - 清洗步骤 clean_account_opening_info 完成
2025-08-04 22:16:42.685 - INFO - [Dummy-2:8076] - data_cleaning.py:2169 - start_cleaning() - 开始执行清洗步骤 4/15: clean_special_characters
2025-08-04 22:16:42.749 - INFO - [Dummy-2:8076] - data_cleaning.py:1056 - clean_special_characters() - 开始清理特殊字符和无效值
2025-08-04 22:19:11.108 - INFO - [Dummy-2:8076] - data_cleaning.py:1144 - clean_special_characters() - 账户交易明细表清理完成，处理了 280487 条记录
2025-08-04 22:19:11.117 - INFO - [Dummy-2:8076] - data_cleaning.py:1177 - clean_special_characters() - 开户信息表清理完成，处理了 3 条记录
2025-08-04 22:19:11.126 - INFO - [Dummy-2:8076] - data_cleaning.py:1180 - clean_special_characters() - 开始生成_digits字段...
2025-08-04 22:19:11.656 - INFO - [Dummy-2:8076] - data_cleaning.py:1200 - clean_special_characters() - 账户交易明细表_digits字段生成完成，处理了 41 条记录
2025-08-04 22:19:11.657 - INFO - [Dummy-2:8076] - data_cleaning.py:1216 - clean_special_characters() - 开户信息表_digits字段生成完成，处理了 0 条记录
2025-08-04 22:19:11.657 - INFO - [Dummy-2:8076] - data_cleaning.py:1219 - clean_special_characters() - 🔢 _digits字段生成总计完成，共处理 41 条记录
2025-08-04 22:19:11.657 - INFO - [Dummy-2:8076] - data_cleaning.py:1224 - clean_special_characters() - 🔧 开始创建_digits字段索引...
2025-08-04 22:19:11.668 - INFO - [Dummy-2:8076] - data_cleaning.py:1251 - clean_special_characters() - ✅ _digits字段索引创建完成
2025-08-04 22:19:11.668 - INFO - [Dummy-2:8076] - data_cleaning.py:1258 - clean_special_characters() - 跳过财付通交易明细表清理（按用户要求）
2025-08-04 22:19:11.668 - INFO - [Dummy-2:8076] - data_cleaning.py:1260 - clean_special_characters() - 特殊字符清理总计完成，共处理 280490 条记录
2025-08-04 22:19:11.668 - INFO - [Dummy-2:8076] - data_cleaning.py:2184 - start_cleaning() - 清洗步骤 clean_special_characters 完成
2025-08-04 22:19:11.675 - INFO - [Dummy-2:8076] - data_cleaning.py:2169 - start_cleaning() - 开始执行清洗步骤 5/15: complement_transaction_account_fields
2025-08-04 22:19:11.777 - INFO - [Dummy-2:8076] - data_cleaning.py:1383 - complement_transaction_account_fields() - 开始执行账户交易明细表的交易账号和交易账卡号字段互补
2025-08-04 22:19:12.708 - INFO - [Dummy-2:8076] - data_cleaning.py:1402 - complement_transaction_account_fields() - 交易账号字段互补完成，更新了 0 条记录
2025-08-04 22:19:13.050 - INFO - [Dummy-2:8076] - data_cleaning.py:1419 - complement_transaction_account_fields() - 交易账卡号字段互补完成，更新了 0 条记录
2025-08-04 22:19:13.050 - INFO - [Dummy-2:8076] - data_cleaning.py:1422 - complement_transaction_account_fields() - 字段互补完成后，重新生成_digits字段...
2025-08-04 22:19:13.057 - INFO - [Dummy-2:8076] - data_cleaning.py:1437 - complement_transaction_account_fields() - 账户交易明细表_digits字段重新生成完成，更新了 1 条记录
2025-08-04 22:19:13.058 - INFO - [Dummy-2:8076] - data_cleaning.py:1439 - complement_transaction_account_fields() - 账户交易明细表字段互补总计完成，共更新 0 条记录
2025-08-04 22:19:13.060 - INFO - [Dummy-2:8076] - data_cleaning.py:2184 - start_cleaning() - 清洗步骤 complement_transaction_account_fields 完成
2025-08-04 22:19:13.061 - INFO - [Dummy-2:8076] - data_cleaning.py:2169 - start_cleaning() - 开始执行清洗步骤 6/15: clean_numeric_account_names
2025-08-04 22:19:13.125 - INFO - [Dummy-2:8076] - data_cleaning.py:1465 - clean_numeric_account_names() - 🔄 开始清理开户信息表中的数字账户开户名称...
2025-08-04 22:19:13.133 - INFO - [Dummy-2:8076] - data_cleaning.py:1500 - clean_numeric_account_names() - ✅ 未发现纯数字账户开户名称，无需清理
2025-08-04 22:19:13.133 - INFO - [Dummy-2:8076] - data_cleaning.py:2184 - start_cleaning() - 清洗步骤 clean_numeric_account_names 完成
2025-08-04 22:19:13.136 - INFO - [Dummy-2:8076] - data_cleaning.py:2169 - start_cleaning() - 开始执行清洗步骤 7/15: enrich_account_opening_info
2025-08-04 22:19:13.206 - INFO - [Dummy-2:8076] - data_cleaning.py:1531 - enrich_account_opening_info() - 🔄 开始增强开户信息表数据...
2025-08-04 22:19:13.206 - INFO - [Dummy-2:8076] - data_cleaning.py:1536 - enrich_account_opening_info() - 📝 处理账户开户名称为空的记录...
2025-08-04 22:19:13.216 - INFO - [Dummy-2:8076] - data_cleaning.py:1561 - enrich_account_opening_info() - ✅ 通过交易卡号更新账户开户名称：0 条记录
2025-08-04 22:19:13.216 - INFO - [Dummy-2:8076] - data_cleaning.py:1586 - enrich_account_opening_info() - ✅ 通过交易账号更新账户开户名称：0 条记录
2025-08-04 22:19:13.216 - INFO - [Dummy-2:8076] - data_cleaning.py:1589 - enrich_account_opening_info() - 📝 处理开户人证件号码为空的记录...
2025-08-04 22:19:13.216 - INFO - [Dummy-2:8076] - data_cleaning.py:1614 - enrich_account_opening_info() - ✅ 通过交易卡号更新开户人证件号码：0 条记录
2025-08-04 22:19:13.216 - INFO - [Dummy-2:8076] - data_cleaning.py:1639 - enrich_account_opening_info() - ✅ 通过交易账号更新开户人证件号码：0 条记录
2025-08-04 22:19:13.222 - INFO - [Dummy-2:8076] - data_cleaning.py:1642 - enrich_account_opening_info() - 📝 从账户交易明细表的对手信息中补充账户开户名称...
2025-08-04 22:19:13.632 - INFO - [Dummy-2:8076] - data_cleaning.py:1670 - enrich_account_opening_info() - ✅ 通过对手卡号更新账户开户名称：0 条记录
2025-08-04 22:19:14.167 - INFO - [Dummy-2:8076] - data_cleaning.py:1698 - enrich_account_opening_info() - ✅ 通过对手账号更新账户开户名称：0 条记录
2025-08-04 22:19:14.176 - INFO - [Dummy-2:8076] - data_cleaning.py:1701 - enrich_account_opening_info() - 🗑️ 删除账户开户名称仍为空的开户信息表记录...
2025-08-04 22:19:14.176 - INFO - [Dummy-2:8076] - data_cleaning.py:1711 - enrich_account_opening_info() - 🗑️ 删除账户开户名称为空的记录：0 条
2025-08-04 22:19:14.176 - INFO - [Dummy-2:8076] - data_cleaning.py:1714 - enrich_account_opening_info() - 🎯 开户信息表数据增强完成，总计更新 0 条记录，删除 0 条记录
2025-08-04 22:19:14.176 - INFO - [Dummy-2:8076] - data_cleaning.py:1715 - enrich_account_opening_info() - 📊 详细统计：
2025-08-04 22:19:14.176 - INFO - [Dummy-2:8076] - data_cleaning.py:1716 - enrich_account_opening_info() -    - 通过交易卡号更新客户名称：0 条
2025-08-04 22:19:14.176 - INFO - [Dummy-2:8076] - data_cleaning.py:1717 - enrich_account_opening_info() -    - 通过交易账号更新客户名称：0 条
2025-08-04 22:19:14.176 - INFO - [Dummy-2:8076] - data_cleaning.py:1718 - enrich_account_opening_info() -    - 通过交易卡号更新证件号码：0 条
2025-08-04 22:19:14.176 - INFO - [Dummy-2:8076] - data_cleaning.py:1719 - enrich_account_opening_info() -    - 通过交易账号更新证件号码：0 条
2025-08-04 22:19:14.176 - INFO - [Dummy-2:8076] - data_cleaning.py:1720 - enrich_account_opening_info() -    - 通过对手卡号更新账户开户名称：0 条
2025-08-04 22:19:14.176 - INFO - [Dummy-2:8076] - data_cleaning.py:1721 - enrich_account_opening_info() -    - 通过对手账号更新账户开户名称：0 条
2025-08-04 22:19:14.176 - INFO - [Dummy-2:8076] - data_cleaning.py:1722 - enrich_account_opening_info() -    - 删除账户开户名称为空的记录：0 条
2025-08-04 22:19:14.183 - INFO - [Dummy-2:8076] - data_cleaning.py:2184 - start_cleaning() - 清洗步骤 enrich_account_opening_info 完成
2025-08-04 22:19:14.183 - INFO - [Dummy-2:8076] - data_cleaning.py:2169 - start_cleaning() - 开始执行清洗步骤 8/15: preprocess_data
2025-08-04 22:19:14.246 - INFO - [Dummy-2:8076] - data_cleaning.py:486 - preprocess_data() - 开始数据预处理
2025-08-04 22:19:14.246 - INFO - [Dummy-2:8076] - data_cleaning.py:505 - preprocess_data() - 所有必要的列已成功添加并填充。
2025-08-04 22:19:14.246 - INFO - [Dummy-2:8076] - data_cleaning.py:2184 - start_cleaning() - 清洗步骤 preprocess_data 完成
2025-08-04 22:19:14.246 - INFO - [Dummy-2:8076] - data_cleaning.py:2169 - start_cleaning() - 开始执行清洗步骤 9/15: match_transaction_names
2025-08-04 22:19:14.314 - INFO - [Dummy-2:8076] - data_cleaning.py:523 - match_transaction_names() - 开始批量匹配交易户名
2025-08-04 22:19:14.315 - INFO - [Dummy-2:8076] - data_cleaning.py:528 - match_transaction_names() - 步骤1：交易账号_digits → 开户信息表.交易账号_digits
2025-08-04 22:19:14.427 - INFO - [Dummy-2:8076] - data_cleaning.py:546 - match_transaction_names() - 步骤1完成：通过交易账号_digits精确匹配，成功匹配 29 条记录
2025-08-04 22:19:14.427 - INFO - [Dummy-2:8076] - data_cleaning.py:549 - match_transaction_names() - 步骤2：交易账号_digits → 开户信息表.交易卡号_digits
2025-08-04 22:19:14.463 - INFO - [Dummy-2:8076] - data_cleaning.py:567 - match_transaction_names() - 步骤2完成：通过交易账号_digits匹配交易卡号_digits，成功匹配 29 条记录
2025-08-04 22:19:14.465 - INFO - [Dummy-2:8076] - data_cleaning.py:570 - match_transaction_names() - 步骤3：交易账卡号_digits → 开户信息表.交易账号_digits
2025-08-04 22:19:14.500 - INFO - [Dummy-2:8076] - data_cleaning.py:588 - match_transaction_names() - 步骤3完成：通过交易账卡号_digits匹配交易账号_digits，成功匹配 29 条记录
2025-08-04 22:19:14.500 - INFO - [Dummy-2:8076] - data_cleaning.py:591 - match_transaction_names() - 步骤4：交易账卡号_digits → 开户信息表.交易卡号_digits
2025-08-04 22:19:14.530 - INFO - [Dummy-2:8076] - data_cleaning.py:609 - match_transaction_names() - 步骤4完成：通过交易账卡号_digits匹配交易卡号_digits，成功匹配 29 条记录
2025-08-04 22:19:14.530 - INFO - [Dummy-2:8076] - data_cleaning.py:612 - match_transaction_names() - 🎯 交易户名匹配完成！总计匹配 116 条记录
2025-08-04 22:19:14.530 - INFO - [Dummy-2:8076] - data_cleaning.py:613 - match_transaction_names() - 📊 详细统计：
2025-08-04 22:19:14.530 - INFO - [Dummy-2:8076] - data_cleaning.py:614 - match_transaction_names() -    - 步骤1（账号对账号）：29 条
2025-08-04 22:19:14.530 - INFO - [Dummy-2:8076] - data_cleaning.py:615 - match_transaction_names() -    - 步骤2（账号对卡号）：29 条
2025-08-04 22:19:14.530 - INFO - [Dummy-2:8076] - data_cleaning.py:616 - match_transaction_names() -    - 步骤3（卡号对账号）：29 条
2025-08-04 22:19:14.535 - INFO - [Dummy-2:8076] - data_cleaning.py:617 - match_transaction_names() -    - 步骤4（卡号对卡号）：29 条
2025-08-04 22:19:14.557 - INFO - [Dummy-2:8076] - data_cleaning.py:2184 - start_cleaning() - 清洗步骤 match_transaction_names 完成
2025-08-04 22:19:14.561 - INFO - [Dummy-2:8076] - data_cleaning.py:2169 - start_cleaning() - 开始执行清洗步骤 10/15: match_certificate_numbers
2025-08-04 22:19:14.626 - INFO - [Dummy-2:8076] - data_cleaning.py:636 - match_certificate_numbers() - 开始批量匹配交易证件号码
2025-08-04 22:19:14.626 - INFO - [Dummy-2:8076] - data_cleaning.py:641 - match_certificate_numbers() - 步骤1：交易账号_digits → 开户信息表.交易账号_digits
2025-08-04 22:19:37.058 - INFO - [Dummy-2:8076] - data_cleaning.py:659 - match_certificate_numbers() - 步骤1完成：通过交易账号_digits精确匹配，成功匹配 40025 条记录
2025-08-04 22:19:37.058 - INFO - [Dummy-2:8076] - data_cleaning.py:662 - match_certificate_numbers() - 步骤2：交易账号_digits → 开户信息表.交易卡号_digits
