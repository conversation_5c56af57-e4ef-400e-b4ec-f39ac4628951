2025-08-03 09:51:51,041 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\app_2025-08-03.log
2025-08-03 09:51:51,041 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: G:\数据分析系统20250725\LOGS
2025-08-03 09:51:51,041 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-03 09:53:56,103 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\app_2025-08-03.log
2025-08-03 09:53:56,104 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: G:\数据分析系统20250725\LOGS
2025-08-03 09:53:56,116 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-03 09:53:56,121 - INFO - [test_cash_standardization.py:21] - test_import - ✅ standardize_cash_counterparty_names方法导入成功
2025-08-03 09:53:56,121 - INFO - [test_cash_standardization.py:98] - main - ✅ 导入测试 通过
2025-08-03 09:53:56,121 - INFO - [test_cash_standardization.py:94] - main - 
📋 执行测试: 方法签名测试
2025-08-03 09:53:56,129 - INFO - [test_cash_standardization.py:42] - test_method_signature - ✅ 方法签名正确: ['cursor', 'case_id']
2025-08-03 09:53:56,130 - INFO - [test_cash_standardization.py:98] - main - ✅ 方法签名测试 通过
2025-08-03 09:53:56,130 - INFO - [test_cash_standardization.py:94] - main - 
📋 执行测试: 清洗步骤信息测试
2025-08-03 09:53:56,131 - ERROR - [test_cash_standardization.py:77] - test_cleaning_steps_info - ❌ 测试清洗步骤信息时出错: DataCleaner.__init__() missing 1 required positional argument: 'case_id'
2025-08-03 09:53:56,132 - ERROR - [test_cash_standardization.py:100] - main - ❌ 清洗步骤信息测试 失败
2025-08-03 09:53:56,152 - INFO - [test_cash_standardization.py:104] - main - 
📊 测试结果: 2/3 通过
2025-08-03 09:53:56,154 - ERROR - [test_cash_standardization.py:110] - main - ⚠️ 部分测试失败，请检查代码
2025-08-03 09:54:47,426 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\app_2025-08-03.log
2025-08-03 09:54:47,426 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: G:\数据分析系统20250725\LOGS
2025-08-03 09:54:47,427 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-03 09:54:47,428 - INFO - [test_cash_standardization.py:21] - test_import - ✅ standardize_cash_counterparty_names方法导入成功
2025-08-03 09:54:47,429 - INFO - [test_cash_standardization.py:98] - main - ✅ 导入测试 通过
2025-08-03 09:54:47,429 - INFO - [test_cash_standardization.py:94] - main - 
📋 执行测试: 方法签名测试
2025-08-03 09:54:47,432 - INFO - [test_cash_standardization.py:42] - test_method_signature - ✅ 方法签名正确: ['cursor', 'case_id']
2025-08-03 09:54:47,432 - INFO - [test_cash_standardization.py:98] - main - ✅ 方法签名测试 通过
2025-08-03 09:54:47,433 - INFO - [test_cash_standardization.py:94] - main - 
📋 执行测试: 清洗步骤信息测试
2025-08-03 09:54:47,434 - ERROR - [test_cash_standardization.py:77] - test_cleaning_steps_info - ❌ 测试清洗步骤信息时出错: 'DataCleaner' object has no attribute 'cleaning_steps_info'
2025-08-03 09:54:47,435 - ERROR - [test_cash_standardization.py:100] - main - ❌ 清洗步骤信息测试 失败
2025-08-03 09:54:47,435 - INFO - [test_cash_standardization.py:104] - main - 
📊 测试结果: 2/3 通过
2025-08-03 09:54:47,436 - ERROR - [test_cash_standardization.py:110] - main - ⚠️ 部分测试失败，请检查代码
2025-08-03 09:56:41,425 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: G:\数据分析系统20250725\LOGS\app_2025-08-03.log
2025-08-03 09:56:41,426 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: G:\数据分析系统20250725\LOGS
2025-08-03 09:56:41,427 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-03 09:56:41,432 - INFO - [test_cash_standardization.py:21] - test_import - ✅ standardize_cash_counterparty_names方法导入成功
2025-08-03 09:56:41,433 - INFO - [test_cash_standardization.py:102] - main - ✅ 导入测试 通过
2025-08-03 09:56:41,433 - INFO - [test_cash_standardization.py:98] - main - 
📋 执行测试: 方法签名测试
2025-08-03 09:56:41,435 - INFO - [test_cash_standardization.py:42] - test_method_signature - ✅ 方法签名正确: ['cursor', 'case_id']
2025-08-03 09:56:41,436 - INFO - [test_cash_standardization.py:102] - main - ✅ 方法签名测试 通过
2025-08-03 09:56:41,437 - INFO - [test_cash_standardization.py:98] - main - 
📋 执行测试: 清洗步骤信息测试
2025-08-03 09:56:41,439 - INFO - [test_cash_standardization.py:60] - test_cleaning_steps_info - ✅ 新的清洗步骤已添加到源代码中
2025-08-03 09:56:41,442 - INFO - [test_cash_standardization.py:64] - test_cleaning_steps_info - ✅ 步骤信息正确定义
2025-08-03 09:56:41,443 - INFO - [test_cash_standardization.py:68] - test_cleaning_steps_info - ✅ 执行逻辑已添加
2025-08-03 09:56:41,443 - INFO - [test_cash_standardization.py:102] - main - ✅ 清洗步骤信息测试 通过
2025-08-03 09:56:41,444 - INFO - [test_cash_standardization.py:108] - main - 
📊 测试结果: 3/3 通过
2025-08-03 09:56:41,444 - INFO - [test_cash_standardization.py:111] - main - 🎉 所有测试通过！标准化现金对手户名功能已正确添加
2025-08-03 10:03:39,344 - INFO - [logger_config.py:160] - setup_logger - 日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\app_2025-08-03.log
2025-08-03 10:03:39,344 - INFO - [logger_config.py:161] - setup_logger - 日志文件夹: g:\数据分析系统20250725\LOGS
2025-08-03 10:03:39,344 - INFO - [logger_config.py:162] - setup_logger - 日志保留天数: 7天
2025-08-03 10:03:39,344 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\database_table_checker_20250803_100339.log
2025-08-03 10:03:39,995 - INFO - [logger_config.py:238] - setup_script_logger - 脚本日志系统初始化完成，日志文件: g:\数据分析系统20250725\LOGS\import_data_20250803_100339.log
2025-08-03 10:03:40,107 - INFO - [import_data.py:38] - <module> - ✅ 增强日志功能已加载
