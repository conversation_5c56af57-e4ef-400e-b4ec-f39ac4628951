2025-08-04 14:45:09.862 - INFO - [MainThread:17868] - enhanced_logging_patch.py:84 - setup_enhanced_logging() - 🔍 增强日志记录已启动
2025-08-04 14:45:09.889 - INFO - [MainThread:17868] - memory_optimizer.py:38 - __init__() - ✅ 内存监控模块已加载
2025-08-04 14:45:09.889 - INFO - [MainThread:17868] - memory_optimizer.py:42 - __init__() - 📊 系统内存信息: 总计 31.7GB, 可用 21.5GB, 使用率 32.2%
2025-08-04 14:45:10.050 - INFO - [MainThread:17868] - database_table_checker.py:417 - check_database_tables() - 开始数据库表格完整性检查...
2025-08-04 14:45:10.117 - INFO - [MainThread:17868] - database_table_checker.py:115 - load_excel_table_config() - 成功读取Excel文件，共94行数据
2025-08-04 14:45:10.127 - INFO - [MainThread:17868] - database_table_checker.py:169 - load_excel_table_config() - 从Excel文件解析出94个有效表配置（处理了94行数据）
2025-08-04 14:45:10.127 - INFO - [MainThread:17868] - database_table_checker.py:182 - load_excel_table_config() - Excel文件统计：总行数94，重复表名0个，有效表格94个
2025-08-04 14:45:10.196 - INFO - [MainThread:17868] - database_table_checker.py:444 - check_database_tables() - 数据库中现有表格总数：106
2025-08-04 14:45:10.196 - INFO - [MainThread:17868] - database_table_checker.py:456 - check_database_tables() - 核心系统表检查：9个必需，9个已存在
2025-08-04 14:45:16.048 - INFO - [MainThread:17868] - database_table_checker.py:507 - check_database_tables() - Excel配置表检查：94个定义，94个已存在
2025-08-04 14:45:16.048 - INFO - [MainThread:17868] - database_table_checker.py:511 - check_database_tables() - 所有Excel配置表都已存在，无需创建
2025-08-04 14:45:16.048 - INFO - [MainThread:17868] - database_table_checker.py:541 - check_database_tables() - 所有表都已存在，无需创建任何表
2025-08-04 14:45:16.048 - INFO - [MainThread:17868] - database_table_checker.py:344 - update_table_column_types() - 开始检查并更新表字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_建设用地宅基地' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_房地产权表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_抵押权表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_查封登记表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '不动产查询_不动产全国总库_预告登记表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同乘三次以上同行人' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同订单同行人已成行' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班同行人信息_同订单同行人未成行' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班进出港_航班进出港已成行表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国航空_航班进出港_航班进出港未成行表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券持有_持有信息' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券持有变动_持' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国证券登记结算有限公司_证券账户_证券账户' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_同订单同行人_同行人员信息表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_同订单同行人_同行人员客票' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_用户注册_互联网注册信息表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_用户注册_常用联系人信息表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_铁路客票_交易信息表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '中国铁路总公司_铁路客票_票面信息表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '临时账户交易明细表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_产品信息表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_受益人信息' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_委托人信息' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_登记信息_受益权结构' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_登记信息_合同信息' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_信托产品_终止登记' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_委托人或受益人变动信息表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_登记信息_受益权结构表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_登记信息_合同信息表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '信托登记公司_终止登记表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_交通违法_机动车违章信息表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_出入境记录_出入境记录信息表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_出国_境_证件_出入境证件信息' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同住址_同住址表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同户人_同户人表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_同车违章_同车违章表' 的字段类型...
2025-08-04 14:45:16.260 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃人员_在逃人员登记信息' 的字段类型...
2025-08-04 14:45:16.276 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃同案撤销人员_在逃同案撤销人员' 的字段类型...
2025-08-04 14:45:16.276 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_在逃撤销_在逃人员撤销信息' 的字段类型...
2025-08-04 14:45:16.276 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_户籍人口_基本人员信息表' 的字段类型...
2025-08-04 14:45:16.276 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_旅馆住宿_旅馆住宿人员信息表' 的字段类型...
2025-08-04 14:45:16.276 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_机动车_机动车信息' 的字段类型...
2025-08-04 14:45:16.276 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '公安部_驾驶证_驾驶证信息表' 的字段类型...
2025-08-04 14:45:16.276 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_住院结算数据' 的字段类型...
2025-08-04 14:45:16.277 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_参保信息' 的字段类型...
2025-08-04 14:45:16.277 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_普通门诊' 的字段类型...
2025-08-04 14:45:16.277 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_药店购药' 的字段类型...
2025-08-04 14:45:16.277 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '医保_药店购药明细' 的字段类型...
2025-08-04 14:45:16.277 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '国家税务总局_纳税人登记信息_登记信息表' 的字段类型...
2025-08-04 14:45:16.277 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '国家税务总局_纳税信息_税务缴纳信息表' 的字段类型...
2025-08-04 14:45:16.278 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '增值税发票表' 的字段类型...
2025-08-04 14:45:16.278 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '字段匹配规则' 的字段类型...
2025-08-04 14:45:16.278 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '对手信息' 的字段类型...
2025-08-04 14:45:16.278 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '导入记录表' 的字段类型...
2025-08-04 14:45:16.278 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_主要人员表' 的字段类型...
2025-08-04 14:45:16.278 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_内资补充信息表' 的字段类型...
2025-08-04 14:45:16.279 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_农专补充信息表' 的字段类型...
2025-08-04 14:45:16.279 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_分支机构备案信息表' 的字段类型...
2025-08-04 14:45:16.279 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_变更备案信息表' 的字段类型...
2025-08-04 14:45:16.279 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_吊销信息表' 的字段类型...
2025-08-04 14:45:16.279 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_外资补充信息表' 的字段类型...
2025-08-04 14:45:16.280 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_注销信息表' 的字段类型...
2025-08-04 14:45:16.280 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_清算基本信息表' 的字段类型...
2025-08-04 14:45:16.280 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_清算成员信息表' 的字段类型...
2025-08-04 14:45:16.280 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_联络员信息表' 的字段类型...
2025-08-04 14:45:16.281 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_自然人出资信息表' 的字段类型...
2025-08-04 14:45:16.281 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_许可信息表' 的字段类型...
2025-08-04 14:45:16.281 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_财务负责人信息表' 的字段类型...
2025-08-04 14:45:16.281 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业公示_非自然人出资信息表' 的字段类型...
2025-08-04 14:45:16.282 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_企业登记_企业基本信息表' 的字段类型...
2025-08-04 14:45:16.282 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '市监_统一社会信用代码_统一社会信用代码表' 的字段类型...
2025-08-04 14:45:16.282 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '开户信息表' 的字段类型...
2025-08-04 14:45:16.283 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '本地银行_客户信息本地表' 的字段类型...
2025-08-04 14:45:16.283 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '案件信息表' 的字段类型...
2025-08-04 14:45:16.283 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_投资行业信息表' 的字段类型...
2025-08-04 14:45:16.284 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_持有信息表' 的字段类型...
2025-08-04 14:45:16.284 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '理财登记中心_理财产品_理财产品信息表' 的字段类型...
2025-08-04 14:45:16.284 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '用户信息表' 的字段类型...
2025-08-04 14:45:16.284 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '电话_登记信息_运营商登记信息表' 的字段类型...
2025-08-04 14:45:16.284 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '电话_话单信息_运营商话单信息表' 的字段类型...
2025-08-04 14:45:16.285 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_专票货物或应税劳务名称表' 的字段类型...
2025-08-04 14:45:16.285 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_增值税专用发票表' 的字段类型...
2025-08-04 14:45:16.285 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_增值税普通发票表' 的字段类型...
2025-08-04 14:45:16.285 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '税务_增值税发票_普票货物或应税劳务服务名' 的字段类型...
2025-08-04 14:45:16.285 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '系统信息表' 的字段类型...
2025-08-04 14:45:16.286 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '系统配置表' 的字段类型...
2025-08-04 14:45:16.286 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '虚拟运营商_登记信息_虚拟运营商登记信息表' 的字段类型...
2025-08-04 14:45:16.286 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '表类型匹配规则_导出文件名分类表' 的字段类型...
2025-08-04 14:45:16.286 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '表类型匹配规则表' 的字段类型...
2025-08-04 14:45:16.286 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '证券登记结算_证券持有变动_持' 的字段类型...
2025-08-04 14:45:16.287 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '证券登记结算_证券持有变动_证券持有变动' 的字段类型...
2025-08-04 14:45:16.287 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '财付通交易明细表' 的字段类型...
2025-08-04 14:45:16.287 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户交易明细表' 的字段类型...
2025-08-04 14:45:16.287 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_共有权优先权信息表' 的字段类型...
2025-08-04 14:45:16.287 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_关联子账户信息表' 的字段类型...
2025-08-04 14:45:16.287 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_关联子账户信息表本地' 的字段类型...
2025-08-04 14:45:16.287 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_客户基本信息表' 的字段类型...
2025-08-04 14:45:16.288 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息_强制措施信息表' 的字段类型...
2025-08-04 14:45:16.288 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '账户信息（本地）_优先权信息表' 的字段类型...
2025-08-04 14:45:16.288 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '金融理财_金融理财信息表' 的字段类型...
2025-08-04 14:45:16.288 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '金融理财_金融理财账户信息表' 的字段类型...
2025-08-04 14:45:16.288 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险人员信息表' 的字段类型...
2025-08-04 14:45:16.288 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险保单信息表' 的字段类型...
2025-08-04 14:45:16.289 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_保险赔案信息表' 的字段类型...
2025-08-04 14:45:16.289 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_家庭财产保险表' 的字段类型...
2025-08-04 14:45:16.289 - INFO - [MainThread:17868] - database_table_checker.py:370 - update_table_column_types() - 检查表 '银保信_保险产品_航空延误保险表' 的字段类型...
2025-08-04 14:45:16.289 - INFO - [MainThread:17868] - database_table_checker.py:404 - update_table_column_types() - 字段类型更新完成: 已更新 0 个字段, 跳过 2702 个字段, 失败 0 个字段
2025-08-04 14:45:16.464 - INFO - [MainThread:17868] - database_table_checker.py:580 - check_database_tables() - === 数据库表格检查报告 ===
2025-08-04 14:45:16.464 - INFO - [MainThread:17868] - database_table_checker.py:581 - check_database_tables() - 总计检查表格：103个 (核心9个 + Excel94个)
2025-08-04 14:45:16.464 - INFO - [MainThread:17868] - database_table_checker.py:582 - check_database_tables() - 最终存在表格：103个
2025-08-04 14:45:16.464 - INFO - [MainThread:17868] - database_table_checker.py:589 - check_database_tables() - 本次未创建任何新表格（所有表都已存在）
2025-08-04 14:45:36.168 - INFO - [MainThread:17868] - import_error_handler.py:449 - check_for_previous_crash() - 检测到长时间未使用（28.2小时），清理心跳文件
2025-08-04 14:45:36.177 - INFO - [MainThread:17868] - import_error_handler.py:495 - cleanup_heartbeat() - 心跳文件已清理
2025-08-04 14:45:36.178 - INFO - [MainThread:17868] - import_error_handler.py:523 - start_heartbeat_monitor() - 心跳监控已启动
2025-08-04 14:45:36.189 - INFO - [MainThread:17868] - import_data.py:148 - __init__() - ✅ 自动化管理器已初始化
2025-08-04 14:46:30.304 - INFO - [MainThread:17868] - import_data.py:8551 - preload_table_type_rules() - 预加载了 113 条表类型匹配规则
2025-08-04 14:46:30.336 - DEBUG - [MainThread:17868] - import_data.py:8465 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/20250719YCW/央地汇总/20250731YCW反馈/黔监查〔2025〕4688号-通信/黔监查〔2025〕4688号-通信_按对象/022_杨朝雄_522731196602219012/移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx - 短信记录（本地） (行数: 10, 非空值: 80)
2025-08-04 14:46:30.351 - DEBUG - [MainThread:17868] - import_data.py:8465 - is_worksheet_empty() - ✅ 工作表包含有效数据: G:/SJW/2025线索/20250719YCW/央地汇总/20250731YCW反馈/黔监查〔2025〕4688号-通信/黔监查〔2025〕4688号-通信_按对象/022_杨朝雄_522731196602219012/移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx - 短信记录（本地） (行数: 10, 非空值: 80)
2025-08-04 14:46:30.399 - INFO - [MainThread:17868] - import_data.py:8836 - get_available_table_types() - 成功加载 89 个动态表类型
2025-08-04 14:46:30.399 - DEBUG - [MainThread:17868] - import_data.py:8852 - get_available_table_types() - 可用表类型总数: 95, 列表: ['', '开户信息表', '临时账户交易明细表', '账户交易明细表', '财付通交易明细表', '增值税发票表', '本地银行_客户信息本地表', '不动产查询_不动产全国总库_查封登记表', '不动产查询_不动产全国总库_抵押权表', '不动产查询_不动产全国总库_房地产权表']...
2025-08-04 14:46:30.416 - INFO - [MainThread:17868] - import_data.py:11761 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'移动_短信记录（本地）_022_杨朝雄_522731196602219012_' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-04 14:46:30.416 - INFO - [MainThread:17868] - import_data.py:11767 - on_table_type_changed() - ✓ 手动选择表类型: 工作表'移动_短信记录（本地）_022_杨朝雄_522731196602219012_' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-04 14:46:30.416 - INFO - [MainThread:17868] - import_data.py:8616 - batch_add_worksheets_to_tree() - ✓ 表类型匹配: 工作表'移动_短信记录（本地）_022_杨朝雄_522731196602219012_' → 表类型'电话_话单信息_运营商话单信息表'
2025-08-04 14:46:31.632 - INFO - [MainThread:17868] - import_data.py:11579 - sort_worksheets_by_table_type_match_status() - 🔄 开始按表类型匹配状态排序工作表
2025-08-04 14:46:31.632 - INFO - [MainThread:17868] - import_data.py:11627 - sort_worksheets_by_table_type_match_status() - 📁 文件 '移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx' 下的工作表已按表类型匹配状态排序
2025-08-04 14:46:31.632 - INFO - [MainThread:17868] - import_data.py:11634 - sort_worksheets_by_table_type_match_status() - ✅ 工作表按表类型匹配状态排序完成
2025-08-04 14:46:35.192 - INFO - [MainThread:17868] - import_data.py:5896 - auto_match_fields_by_rules() - 🔧 自动匹配字段按钮被点击，开始执行自动匹配逻辑
2025-08-04 14:46:35.241 - INFO - [MainThread:17868] - import_data.py:6217 - find_best_field_mapping_rule() - 🔍 为表类型 '电话_话单信息_运营商话单信息表' 查找字段映射规则
2025-08-04 14:46:35.241 - INFO - [MainThread:17868] - import_data.py:6218 - find_best_field_mapping_rule() - 📋 工作表字段: ['对象编号', '姓名(查询条件)', '证件号码(查询条件)', '话单类型', '本机号码', '对方号码', '短信发送/接受时间', '本机归属运营商', '本机RAC号', '本机LAC号', '本机cellid', '本机基站ID', '对方归属运营商', '对方号码归属地']
2025-08-04 14:46:35.302 - INFO - [MainThread:17868] - import_data.py:6257 - find_best_field_mapping_rule() - 📋 找到 5 个规则: ['电话_话单信息_运营商话单信息表_20250624_143128', '电话_话单信息_运营商话单信息表_20250702_103928', '电话_话单信息_运营商话单信息表_20250724_133447', '电话_话单信息_运营商话单信息表_20250724_133518', '电话_话单信息_运营商话单信息表_20250724_133544']
2025-08-04 14:46:35.302 - INFO - [MainThread:17868] - import_data.py:6265 - find_best_field_mapping_rule() - 📊 工作表原始字段: ['姓名(查询条件)', '对方号码', '对方号码归属地', '对方归属运营商', '对象编号', '本机LAC号', '本机RAC号', '本机cellid', '本机号码', '本机基站ID', '本机归属运营商', '短信发送/接受时间', '证件号码(查询条件)', '话单类型']
2025-08-04 14:46:35.302 - INFO - [MainThread:17868] - import_data.py:6273 - find_best_field_mapping_rule() - 🔍 检查规则: '电话_话单信息_运营商话单信息表_20250624_143128'
2025-08-04 14:46:35.302 - INFO - [MainThread:17868] - import_data.py:6278 - find_best_field_mapping_rule() - 📋 规则 '电话_话单信息_运营商话单信息表_20250624_143128' 完整待导入字段: ['前转主叫号码', '呼叫开始时间', '呼叫时长', '对方cellid', '对方imei号', '对方imsi号', '对方lac号', '对方rac号', '对方号码', '对方号码归属地', '对方基站id', '对方归属运营商', '对方通话所在地', '序号', '是否群内呼叫', '本机cellid', '本机imei号', '本机imsi号', '本机lac号', '本机rac号', '本机号码', '本机基站id', '本机归属运营商', '本机通话所在地', '短信发送接收时间', '群组名称', '群组编号', '话单类型', '通信记录唯一标识', '通话类型']
2025-08-04 14:46:35.302 - INFO - [MainThread:17868] - import_data.py:6290 - find_best_field_mapping_rule() - ❌ 规则 '电话_话单信息_运营商话单信息表_20250624_143128' 字段数量不匹配:
2025-08-04 14:46:35.302 - INFO - [MainThread:17868] - import_data.py:6291 - find_best_field_mapping_rule() -    工作表字段数: 14
2025-08-04 14:46:35.302 - INFO - [MainThread:17868] - import_data.py:6292 - find_best_field_mapping_rule() -    规则字段数: 30
2025-08-04 14:46:35.302 - INFO - [MainThread:17868] - import_data.py:6273 - find_best_field_mapping_rule() - 🔍 检查规则: '电话_话单信息_运营商话单信息表_20250702_103928'
2025-08-04 14:46:35.302 - INFO - [MainThread:17868] - import_data.py:6278 - find_best_field_mapping_rule() - 📋 规则 '电话_话单信息_运营商话单信息表_20250702_103928' 完整待导入字段: ['前转主叫号码', '呼叫开始时间', '呼叫时长', '对方CELLID', '对方IMEI号', '对方IMSI号', '对方LAC号', '对方RAC号', '对方号码', '对方号码归属地', '对方基站ID', '对方归属运营商', '对方通话所在地', '序号', '是否群内呼叫', '本机CELLID', '本机IMEI号', '本机IMSI号', '本机LAC号', '本机RAC号', '本机号码', '本机基站ID', '本机归属运营商', '本机通话所在地', '短信发送接收时间', '群组名称', '群组编号', '话单类型', '通信记录唯一标识', '通话类型']
2025-08-04 14:46:35.302 - INFO - [MainThread:17868] - import_data.py:6290 - find_best_field_mapping_rule() - ❌ 规则 '电话_话单信息_运营商话单信息表_20250702_103928' 字段数量不匹配:
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6291 - find_best_field_mapping_rule() -    工作表字段数: 14
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6292 - find_best_field_mapping_rule() -    规则字段数: 30
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6273 - find_best_field_mapping_rule() - 🔍 检查规则: '电话_话单信息_运营商话单信息表_20250724_133447'
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6278 - find_best_field_mapping_rule() - 📋 规则 '电话_话单信息_运营商话单信息表_20250724_133447' 完整待导入字段: ['姓名(查询条件)', '对方号码', '对方号码归属地', '对方归属运营商', '对象编号', '本机LAC号', '本机RAC号', '本机cellid', '本机号码', '本机基站ID', '本机归属运营商', '短信发送/接受时间', '证件号码(查询条件)', '话单类型']
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6299 - find_best_field_mapping_rule() - 📊 规则 '电话_话单信息_运营商话单信息表_20250724_133447' 匹配分析:
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6300 - find_best_field_mapping_rule() -    工作表字段数: 14
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6301 - find_best_field_mapping_rule() -    规则字段数: 14
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6302 - find_best_field_mapping_rule() -    匹配字段数: 14
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6303 - find_best_field_mapping_rule() -    匹配度: 100.00%
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6307 - find_best_field_mapping_rule() - ✅ 规则 '电话_话单信息_运营商话单信息表_20250724_133447' 完全匹配 (100%)
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6326 - find_best_field_mapping_rule() - 🎯 最终选择规则: '电话_话单信息_运营商话单信息表_20250724_133447' (完全匹配)
2025-08-04 14:46:35.318 - INFO - [MainThread:17868] - import_data.py:6074 - auto_match_fields_by_rules() - ✅ 工作表 '移动_短信记录（本地）_022_杨朝雄_522731196602219012_' 字段映射匹配成功，使用规则: 电话_话单信息_运营商话单信息表_20250724_133447, 映射字段数: 13
2025-08-04 14:46:36.629 - INFO - [MainThread:17868] - import_data.py:6180 - auto_match_fields_by_rules() - 🔍 检查自动化状态: automation_manager存在，matched_files=1
2025-08-04 14:46:36.629 - INFO - [MainThread:17868] - import_data.py:6187 - auto_match_fields_by_rules() - ⚠️ 自动化未激活，跳过自动导入流程
2025-08-04 14:46:36.629 - INFO - [MainThread:17868] - import_data.py:6193 - auto_match_fields_by_rules() - 💡 提示：字段匹配完成，如需自动导入，请点击【启动自动化】按钮
2025-08-04 14:46:36.629 - INFO - [MainThread:17868] - import_data.py:11517 - sort_worksheets_by_match_status() - 🔄 开始按字段匹配状态排序工作表
2025-08-04 14:46:36.629 - INFO - [MainThread:17868] - import_data.py:11561 - sort_worksheets_by_match_status() - 📁 文件 '移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx' 下的工作表已按匹配状态排序
2025-08-04 14:46:36.629 - INFO - [MainThread:17868] - import_data.py:11568 - sort_worksheets_by_match_status() - ✅ 工作表按字段匹配状态排序完成
2025-08-04 14:46:39.045 - INFO - [MainThread:17868] - import_data.py:6623 - confirm_import() - ✅ 成功读取映射文件，包含 1 个工作表的匹配规则
2025-08-04 14:46:39.051 - INFO - [MainThread:17868] - import_data.py:6642 - confirm_import() - 🚀 开始处理 1 个已匹配的工作表
2025-08-04 14:46:39.051 - INFO - [MainThread:17868] - import_data.py:6670 - confirm_import() - ✅ 处理工作表: 移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx::短信记录（本地） -> 电话_话单信息_运营商话单信息表
2025-08-04 14:46:39.240 - INFO - [MainThread:17868] - import_data.py:6682 - confirm_import() - ✅ 添加到导入队列: 移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx::短信记录（本地） (2238 行数据)
2025-08-04 14:46:39.240 - INFO - [MainThread:17868] - import_data.py:6695 - confirm_import() - 📊 导入准备完成: 可导入 1 个工作表
2025-08-04 14:46:39.240 - INFO - [MainThread:17868] - import_data.py:6696 - confirm_import() - 📊 按表类型分组: 电话_话单信息_运营商话单信息表: 1个
2025-08-04 14:46:39.247 - INFO - [MainThread:17868] - import_data.py:6391 - generate_table_type_mappings() - ✅ 生成表类型专用映射文件: mapp/temp_mapping_电话_话单信息_运营商话单信息表.json (1 个工作表)
2025-08-04 14:46:39.247 - INFO - [MainThread:17868] - import_data.py:6393 - generate_table_type_mappings() - 🎯 成功生成 1 个表类型的专用映射文件
2025-08-04 14:46:39.263 - INFO - [MainThread:17868] - import_data.py:6771 - confirm_import() - ✅ 已为表类型 '电话_话单信息_运营商话单信息表' 创建专用映射文件: mapp/temp_mapping_电话_话单信息_运营商话单信息表.json
2025-08-04 14:46:40.282 - INFO - [MainThread:17868] - import_error_handler.py:319 - start_monitoring() - 进程监控: 开始监控导入过程
2025-08-04 14:46:40.282 - INFO - [MainThread:17868] - import_error_handler.py:299 - update_activity() - 进程监控: 当前操作 - 开始导入过程
2025-08-04 14:46:40.311 - INFO - [MainThread:17868] - import_error_handler.py:299 - update_activity() - 进程监控: 当前操作 - 检查导入表类型
2025-08-04 14:46:40.335 - INFO - [MainThread:17868] - import_data.py:7039 - import_next_table_type() - 🔄 开始导入表类型: 电话_话单信息_运营商话单信息表 (1 个工作表)
2025-08-04 14:46:40.335 - INFO - [MainThread:17868] - import_error_handler.py:299 - update_activity() - 进程监控: 当前操作 - 导入表类型: 电话_话单信息_运营商话单信息表
2025-08-04 14:46:40.335 - INFO - [MainThread:17868] - import_error_handler.py:299 - update_activity() - 进程监控: 当前操作 - 创建导入线程
2025-08-04 14:46:40.358 - INFO - [MainThread:17868] - import_data.py:7085 - import_next_table_type() - 🚀 准备创建导入线程，处理 1 个文件
2025-08-04 14:46:40.437 - INFO - [MainThread:17868] - import_error_handler.py:299 - update_activity() - 进程监控: 当前操作 - 启动导入线程
2025-08-04 14:46:41.442 - INFO - [MainThread:17868] - import_data.py:7123 - delayed_start() - 🎯 正式启动导入线程
2025-08-04 14:46:41.443 - INFO - [MainThread:17868] - import_data.py:7171 - start_timeout_check() - 启动导入超时检查定时器
2025-08-04 14:46:41.443 - INFO - [Dummy-2:23220] - import_data.py:3593 - run() - 使用 1 个工作线程进行并发数据导入
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5362 - process_data_fields() - DataFrame原始列名: ['对象编号', '姓名(查询条件)', '证件号码(查询条件)', '话单类型', '本机号码', '对方号码', '短信发送_接受时间', '本机归属运营商', '本机rac号', '本机lac号', '本机cellid', '本机基站id', '对方归属运营商', '对方号码归属地']
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5363 - process_data_fields() - DataFrame清理后列名: ['对象编号', '姓名(查询条件)', '证件号码(查询条件)', '话单类型', '本机号码', '对方号码', '短信发送_接受时间', '本机归属运营商', '本机rac号', '本机lac号', '本机cellid', '本机基站id', '对方归属运营商', '对方号码归属地']
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '本机cellid' -> '本机cellid'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'本机CELLID' <- 源字段'本机cellid'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '对方号码' -> '对方号码'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'对方号码' <- 源字段'对方号码'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '本机归属运营商' -> '本机归属运营商'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'本机归属运营商' <- 源字段'本机归属运营商'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '本机RAC号' -> '本机rac号'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'本机RAC号' <- 源字段'本机rac号'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '证件号码(查询条件)' -> '证件号码(查询条件)'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'用户身份证号码' <- 源字段'证件号码(查询条件)'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '本机号码' -> '本机号码'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'本机号码' <- 源字段'本机号码'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '话单类型' -> '话单类型'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'话单类型' <- 源字段'话单类型'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '本机基站ID' -> '本机基站id'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'本机基站ID' <- 源字段'本机基站id'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '姓名(查询条件)' -> '姓名(查询条件)'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'用户姓名' <- 源字段'姓名(查询条件)'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '对方号码归属地' -> '对方号码归属地'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'对方号码归属地' <- 源字段'对方号码归属地'
2025-08-04 14:46:41.443 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '对方归属运营商' -> '对方归属运营商'
2025-08-04 14:46:41.457 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'对方归属运营商' <- 源字段'对方归属运营商'
2025-08-04 14:46:41.457 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5390 - process_data_fields() - ✅ 清理匹配成功: '短信发送/接受时间' (清理后: '短信发送_接受时间') -> '短信发送_接受时间'
2025-08-04 14:46:41.457 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'短信发送接收时间' <- 源字段'短信发送_接受时间'
2025-08-04 14:46:41.457 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5379 - process_data_fields() - ✅ 直接匹配成功: '本机LAC号' -> '本机lac号'
2025-08-04 14:46:41.457 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5408 - process_data_fields() - ✅ 字段映射成功: 数据库字段'本机LAC号' <- 源字段'本机lac号'
2025-08-04 14:46:41.457 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:5419 - process_data_fields() - ✅ 字段映射完成: 映射了13/13个字段
2025-08-04 14:46:41.457 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:5420 - process_data_fields() - 映射后的数据框架列: ['本机CELLID', '对方号码', '本机归属运营商', '本机RAC号', '用户身份证号码', '本机号码', '话单类型', '本机基站ID', '用户姓名', '对方号码归属地', '对方归属运营商', '短信发送接收时间', '本机LAC号']
2025-08-04 14:46:41.550 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:3974 - check_table_has_data_source_field() - ✅ 表 '电话_话单信息_运营商话单信息表' 包含数据源文件字段: '数据源文件' (类型: text)
2025-08-04 14:46:41.550 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:3742 - extract_file_info() - 🔍 提取文件信息: 原始文件名='移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx', 去扩展名='移动_短信记录（本地）_022_杨朝雄_522731196602219012_'
2025-08-04 14:46:41.550 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:3753 - extract_file_info() - 🧹 清理后文件名: '移动_短信记录（本地）_022_杨朝雄_522731196602219012'
2025-08-04 14:46:41.550 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:3757 - extract_file_info() - 📋 分割后部分: ['移动', '短信记录（本地）', '022', '杨朝雄', '522731196602219012']
2025-08-04 14:46:41.552 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:3888 - _is_valid_name() - 🔍 姓名验证: '杨朝雄' → 中文:True, 非数字:True, 无特殊字符:True, 非排除词:True → 结果:True
2025-08-04 14:46:41.552 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:3790 - extract_file_info() - ✅ 成功提取个人信息: 姓名='杨朝雄', 身份证='522731196602219012' → '杨朝雄_522731196602219012'
2025-08-04 14:46:41.552 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:3557 - process_file() - ✅ 数据源文件字段 '数据源文件' 是text类型，值: '杨朝雄_522731196602219012'
2025-08-04 14:46:41.552 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:3564 - process_file() - ✅ 已安全填充数据源文件字段 '数据源文件': 杨朝雄_522731196602219012
2025-08-04 14:46:41.552 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:3565 - process_file() - 📄 原始文件名: '移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx' → 提取信息: '杨朝雄_522731196602219012'
2025-08-04 14:46:41.552 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:3566 - process_file() - 📊 数据源文件字段填充完成，DataFrame形状: (2238, 17)
2025-08-04 14:46:41.624 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:4159 - import_to_db() - 📊 接收到已映射DataFrame: 表名=电话_话单信息_运营商话单信息表, 列数=17, 行数=2238
2025-08-04 14:46:41.624 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:4160 - import_to_db() - 📋 DataFrame列名: ['本机CELLID', '对方号码', '本机归属运营商', '本机RAC号', '用户身份证号码', '本机号码', '话单类型', '本机基站ID', '用户姓名', '对方号码归属地', '对方归属运营商', '短信发送接收时间', '本机LAC号', '案件编号', '源文件位置', '导入批次', '数据源文件']
2025-08-04 14:46:41.624 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:4181 - import_to_db() - ✅ 有效列数量: 17, 列名: ['本机CELLID', '对方号码', '本机归属运营商', '本机RAC号', '用户身份证号码', '本机号码', '话单类型', '本机基站ID', '用户姓名', '对方号码归属地', '对方归属运营商', '短信发送接收时间', '本机LAC号', '案件编号', '源文件位置', '导入批次', '数据源文件']
2025-08-04 14:46:41.789 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4557 - _batch_insert() - 🔧 使用专门的清理函数处理数据源文件字段 '数据源文件'
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4588 - _batch_insert() - 准备插入的数据样本 (前2行): [(None, '10086766', '贵州移动', None, '522731196602219012', '8613984442579', '短信接收话单', None, '杨朝雄', None, None, '2025-01-22 14:19:29', None, '20250804144533', 'G:/SJW/2025线索/20250719YCW/央地汇总/20250731YCW反馈/黔监查〔2025〕4688号-通信/黔监查〔2025〕4688号-通信_按对象/022_杨朝雄_522731196602219012/移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx', 1, '杨朝雄_522731196602219012'), (None, '10086766', '贵州移动', None, '522731196602219012', '8613984442579', '短信接收话单', None, '杨朝雄', None, None, '2025-01-22 14:19:29', None, '20250804144533', 'G:/SJW/2025线索/20250719YCW/央地汇总/20250731YCW反馈/黔监查〔2025〕4688号-通信/黔监查〔2025〕4688号-通信_按对象/022_杨朝雄_522731196602219012/移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx', 1, '杨朝雄_522731196602219012')]
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '本机CELLID' 样本值: None (类型: <class 'NoneType'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '对方号码' 样本值: '10086766' (类型: <class 'str'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '本机归属运营商' 样本值: '贵州移动' (类型: <class 'str'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '本机RAC号' 样本值: None (类型: <class 'NoneType'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '用户身份证号码' 样本值: '522731196602219012' (类型: <class 'str'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '本机号码' 样本值: '8613984442579' (类型: <class 'str'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '话单类型' 样本值: '短信接收话单' (类型: <class 'str'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '本机基站ID' 样本值: None (类型: <class 'NoneType'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '用户姓名' 样本值: '杨朝雄' (类型: <class 'str'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '对方号码归属地' 样本值: None (类型: <class 'NoneType'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '对方归属运营商' 样本值: None (类型: <class 'NoneType'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '短信发送接收时间' 样本值: '2025-01-22 14:19:29' (类型: <class 'str'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '本机LAC号' 样本值: None (类型: <class 'NoneType'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '案件编号' 样本值: '20250804144533' (类型: <class 'str'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '源文件位置' 样本值: 'G:/SJW/2025线索/20250719YCW/央地汇总/20250731YCW反馈/黔监查〔2025〕4688号-通信/黔监查〔2025〕4688号-通信_按对象/022_杨朝雄_522731196602219012/移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx' (类型: <class 'str'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '导入批次' 样本值: 1 (类型: <class 'int'>)
2025-08-04 14:46:41.912 - DEBUG - [ThreadPoolExecutor-0_0:22612] - import_data.py:4591 - _batch_insert() - 列 '数据源文件' 样本值: '杨朝雄_522731196602219012' (类型: <class 'str'>)
2025-08-04 14:46:42.010 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:4689 - _batch_insert() - 批量插入成功导入 2238 条记录到表 电话_话单信息_运营商话单信息表
2025-08-04 14:46:42.010 - INFO - [ThreadPoolExecutor-0_0:22612] - import_data.py:3583 - process_file() - 文件 G:/SJW/2025线索/20250719YCW/央地汇总/20250731YCW反馈/黔监查〔2025〕4688号-通信/黔监查〔2025〕4688号-通信_按对象/022_杨朝雄_522731196602219012/移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx 成功导入 2238 条记录
2025-08-04 14:46:42.010 - INFO - [MainThread:17868] - import_data.py:9435 - show_import_result() - 📊 处理导入结果: file_path=G:/SJW/2025线索/20250719YCW/央地汇总/20250731YCW反馈/黔监查〔2025〕4688号-通信/黔监查〔2025〕4688号-通信_按对象/022_杨朝雄_522731196602219012/移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx, count=2238, total=2238
2025-08-04 14:46:42.018 - INFO - [Dummy-2:23220] - import_data.py:3706 - run() - 未检测到临时账户交易明细表，跳过临时数据处理步骤
2025-08-04 14:46:42.018 - DEBUG - [MainThread:17868] - import_data.py:9520 - show_import_result() - 🎯 精确匹配: 文件=移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx, 工作表=短信记录（本地）
2025-08-04 14:46:42.018 - INFO - [MainThread:17868] - import_data.py:9549 - show_import_result() - ✅ 表类型: 电话_话单信息_运营商话单信息表, 工作表: 移动_短信记录（本地）_022_杨朝雄_522731196602219012_, 成功导入: 2238 条记录
2025-08-04 14:46:42.018 - INFO - [Dummy-2:23220] - import_data.py:3714 - run() - 数据导入完成！共导入 2238 条记录，处理了 1/1 个文件
2025-08-04 14:46:42.032 - INFO - [MainThread:17868] - import_data.py:9435 - show_import_result() - 📊 处理导入结果: file_path=导入完成, count=2238, total=2238
2025-08-04 14:46:42.032 - INFO - [MainThread:17868] - import_data.py:9439 - show_import_result() - 🎉 收到导入完成信号，开始最终处理...
2025-08-04 14:46:42.033 - INFO - [MainThread:17868] - import_data.py:9447 - show_import_result() - ✅ 数据导入任务完成！总共导入 2238 条记录
2025-08-04 14:46:42.034 - INFO - [MainThread:17868] - import_data.py:7315 - table_type_import_finished() - ✅ 表类型 '电话_话单信息_运营商话单信息表' 导入完成
2025-08-04 14:46:42.051 - INFO - [MainThread:17868] - import_error_handler.py:299 - update_activity() - 进程监控: 当前操作 - 检查导入表类型
2025-08-04 14:46:42.054 - INFO - [MainThread:17868] - import_error_handler.py:299 - update_activity() - 进程监控: 当前操作 - 所有表类型导入完成
2025-08-04 14:46:42.055 - INFO - [MainThread:17868] - import_error_handler.py:324 - stop_monitoring() - 进程监控: 停止监控导入过程
2025-08-04 14:46:42.055 - INFO - [MainThread:17868] - import_data.py:7181 - stop_timeout_check() - 停止导入超时检查定时器
2025-08-04 14:46:42.055 - INFO - [MainThread:17868] - import_data.py:7709 - all_imports_finished() - 🎉 所有表类型导入完成，开始后续处理
2025-08-04 14:46:42.114 - INFO - [MainThread:17868] - import_data.py:7736 - all_imports_finished() - ✅ 创建交易账卡号索引成功
2025-08-04 14:46:42.114 - INFO - [MainThread:17868] - import_data.py:7743 - all_imports_finished() - ✅ 创建交易账号索引成功
2025-08-04 14:46:42.114 - INFO - [MainThread:17868] - import_data.py:7754 - all_imports_finished() - ✅ 创建案件编号索引成功
2025-08-04 14:46:42.114 - INFO - [MainThread:17868] - import_data.py:7763 - all_imports_finished() - ✅ _digits索引已移动到数据清洗阶段，导入性能优化完成
2025-08-04 14:46:42.116 - WARNING - [MainThread:17868] - import_data.py:7789 - all_imports_finished() - 创建文本搜索索引失败（不影响主流程）: 错误:  文本搜寻配置 "jiebacfg" 不存在
LINE 3: ...          ON 账户交易明细表 USING gin(to_tsvector('jiebacfg'...
                                                             ^

2025-08-04 14:46:42.117 - ERROR - [MainThread:17868] - import_data.py:7806 - all_imports_finished() - 创建日期金额索引失败: 错误:  当前事务被终止, 事务块结束之前的查询被忽略

2025-08-04 14:46:42.117 - ERROR - [MainThread:17868] - import_data.py:7815 - all_imports_finished() - ❌ 创建索引时发生错误: 错误:  当前事务被终止, 事务块结束之前的查询被忽略

2025-08-04 14:46:42.117 - INFO - [MainThread:17868] - import_data.py:7819 - all_imports_finished() - 🔄 已回滚数据库事务
2025-08-04 14:46:42.118 - INFO - [MainThread:17868] - import_data.py:7826 - all_imports_finished() - 🔌 数据库连接已关闭
2025-08-04 14:46:42.120 - INFO - [MainThread:17868] - import_data.py:7900 - clear_temp_mapping_files() - ✅ 删除mapp临时文件: mapp\temp_mapping.json
2025-08-04 14:46:42.121 - INFO - [MainThread:17868] - import_data.py:7900 - clear_temp_mapping_files() - ✅ 删除mapp临时文件: mapp\temp_mapping_电话_话单信息_运营商话单信息表.json
2025-08-04 14:46:42.122 - INFO - [MainThread:17868] - import_data.py:7915 - clear_temp_mapping_files() - ✅ 创建主临时文件: mapp/temp_mapping.json
2025-08-04 14:46:42.130 - INFO - [MainThread:17868] - import_data.py:7928 - clear_temp_mapping_files() - ✅ 临时文件清理完成，共清理 2 个文件
2025-08-04 14:46:43.285 - INFO - [MainThread:17868] - import_data.py:7900 - clear_temp_mapping_files() - ✅ 删除mapp临时文件: mapp\temp_mapping.json
2025-08-04 14:46:43.285 - INFO - [MainThread:17868] - import_data.py:7915 - clear_temp_mapping_files() - ✅ 创建主临时文件: mapp/temp_mapping.json
2025-08-04 14:46:43.293 - INFO - [MainThread:17868] - import_data.py:7928 - clear_temp_mapping_files() - ✅ 临时文件清理完成，共清理 1 个文件
2025-08-04 14:46:43.304 - INFO - [MainThread:17868] - import_data.py:9600 - show_import_result() - 📊 实时导入反馈: 文件=移动_短信记录（本地）_022_杨朝雄_522731196602219012_.xlsx, 成功导入=2238条, 累计导入=2238条
2025-08-04 14:46:43.304 - INFO - [MainThread:17868] - import_data.py:9606 - show_import_result() - ✅ show_import_result函数执行完成，程序继续运行
2025-08-04 15:04:17.410 - INFO - [MainThread:17868] - import_data.py:5519 - closeEvent() - 数据导入窗口正在关闭
2025-08-04 15:04:17.410 - INFO - [MainThread:17868] - import_error_handler.py:324 - stop_monitoring() - 进程监控: 停止监控导入过程
2025-08-04 15:04:17.413 - INFO - [MainThread:17868] - import_error_handler.py:495 - cleanup_heartbeat() - 心跳文件已清理
2025-08-04 15:04:17.413 - INFO - [MainThread:17868] - import_data.py:5548 - closeEvent() - 数据导入窗口关闭完成
2025-08-04 15:19:05.028 - CRITICAL - [MainThread:17868] - enhanced_logging_patch.py:322 - monitored_exit() - 🚨 程序即将退出! 退出码: 0
2025-08-04 15:19:05.029 - CRITICAL - [MainThread:17868] - enhanced_logging_patch.py:323 - monitored_exit() - 退出时的堆栈跟踪:
2025-08-04 15:19:05.031 - CRITICAL - [MainThread:17868] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\main.py", line 327, in <module>
    main()
2025-08-04 15:19:05.031 - CRITICAL - [MainThread:17868] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\main.py", line 324, in main
    sys.exit(app.exec())
2025-08-04 15:19:05.031 - CRITICAL - [MainThread:17868] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\enhanced_logging_patch.py", line 326, in monitored_exit
    stack = traceback.format_stack()
2025-08-04 15:19:05.032 - INFO - [MainThread:17868] - enhanced_logging_patch.py:155 - log_memory_usage() - 💾 内存使用 程序退出时: 进程=302.2MB, 系统可用=19249.9MB, 系统使用率=40.8%
2025-08-04 15:19:05.032 - DEBUG - [MainThread:17868] - enhanced_logging_patch.py:210 - log_thread_status() - 🧵 当前线程: MainThread (ID: 17868)
2025-08-04 15:19:05.032 - DEBUG - [MainThread:17868] - enhanced_logging_patch.py:211 - log_thread_status() - 🧵 活跃线程数: 3
2025-08-04 15:19:05.032 - DEBUG - [MainThread:17868] - enhanced_logging_patch.py:215 - log_thread_status() -    - MainThread: 运行中
2025-08-04 15:19:05.032 - DEBUG - [MainThread:17868] - enhanced_logging_patch.py:215 - log_thread_status() -    - Thread-1 (heartbeat_worker): 运行中
2025-08-04 15:19:05.032 - DEBUG - [MainThread:17868] - enhanced_logging_patch.py:215 - log_thread_status() -    - Dummy-2: 运行中
