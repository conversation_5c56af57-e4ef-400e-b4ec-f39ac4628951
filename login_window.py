import os
import sys
from datetime import datetime
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QIcon, QPixmap
from PySide6.QtWidgets import QApplication, QMainWindow, QLabel, QLineEdit, QPushButton, QVBoxLayout, QHBoxLayout, \
    QWidget, QMessageBox, QInputDialog
from cryptography.fernet import Fernet
from database_setup import get_db_connection, DATABASE_PATH  # 导入PostgreSQL连接函数

# 加载加密密钥
ENCRYPTION_KEY = b'2j8RXEaE7UviPO9i1_tnJ822zlkJNQAfJk6zlmKNAuk='
cipher_suite = Fernet(ENCRYPTION_KEY)

class LoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle('登录界面')
        self.setFixedSize(800, 400)
        self.setWindowIcon(QIcon('gui/images/icons/icon.ico'))
        self.setStyleSheet("background-color: #2C313C; color: white;")

        # 主布局
        main_layout = QHBoxLayout()

        # 图片部分
        icon_path = os.path.join(os.path.dirname(__file__), 'gui/images/icons/login.png')
        self.image_label = QLabel(self)
        self.image_label.setPixmap(QPixmap(icon_path))
        self.image_label.setScaledContents(True)
        main_layout.addWidget(self.image_label, 1)

        # 登录表单部分
        form_layout = QVBoxLayout()
        form_layout.setAlignment(Qt.AlignCenter)

        # 欢迎信息
        welcome_label = QLabel('欢迎使用资金分析系统')
        welcome_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        welcome_label.setAlignment(Qt.AlignCenter)

        self.username_label = QLabel('用户名:')
        self.username_label.setFont(QFont("Microsoft YaHei", 12))
        self.username_input = QLineEdit()
        self.username_input.setFont(QFont("Microsoft YaHei", 12))
        self.username_input.setText("test")  # 默认用户名

        self.password_label = QLabel('密码:')
        self.password_label.setFont(QFont("Microsoft YaHei", 12))
        self.password_input = QLineEdit()
        self.password_input.setFont(QFont("Microsoft YaHei", 12))
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("test123")  # 默认密码

        self.login_button = QPushButton('登录')
        self.login_button.setFont(QFont("Microsoft YaHei", 12))
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #44475a;
                color: white;
                border-radius: 10px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #4f5368;
            }
            QPushButton:pressed {
                background-color: #282a36;
            }
        """)
        self.login_button.clicked.connect(self.login)

        self.exit_button = QPushButton('退出')
        self.exit_button.setFont(QFont("Microsoft YaHei", 12))
        self.exit_button.setStyleSheet("""
            QPushButton {
                background-color: #44475a;
                color: white;
                border-radius: 10px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #4f5368;
            }
            QPushButton:pressed {
                background-color: #282a36;
            }
        """)
        self.exit_button.clicked.connect(self.exit_program)

        # 数据库配置按钮
        self.db_config_button = QPushButton('数据库配置')
        self.db_config_button.setFont(QFont("Microsoft YaHei", 12))
        self.db_config_button.setStyleSheet("""
            QPushButton {
                background-color: #6272a4;
                color: white;
                border-radius: 10px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #8be9fd;
                color: #2C313C;
            }
            QPushButton:pressed {
                background-color: #50fa7b;
                color: #2C313C;
            }
        """)
        self.db_config_button.clicked.connect(self.show_database_config)

        form_layout.addWidget(welcome_label)
        form_layout.addWidget(self.username_label)
        form_layout.addWidget(self.username_input)
        form_layout.addWidget(self.password_label)
        form_layout.addWidget(self.password_input)
        form_layout.addWidget(self.login_button)
        form_layout.addWidget(self.db_config_button)
        form_layout.addWidget(self.exit_button)

        # 版本和作者信息
        version_label = QLabel('软件版本: 1.3.0测试版')
        version_label.setFont(QFont("Microsoft YaHei", 10))
        version_label.setAlignment(Qt.AlignCenter)
        author_label = QLabel('作者: 云岩王森')
        author_label.setFont(QFont("Microsoft YaHei", 10))
        author_label.setAlignment(Qt.AlignCenter)

        form_layout.addStretch()
        form_layout.addWidget(version_label)
        form_layout.addWidget(author_label)

        main_layout.addLayout(form_layout, 1)

        container = QWidget()
        container.setLayout(main_layout)
        self.setCentralWidget(container)

    def login(self):
        username = self.username_input.text()
        password = self.password_input.text()

        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM 用户信息表 WHERE 用户名 = %s", (username,))
            user = cursor.fetchone()

            if user:
                stored_password = cipher_suite.decrypt(user[1].encode()).decode()
                if password == stored_password:
                    if username == 'admin':
                        self.prompt_reset_trial_period()
                    else:
                        self.check_trial_period(username)
                else:
                    QMessageBox.warning(self, '错误', '用户名或密码错误')
            else:
                QMessageBox.warning(self, '错误', '用户名或密码错误')

            conn.close()
        except Exception as e:
            QMessageBox.critical(self, '错误', f"数据库错误: {e}")

    def prompt_reset_trial_period(self):
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT 配置值 FROM 系统配置表 WHERE 配置项 = 'reset_password'")
            encrypted_reset_password = cursor.fetchone()[0]
            reset_password = cipher_suite.decrypt(encrypted_reset_password.encode()).decode()
            conn.close()

            input_password, ok = QInputDialog.getText(self, '重置试用期', '请输入重置密码:', QLineEdit.Password)
            if ok and input_password == reset_password:
                self.reset_trial_period()
            else:
                QMessageBox.warning(self, '错误', '重置密码错误')
        except Exception as e:
            QMessageBox.critical(self, '错误', f"数据库错误: {e}")

    def reset_trial_period(self):
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            new_install_date = datetime.now().strftime('%Y-%m-%d')
            cursor.execute("UPDATE 系统信息表 SET 安装日期 = %s", (new_install_date,))
            conn.commit()
            conn.close()
            QMessageBox.information(self, '成功', '试用期已重置为365天')
            self.exit_program()
        except Exception as e:
            QMessageBox.critical(self, '错误', f"数据库错误: {e}")

    def check_trial_period(self, username):
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT 安装日期 FROM 系统信息表")
            install_date_str = cursor.fetchone()[0]
            install_date = datetime.strptime(install_date_str, '%Y-%m-%d')
            trial_period_days = 365  # 修改为365天
            current_date = datetime.now()
            days_remaining = trial_period_days - (current_date - install_date).days

            if days_remaining > 0:
                QMessageBox.information(self, '成功', f'登录成功！本系统为测试版，还剩 {days_remaining} 天。')
                self.open_main_window(username)
            else:
                QMessageBox.warning(self, '试用期结束', '本系统的试用期已结束，请联系管理员。')
        except Exception as e:
            QMessageBox.critical(self, '错误', f"数据库错误: {e}")

    def open_main_window(self, username):
        from main import MainWindow  # 在这里导入MainWindow类
        self.main_window = MainWindow()
        self.main_window.show()
        self.close()

    def show_database_config(self):
        """显示数据库配置对话框（带密码保护）"""
        try:
            from database_config import show_database_config_dialog
            result = show_database_config_dialog(self)
            if result:  # 配置保存成功
                QMessageBox.information(self, "配置完成", 
                    "数据库配置已更新！\n请重新启动系统以使配置生效。")
        except ImportError as e:
            QMessageBox.critical(self, "模块错误", f"无法加载数据库配置模块：{e}")
        except Exception as e:
            QMessageBox.critical(self, "配置错误", f"数据库配置过程中发生错误：{e}")

    def exit_program(self):
        QApplication.quit()


if __name__ == '__main__':
    app = QApplication(sys.argv)
    login_window = LoginWindow()
    login_window.show()
    sys.exit(app.exec())
