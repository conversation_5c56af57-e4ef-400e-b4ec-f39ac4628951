
def all_imports_finished_optimized(self):
    """
    优化版本的导入完成处理
    - 移除_digits索引创建
    - 只创建必要的基础索引
    - 将复杂索引延迟到数据清洗阶段
    """
    try:
        # 创建基础索引（只创建必要的）
        conn = get_db_connection()
        cursor = conn.cursor()
        
        self.progress_dialog.update_status("正在创建基础索引...")
        
        # 只创建最基础的索引，不包括_digits索引
        try:
            cursor.execute("""
                -- 只创建基础的账号索引
                CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_jiaoyi_zhangkahao 
                ON 账户交易明细表 (交易账卡号) 
                WHERE 交易账卡号 IS NOT NULL AND 交易账卡号 != '';
                
                CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_jiaoyi_zhanghao 
                ON 账户交易明细表 (交易账号) 
                WHERE 交易账号 IS NOT NULL AND 交易账号 != '';
                
                -- 案件编号索引（用于数据清洗）
                CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_case_id
                ON 账户交易明细表 (案件编号);
            """)
            logging.info("✅ 创建基础索引成功")
            
        except Exception as e:
            logging.error(f"创建基础索引失败: {e}")
        
        # 不创建_digits索引，留到数据清洗阶段
        # 不创建文本搜索索引，留到需要时创建
        # 不创建复杂的组合索引
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logging.info("✅ 导入完成，基础索引创建完毕")
        
        # 更新进度
        self.progress_dialog.update_status("数据导入完成")
        
        # 显示导入完成对话框
        self.show_import_completion_dialog()
        
    except Exception as e:
        logging.error(f"❌ 导入完成处理时发生错误: {e}")
    finally:
        # 清理资源
        self.cleanup_import_resources()

def create_digits_indexes_in_cleaning():
    """
    在数据清洗阶段创建_digits索引
    这个函数应该在数据清洗开始时调用
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        logging.info("🔧 开始创建_digits字段索引...")
        
        # 创建_digits索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_jyzhdigits 
            ON 账户交易明细表 (交易账号_digits) 
            WHERE 交易账号_digits IS NOT NULL AND 交易账号_digits != '';
            
            CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_jykahdigits 
            ON 账户交易明细表 (交易账卡号_digits) 
            WHERE 交易账卡号_digits IS NOT NULL AND 交易账卡号_digits != '';
            
            CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_dshzhdigits 
            ON 账户交易明细表 (对手账号_digits) 
            WHERE 对手账号_digits IS NOT NULL AND 对手账号_digits != '';
            
            CREATE INDEX IF NOT EXISTS idx_jiaoyimingxi_dskahdigits 
            ON 账户交易明细表 (对手卡号_digits) 
            WHERE 对手卡号_digits IS NOT NULL AND 对手卡号_digits != '';
        """)
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logging.info("✅ _digits索引创建完成")
        return True
        
    except Exception as e:
        logging.error(f"❌ 创建_digits索引失败: {e}")
        return False

def check_data_volume_before_index_creation(table_name, threshold=1000):
    """
    检查数据量，决定是否需要创建索引
    小数据量时不创建复杂索引
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
        count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        logging.info(f"表 {table_name} 当前记录数: {count}")
        
        if count < threshold:
            logging.info(f"数据量较小({count} < {threshold})，跳过复杂索引创建")
            return False
        else:
            logging.info(f"数据量较大({count} >= {threshold})，需要创建索引")
            return True
            
    except Exception as e:
        logging.error(f"检查数据量失败: {e}")
        return False
