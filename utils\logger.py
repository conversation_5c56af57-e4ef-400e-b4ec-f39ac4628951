import os
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime

def setup_logger(name=None, log_level=logging.INFO, log_dir="logs"):
    """
    设置日志记录器
    
    参数:
        name: 日志记录器名称，默认为None使用根记录器
        log_level: 日志级别，默认为INFO
        log_dir: 日志目录，默认为"logs"
        
    返回:
        配置好的日志记录器
    """
    # 获取日志记录器
    logger = logging.getLogger(name)
    
    # 如果已经配置过处理器，则直接返回
    if logger.handlers:
        return logger
        
    # 设置日志级别
    logger.setLevel(log_level)
    
    # 创建日志目录
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
        
    # 创建日志文件名（按日期）
    log_file = os.path.join(log_dir, f"app_{datetime.now().strftime('%Y-%m-%d')}.log")
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    
    # 创建文件处理器（滚动日志文件）
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding="utf-8"
    )
    file_handler.setLevel(log_level)
    
    # 创建日志格式
    formatter = logging.Formatter(
        "%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(funcName)s - %(message)s"
    )
    
    # 设置处理器格式
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)
    
    # 添加处理器到记录器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    # 记录初始化日志
    logger.info(f"日志系统初始化完成，日志文件: {os.path.abspath(log_file)}")
    
    return logger 