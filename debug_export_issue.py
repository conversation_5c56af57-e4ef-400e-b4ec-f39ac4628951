#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试导出问题

本文件的功能和实现逻辑：
1. 检查规则文件是否正确读取
2. 测试导出映射构建过程
3. 诊断导出功能为什么没有按分类进行
"""

import pandas as pd
import os
import logging
from datetime import datetime

# 设置日志
log_file = f'debug_export_issue_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_rule_file_reading():
    """测试规则文件读取"""
    logging.info("🔍 测试规则文件读取...")
    
    rule_path = '表类型匹配规则_导出文件名分类.xlsx'
    
    try:
        # 检查文件是否存在
        if not os.path.exists(rule_path):
            logging.error(f"❌ 规则文件不存在: {rule_path}")
            return False
        
        logging.info(f"✅ 规则文件存在: {rule_path}")
        
        # 读取文件
        rule_df = pd.read_excel(rule_path)
        logging.info(f"✅ 成功读取规则文件，共 {len(rule_df)} 行")
        logging.info(f"📊 列名: {rule_df.columns.tolist()}")
        
        # 显示前几行
        logging.info("📋 前5行数据:")
        for i, row in rule_df.head().iterrows():
            logging.info(f"   {i+1}: {row['数据库表名']} → {row['工作表名']} → {row['导出文件名']}")
        
        # 统计分类
        categories = rule_df['导出文件名'].value_counts()
        logging.info(f"📈 分类统计 (共{len(categories)}个分类):")
        for category, count in categories.items():
            logging.info(f"   {category}: {count}个表")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 读取规则文件失败: {e}")
        return False

def test_export_mapping_build():
    """测试导出映射构建"""
    logging.info("\n🔍 测试导出映射构建...")
    
    try:
        # 模拟CategoryExportWorker的映射构建过程
        rule_path = '表类型匹配规则_导出文件名分类.xlsx'
        rule_df = pd.read_excel(rule_path)
        
        # 查找列名
        db_col, ws_col, file_col = None, None, None
        for c in rule_df.columns:
            if '数据库表' in c:
                db_col = c
                logging.info(f"找到数据库表列: {c}")
            if '工作表' in c:
                ws_col = c
                logging.info(f"找到工作表列: {c}")
            if '导出文件' in c:
                file_col = c
                logging.info(f"找到导出文件列: {c}")
        
        if not (db_col and ws_col and file_col):
            missing = []
            if not db_col: missing.append('数据库表相关列')
            if not ws_col: missing.append('工作表相关列')
            if not file_col: missing.append('导出文件相关列')
            logging.error(f'❌ 规则表缺少必要列: {missing}')
            return False
        
        # 构建导出映射
        export_map = {}
        for _, row in rule_df.iterrows():
            try:
                db = str(row[db_col]).strip() if pd.notna(row[db_col]) else ""
                ws = str(row[ws_col]).strip() if pd.notna(row[ws_col]) else ""
                fn = str(row[file_col]).strip() if pd.notna(row[file_col]) else ""
                
                if not (db and ws and fn) or db == 'nan' or ws == 'nan' or fn == 'nan':
                    continue
                
                export_map.setdefault(fn, {}).setdefault(ws, []).append(db)
            except Exception as e:
                logging.error(f"处理规则行时出错: {e}")
                continue
        
        logging.info(f"✅ 成功构建导出映射，共 {len(export_map)} 个分类")
        
        # 显示映射结构
        for category, worksheets in export_map.items():
            worksheet_count = len(worksheets)
            table_count = sum(len(tables) for tables in worksheets.values())
            logging.info(f"📁 {category}: {worksheet_count}个工作表, {table_count}个表")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 构建导出映射失败: {e}")
        return False

def test_export_function_call():
    """测试导出函数调用"""
    logging.info("\n🔍 测试导出函数调用...")
    
    try:
        # 检查data_cleaning.py中的export_case_data函数
        with open('data_cleaning.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'def export_case_data(' in content:
            logging.info("✅ export_case_data函数存在")
            
            # 检查是否调用了正确的导出函数
            if 'from pivot_export import export_data_by_category' in content:
                logging.info("✅ 正确导入了export_data_by_category")
            else:
                logging.error("❌ 未导入export_data_by_category")
                return False
            
            if 'export_data_by_category(case_id, case_name)' in content:
                logging.info("✅ 正确调用了export_data_by_category")
            else:
                logging.error("❌ 未调用export_data_by_category")
                return False
        else:
            logging.error("❌ export_case_data函数不存在")
            return False
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 检查导出函数调用失败: {e}")
        return False

def test_main_window_export():
    """测试主窗口导出按钮"""
    logging.info("\n🔍 测试主窗口导出按钮...")
    
    try:
        # 检查main.py中的导出按钮连接
        with open('main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        if 'self.ui.ui_pages.btn_export_data.clicked.connect(self.export_case_data)' in main_content:
            logging.info("✅ 主窗口导出按钮已连接")
        else:
            logging.error("❌ 主窗口导出按钮未连接")
            return False
        
        if 'def export_case_data(self):' in main_content:
            logging.info("✅ 主窗口export_case_data方法存在")
        else:
            logging.error("❌ 主窗口export_case_data方法不存在")
            return False
        
        # 检查UI中的导出按钮
        with open('gui/pages/ui_pages.py', 'r', encoding='utf-8') as f:
            ui_content = f.read()
        
        if 'self.btn_export_data = QPushButton("导出数据")' in ui_content:
            logging.info("✅ UI中导出按钮已定义")
        else:
            logging.error("❌ UI中导出按钮未定义")
            return False
        
        if 'self.button_layout.addWidget(self.btn_export_data, 2, 1)' in ui_content:
            logging.info("✅ UI中导出按钮已添加到布局")
        else:
            logging.error("❌ UI中导出按钮未添加到布局")
            return False
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 检查主窗口导出按钮失败: {e}")
        return False

def create_simple_test():
    """创建简单的导出测试"""
    logging.info("\n📝 创建简单的导出测试...")
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的导出测试

直接调用导出功能进行测试
"""

def test_direct_export():
    """直接测试导出功能"""
    try:
        from data_cleaning import export_case_data
        
        # 使用测试案件ID
        case_id = "20250716182021"  # 请替换为实际的案件ID
        case_name = "测试案件"
        
        print(f"🚀 开始测试导出案件: {case_id}")
        export_case_data(case_id, case_name)
        print("✅ 导出测试完成")
        
    except Exception as e:
        print(f"❌ 导出测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_direct_export()
'''
    
    try:
        with open('test_direct_export.py', 'w', encoding='utf-8') as f:
            f.write(test_script)
        logging.info("✅ 简单导出测试脚本创建完成")
        return True
    except Exception as e:
        logging.error(f"❌ 创建测试脚本失败: {e}")
        return False

def main():
    """主函数"""
    print(f"🔍 开始调试导出问题")
    print(f"📄 详细日志保存到: {log_file}")
    
    success = True
    
    # 测试1: 规则文件读取
    logging.info("=" * 60)
    logging.info("测试1: 规则文件读取")
    logging.info("=" * 60)
    if not test_rule_file_reading():
        success = False
    
    # 测试2: 导出映射构建
    logging.info("=" * 60)
    logging.info("测试2: 导出映射构建")
    logging.info("=" * 60)
    if not test_export_mapping_build():
        success = False
    
    # 测试3: 导出函数调用
    logging.info("=" * 60)
    logging.info("测试3: 导出函数调用")
    logging.info("=" * 60)
    if not test_export_function_call():
        success = False
    
    # 测试4: 主窗口导出按钮
    logging.info("=" * 60)
    logging.info("测试4: 主窗口导出按钮")
    logging.info("=" * 60)
    if not test_main_window_export():
        success = False
    
    # 创建测试脚本
    logging.info("=" * 60)
    logging.info("创建测试脚本")
    logging.info("=" * 60)
    create_simple_test()
    
    # 总结
    print("\\n" + "=" * 60)
    print("🎯 调试结果")
    print("=" * 60)
    
    if success:
        print("✅ 所有组件检查通过！")
        print("\\n💡 可能的问题:")
        print("1. 用户没有选择案件就点击导出")
        print("2. 数据库中没有对应的表数据")
        print("3. 导出过程中发生了未捕获的错误")
        
        print("\\n🧪 建议测试:")
        print("1. 运行 python test_direct_export.py")
        print("2. 检查导出目录是否生成了分类文件")
        print("3. 查看程序运行日志了解详细错误")
        
        return 0
    else:
        print("❌ 发现问题，请根据日志修复")
        return 1

if __name__ == "__main__":
    exit(main())
