2025-08-05 14:44:29.240 - ERROR - [MainThread:9448] - test_enhanced_logging.py:57 - test_different_log_levels() - 这是一条ERROR日志
2025-08-05 14:44:29.241 - CRITICAL - [MainThread:9448] - test_enhanced_logging.py:58 - test_different_log_levels() - 这是一条CRITICAL日志
2025-08-05 14:44:29.241 - ERROR - [MainThread:9448] - test_enhanced_logging.py:63 - test_different_log_levels() - 这是导入过程错误日志
2025-08-05 14:44:29.241 - ERROR - [MainThread:9448] - test_enhanced_logging.py:66 - test_different_log_levels() - 这是错误跟踪日志
2025-08-05 14:44:29.242 - CRITICAL - [MainThread:9448] - test_enhanced_logging.py:67 - test_different_log_levels() - 这是错误跟踪严重日志
2025-08-05 14:44:29.247 - ERROR - [MainThread:9448] - test_enhanced_logging.py:88 - test_exception_handling() - 捕获到异常: 这是一个测试异常
2025-08-05 14:44:29.250 - ERROR - [MainThread:9448] - test_enhanced_logging.py:93 - test_exception_handling() - 异常详情: Traceback (most recent call last):
  File "G:\数据分析系统20250725\test_enhanced_logging.py", line 86, in test_exception_handling
    cause_exception()
    ~~~~~~~~~~~~~~~^^
  File "G:\数据分析系统20250725\test_enhanced_logging.py", line 83, in cause_exception
    raise ValueError("这是一个测试异常")
ValueError: 这是一个测试异常

2025-08-05 14:44:29.603 - CRITICAL - [MainThread:9448] - test_enhanced_logging.py:215 - simulate_crash_scenario() - 🚨 模拟程序崩溃: {'exception_type': 'MemoryError', 'exception_message': '内存不足', 'timestamp': '2025-08-05T14:44:29.603490', 'thread_id': 12345, 'process_id': 26856}
2025-08-05 14:44:29.618 - CRITICAL - [MainThread:9448] - enhanced_logging_patch.py:322 - monitored_exit() - 🚨 程序即将退出! 退出码: 0
2025-08-05 14:44:29.619 - CRITICAL - [MainThread:9448] - enhanced_logging_patch.py:323 - monitored_exit() - 退出时的堆栈跟踪:
2025-08-05 14:44:29.620 - CRITICAL - [MainThread:9448] - enhanced_logging_patch.py:328 - monitored_exit() -   File "G:\数据分析系统20250725\test_enhanced_logging.py", line 293, in <module>
    sys.exit(0 if success else 1)
2025-08-05 14:44:29.621 - CRITICAL - [MainThread:9448] - enhanced_logging_patch.py:328 - monitored_exit() -   File "G:\数据分析系统20250725\enhanced_logging_patch.py", line 326, in monitored_exit
    stack = traceback.format_stack()
