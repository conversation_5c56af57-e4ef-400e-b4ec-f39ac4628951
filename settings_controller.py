from PySide6.QtWidgets import QApplication

class SettingsController:
    def __init__(self, main_window, theme_button):
        self.main_window = main_window
        self.theme_button = theme_button
        self.is_dark_theme = True

    def toggle_theme(self):
        if self.is_dark_theme:
            self.apply_light_theme()
            self.theme_button.setText("关灯")
        else:
            self.apply_dark_theme()
            self.theme_button.setText("开灯")
        self.is_dark_theme = not self.is_dark_theme

    def apply_light_theme(self):
        # 设置白色背景和黑色字体
        self.main_window.setStyleSheet("""
            QWidget {
                background-color: white;
                color: black;
            }
            QFrame {
                background-color: white;
                color: black;
            }
            QPushButton {
                background-color: #f0f0f0;
                color: black;
                border: 2px solid #c3ccdf;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            QScrollArea {
                background-color: white;
            }
        """)

    def apply_dark_theme(self):
        # 恢复为深色主题
        self.main_window.setStyleSheet("""
            QWidget {
                background-color: #282a36;
                color: #f8f8f2;
            }
            QFrame {
                background-color: #282a36;
                color: #f8f8f2;
            }
            QPushButton {
                background-color: #44475a;
                color: #f8f8f2;
                border: 2px solid #c3ccdf;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #4f5368;
            }
            QPushButton:pressed {
                background-color: #282a36;
            }
            QScrollArea {
                background-color: #282a36;
            }
        """)

    def exit_application(self):
        """
        安全退出应用程序

        功能说明：
        - 本文件的功能和实现逻辑：确保Qt资源正确释放，避免程序意外退出
        - 清理所有Qt资源和线程
        - 强制处理待处理的事件
        - 安全退出应用程序
        """
        try:
            from PySide6.QtWidgets import QApplication
            from PySide6.QtCore import QTimer
            import gc
            import logging

            # 获取日志记录器
            logger = logging.getLogger(__name__)
            logger.info("开始安全退出应用程序...")

            # 🔧 修复：强制处理所有待处理的Qt事件
            app = QApplication.instance()
            if app:
                app.processEvents()

                # 🔧 修复：延迟退出，确保所有资源清理完成
                def delayed_quit():
                    try:
                        # 强制垃圾回收
                        gc.collect()
                        logger.info("✅ 应用程序安全退出")
                        app.quit()
                    except Exception as e:
                        logger.error(f"延迟退出时发生错误: {e}")
                        app.quit()

                # 延迟500ms退出，确保所有清理操作完成
                QTimer.singleShot(500, delayed_quit)
            else:
                logger.warning("未找到QApplication实例")

        except Exception as e:
            # 如果安全退出失败，使用原始方法
            print(f"安全退出失败，使用原始退出方法: {e}")
            QApplication.instance().quit()
