#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析临时表到正式表的转存过程

本文件的功能和实现逻辑：
1. 分析临时账户交易明细表到账户交易明细表的转存逻辑
2. 检查字段映射在转存过程中是否出现错误
3. 验证IP地址、MAC地址、交易发生地字段的转存过程
4. 找出可能导致字段错位的原因
"""

import logging
from datetime import datetime
from database_setup import get_db_connection

# 设置日志
log_file = f'analyze_temp_to_main_transfer_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def get_table_structure(table_name):
    """获取表结构"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT column_name, ordinal_position, data_type 
            FROM information_schema.columns 
            WHERE table_name = %s
            ORDER BY ordinal_position
        """, (table_name,))
        
        columns = cursor.fetchall()
        cursor.close()
        conn.close()
        
        return columns
        
    except Exception as e:
        logging.error(f"获取表结构时出错: {e}")
        return []

def compare_table_structures():
    """比较临时表和正式表的结构"""
    logging.info("🔍 比较临时表和正式表的结构...")
    
    # 获取两个表的结构
    temp_structure = get_table_structure('临时账户交易明细表')
    main_structure = get_table_structure('账户交易明细表')
    
    if not temp_structure or not main_structure:
        logging.error("❌ 无法获取表结构")
        return False
    
    logging.info(f"📊 临时表字段数: {len(temp_structure)}")
    logging.info(f"📊 正式表字段数: {len(main_structure)}")
    
    # 转换为字典便于比较
    temp_dict = {col[0]: (col[1], col[2]) for col in temp_structure}
    main_dict = {col[0]: (col[1], col[2]) for col in main_structure}
    
    # 找出关键字段的位置
    key_fields = ['IP地址', 'MAC地址', '交易发生地']
    
    logging.info("=" * 60)
    logging.info("关键字段位置对比:")
    logging.info("=" * 60)
    
    for field in key_fields:
        temp_pos = temp_dict.get(field, (None, None))[0]
        main_pos = main_dict.get(field, (None, None))[0]
        
        logging.info(f"{field}:")
        logging.info(f"  临时表位置: {temp_pos}")
        logging.info(f"  正式表位置: {main_pos}")
        
        if temp_pos and main_pos:
            if temp_pos == main_pos:
                logging.info(f"  ✅ 位置一致")
            else:
                logging.warning(f"  ⚠️ 位置不一致 (差异: {abs(temp_pos - main_pos)})")
        else:
            logging.error(f"  ❌ 字段缺失")
        
        logging.info("-" * 30)
    
    # 找出共同字段
    common_fields = set(temp_dict.keys()) & set(main_dict.keys())
    temp_only = set(temp_dict.keys()) - set(main_dict.keys())
    main_only = set(main_dict.keys()) - set(temp_dict.keys())
    
    logging.info("=" * 60)
    logging.info("字段对比统计:")
    logging.info("=" * 60)
    logging.info(f"共同字段: {len(common_fields)} 个")
    logging.info(f"仅临时表有: {len(temp_only)} 个")
    logging.info(f"仅正式表有: {len(main_only)} 个")
    
    if temp_only:
        logging.info(f"仅临时表字段: {list(temp_only)}")
    
    if main_only:
        logging.info(f"仅正式表字段: {list(main_only)}")
    
    return {
        'common_fields': common_fields,
        'temp_only': temp_only,
        'main_only': main_only,
        'temp_structure': temp_dict,
        'main_structure': main_dict
    }

def analyze_transfer_logic():
    """分析转存逻辑"""
    logging.info("\n🔍 分析转存逻辑...")
    
    try:
        # 读取temp_import_data.py中的转存逻辑
        with open('temp_import_data.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找转存相关的代码
        transfer_patterns = [
            'INSERT INTO 账户交易明细表',
            'SELECT.*FROM 临时账户交易明细表',
            'common_columns',
            'columns_str'
        ]
        
        issues = []
        
        for pattern in transfer_patterns:
            if pattern in content:
                logging.info(f"✅ 找到转存模式: {pattern}")
            else:
                logging.warning(f"⚠️ 未找到转存模式: {pattern}")
                issues.append(f"缺少转存模式: {pattern}")
        
        # 检查转存逻辑的关键部分
        if 'common_columns = list(set(temp_columns) & set(main_columns))' in content:
            logging.info("✅ 使用共同字段进行转存")
        else:
            logging.error("❌ 未使用共同字段逻辑")
            issues.append("未使用共同字段逻辑")
        
        # 检查是否有字段顺序处理
        if 'columns_str = \', \'.join([f\'"{col}"\' for col in common_columns])' in content:
            logging.info("✅ 动态生成字段列表")
        else:
            logging.error("❌ 未动态生成字段列表")
            issues.append("未动态生成字段列表")
        
        return issues
        
    except Exception as e:
        logging.error(f"❌ 分析转存逻辑时出错: {e}")
        return [f"分析转存逻辑出错: {e}"]

def simulate_transfer_process(structure_info):
    """模拟转存过程"""
    logging.info("\n🔍 模拟转存过程...")
    
    if not structure_info:
        logging.error("❌ 缺少表结构信息")
        return False
    
    common_fields = structure_info['common_fields']
    temp_structure = structure_info['temp_structure']
    main_structure = structure_info['main_structure']
    
    # 模拟转存SQL的生成
    logging.info("📊 模拟转存SQL生成:")
    
    # 按照代码逻辑，common_columns是无序的set操作结果
    common_columns_list = list(common_fields)
    
    logging.info(f"共同字段数量: {len(common_columns_list)}")
    
    # 检查关键字段是否在共同字段中
    key_fields = ['IP地址', 'MAC地址', '交易发生地']
    
    for field in key_fields:
        if field in common_columns_list:
            temp_pos = temp_structure[field][0]
            main_pos = main_structure[field][0]
            logging.info(f"✅ {field} 在共同字段中 (临时表位置:{temp_pos}, 正式表位置:{main_pos})")
        else:
            logging.error(f"❌ {field} 不在共同字段中")
    
    # 模拟SQL生成
    columns_str = ', '.join([f'"{col}"' for col in common_columns_list])
    
    simulated_sql = f"""
    INSERT INTO 账户交易明细表({columns_str})
    SELECT {columns_str}
    FROM 临时账户交易明细表
    WHERE 案件编号 = %s
    """
    
    logging.info("生成的转存SQL:")
    logging.info(simulated_sql[:200] + "..." if len(simulated_sql) > 200 else simulated_sql)
    
    return True

def check_actual_data():
    """检查实际数据中的问题"""
    logging.info("\n🔍 检查实际数据中的问题...")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查临时表中是否有数据
        cursor.execute("SELECT COUNT(*) FROM 临时账户交易明细表")
        temp_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM 账户交易明细表")
        main_count = cursor.fetchone()[0]
        
        logging.info(f"📊 临时表记录数: {temp_count}")
        logging.info(f"📊 正式表记录数: {main_count}")
        
        if temp_count > 0:
            # 检查临时表中关键字段的数据样本
            cursor.execute("""
                SELECT "IP地址", "MAC地址", "交易发生地"
                FROM 临时账户交易明细表
                WHERE ("IP地址" IS NOT NULL AND "IP地址" != '')
                   OR ("MAC地址" IS NOT NULL AND "MAC地址" != '')
                   OR ("交易发生地" IS NOT NULL AND "交易发生地" != '')
                LIMIT 5
            """)
            
            temp_samples = cursor.fetchall()
            
            logging.info("临时表关键字段样本数据:")
            for i, sample in enumerate(temp_samples, 1):
                logging.info(f"  样本{i}: IP={sample[0]}, MAC={sample[1]}, 交易发生地={sample[2]}")
        
        if main_count > 0:
            # 检查正式表中关键字段的数据样本
            cursor.execute("""
                SELECT "IP地址", "MAC地址", "交易发生地"
                FROM 账户交易明细表
                WHERE ("IP地址" IS NOT NULL AND "IP地址" != '')
                   OR ("MAC地址" IS NOT NULL AND "MAC地址" != '')
                   OR ("交易发生地" IS NOT NULL AND "交易发生地" != '')
                LIMIT 5
            """)
            
            main_samples = cursor.fetchall()
            
            logging.info("正式表关键字段样本数据:")
            for i, sample in enumerate(main_samples, 1):
                logging.info(f"  样本{i}: IP={sample[0]}, MAC={sample[1]}, 交易发生地={sample[2]}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 检查实际数据时出错: {e}")
        return False

def generate_fix_recommendations(structure_info, transfer_issues):
    """生成修复建议"""
    logging.info("\n💡 生成修复建议...")
    
    recommendations = []
    
    # 基于结构分析的建议
    if structure_info:
        key_fields = ['IP地址', 'MAC地址', '交易发生地']
        temp_structure = structure_info['temp_structure']
        main_structure = structure_info['main_structure']
        
        for field in key_fields:
            if field in temp_structure and field in main_structure:
                temp_pos = temp_structure[field][0]
                main_pos = main_structure[field][0]
                
                if temp_pos != main_pos:
                    recommendations.append(f"⚠️ {field} 在两表中位置不同 (临时表:{temp_pos}, 正式表:{main_pos})")
    
    # 基于转存逻辑的建议
    if transfer_issues:
        recommendations.append("🔧 转存逻辑问题:")
        for issue in transfer_issues:
            recommendations.append(f"  - {issue}")
    
    # 通用建议
    recommendations.extend([
        "\n🛠️ 修复建议:",
        "1. 验证转存过程使用的是字段名匹配而不是位置匹配",
        "2. 检查common_columns的生成逻辑是否正确",
        "3. 确认INSERT...SELECT语句中字段顺序一致",
        "4. 添加转存过程的调试日志",
        "5. 在转存前后对比关键字段的数据"
    ])
    
    for rec in recommendations:
        logging.info(rec)
    
    return recommendations

def main():
    """主函数"""
    print(f"🔍 开始分析临时表到正式表的转存过程")
    print(f"📄 详细日志保存到: {log_file}")
    
    # 比较表结构
    logging.info("=" * 60)
    logging.info("步骤1: 比较表结构")
    logging.info("=" * 60)
    structure_info = compare_table_structures()
    
    # 分析转存逻辑
    logging.info("=" * 60)
    logging.info("步骤2: 分析转存逻辑")
    logging.info("=" * 60)
    transfer_issues = analyze_transfer_logic()
    
    # 模拟转存过程
    logging.info("=" * 60)
    logging.info("步骤3: 模拟转存过程")
    logging.info("=" * 60)
    simulate_transfer_process(structure_info)
    
    # 检查实际数据
    logging.info("=" * 60)
    logging.info("步骤4: 检查实际数据")
    logging.info("=" * 60)
    check_actual_data()
    
    # 生成修复建议
    logging.info("=" * 60)
    logging.info("步骤5: 生成修复建议")
    logging.info("=" * 60)
    recommendations = generate_fix_recommendations(structure_info, transfer_issues)
    
    # 总结
    print("\\n" + "=" * 60)
    print("🎯 分析结果总结")
    print("=" * 60)
    
    if structure_info:
        print(f"📊 表结构对比: 共同字段 {len(structure_info['common_fields'])} 个")
    
    if transfer_issues:
        print(f"⚠️ 转存逻辑问题: {len(transfer_issues)} 个")
    else:
        print("✅ 转存逻辑检查通过")
    
    print("\\n🎯 关键发现:")
    print("转存过程使用INSERT...SELECT语句，基于字段名匹配而不是位置匹配")
    print("如果IP地址、MAC地址出现在交易发生地字段中，问题可能在于:")
    print("1. 临时表中的数据本身就是错误的")
    print("2. 转存过程中字段映射出现问题")
    print("3. 共同字段识别逻辑有误")
    
    return 0

if __name__ == "__main__":
    exit(main())
