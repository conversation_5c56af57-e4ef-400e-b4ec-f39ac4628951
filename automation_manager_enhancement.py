
# AutomationManager增强方案

class AutomationManager:
    """自动化管理器 - 增强版本"""
    
    def __init__(self, import_window):
        self.import_window = import_window
        self.is_automation_active = False
        self.current_step = "idle"
        self.step_timer = QTimer()
        self.step_timer.timeout.connect(self.execute_next_step)
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.countdown_seconds = 0
        self.status_label = None
        
        # 🔧 增强：添加错误重试机制
        self.max_retries = 3
        self.current_retries = 0
        self.last_error = None
        
        # 🔧 增强：添加数据量检查配置
        self.large_data_threshold = 100000  # 10万条记录
        self.auto_confirm_large_data = True  # 自动确认大数据量导入
        
        # 步骤配置
        self.steps = {
            "match_fields": {
                "name": "自动匹配字段",
                "wait_time": 30,
                "next_step": "waiting_for_import"
            },
            "import_data": {
                "name": "自动导入数据",
                "wait_time": 30,  # 🔧 增强：根据数据量动态调整等待时间
                "next_step": "waiting_for_clean"
            },
            "clean_data": {
                "name": "自动数据清洗",
                "wait_time": 60,
                "next_step": "idle"
            }
        }
        
        logging.info("✅ 增强版自动化管理器已初始化")
    
    def start_automation(self):
        """启动自动化流程 - 增强版本"""
        if self.is_automation_active:
            logging.warning("⚠️ 自动化流程已在运行中")
            return
        
        self.is_automation_active = True
        self.current_step = "waiting_for_match"
        self.current_retries = 0
        self.last_error = None
        
        # 🔧 增强：检查数据量并调整配置
        self.adjust_automation_config()
        
        self.start_timer("match_fields")
        logging.info("🚀 增强版自动化流程已启动")
    
    def adjust_automation_config(self):
        """根据数据量调整自动化配置"""
        try:
            # 估算数据量
            total_data_count = self.estimate_total_data_count()
            
            if total_data_count > self.large_data_threshold:
                logging.info(f"📊 检测到大数据量: {total_data_count} 条记录")
                
                # 调整导入步骤的等待时间
                self.steps["import_data"]["wait_time"] = min(120, max(60, total_data_count // 10000))
                
                # 启用大数据量自动确认
                self.auto_confirm_large_data = True
                
                logging.info(f"🔧 已调整导入等待时间为 {self.steps['import_data']['wait_time']} 秒")
            else:
                logging.info(f"📊 数据量正常: {total_data_count} 条记录")
                
        except Exception as e:
            logging.warning(f"⚠️ 调整自动化配置时出错: {e}")
    
    def estimate_total_data_count(self):
        """估算总数据量"""
        try:
            # 从文件树中估算数据量
            total_count = 0
            
            if hasattr(self.import_window, 'file_tree'):
                root = self.import_window.file_tree.invisibleRootItem()
                for i in range(root.childCount()):
                    file_item = root.child(i)
                    for j in range(file_item.childCount()):
                        worksheet_item = file_item.child(j)
                        # 尝试从工作表项中获取数据量信息
                        text = worksheet_item.text(0)
                        if '行' in text:
                            try:
                                # 提取行数信息
                                import re
                                match = re.search(r'(\d+)行', text)
                                if match:
                                    total_count += int(match.group(1))
                            except:
                                pass
            
            return total_count
            
        except Exception as e:
            logging.warning(f"⚠️ 估算数据量时出错: {e}")
            return 0
    
    def execute_next_step(self):
        """执行下一步操作 - 增强版本"""
        try:
            if self.current_step == "waiting_for_match":
                self.update_status_display("🔧 正在执行自动匹配字段...")
                self.import_window.auto_match_fields_by_rules()
                
            elif self.current_step == "waiting_for_import":
                self.update_status_display("🔧 正在执行自动导入数据...")
                
                # 🔧 增强：在导入前检查是否需要自动确认大数据量
                if self.auto_confirm_large_data:
                    self.setup_large_data_auto_confirm()
                
                self.import_window.confirm_import()
                
            elif self.current_step == "waiting_for_clean":
                self.update_status_display("🔧 正在执行自动数据清洗...")
                self.import_window.clean_data()
                self.stop_automation()
                
            # 🔧 增强：重置重试计数
            self.current_retries = 0
            self.last_error = None
                
        except Exception as e:
            logging.error(f"❌ 自动化步骤执行失败: {e}")
            self.handle_automation_error(e)
    
    def setup_large_data_auto_confirm(self):
        """设置大数据量自动确认"""
        logging.info("🔧 设置大数据量自动确认模式")
        
        # 临时替换确认对话框为自动确认版本
        if hasattr(self.import_window, 'show_large_data_confirmation'):
            original_method = self.import_window.show_large_data_confirmation
            
            def auto_confirm_large_data(*args, **kwargs):
                logging.info("🤖 大数据量自动确认：跳过用户确认")
                return True  # 自动返回确认
            
            self.import_window.show_large_data_confirmation = auto_confirm_large_data
    
    def handle_automation_error(self, error):
        """处理自动化错误 - 增强版本"""
        self.current_retries += 1
        self.last_error = str(error)
        
        if self.current_retries <= self.max_retries:
            logging.warning(f"⚠️ 自动化步骤失败，{5}秒后重试 ({self.current_retries}/{self.max_retries})")
            self.update_status_display(f"⚠️ 步骤失败，{5}秒后重试 ({self.current_retries}/{self.max_retries})")
            
            # 延迟重试
            QTimer.singleShot(5000, self.execute_next_step)
        else:
            logging.error(f"❌ 自动化步骤多次失败，停止自动化: {error}")
            self.update_status_display(f"❌ 自动化失败: {error}")
            self.stop_automation()
    
    def is_active(self):
        """检查自动化是否激活 - 增强版本"""
        return self.is_automation_active and self.current_step != "idle"
