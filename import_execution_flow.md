# 确认导入按钮执行流程详解

## 📋 **总体执行流程概览**

当用户点击"确认导入"按钮后，程序会执行以下主要步骤：

```
用户点击"确认导入" 
    ↓
1. 检查匹配规则文件
    ↓
2. 验证工作表匹配状态
    ↓
3. 按表类型分组文件
    ↓
4. 创建进度对话框
    ↓
5. 按表类型顺序导入数据
    ↓
6. 创建数据库索引
    ↓
7. 显示导入统计
    ↓
8. 自动数据清洗确认
```

## 🔍 **详细执行步骤分析**

### **第1步：检查匹配规则文件**
**方法**：`confirm_import()` - 开始部分

**执行逻辑**：
1. **检查新版映射文件**：`mapp/temp_mapping.json`
2. **兼容旧版文件**：如果新版不存在，检查根目录的 `temp_mapping.json`
3. **文件迁移处理**：提示用户是否迁移旧版文件到新位置
4. **文件验证**：检查文件是否为空或格式错误

**关键代码位置**：`import_data.py` 第8342-8422行

### **第2步：验证工作表匹配状态**
**方法**：`confirm_import()` - 中间部分

**执行逻辑**：
1. **遍历文件树**：检查每个工作表的匹配状态
2. **表类型验证**：确认每个工作表都选择了表类型
3. **字段匹配验证**：确认每个工作表都完成了字段匹配
4. **状态分类**：
   - `incomplete_table_type_files` - 未选择表类型的文件
   - `incomplete_field_files` - 未完成字段匹配的文件
   - `files_by_table_type` - 可以导入的文件（按表类型分组）

**关键代码位置**：`import_data.py` 第8432-8553行

### **第3步：按表类型分组文件**
**执行逻辑**：
1. **数据读取**：使用 `read_full_worksheet_data()` 读取完整工作表数据
2. **表类型分组**：将文件按表类型（如"账户交易明细表"、"开户信息表"等）分组
3. **映射规则提取**：为每个表类型提取对应的字段映射规则
4. **创建专用映射文件**：为每个表类型创建独立的临时映射文件

**数据结构**：
```python
files_by_table_type = {
    "账户交易明细表": [
        (file_path, dataframe, table_type, matched_fields, worksheet_name),
        ...
    ],
    "开户信息表": [
        (file_path, dataframe, table_type, matched_fields, worksheet_name),
        ...
    ]
}
```

### **第4步：创建进度对话框**
**方法**：`GoogleStyleProgressDialog`

**执行逻辑**：
1. **初始化统计信息**：
   ```python
   self.import_stats = {
       'total_tables': len(files_by_table_type),
       'total_files': total_files_to_import,
       'imported_records': 0,
       'failed_files': 0,
       'failed_records': 0,
       'imported_tables': 0
   }
   ```
2. **显示进度对话框**：Google风格的进度条界面
3. **记录开始时间**：用于计算导入耗时

### **第5步：按表类型顺序导入数据**
**方法**：`import_next_table_type()` + `DataImportThread`

**执行逻辑**：
1. **顺序处理**：按表类型顺序逐个导入（不是并行）
2. **创建导入线程**：为每个表类型创建 `DataImportThread`
3. **数据导入过程**：
   - 读取DataFrame数据
   - 应用字段映射规则
   - 批量插入到PostgreSQL数据库
   - 使用 `execute_values` 优化插入性能
4. **进度更新**：实时更新进度条和状态信息
5. **错误处理**：记录失败的文件和错误信息

**关键组件**：
- **`DataImportThread`**：负责实际的数据导入工作
- **信号槽机制**：用于线程间通信和进度更新
- **批量插入**：使用PostgreSQL的 `execute_values` 提高性能

### **第6步：创建数据库索引**
**方法**：`all_imports_finished()`

**执行逻辑**：
1. **检查表存在性**：确认"账户交易明细表"等主要表已创建
2. **创建性能索引**：
   - 交易账号相关索引
   - 对手账号相关索引  
   - 文本搜索索引（GIN索引）
   - 日期和金额索引
3. **事务管理**：使用事务确保索引创建的原子性
4. **错误处理**：索引创建失败时回滚事务

**创建的索引**：
```sql
-- 交易账号索引
CREATE INDEX idx_jiaoyimingxi_jiaoyi_zhangkahao ON 账户交易明细表 (交易账卡号);
CREATE INDEX idx_jiaoyimingxi_jiaoyi_zhanghao ON 账户交易明细表 (交易账号);
CREATE INDEX idx_jiaoyimingxi_jyzhdigits ON 账户交易明细表 (交易账号_digits);

-- 对手账号索引
CREATE INDEX idx_jiaoyimingxi_dshzhdigits ON 账户交易明细表 (对手账号_digits);
CREATE INDEX idx_jiaoyimingxi_dskahdigits ON 账户交易明细表 (对手卡号_digits);

-- 文本搜索索引
CREATE INDEX idx_jiaoyimingxi_jiaoyi_huming ON 账户交易明细表 USING gin(to_tsvector('jiebacfg', 交易户名));
CREATE INDEX idx_jiaoyimingxi_duishou_huming ON 账户交易明细表 USING gin(to_tsvector('jiebacfg', 对手户名));

-- 日期金额索引
CREATE INDEX idx_jiaoyimingxi_date ON 账户交易明细表 (交易日期);
CREATE INDEX idx_jiaoyimingxi_amount ON 账户交易明细表 (交易金额);
```

### **第7步：显示导入统计**
**执行逻辑**：
1. **计算统计信息**：
   - 导入表数量和文件数量
   - 导入记录总数
   - 导入成功率
   - 总耗时和平均速度
2. **显示详细报告**：使用 `AutoConfirmMessageBox` 显示统计信息
3. **清理临时文件**：删除所有临时映射文件

**统计信息示例**：
```
🎉 数据导入任务完成！

📊 导入统计信息:
• 导入表数量: 3/3 个
• 导入文件数量: 15/15 个  
• 导入记录总数: 125,847 条
• 导入成功率: 100.0%

⏱️ 耗时统计:
• 总耗时: 2 分 35 秒
• 平均速度: 812 条/秒

✅ 数据已成功导入到数据库，并创建了必要的索引
```

### **第8步：自动数据清洗确认**
**方法**：`show_auto_data_cleaning_dialog()`

**执行逻辑**：
1. **显示清洗确认对话框**：倒计时15秒自动确认
2. **用户选择**：
   - 自动确认：开始数据清洗流程
   - 手动取消：跳过数据清洗
3. **启动数据清洗**：如果确认，调用 `data_cleaning.py` 中的清洗流程

## 🔧 **关键技术实现**

### **1. 线程管理**
- **主线程**：UI界面和用户交互
- **导入线程**：`DataImportThread` 负责数据导入
- **信号槽通信**：线程间安全通信机制

### **2. 数据库操作优化**
- **批量插入**：使用 `execute_values` 提高插入性能
- **事务管理**：确保数据一致性
- **索引创建**：提高后续查询性能

### **3. 错误处理机制**
- **文件级错误处理**：单个文件失败不影响其他文件
- **事务回滚**：数据库操作失败时自动回滚
- **详细日志记录**：记录所有操作和错误信息

### **4. 进度反馈**
- **实时进度更新**：显示当前导入进度
- **状态信息**：显示当前处理的表类型和文件
- **完成统计**：显示详细的导入统计信息

## ⚠️ **注意事项**

### **1. 执行顺序**
- 表类型按顺序导入，不是并行处理
- 每个表类型完成后才开始下一个
- 所有表类型完成后才创建索引

### **2. 错误恢复**
- 单个文件导入失败不会中断整个流程
- 数据库操作失败会回滚事务
- 详细错误信息记录在日志中

### **3. 性能考虑**
- 使用批量插入提高数据库写入性能
- 创建索引提高后续查询性能
- 按表类型分组减少数据库连接开销

这个执行流程确保了数据导入的可靠性、性能和用户体验！
