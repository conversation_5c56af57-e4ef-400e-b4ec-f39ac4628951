2025-08-05 19:17:46.702 - ERROR - [MainThread:22204] - import_data.py:8022 - all_imports_finished() - 创建日期金额索引失败: 错误:  当前事务被终止, 事务块结束之前的查询被忽略

2025-08-05 19:17:46.703 - ERROR - [MainThread:22204] - import_data.py:8031 - all_imports_finished() - ❌ 创建索引时发生错误: 错误:  当前事务被终止, 事务块结束之前的查询被忽略

2025-08-05 19:18:22.832 - CRITICAL - [MainThread:22204] - enhanced_logging_patch.py:322 - monitored_exit() - 🚨 程序即将退出! 退出码: 0
2025-08-05 19:18:22.832 - CRITICAL - [MainThread:22204] - enhanced_logging_patch.py:323 - monitored_exit() - 退出时的堆栈跟踪:
2025-08-05 19:18:22.832 - CRITICAL - [MainThread:22204] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\main.py", line 327, in <module>
    main()
2025-08-05 19:18:22.832 - CRITICAL - [MainThread:22204] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\main.py", line 324, in main
    sys.exit(app.exec())
2025-08-05 19:18:22.832 - CRITICAL - [MainThread:22204] - enhanced_logging_patch.py:328 - monitored_exit() -   File "g:\数据分析系统20250725\enhanced_logging_patch.py", line 326, in monitored_exit
    stack = traceback.format_stack()
